#------------------------------------------------------------------------------
# File:         GeoTiff.pm
#
# Description:  Read GeoTiff meta information
#
# Revisions:    02/23/2004 - <PERSON><PERSON> Created
#               02/25/2004 - PH Added new codes from libgeotiff-1.2.1
#               02/01/2007 - PH Added new codes from libgeotiff-1.2.3
#               01/22/2014 - PH Added new code from libgeotiff-1.4.0
#               01/19/2015 - PH Added ChartTIFF tags
#
# References:   1) ftp://ftp.remotesensing.org/geotiff/libgeotiff/libgeotiff-1.1.4.tar.gz
#               2) http://www.charttiff.com/whitepapers.shtml
#------------------------------------------------------------------------------

package Image::ExifTool::GeoTiff;

use strict;
use vars qw($VERSION);
use Image::ExifTool qw(:DataAccess :Utils);

$VERSION = '1.12';

# format codes for geoTiff directory entries
my %geoTiffFormat = (
    0      => 'int16u', # (value is stored in offset, and count is 1)
    0x87af => 'int16u', # (value is stored after directory)
    0x87b0 => 'double',
    0x87b1 => 'string',
);

my %epsg_units = (
    9001 => 'Linear Meter',
    9002 => 'Linear Foot',
    9003 => 'Linear Foot US Survey',
    9004 => 'Linear Foot Modified American',
    9005 => 'Linear Foot Clarke',
    9006 => 'Linear Foot Indian',
    9007 => 'Linear Link',
    9008 => 'Linear Link Benoit',
    9009 => 'Linear Link Sears',
    9010 => 'Linear Chain Benoit',
    9011 => 'Linear Chain Sears',
    9012 => 'Linear Yard Sears',
    9013 => 'Linear Yard Indian',
    9014 => 'Linear Fathom',
    9015 => 'Linear Mile International Nautical',
    9101 => 'Angular Radian',
    9102 => 'Angular Degree',
    9103 => 'Angular Arc Minute',
    9104 => 'Angular Arc Second',
    9105 => 'Angular Grad',
    9106 => 'Angular Gon',
    9107 => 'Angular DMS',
    9108 => 'Angular DMS Hemisphere',
    32767 => 'User Defined',
);

my %epsg_vertcs = (
    0 => 'Undefined',
    5001 => 'Airy 1830 ellipsoid',
    5002 => 'Airy Modified 1849 ellipsoid',
    5003 => 'ANS ellipsoid',
    5004 => 'Bessel 1841 ellipsoid',
    5005 => 'Bessel Modified ellipsoid',
    5006 => 'Bessel Namibia ellipsoid',
    5007 => 'Clarke 1858 ellipsoid',
    5008 => 'Clarke 1866 ellipsoid',
    5010 => 'Clarke 1880 Benoit ellipsoid',
    5011 => 'Clarke 1880 IGN ellipsoid',
    5012 => 'Clarke 1880 RGS ellipsoid',
    5013 => 'Clarke 1880 Arc ellipsoid',
    5014 => 'Clarke 1880 SGA 1922 ellipsoid',
    5015 => 'Everest 1830 1937 Adjustment ellipsoid',
    5016 => 'Everest 1830 1967 Definition ellipsoid',
    5017 => 'Everest 1830 1975 Definition ellipsoid',
    5018 => 'Everest 1830 Modified ellipsoid',
    5019 => 'GRS 1980 ellipsoid',
    5020 => 'Helmert 1906 ellipsoid',
    5021 => 'INS ellipsoid',
    5022 => 'International 1924 ellipsoid',
    5023 => 'International 1967 ellipsoid',
    5024 => 'Krassowsky 1940 ellipsoid',
    5025 => 'NWL 9D ellipsoid',
    5026 => 'NWL 10D ellipsoid',
    5027 => 'Plessis 1817 ellipsoid',
    5028 => 'Struve 1860 ellipsoid',
    5029 => 'War Office ellipsoid',
    5030 => 'WGS 84 ellipsoid',
    5031 => 'GEM 10C ellipsoid',
    5032 => 'OSU86F ellipsoid',
    5033 => 'OSU91A ellipsoid',
    5101 => 'Newlyn',
    5102 => 'North American Vertical Datum 1929',
    5103 => 'North American Vertical Datum 1988',
    5104 => 'Yellow Sea 1956',
    5105 => 'Baltic Sea',
    5106 => 'Caspian Sea',
    32767 => 'User Defined',
);

%Image::ExifTool::GeoTiff::Main = (
    GROUPS => { 2 => 'Location' },
    1 => {
        # this isn't a real GeoTiff key, but put it here
        # so it will appear in tag lists since we generate it below
        Name => 'GeoTiffVersion',
    },
    1024 => {
        Name => 'GTModelType',
        PrintConv => {
            1 => 'Projected',
            2 => 'Geographic',
            3 => 'Geocentric',
            32767 => 'User Defined',
        },
    },
    1025 => {
        Name => 'GTRasterType',
        PrintConv => {
            1 => 'Pixel Is Area',
            2 => 'Pixel Is Point',
            32767 => 'User Defined',
        },
    },
    1026 => 'GTCitation',
    2048 => {
        Name => 'GeographicType',
        PrintConv => {
            # epsg_gcs
            4001 => 'Airy 1830',
            4002 => 'Airy Modified 1849',
            4003 => 'Australian National Spheroid',
            4004 => 'Bessel 1841',
            4005 => 'Bessel Modified',
            4006 => 'Bessel Namibia',
            4007 => 'Clarke 1858',
            4008 => 'Clarke 1866',
            4009 => 'Clarke 1866 Michigan',
            4010 => 'Clarke 1880 Benoit',
            4011 => 'Clarke 1880 IGN',
            4012 => 'Clarke 1880 RGS',
            4013 => 'Clarke 1880 Arc',
            4014 => 'Clarke 1880 SGA 1922',
            4015 => 'Everest 1830 1937 Adjustment',
            4016 => 'Everest 1830 1967 Definition',
            4017 => 'Everest 1830 1975 Definition',
            4018 => 'Everest 1830 Modified',
            4019 => 'GRS 1980',
            4020 => 'Helmert 1906',
            4021 => 'Indonesian National Spheroid',
            4022 => 'International 1924',
            4023 => 'International 1967',
            4024 => 'Krassowsky 1940',
            4025 => 'NWL9D',
            4026 => 'NWL10D',
            4027 => 'Plessis 1817',
            4028 => 'Struve 1860',
            4029 => 'War Office',
            4030 => 'WGS84',
            4031 => 'GEM10C',
            4032 => 'OSU86F',
            4033 => 'OSU91A',
            4034 => 'Clarke 1880',
            4035 => 'Sphere',
            4120 => 'Greek',
            4121 => 'GGRS87',
            4123 => 'KKJ',
            4124 => 'RT90',
            4133 => 'EST92',
            4815 => 'Greek Athens',
            4201 => 'Adindan',
            4202 => 'AGD66',
            4203 => 'AGD84',
            4204 => 'Ain el Abd',
            4205 => 'Afgooye',
            4206 => 'Agadez',
            4207 => 'Lisbon',
            4208 => 'Aratu',
            4209 => 'Arc 1950',
            4210 => 'Arc 1960',
            4211 => 'Batavia',
            4212 => 'Barbados',
            4213 => 'Beduaram',
            4214 => 'Beijing 1954',
            4215 => 'Belge 1950',
            4216 => 'Bermuda 1957',
            4217 => 'Bern 1898',
            4218 => 'Bogota',
            4219 => 'Bukit Rimpah',
            4220 => 'Camacupa',
            4221 => 'Campo Inchauspe',
            4222 => 'Cape',
            4223 => 'Carthage',
            4224 => 'Chua',
            4225 => 'Corrego Alegre',
            4226 => 'Cote d Ivoire',
            4227 => 'Deir ez Zor',
            4228 => 'Douala',
            4229 => 'Egypt 1907',
            4230 => 'ED50',
            4231 => 'ED87',
            4232 => 'Fahud',
            4233 => 'Gandajika 1970',
            4234 => 'Garoua',
            4235 => 'Guyane Francaise',
            4236 => 'Hu Tzu Shan',
            4237 => 'HD72',
            4238 => 'ID74',
            4239 => 'Indian 1954',
            4240 => 'Indian 1975',
            4241 => 'Jamaica 1875',
            4242 => 'JAD69',
            4243 => 'Kalianpur',
            4244 => 'Kandawala',
            4245 => 'Kertau',
            4246 => 'KOC',
            4247 => 'La Canoa',
            4248 => 'PSAD56',
            4249 => 'Lake',
            4250 => 'Leigon',
            4251 => 'Liberia 1964',
            4252 => 'Lome',
            4253 => 'Luzon 1911',
            4254 => 'Hito XVIII 1963',
            4255 => 'Herat North',
            4256 => 'Mahe 1971',
            4257 => 'Makassar',
            4258 => 'EUREF89',
            4259 => 'Malongo 1987',
            4260 => 'Manoca',
            4261 => 'Merchich',
            4262 => 'Massawa',
            4263 => 'Minna',
            4264 => 'Mhast',
            4265 => 'Monte Mario',
            4266 => 'M poraloko',
            4267 => 'NAD27',
            4268 => 'NAD Michigan',
            4269 => 'NAD83',
            4270 => 'Nahrwan 1967',
            4271 => 'Naparima 1972',
            4272 => 'GD49',
            4273 => 'NGO 1948',
            4274 => '73',
            4275 => 'NTF',
            4276 => 'NSWC 9Z 2',
            4277 => 'OSGB 1936',
            4278 => 'OSGB70',
            4279 => 'OS SN80',
            4280 => 'Padang',
            4281 => 'Palestine 1923',
            4282 => 'Pointe Noire',
            4283 => 'GDA94',
            4284 => 'Pulkovo 1942',
            4285 => 'Qatar',
            4286 => 'Qatar 1948',
            4287 => 'Qornoq',
            4288 => 'Loma Quintana',
            4289 => 'Amersfoort',
            4290 => 'RT38',
            4291 => 'SAD69',
            4292 => 'Sapper Hill 1943',
            4293 => 'Schwarzeck',
            4294 => 'Segora',
            4295 => 'Serindung',
            4296 => 'Sudan',
            4297 => 'Tananarive',
            4298 => 'Timbalai 1948',
            4299 => 'TM65',
            4300 => 'TM75',
            4301 => 'Tokyo',
            4302 => 'Trinidad 1903',
            4303 => 'TC 1948',
            4304 => 'Voirol 1875',
            4305 => 'Voirol Unifie',
            4306 => 'Bern 1938',
            4307 => 'Nord Sahara 1959',
            4308 => 'Stockholm 1938',
            4309 => 'Yacare',
            4310 => 'Yoff',
            4311 => 'Zanderij',
            4312 => 'MGI',
            4313 => 'Belge 1972',
            4314 => 'DHDN',
            4315 => 'Conakry 1905',
            4317 => 'Dealul Piscului 1970',
            4322 => 'WGS 72',
            4324 => 'WGS 72BE',
            4326 => 'WGS 84',
            4801 => 'Bern 1898 Bern',
            4802 => 'Bogota Bogota',
            4803 => 'Lisbon Lisbon',
            4804 => 'Makassar Jakarta',
            4805 => 'MGI Ferro',
            4806 => 'Monte Mario Rome',
            4807 => 'NTF Paris',
            4808 => 'Padang Jakarta',
            4809 => 'Belge 1950 Brussels',
            4810 => 'Tananarive Paris',
            4811 => 'Voirol 1875 Paris',
            4812 => 'Voirol Unifie Paris',
            4813 => 'Batavia Jakarta',
            4901 => 'ATF Paris',
            4902 => 'NDG Paris',
            32767 => 'User Defined',
        },
    },
    2049 => 'GeogCitation',
    2050 => {
        Name => 'GeogGeodeticDatum',
        PrintConv => {
            # epsg_datum,
            6001 => 'Airy 1830',
            6002 => 'Airy Modified 1849',
            6003 => 'Australian National Spheroid',
            6004 => 'Bessel 1841',
            6005 => 'Bessel Modified',
            6006 => 'Bessel Namibia',
            6007 => 'Clarke 1858',
            6008 => 'Clarke 1866',
            6009 => 'Clarke 1866 Michigan',
            6010 => 'Clarke 1880 Benoit',
            6011 => 'Clarke 1880 IGN',
            6012 => 'Clarke 1880 RGS',
            6013 => 'Clarke 1880 Arc',
            6014 => 'Clarke 1880 SGA 1922',
            6015 => 'Everest 1830 1937 Adjustment',
            6016 => 'Everest 1830 1967 Definition',
            6017 => 'Everest 1830 1975 Definition',
            6018 => 'Everest 1830 Modified',
            6019 => 'GRS 1980',
            6020 => 'Helmert 1906',
            6021 => 'Indonesian National Spheroid',
            6022 => 'International 1924',
            6023 => 'International 1967',
            6024 => 'Krassowsky 1960',
            6025 => 'NWL9D',
            6026 => 'NWL10D',
            6027 => 'Plessis 1817',
            6028 => 'Struve 1860',
            6029 => 'War Office',
            6030 => 'WGS84',
            6031 => 'GEM10C',
            6032 => 'OSU86F',
            6033 => 'OSU91A',
            6034 => 'Clarke 1880',
            6035 => 'Sphere',
            6201 => 'Adindan',
            6202 => 'Australian Geodetic Datum 1966',
            6203 => 'Australian Geodetic Datum 1984',
            6204 => 'Ain el Abd 1970',
            6205 => 'Afgooye',
            6206 => 'Agadez',
            6207 => 'Lisbon',
            6208 => 'Aratu',
            6209 => 'Arc 1950',
            6210 => 'Arc 1960',
            6211 => 'Batavia',
            6212 => 'Barbados',
            6213 => 'Beduaram',
            6214 => 'Beijing 1954',
            6215 => 'Reseau National Belge 1950',
            6216 => 'Bermuda 1957',
            6217 => 'Bern 1898',
            6218 => 'Bogota',
            6219 => 'Bukit Rimpah',
            6220 => 'Camacupa',
            6221 => 'Campo Inchauspe',
            6222 => 'Cape',
            6223 => 'Carthage',
            6224 => 'Chua',
            6225 => 'Corrego Alegre',
            6226 => 'Cote d Ivoire',
            6227 => 'Deir ez Zor',
            6228 => 'Douala',
            6229 => 'Egypt 1907',
            6230 => 'European Datum 1950',
            6231 => 'European Datum 1987',
            6232 => 'Fahud',
            6233 => 'Gandajika 1970',
            6234 => 'Garoua',
            6235 => 'Guyane Francaise',
            6236 => 'Hu Tzu Shan',
            6237 => 'Hungarian Datum 1972',
            6238 => 'Indonesian Datum 1974',
            6239 => 'Indian 1954',
            6240 => 'Indian 1975',
            6241 => 'Jamaica 1875',
            6242 => 'Jamaica 1969',
            6243 => 'Kalianpur',
            6244 => 'Kandawala',
            6245 => 'Kertau',
            6246 => 'Kuwait Oil Company',
            6247 => 'La Canoa',
            6248 => 'Provisional S American Datum 1956',
            6249 => 'Lake',
            6250 => 'Leigon',
            6251 => 'Liberia 1964',
            6252 => 'Lome',
            6253 => 'Luzon 1911',
            6254 => 'Hito XVIII 1963',
            6255 => 'Herat North',
            6256 => 'Mahe 1971',
            6257 => 'Makassar',
            6258 => 'European Reference System 1989',
            6259 => 'Malongo 1987',
            6260 => 'Manoca',
            6261 => 'Merchich',
            6262 => 'Massawa',
            6263 => 'Minna',
            6264 => 'Mhast',
            6265 => 'Monte Mario',
            6266 => 'M poraloko',
            6267 => 'North American Datum 1927',
            6268 => 'NAD Michigan',
            6269 => 'North American Datum 1983',
            6270 => 'Nahrwan 1967',
            6271 => 'Naparima 1972',
            6272 => 'New Zealand Geodetic Datum 1949',
            6273 => 'NGO 1948',
            6274 => 'Datum 73',
            6275 => 'Nouvelle Triangulation Francaise',
            6276 => 'NSWC 9Z 2',
            6277 => 'OSGB 1936',
            6278 => 'OSGB 1970 SN',
            6279 => 'OS SN 1980',
            6280 => 'Padang 1884',
            6281 => 'Palestine 1923',
            6282 => 'Pointe Noire',
            6283 => 'Geocentric Datum of Australia 1994',
            6284 => 'Pulkovo 1942',
            6285 => 'Qatar',
            6286 => 'Qatar 1948',
            6287 => 'Qornoq',
            6288 => 'Loma Quintana',
            6289 => 'Amersfoort',
            6290 => 'RT38',
            6291 => 'South American Datum 1969',
            6292 => 'Sapper Hill 1943',
            6293 => 'Schwarzeck',
            6294 => 'Segora',
            6295 => 'Serindung',
            6296 => 'Sudan',
            6297 => 'Tananarive 1925',
            6298 => 'Timbalai 1948',
            6299 => 'TM65',
            6300 => 'TM75',
            6301 => 'Tokyo',
            6302 => 'Trinidad 1903',
            6303 => 'Trucial Coast 1948',
            6304 => 'Voirol 1875',
            6305 => 'Voirol Unifie 1960',
            6306 => 'Bern 1938',
            6307 => 'Nord Sahara 1959',
            6308 => 'Stockholm 1938',
            6309 => 'Yacare',
            6310 => 'Yoff',
            6311 => 'Zanderij',
            6312 => 'Militar Geographische Institut',
            6313 => 'Reseau National Belge 1972',
            6314 => 'Deutsche Hauptdreiecksnetz',
            6315 => 'Conakry 1905',
            6317 => 'Dealul Piscului 1970',
            6322 => 'WGS72',
            6324 => 'WGS72 Transit Broadcast Ephemeris',
            6326 => 'WGS84',
            6901 => 'Ancienne Triangulation Francaise',
            6902 => 'Nord de Guerre',
            32767 => 'User Defined',
        },
    },
    2051 => {
        Name => 'GeogPrimeMeridian',
        PrintConv => {
            # epsg_pm
            8901 => 'Greenwich',
            8902 => 'Lisbon',
            8903 => 'Paris',
            8904 => 'Bogota',
            8905 => 'Madrid',
            8906 => 'Rome',
            8907 => 'Bern',
            8908 => 'Jakarta',
            8909 => 'Ferro',
            8910 => 'Brussels',
            8911 => 'Stockholm',
            32767 => 'User Defined',
        },
    },
    2052 => {
        Name => 'GeogLinearUnits',
        SeparateTable => 'Units',
        PrintConv => \%epsg_units,
    },
    2053 => 'GeogLinearUnitSize',
    2054 => {
        Name => 'GeogAngularUnits',
        SeparateTable => 'Units',
        PrintConv => \%epsg_units,
    },
    2055 => 'GeogAngularUnitSize',
    2056 => {
        Name => 'GeogEllipsoid',
        PrintConv => {
            # epsg_ellipse
            7001 => 'Airy 1830',
            7002 => 'Airy Modified 1849',
            7003 => 'Australian National Spheroid',
            7004 => 'Bessel 1841',
            7005 => 'Bessel Modified',
            7006 => 'Bessel Namibia',
            7007 => 'Clarke 1858',
            7008 => 'Clarke 1866',
            7009 => 'Clarke 1866 Michigan',
            7010 => 'Clarke 1880 Benoit',
            7011 => 'Clarke 1880 IGN',
            7012 => 'Clarke 1880 RGS',
            7013 => 'Clarke 1880 Arc',
            7014 => 'Clarke 1880 SGA 1922',
            7015 => 'Everest 1830 1937 Adjustment',
            7016 => 'Everest 1830 1967 Definition',
            7017 => 'Everest 1830 1975 Definition',
            7018 => 'Everest 1830 Modified',
            7019 => 'GRS 1980',
            7020 => 'Helmert 1906',
            7021 => 'Indonesian National Spheroid',
            7022 => 'International 1924',
            7023 => 'International 1967',
            7024 => 'Krassowsky 1940',
            7025 => 'NWL 9D',
            7026 => 'NWL 10D',
            7027 => 'Plessis 1817',
            7028 => 'Struve 1860',
            7029 => 'War Office',
            7030 => 'WGS 84',
            7031 => 'GEM 10C',
            7032 => 'OSU86F',
            7033 => 'OSU91A',
            7034 => 'Clarke 1880',
            7035 => 'Sphere',
            32767 => 'User Defined',
        },
    },
    2057 => 'GeogSemiMajorAxis',
    2058 => 'GeogSemiMinorAxis',
    2059 => 'GeogInvFlattening',
    2060 => {
        Name => 'GeogAzimuthUnits',
        SeparateTable => 'Units',
        PrintConv => \%epsg_units,
    },
    2061 => 'GeogPrimeMeridianLong',
    2062 => 'GeogToWGS84',
    3072 => {
        Name => 'ProjectedCSType',
        PrintConv => {
            # epsg_pcs
            2100 => 'GGRS87 Greek Grid',
            2176 => 'ETRS89 Poland CS2000 zone 5',
            2177 => 'ETRS89 Poland CS2000 zone 6',
            2177 => 'ETRS89 Poland CS2000 zone 7',
            2178 => 'ETRS89 Poland CS2000 zone 8',
            2180 => 'ETRS89 Poland CS92',
            2204 => 'NAD27 Tennessee',
            2205 => 'NAD83 Kentucky North',
            2391 => 'KKJ Finland zone 1',
            2392 => 'KKJ Finland zone 2',
            2393 => 'KKJ Finland zone 3',
            2394 => 'KKJ Finland zone 4',
            2400 => 'RT90 2 5 gon W',
            2600 => 'Lietuvos Koordinoei Sistema 1994',
            3053 => 'Hjorsey 1955 Lambert',
            3057 => 'ISN93 Lambert 1993',
            3300 => 'Estonian Coordinate System of 1992',
            3786 => 'Popular Visualisation CRS / Mercator', #PH (NC)
            3857 => 'WGS 84 / Pseudo-Mercator', #PH (NC)
            20137 => 'Adindan UTM zone 37N',
            20138 => 'Adindan UTM zone 38N',
            20248 => 'AGD66 AMG zone 48',
            20249 => 'AGD66 AMG zone 49',
            20250 => 'AGD66 AMG zone 50',
            20251 => 'AGD66 AMG zone 51',
            20252 => 'AGD66 AMG zone 52',
            20253 => 'AGD66 AMG zone 53',
            20254 => 'AGD66 AMG zone 54',
            20255 => 'AGD66 AMG zone 55',
            20256 => 'AGD66 AMG zone 56',
            20257 => 'AGD66 AMG zone 57',
            20258 => 'AGD66 AMG zone 58',
            20348 => 'AGD84 AMG zone 48',
            20349 => 'AGD84 AMG zone 49',
            20350 => 'AGD84 AMG zone 50',
            20351 => 'AGD84 AMG zone 51',
            20352 => 'AGD84 AMG zone 52',
            20353 => 'AGD84 AMG zone 53',
            20354 => 'AGD84 AMG zone 54',
            20355 => 'AGD84 AMG zone 55',
            20356 => 'AGD84 AMG zone 56',
            20357 => 'AGD84 AMG zone 57',
            20358 => 'AGD84 AMG zone 58',
            20437 => 'Ain el Abd UTM zone 37N',
            20438 => 'Ain el Abd UTM zone 38N',
            20439 => 'Ain el Abd UTM zone 39N',
            20499 => 'Ain el Abd Bahrain Grid',
            20538 => 'Afgooye UTM zone 38N',
            20539 => 'Afgooye UTM zone 39N',
            20700 => 'Lisbon Portuguese Grid',
            20822 => 'Aratu UTM zone 22S',
            20823 => 'Aratu UTM zone 23S',
            20824 => 'Aratu UTM zone 24S',
            20973 => 'Arc 1950 Lo13',
            20975 => 'Arc 1950 Lo15',
            20977 => 'Arc 1950 Lo17',
            20979 => 'Arc 1950 Lo19',
            20981 => 'Arc 1950 Lo21',
            20983 => 'Arc 1950 Lo23',
            20985 => 'Arc 1950 Lo25',
            20987 => 'Arc 1950 Lo27',
            20989 => 'Arc 1950 Lo29',
            20991 => 'Arc 1950 Lo31',
            20993 => 'Arc 1950 Lo33',
            20995 => 'Arc 1950 Lo35',
            21100 => 'Batavia NEIEZ',
            21148 => 'Batavia UTM zone 48S',
            21149 => 'Batavia UTM zone 49S',
            21150 => 'Batavia UTM zone 50S',
            21413 => 'Beijing Gauss zone 13',
            21414 => 'Beijing Gauss zone 14',
            21415 => 'Beijing Gauss zone 15',
            21416 => 'Beijing Gauss zone 16',
            21417 => 'Beijing Gauss zone 17',
            21418 => 'Beijing Gauss zone 18',
            21419 => 'Beijing Gauss zone 19',
            21420 => 'Beijing Gauss zone 20',
            21421 => 'Beijing Gauss zone 21',
            21422 => 'Beijing Gauss zone 22',
            21423 => 'Beijing Gauss zone 23',
            21473 => 'Beijing Gauss 13N',
            21474 => 'Beijing Gauss 14N',
            21475 => 'Beijing Gauss 15N',
            21476 => 'Beijing Gauss 16N',
            21477 => 'Beijing Gauss 17N',
            21478 => 'Beijing Gauss 18N',
            21479 => 'Beijing Gauss 19N',
            21480 => 'Beijing Gauss 20N',
            21481 => 'Beijing Gauss 21N',
            21482 => 'Beijing Gauss 22N',
            21483 => 'Beijing Gauss 23N',
            21500 => 'Belge Lambert 50',
            21790 => 'Bern 1898 Swiss Old',
            21817 => 'Bogota UTM zone 17N',
            21818 => 'Bogota UTM zone 18N',
            21891 => 'Bogota Colombia 3W',
            21892 => 'Bogota Colombia Bogota',
            21893 => 'Bogota Colombia 3E',
            21894 => 'Bogota Colombia 6E',
            22032 => 'Camacupa UTM 32S',
            22033 => 'Camacupa UTM 33S',
            22191 => 'C Inchauspe Argentina 1',
            22192 => 'C Inchauspe Argentina 2',
            22193 => 'C Inchauspe Argentina 3',
            22194 => 'C Inchauspe Argentina 4',
            22195 => 'C Inchauspe Argentina 5',
            22196 => 'C Inchauspe Argentina 6',
            22197 => 'C Inchauspe Argentina 7',
            22332 => 'Carthage UTM zone 32N',
            22391 => 'Carthage Nord Tunisie',
            22392 => 'Carthage Sud Tunisie',
            22523 => 'Corrego Alegre UTM 23S',
            22524 => 'Corrego Alegre UTM 24S',
            22832 => 'Douala UTM zone 32N',
            22992 => 'Egypt 1907 Red Belt',
            22993 => 'Egypt 1907 Purple Belt',
            22994 => 'Egypt 1907 Ext Purple',
            23028 => 'ED50 UTM zone 28N',
            23029 => 'ED50 UTM zone 29N',
            23030 => 'ED50 UTM zone 30N',
            23031 => 'ED50 UTM zone 31N',
            23032 => 'ED50 UTM zone 32N',
            23033 => 'ED50 UTM zone 33N',
            23034 => 'ED50 UTM zone 34N',
            23035 => 'ED50 UTM zone 35N',
            23036 => 'ED50 UTM zone 36N',
            23037 => 'ED50 UTM zone 37N',
            23038 => 'ED50 UTM zone 38N',
            23239 => 'Fahud UTM zone 39N',
            23240 => 'Fahud UTM zone 40N',
            23433 => 'Garoua UTM zone 33N',
            23700 => 'HD72 EOV',
            23846 => 'ID74 UTM zone 46N',
            23847 => 'ID74 UTM zone 47N',
            23848 => 'ID74 UTM zone 48N',
            23849 => 'ID74 UTM zone 49N',
            23850 => 'ID74 UTM zone 50N',
            23851 => 'ID74 UTM zone 51N',
            23852 => 'ID74 UTM zone 52N',
            23853 => 'ID74 UTM zone 53N',
            23886 => 'ID74 UTM zone 46S',
            23887 => 'ID74 UTM zone 47S',
            23888 => 'ID74 UTM zone 48S',
            23889 => 'ID74 UTM zone 49S',
            23890 => 'ID74 UTM zone 50S',
            23891 => 'ID74 UTM zone 51S',
            23892 => 'ID74 UTM zone 52S',
            23893 => 'ID74 UTM zone 53S',
            23894 => 'ID74 UTM zone 54S',
            23947 => 'Indian 1954 UTM 47N',
            23948 => 'Indian 1954 UTM 48N',
            24047 => 'Indian 1975 UTM 47N',
            24048 => 'Indian 1975 UTM 48N',
            24100 => 'Jamaica 1875 Old Grid',
            24200 => 'JAD69 Jamaica Grid',
            24370 => 'Kalianpur India 0',
            24371 => 'Kalianpur India I',
            24372 => 'Kalianpur India IIa',
            24373 => 'Kalianpur India IIIa',
            24374 => 'Kalianpur India IVa',
            24382 => 'Kalianpur India IIb',
            24383 => 'Kalianpur India IIIb',
            24384 => 'Kalianpur India IVb',
            24500 => 'Kertau Singapore Grid',
            24547 => 'Kertau UTM zone 47N',
            24548 => 'Kertau UTM zone 48N',
            24720 => 'La Canoa UTM zone 20N',
            24721 => 'La Canoa UTM zone 21N',
            24818 => 'PSAD56 UTM zone 18N',
            24819 => 'PSAD56 UTM zone 19N',
            24820 => 'PSAD56 UTM zone 20N',
            24821 => 'PSAD56 UTM zone 21N',
            24877 => 'PSAD56 UTM zone 17S',
            24878 => 'PSAD56 UTM zone 18S',
            24879 => 'PSAD56 UTM zone 19S',
            24880 => 'PSAD56 UTM zone 20S',
            24891 => 'PSAD56 Peru west zone',
            24892 => 'PSAD56 Peru central',
            24893 => 'PSAD56 Peru east zone',
            25000 => 'Leigon Ghana Grid',
            25231 => 'Lome UTM zone 31N',
            25391 => 'Luzon Philippines I',
            25392 => 'Luzon Philippines II',
            25393 => 'Luzon Philippines III',
            25394 => 'Luzon Philippines IV',
            25395 => 'Luzon Philippines V',
            25700 => 'Makassar NEIEZ',
            25932 => 'Malongo 1987 UTM 32S',
            26191 => 'Merchich Nord Maroc',
            26192 => 'Merchich Sud Maroc',
            26193 => 'Merchich Sahara',
            26237 => 'Massawa UTM zone 37N',
            26331 => 'Minna UTM zone 31N',
            26332 => 'Minna UTM zone 32N',
            26391 => 'Minna Nigeria West',
            26392 => 'Minna Nigeria Mid Belt',
            26393 => 'Minna Nigeria East',
            26432 => 'Mhast UTM zone 32S',
            26591 => 'Monte Mario Italy 1',
            26592 => 'Monte Mario Italy 2',
            26632 => 'M poraloko UTM 32N',
            26692 => 'M poraloko UTM 32S',
            26703 => 'NAD27 UTM zone 3N',
            26704 => 'NAD27 UTM zone 4N',
            26705 => 'NAD27 UTM zone 5N',
            26706 => 'NAD27 UTM zone 6N',
            26707 => 'NAD27 UTM zone 7N',
            26708 => 'NAD27 UTM zone 8N',
            26709 => 'NAD27 UTM zone 9N',
            26710 => 'NAD27 UTM zone 10N',
            26711 => 'NAD27 UTM zone 11N',
            26712 => 'NAD27 UTM zone 12N',
            26713 => 'NAD27 UTM zone 13N',
            26714 => 'NAD27 UTM zone 14N',
            26715 => 'NAD27 UTM zone 15N',
            26716 => 'NAD27 UTM zone 16N',
            26717 => 'NAD27 UTM zone 17N',
            26718 => 'NAD27 UTM zone 18N',
            26719 => 'NAD27 UTM zone 19N',
            26720 => 'NAD27 UTM zone 20N',
            26721 => 'NAD27 UTM zone 21N',
            26722 => 'NAD27 UTM zone 22N',
            26729 => 'NAD27 Alabama East',
            26730 => 'NAD27 Alabama West',
            26731 => 'NAD27 Alaska zone 1',
            26732 => 'NAD27 Alaska zone 2',
            26733 => 'NAD27 Alaska zone 3',
            26734 => 'NAD27 Alaska zone 4',
            26735 => 'NAD27 Alaska zone 5',
            26736 => 'NAD27 Alaska zone 6',
            26737 => 'NAD27 Alaska zone 7',
            26738 => 'NAD27 Alaska zone 8',
            26739 => 'NAD27 Alaska zone 9',
            26740 => 'NAD27 Alaska zone 10',
            26741 => 'NAD27 California I',
            26742 => 'NAD27 California II',
            26743 => 'NAD27 California III',
            26744 => 'NAD27 California IV',
            26745 => 'NAD27 California V',
            26746 => 'NAD27 California VI',
            26747 => 'NAD27 California VII',
            26748 => 'NAD27 Arizona East',
            26749 => 'NAD27 Arizona Central',
            26750 => 'NAD27 Arizona West',
            26751 => 'NAD27 Arkansas North',
            26752 => 'NAD27 Arkansas South',
            26753 => 'NAD27 Colorado North',
            26754 => 'NAD27 Colorado Central',
            26755 => 'NAD27 Colorado South',
            26756 => 'NAD27 Connecticut',
            26757 => 'NAD27 Delaware',
            26758 => 'NAD27 Florida East',
            26759 => 'NAD27 Florida West',
            26760 => 'NAD27 Florida North',
            26761 => 'NAD27 Hawaii zone 1',
            26762 => 'NAD27 Hawaii zone 2',
            26763 => 'NAD27 Hawaii zone 3',
            26764 => 'NAD27 Hawaii zone 4',
            26765 => 'NAD27 Hawaii zone 5',
            26766 => 'NAD27 Georgia East',
            26767 => 'NAD27 Georgia West',
            26768 => 'NAD27 Idaho East',
            26769 => 'NAD27 Idaho Central',
            26770 => 'NAD27 Idaho West',
            26771 => 'NAD27 Illinois East',
            26772 => 'NAD27 Illinois West',
            26773 => 'NAD27 Indiana East',
            26774 => 'NAD27 BLM 14N feet',
            26774 => 'NAD27 Indiana West',
            26775 => 'NAD27 BLM 15N feet',
            26775 => 'NAD27 Iowa North',
            26776 => 'NAD27 BLM 16N feet',
            26776 => 'NAD27 Iowa South',
            26777 => 'NAD27 BLM 17N feet',
            26777 => 'NAD27 Kansas North',
            26778 => 'NAD27 Kansas South',
            26779 => 'NAD27 Kentucky North',
            26780 => 'NAD27 Kentucky South',
            26781 => 'NAD27 Louisiana North',
            26782 => 'NAD27 Louisiana South',
            26783 => 'NAD27 Maine East',
            26784 => 'NAD27 Maine West',
            26785 => 'NAD27 Maryland',
            26786 => 'NAD27 Massachusetts',
            26787 => 'NAD27 Massachusetts Is',
            26788 => 'NAD27 Michigan North',
            26789 => 'NAD27 Michigan Central',
            26790 => 'NAD27 Michigan South',
            26791 => 'NAD27 Minnesota North',
            26792 => 'NAD27 Minnesota Cent',
            26793 => 'NAD27 Minnesota South',
            26794 => 'NAD27 Mississippi East',
            26795 => 'NAD27 Mississippi West',
            26796 => 'NAD27 Missouri East',
            26797 => 'NAD27 Missouri Central',
            26798 => 'NAD27 Missouri West',
            26801 => 'NAD Michigan Michigan East',
            26802 => 'NAD Michigan Michigan Old Central',
            26803 => 'NAD Michigan Michigan West',
            26903 => 'NAD83 UTM zone 3N',
            26904 => 'NAD83 UTM zone 4N',
            26905 => 'NAD83 UTM zone 5N',
            26906 => 'NAD83 UTM zone 6N',
            26907 => 'NAD83 UTM zone 7N',
            26908 => 'NAD83 UTM zone 8N',
            26909 => 'NAD83 UTM zone 9N',
            26910 => 'NAD83 UTM zone 10N',
            26911 => 'NAD83 UTM zone 11N',
            26912 => 'NAD83 UTM zone 12N',
            26913 => 'NAD83 UTM zone 13N',
            26914 => 'NAD83 UTM zone 14N',
            26915 => 'NAD83 UTM zone 15N',
            26916 => 'NAD83 UTM zone 16N',
            26917 => 'NAD83 UTM zone 17N',
            26918 => 'NAD83 UTM zone 18N',
            26919 => 'NAD83 UTM zone 19N',
            26920 => 'NAD83 UTM zone 20N',
            26921 => 'NAD83 UTM zone 21N',
            26922 => 'NAD83 UTM zone 22N',
            26923 => 'NAD83 UTM zone 23N',
            26929 => 'NAD83 Alabama East',
            26930 => 'NAD83 Alabama West',
            26931 => 'NAD83 Alaska zone 1',
            26932 => 'NAD83 Alaska zone 2',
            26933 => 'NAD83 Alaska zone 3',
            26934 => 'NAD83 Alaska zone 4',
            26935 => 'NAD83 Alaska zone 5',
            26936 => 'NAD83 Alaska zone 6',
            26937 => 'NAD83 Alaska zone 7',
            26938 => 'NAD83 Alaska zone 8',
            26939 => 'NAD83 Alaska zone 9',
            26940 => 'NAD83 Alaska zone 10',
            26941 => 'NAD83 California 1',
            26942 => 'NAD83 California 2',
            26943 => 'NAD83 California 3',
            26944 => 'NAD83 California 4',
            26945 => 'NAD83 California 5',
            26946 => 'NAD83 California 6',
            26948 => 'NAD83 Arizona East',
            26949 => 'NAD83 Arizona Central',
            26950 => 'NAD83 Arizona West',
            26951 => 'NAD83 Arkansas North',
            26952 => 'NAD83 Arkansas South',
            26953 => 'NAD83 Colorado North',
            26954 => 'NAD83 Colorado Central',
            26955 => 'NAD83 Colorado South',
            26956 => 'NAD83 Connecticut',
            26957 => 'NAD83 Delaware',
            26958 => 'NAD83 Florida East',
            26959 => 'NAD83 Florida West',
            26960 => 'NAD83 Florida North',
            26961 => 'NAD83 Hawaii zone 1',
            26962 => 'NAD83 Hawaii zone 2',
            26963 => 'NAD83 Hawaii zone 3',
            26964 => 'NAD83 Hawaii zone 4',
            26965 => 'NAD83 Hawaii zone 5',
            26966 => 'NAD83 Georgia East',
            26967 => 'NAD83 Georgia West',
            26968 => 'NAD83 Idaho East',
            26969 => 'NAD83 Idaho Central',
            26970 => 'NAD83 Idaho West',
            26971 => 'NAD83 Illinois East',
            26972 => 'NAD83 Illinois West',
            26973 => 'NAD83 Indiana East',
            26974 => 'NAD83 Indiana West',
            26975 => 'NAD83 Iowa North',
            26976 => 'NAD83 Iowa South',
            26977 => 'NAD83 Kansas North',
            26978 => 'NAD83 Kansas South',
            26979 => 'NAD83 Kentucky North',
            26980 => 'NAD83 Kentucky South',
            26981 => 'NAD83 Louisiana North',
            26982 => 'NAD83 Louisiana South',
            26983 => 'NAD83 Maine East',
            26984 => 'NAD83 Maine West',
            26985 => 'NAD83 Maryland',
            26986 => 'NAD83 Massachusetts',
            26987 => 'NAD83 Massachusetts Is',
            26988 => 'NAD83 Michigan North',
            26989 => 'NAD83 Michigan Central',
            26990 => 'NAD83 Michigan South',
            26991 => 'NAD83 Minnesota North',
            26992 => 'NAD83 Minnesota Cent',
            26993 => 'NAD83 Minnesota South',
            26994 => 'NAD83 Mississippi East',
            26995 => 'NAD83 Mississippi West',
            26996 => 'NAD83 Missouri East',
            26997 => 'NAD83 Missouri Central',
            26998 => 'NAD83 Missouri West',
            27038 => 'Nahrwan 1967 UTM 38N',
            27039 => 'Nahrwan 1967 UTM 39N',
            27040 => 'Nahrwan 1967 UTM 40N',
            27120 => 'Naparima UTM 20N',
            27200 => 'GD49 NZ Map Grid',
            27291 => 'GD49 North Island Grid',
            27292 => 'GD49 South Island Grid',
            27429 => 'Datum 73 UTM zone 29N',
            27500 => 'ATF Nord de Guerre',
            27581 => 'NTF France I',
            27582 => 'NTF France II',
            27583 => 'NTF France III',
            27591 => 'NTF Nord France',
            27592 => 'NTF Centre France',
            27593 => 'NTF Sud France',
            27700 => 'British National Grid',
            28232 => 'Point Noire UTM 32S',
            28348 => 'GDA94 MGA zone 48',
            28349 => 'GDA94 MGA zone 49',
            28350 => 'GDA94 MGA zone 50',
            28351 => 'GDA94 MGA zone 51',
            28352 => 'GDA94 MGA zone 52',
            28353 => 'GDA94 MGA zone 53',
            28354 => 'GDA94 MGA zone 54',
            28355 => 'GDA94 MGA zone 55',
            28356 => 'GDA94 MGA zone 56',
            28357 => 'GDA94 MGA zone 57',
            28358 => 'GDA94 MGA zone 58',
            28404 => 'Pulkovo Gauss zone 4',
            28405 => 'Pulkovo Gauss zone 5',
            28406 => 'Pulkovo Gauss zone 6',
            28407 => 'Pulkovo Gauss zone 7',
            28408 => 'Pulkovo Gauss zone 8',
            28409 => 'Pulkovo Gauss zone 9',
            28410 => 'Pulkovo Gauss zone 10',
            28411 => 'Pulkovo Gauss zone 11',
            28412 => 'Pulkovo Gauss zone 12',
            28413 => 'Pulkovo Gauss zone 13',
            28414 => 'Pulkovo Gauss zone 14',
            28415 => 'Pulkovo Gauss zone 15',
            28416 => 'Pulkovo Gauss zone 16',
            28417 => 'Pulkovo Gauss zone 17',
            28418 => 'Pulkovo Gauss zone 18',
            28419 => 'Pulkovo Gauss zone 19',
            28420 => 'Pulkovo Gauss zone 20',
            28421 => 'Pulkovo Gauss zone 21',
            28422 => 'Pulkovo Gauss zone 22',
            28423 => 'Pulkovo Gauss zone 23',
            28424 => 'Pulkovo Gauss zone 24',
            28425 => 'Pulkovo Gauss zone 25',
            28426 => 'Pulkovo Gauss zone 26',
            28427 => 'Pulkovo Gauss zone 27',
            28428 => 'Pulkovo Gauss zone 28',
            28429 => 'Pulkovo Gauss zone 29',
            28430 => 'Pulkovo Gauss zone 30',
            28431 => 'Pulkovo Gauss zone 31',
            28432 => 'Pulkovo Gauss zone 32',
            28464 => 'Pulkovo Gauss 4N',
            28465 => 'Pulkovo Gauss 5N',
            28466 => 'Pulkovo Gauss 6N',
            28467 => 'Pulkovo Gauss 7N',
            28468 => 'Pulkovo Gauss 8N',
            28469 => 'Pulkovo Gauss 9N',
            28470 => 'Pulkovo Gauss 10N',
            28471 => 'Pulkovo Gauss 11N',
            28472 => 'Pulkovo Gauss 12N',
            28473 => 'Pulkovo Gauss 13N',
            28474 => 'Pulkovo Gauss 14N',
            28475 => 'Pulkovo Gauss 15N',
            28476 => 'Pulkovo Gauss 16N',
            28477 => 'Pulkovo Gauss 17N',
            28478 => 'Pulkovo Gauss 18N',
            28479 => 'Pulkovo Gauss 19N',
            28480 => 'Pulkovo Gauss 20N',
            28481 => 'Pulkovo Gauss 21N',
            28482 => 'Pulkovo Gauss 22N',
            28483 => 'Pulkovo Gauss 23N',
            28484 => 'Pulkovo Gauss 24N',
            28485 => 'Pulkovo Gauss 25N',
            28486 => 'Pulkovo Gauss 26N',
            28487 => 'Pulkovo Gauss 27N',
            28488 => 'Pulkovo Gauss 28N',
            28489 => 'Pulkovo Gauss 29N',
            28490 => 'Pulkovo Gauss 30N',
            28491 => 'Pulkovo Gauss 31N',
            28492 => 'Pulkovo Gauss 32N',
            28600 => 'Qatar National Grid',
            28991 => 'RD Netherlands Old',
            28992 => 'RD Netherlands New',
            29118 => 'SAD69 UTM zone 18N',
            29119 => 'SAD69 UTM zone 19N',
            29120 => 'SAD69 UTM zone 20N',
            29121 => 'SAD69 UTM zone 21N',
            29122 => 'SAD69 UTM zone 22N',
            29177 => 'SAD69 UTM zone 17S',
            29178 => 'SAD69 UTM zone 18S',
            29179 => 'SAD69 UTM zone 19S',
            29180 => 'SAD69 UTM zone 20S',
            29181 => 'SAD69 UTM zone 21S',
            29182 => 'SAD69 UTM zone 22S',
            29183 => 'SAD69 UTM zone 23S',
            29184 => 'SAD69 UTM zone 24S',
            29185 => 'SAD69 UTM zone 25S',
            29220 => 'Sapper Hill UTM 20S',
            29221 => 'Sapper Hill UTM 21S',
            29333 => 'Schwarzeck UTM 33S',
            29635 => 'Sudan UTM zone 35N',
            29636 => 'Sudan UTM zone 36N',
            29700 => 'Tananarive Laborde',
            29738 => 'Tananarive UTM 38S',
            29739 => 'Tananarive UTM 39S',
            29800 => 'Timbalai 1948 Borneo',
            29849 => 'Timbalai 1948 UTM 49N',
            29850 => 'Timbalai 1948 UTM 50N',
            29900 => 'TM65 Irish Nat Grid',
            30200 => 'Trinidad 1903 Trinidad',
            30339 => 'TC 1948 UTM zone 39N',
            30340 => 'TC 1948 UTM zone 40N',
            30491 => 'Voirol N Algerie ancien',
            30492 => 'Voirol S Algerie ancien',
            30591 => 'Voirol Unifie N Algerie',
            30592 => 'Voirol Unifie S Algerie',
            30600 => 'Bern 1938 Swiss New',
            30729 => 'Nord Sahara UTM 29N',
            30730 => 'Nord Sahara UTM 30N',
            30731 => 'Nord Sahara UTM 31N',
            30732 => 'Nord Sahara UTM 32N',
            31028 => 'Yoff UTM zone 28N',
            31121 => 'Zanderij UTM zone 21N',
            31291 => 'MGI Austria West',
            31292 => 'MGI Austria Central',
            31293 => 'MGI Austria East',
            31300 => 'Belge Lambert 72',
            31491 => 'DHDN Germany zone 1',
            31492 => 'DHDN Germany zone 2',
            31493 => 'DHDN Germany zone 3',
            31494 => 'DHDN Germany zone 4',
            31495 => 'DHDN Germany zone 5',
            31700 => 'Dealul Piscului 1970 Stereo 70',
            32001 => 'NAD27 Montana North',
            32002 => 'NAD27 Montana Central',
            32003 => 'NAD27 Montana South',
            32005 => 'NAD27 Nebraska North',
            32006 => 'NAD27 Nebraska South',
            32007 => 'NAD27 Nevada East',
            32008 => 'NAD27 Nevada Central',
            32009 => 'NAD27 Nevada West',
            32010 => 'NAD27 New Hampshire',
            32011 => 'NAD27 New Jersey',
            32012 => 'NAD27 New Mexico East',
            32013 => 'NAD27 New Mexico Cent',
            32014 => 'NAD27 New Mexico West',
            32015 => 'NAD27 New York East',
            32016 => 'NAD27 New York Central',
            32017 => 'NAD27 New York West',
            32018 => 'NAD27 New York Long Is',
            32019 => 'NAD27 North Carolina',
            32020 => 'NAD27 North Dakota N',
            32021 => 'NAD27 North Dakota S',
            32022 => 'NAD27 Ohio North',
            32023 => 'NAD27 Ohio South',
            32024 => 'NAD27 Oklahoma North',
            32025 => 'NAD27 Oklahoma South',
            32026 => 'NAD27 Oregon North',
            32027 => 'NAD27 Oregon South',
            32028 => 'NAD27 Pennsylvania N',
            32029 => 'NAD27 Pennsylvania S',
            32030 => 'NAD27 Rhode Island',
            32031 => 'NAD27 South Carolina N',
            32033 => 'NAD27 South Carolina S',
            32034 => 'NAD27 South Dakota N',
            32035 => 'NAD27 South Dakota S',
            32036 => 'NAD27 Tennessee',
            32037 => 'NAD27 Texas North',
            32038 => 'NAD27 Texas North Cen',
            32039 => 'NAD27 Texas Central',
            32040 => 'NAD27 Texas South Cen',
            32041 => 'NAD27 Texas South',
            32042 => 'NAD27 Utah North',
            32043 => 'NAD27 Utah Central',
            32044 => 'NAD27 Utah South',
            32045 => 'NAD27 Vermont',
            32046 => 'NAD27 Virginia North',
            32047 => 'NAD27 Virginia South',
            32048 => 'NAD27 Washington North',
            32049 => 'NAD27 Washington South',
            32050 => 'NAD27 West Virginia N',
            32051 => 'NAD27 West Virginia S',
            32052 => 'NAD27 Wisconsin North',
            32053 => 'NAD27 Wisconsin Cen',
            32054 => 'NAD27 Wisconsin South',
            32055 => 'NAD27 Wyoming East',
            32056 => 'NAD27 Wyoming E Cen',
            32057 => 'NAD27 Wyoming W Cen',
            32058 => 'NAD27 Wyoming West',
            32059 => 'NAD27 Puerto Rico',
            32060 => 'NAD27 St Croix',
            32100 => 'NAD83 Montana',
            32104 => 'NAD83 Nebraska',
            32107 => 'NAD83 Nevada East',
            32108 => 'NAD83 Nevada Central',
            32109 => 'NAD83 Nevada West',
            32110 => 'NAD83 New Hampshire',
            32111 => 'NAD83 New Jersey',
            32112 => 'NAD83 New Mexico East',
            32113 => 'NAD83 New Mexico Cent',
            32114 => 'NAD83 New Mexico West',
            32115 => 'NAD83 New York East',
            32116 => 'NAD83 New York Central',
            32117 => 'NAD83 New York West',
            32118 => 'NAD83 New York Long Is',
            32119 => 'NAD83 North Carolina',
            32120 => 'NAD83 North Dakota N',
            32121 => 'NAD83 North Dakota S',
            32122 => 'NAD83 Ohio North',
            32123 => 'NAD83 Ohio South',
            32124 => 'NAD83 Oklahoma North',
            32125 => 'NAD83 Oklahoma South',
            32126 => 'NAD83 Oregon North',
            32127 => 'NAD83 Oregon South',
            32128 => 'NAD83 Pennsylvania N',
            32129 => 'NAD83 Pennsylvania S',
            32130 => 'NAD83 Rhode Island',
            32133 => 'NAD83 South Carolina',
            32134 => 'NAD83 South Dakota N',
            32135 => 'NAD83 South Dakota S',
            32136 => 'NAD83 Tennessee',
            32137 => 'NAD83 Texas North',
            32138 => 'NAD83 Texas North Cen',
            32139 => 'NAD83 Texas Central',
            32140 => 'NAD83 Texas South Cen',
            32141 => 'NAD83 Texas South',
            32142 => 'NAD83 Utah North',
            32143 => 'NAD83 Utah Central',
            32144 => 'NAD83 Utah South',
            32145 => 'NAD83 Vermont',
            32146 => 'NAD83 Virginia North',
            32147 => 'NAD83 Virginia South',
            32148 => 'NAD83 Washington North',
            32149 => 'NAD83 Washington South',
            32150 => 'NAD83 West Virginia N',
            32151 => 'NAD83 West Virginia S',
            32152 => 'NAD83 Wisconsin North',
            32153 => 'NAD83 Wisconsin Cen',
            32154 => 'NAD83 Wisconsin South',
            32155 => 'NAD83 Wyoming East',
            32156 => 'NAD83 Wyoming E Cen',
            32157 => 'NAD83 Wyoming W Cen',
            32158 => 'NAD83 Wyoming West',
            32161 => 'NAD83 Puerto Rico Virgin Is',
            32201 => 'WGS72 UTM zone 1N',
            32202 => 'WGS72 UTM zone 2N',
            32203 => 'WGS72 UTM zone 3N',
            32204 => 'WGS72 UTM zone 4N',
            32205 => 'WGS72 UTM zone 5N',
            32206 => 'WGS72 UTM zone 6N',
            32207 => 'WGS72 UTM zone 7N',
            32208 => 'WGS72 UTM zone 8N',
            32209 => 'WGS72 UTM zone 9N',
            32210 => 'WGS72 UTM zone 10N',
            32211 => 'WGS72 UTM zone 11N',
            32212 => 'WGS72 UTM zone 12N',
            32213 => 'WGS72 UTM zone 13N',
            32214 => 'WGS72 UTM zone 14N',
            32215 => 'WGS72 UTM zone 15N',
            32216 => 'WGS72 UTM zone 16N',
            32217 => 'WGS72 UTM zone 17N',
            32218 => 'WGS72 UTM zone 18N',
            32219 => 'WGS72 UTM zone 19N',
            32220 => 'WGS72 UTM zone 20N',
            32221 => 'WGS72 UTM zone 21N',
            32222 => 'WGS72 UTM zone 22N',
            32223 => 'WGS72 UTM zone 23N',
            32224 => 'WGS72 UTM zone 24N',
            32225 => 'WGS72 UTM zone 25N',
            32226 => 'WGS72 UTM zone 26N',
            32227 => 'WGS72 UTM zone 27N',
            32228 => 'WGS72 UTM zone 28N',
            32229 => 'WGS72 UTM zone 29N',
            32230 => 'WGS72 UTM zone 30N',
            32231 => 'WGS72 UTM zone 31N',
            32232 => 'WGS72 UTM zone 32N',
            32233 => 'WGS72 UTM zone 33N',
            32234 => 'WGS72 UTM zone 34N',
            32235 => 'WGS72 UTM zone 35N',
            32236 => 'WGS72 UTM zone 36N',
            32237 => 'WGS72 UTM zone 37N',
            32238 => 'WGS72 UTM zone 38N',
            32239 => 'WGS72 UTM zone 39N',
            32240 => 'WGS72 UTM zone 40N',
            32241 => 'WGS72 UTM zone 41N',
            32242 => 'WGS72 UTM zone 42N',
            32243 => 'WGS72 UTM zone 43N',
            32244 => 'WGS72 UTM zone 44N',
            32245 => 'WGS72 UTM zone 45N',
            32246 => 'WGS72 UTM zone 46N',
            32247 => 'WGS72 UTM zone 47N',
            32248 => 'WGS72 UTM zone 48N',
            32249 => 'WGS72 UTM zone 49N',
            32250 => 'WGS72 UTM zone 50N',
            32251 => 'WGS72 UTM zone 51N',
            32252 => 'WGS72 UTM zone 52N',
            32253 => 'WGS72 UTM zone 53N',
            32254 => 'WGS72 UTM zone 54N',
            32255 => 'WGS72 UTM zone 55N',
            32256 => 'WGS72 UTM zone 56N',
            32257 => 'WGS72 UTM zone 57N',
            32258 => 'WGS72 UTM zone 58N',
            32259 => 'WGS72 UTM zone 59N',
            32260 => 'WGS72 UTM zone 60N',
            32301 => 'WGS72 UTM zone 1S',
            32302 => 'WGS72 UTM zone 2S',
            32303 => 'WGS72 UTM zone 3S',
            32304 => 'WGS72 UTM zone 4S',
            32305 => 'WGS72 UTM zone 5S',
            32306 => 'WGS72 UTM zone 6S',
            32307 => 'WGS72 UTM zone 7S',
            32308 => 'WGS72 UTM zone 8S',
            32309 => 'WGS72 UTM zone 9S',
            32310 => 'WGS72 UTM zone 10S',
            32311 => 'WGS72 UTM zone 11S',
            32312 => 'WGS72 UTM zone 12S',
            32313 => 'WGS72 UTM zone 13S',
            32314 => 'WGS72 UTM zone 14S',
            32315 => 'WGS72 UTM zone 15S',
            32316 => 'WGS72 UTM zone 16S',
            32317 => 'WGS72 UTM zone 17S',
            32318 => 'WGS72 UTM zone 18S',
            32319 => 'WGS72 UTM zone 19S',
            32320 => 'WGS72 UTM zone 20S',
            32321 => 'WGS72 UTM zone 21S',
            32322 => 'WGS72 UTM zone 22S',
            32323 => 'WGS72 UTM zone 23S',
            32324 => 'WGS72 UTM zone 24S',
            32325 => 'WGS72 UTM zone 25S',
            32326 => 'WGS72 UTM zone 26S',
            32327 => 'WGS72 UTM zone 27S',
            32328 => 'WGS72 UTM zone 28S',
            32329 => 'WGS72 UTM zone 29S',
            32330 => 'WGS72 UTM zone 30S',
            32331 => 'WGS72 UTM zone 31S',
            32332 => 'WGS72 UTM zone 32S',
            32333 => 'WGS72 UTM zone 33S',
            32334 => 'WGS72 UTM zone 34S',
            32335 => 'WGS72 UTM zone 35S',
            32336 => 'WGS72 UTM zone 36S',
            32337 => 'WGS72 UTM zone 37S',
            32338 => 'WGS72 UTM zone 38S',
            32339 => 'WGS72 UTM zone 39S',
            32340 => 'WGS72 UTM zone 40S',
            32341 => 'WGS72 UTM zone 41S',
            32342 => 'WGS72 UTM zone 42S',
            32343 => 'WGS72 UTM zone 43S',
            32344 => 'WGS72 UTM zone 44S',
            32345 => 'WGS72 UTM zone 45S',
            32346 => 'WGS72 UTM zone 46S',
            32347 => 'WGS72 UTM zone 47S',
            32348 => 'WGS72 UTM zone 48S',
            32349 => 'WGS72 UTM zone 49S',
            32350 => 'WGS72 UTM zone 50S',
            32351 => 'WGS72 UTM zone 51S',
            32352 => 'WGS72 UTM zone 52S',
            32353 => 'WGS72 UTM zone 53S',
            32354 => 'WGS72 UTM zone 54S',
            32355 => 'WGS72 UTM zone 55S',
            32356 => 'WGS72 UTM zone 56S',
            32357 => 'WGS72 UTM zone 57S',
            32358 => 'WGS72 UTM zone 58S',
            32359 => 'WGS72 UTM zone 59S',
            32360 => 'WGS72 UTM zone 60S',
            32401 => 'WGS72BE UTM zone 1N',
            32402 => 'WGS72BE UTM zone 2N',
            32403 => 'WGS72BE UTM zone 3N',
            32404 => 'WGS72BE UTM zone 4N',
            32405 => 'WGS72BE UTM zone 5N',
            32406 => 'WGS72BE UTM zone 6N',
            32407 => 'WGS72BE UTM zone 7N',
            32408 => 'WGS72BE UTM zone 8N',
            32409 => 'WGS72BE UTM zone 9N',
            32410 => 'WGS72BE UTM zone 10N',
            32411 => 'WGS72BE UTM zone 11N',
            32412 => 'WGS72BE UTM zone 12N',
            32413 => 'WGS72BE UTM zone 13N',
            32414 => 'WGS72BE UTM zone 14N',
            32415 => 'WGS72BE UTM zone 15N',
            32416 => 'WGS72BE UTM zone 16N',
            32417 => 'WGS72BE UTM zone 17N',
            32418 => 'WGS72BE UTM zone 18N',
            32419 => 'WGS72BE UTM zone 19N',
            32420 => 'WGS72BE UTM zone 20N',
            32421 => 'WGS72BE UTM zone 21N',
            32422 => 'WGS72BE UTM zone 22N',
            32423 => 'WGS72BE UTM zone 23N',
            32424 => 'WGS72BE UTM zone 24N',
            32425 => 'WGS72BE UTM zone 25N',
            32426 => 'WGS72BE UTM zone 26N',
            32427 => 'WGS72BE UTM zone 27N',
            32428 => 'WGS72BE UTM zone 28N',
            32429 => 'WGS72BE UTM zone 29N',
            32430 => 'WGS72BE UTM zone 30N',
            32431 => 'WGS72BE UTM zone 31N',
            32432 => 'WGS72BE UTM zone 32N',
            32433 => 'WGS72BE UTM zone 33N',
            32434 => 'WGS72BE UTM zone 34N',
            32435 => 'WGS72BE UTM zone 35N',
            32436 => 'WGS72BE UTM zone 36N',
            32437 => 'WGS72BE UTM zone 37N',
            32438 => 'WGS72BE UTM zone 38N',
            32439 => 'WGS72BE UTM zone 39N',
            32440 => 'WGS72BE UTM zone 40N',
            32441 => 'WGS72BE UTM zone 41N',
            32442 => 'WGS72BE UTM zone 42N',
            32443 => 'WGS72BE UTM zone 43N',
            32444 => 'WGS72BE UTM zone 44N',
            32445 => 'WGS72BE UTM zone 45N',
            32446 => 'WGS72BE UTM zone 46N',
            32447 => 'WGS72BE UTM zone 47N',
            32448 => 'WGS72BE UTM zone 48N',
            32449 => 'WGS72BE UTM zone 49N',
            32450 => 'WGS72BE UTM zone 50N',
            32451 => 'WGS72BE UTM zone 51N',
            32452 => 'WGS72BE UTM zone 52N',
            32453 => 'WGS72BE UTM zone 53N',
            32454 => 'WGS72BE UTM zone 54N',
            32455 => 'WGS72BE UTM zone 55N',
            32456 => 'WGS72BE UTM zone 56N',
            32457 => 'WGS72BE UTM zone 57N',
            32458 => 'WGS72BE UTM zone 58N',
            32459 => 'WGS72BE UTM zone 59N',
            32460 => 'WGS72BE UTM zone 60N',
            32501 => 'WGS72BE UTM zone 1S',
            32502 => 'WGS72BE UTM zone 2S',
            32503 => 'WGS72BE UTM zone 3S',
            32504 => 'WGS72BE UTM zone 4S',
            32505 => 'WGS72BE UTM zone 5S',
            32506 => 'WGS72BE UTM zone 6S',
            32507 => 'WGS72BE UTM zone 7S',
            32508 => 'WGS72BE UTM zone 8S',
            32509 => 'WGS72BE UTM zone 9S',
            32510 => 'WGS72BE UTM zone 10S',
            32511 => 'WGS72BE UTM zone 11S',
            32512 => 'WGS72BE UTM zone 12S',
            32513 => 'WGS72BE UTM zone 13S',
            32514 => 'WGS72BE UTM zone 14S',
            32515 => 'WGS72BE UTM zone 15S',
            32516 => 'WGS72BE UTM zone 16S',
            32517 => 'WGS72BE UTM zone 17S',
            32518 => 'WGS72BE UTM zone 18S',
            32519 => 'WGS72BE UTM zone 19S',
            32520 => 'WGS72BE UTM zone 20S',
            32521 => 'WGS72BE UTM zone 21S',
            32522 => 'WGS72BE UTM zone 22S',
            32523 => 'WGS72BE UTM zone 23S',
            32524 => 'WGS72BE UTM zone 24S',
            32525 => 'WGS72BE UTM zone 25S',
            32526 => 'WGS72BE UTM zone 26S',
            32527 => 'WGS72BE UTM zone 27S',
            32528 => 'WGS72BE UTM zone 28S',
            32529 => 'WGS72BE UTM zone 29S',
            32530 => 'WGS72BE UTM zone 30S',
            32531 => 'WGS72BE UTM zone 31S',
            32532 => 'WGS72BE UTM zone 32S',
            32533 => 'WGS72BE UTM zone 33S',
            32534 => 'WGS72BE UTM zone 34S',
            32535 => 'WGS72BE UTM zone 35S',
            32536 => 'WGS72BE UTM zone 36S',
            32537 => 'WGS72BE UTM zone 37S',
            32538 => 'WGS72BE UTM zone 38S',
            32539 => 'WGS72BE UTM zone 39S',
            32540 => 'WGS72BE UTM zone 40S',
            32541 => 'WGS72BE UTM zone 41S',
            32542 => 'WGS72BE UTM zone 42S',
            32543 => 'WGS72BE UTM zone 43S',
            32544 => 'WGS72BE UTM zone 44S',
            32545 => 'WGS72BE UTM zone 45S',
            32546 => 'WGS72BE UTM zone 46S',
            32547 => 'WGS72BE UTM zone 47S',
            32548 => 'WGS72BE UTM zone 48S',
            32549 => 'WGS72BE UTM zone 49S',
            32550 => 'WGS72BE UTM zone 50S',
            32551 => 'WGS72BE UTM zone 51S',
            32552 => 'WGS72BE UTM zone 52S',
            32553 => 'WGS72BE UTM zone 53S',
            32554 => 'WGS72BE UTM zone 54S',
            32555 => 'WGS72BE UTM zone 55S',
            32556 => 'WGS72BE UTM zone 56S',
            32557 => 'WGS72BE UTM zone 57S',
            32558 => 'WGS72BE UTM zone 58S',
            32559 => 'WGS72BE UTM zone 59S',
            32560 => 'WGS72BE UTM zone 60S',
            32601 => 'WGS84 UTM zone 1N',
            32602 => 'WGS84 UTM zone 2N',
            32603 => 'WGS84 UTM zone 3N',
            32604 => 'WGS84 UTM zone 4N',
            32605 => 'WGS84 UTM zone 5N',
            32606 => 'WGS84 UTM zone 6N',
            32607 => 'WGS84 UTM zone 7N',
            32608 => 'WGS84 UTM zone 8N',
            32609 => 'WGS84 UTM zone 9N',
            32610 => 'WGS84 UTM zone 10N',
            32611 => 'WGS84 UTM zone 11N',
            32612 => 'WGS84 UTM zone 12N',
            32613 => 'WGS84 UTM zone 13N',
            32614 => 'WGS84 UTM zone 14N',
            32615 => 'WGS84 UTM zone 15N',
            32616 => 'WGS84 UTM zone 16N',
            32617 => 'WGS84 UTM zone 17N',
            32618 => 'WGS84 UTM zone 18N',
            32619 => 'WGS84 UTM zone 19N',
            32620 => 'WGS84 UTM zone 20N',
            32621 => 'WGS84 UTM zone 21N',
            32622 => 'WGS84 UTM zone 22N',
            32623 => 'WGS84 UTM zone 23N',
            32624 => 'WGS84 UTM zone 24N',
            32625 => 'WGS84 UTM zone 25N',
            32626 => 'WGS84 UTM zone 26N',
            32627 => 'WGS84 UTM zone 27N',
            32628 => 'WGS84 UTM zone 28N',
            32629 => 'WGS84 UTM zone 29N',
            32630 => 'WGS84 UTM zone 30N',
            32631 => 'WGS84 UTM zone 31N',
            32632 => 'WGS84 UTM zone 32N',
            32633 => 'WGS84 UTM zone 33N',
            32634 => 'WGS84 UTM zone 34N',
            32635 => 'WGS84 UTM zone 35N',
            32636 => 'WGS84 UTM zone 36N',
            32637 => 'WGS84 UTM zone 37N',
            32638 => 'WGS84 UTM zone 38N',
            32639 => 'WGS84 UTM zone 39N',
            32640 => 'WGS84 UTM zone 40N',
            32641 => 'WGS84 UTM zone 41N',
            32642 => 'WGS84 UTM zone 42N',
            32643 => 'WGS84 UTM zone 43N',
            32644 => 'WGS84 UTM zone 44N',
            32645 => 'WGS84 UTM zone 45N',
            32646 => 'WGS84 UTM zone 46N',
            32647 => 'WGS84 UTM zone 47N',
            32648 => 'WGS84 UTM zone 48N',
            32649 => 'WGS84 UTM zone 49N',
            32650 => 'WGS84 UTM zone 50N',
            32651 => 'WGS84 UTM zone 51N',
            32652 => 'WGS84 UTM zone 52N',
            32653 => 'WGS84 UTM zone 53N',
            32654 => 'WGS84 UTM zone 54N',
            32655 => 'WGS84 UTM zone 55N',
            32656 => 'WGS84 UTM zone 56N',
            32657 => 'WGS84 UTM zone 57N',
            32658 => 'WGS84 UTM zone 58N',
            32659 => 'WGS84 UTM zone 59N',
            32660 => 'WGS84 UTM zone 60N',
            32701 => 'WGS84 UTM zone 1S',
            32702 => 'WGS84 UTM zone 2S',
            32703 => 'WGS84 UTM zone 3S',
            32704 => 'WGS84 UTM zone 4S',
            32705 => 'WGS84 UTM zone 5S',
            32706 => 'WGS84 UTM zone 6S',
            32707 => 'WGS84 UTM zone 7S',
            32708 => 'WGS84 UTM zone 8S',
            32709 => 'WGS84 UTM zone 9S',
            32710 => 'WGS84 UTM zone 10S',
            32711 => 'WGS84 UTM zone 11S',
            32712 => 'WGS84 UTM zone 12S',
            32713 => 'WGS84 UTM zone 13S',
            32714 => 'WGS84 UTM zone 14S',
            32715 => 'WGS84 UTM zone 15S',
            32716 => 'WGS84 UTM zone 16S',
            32717 => 'WGS84 UTM zone 17S',
            32718 => 'WGS84 UTM zone 18S',
            32719 => 'WGS84 UTM zone 19S',
            32720 => 'WGS84 UTM zone 20S',
            32721 => 'WGS84 UTM zone 21S',
            32722 => 'WGS84 UTM zone 22S',
            32723 => 'WGS84 UTM zone 23S',
            32724 => 'WGS84 UTM zone 24S',
            32725 => 'WGS84 UTM zone 25S',
            32726 => 'WGS84 UTM zone 26S',
            32727 => 'WGS84 UTM zone 27S',
            32728 => 'WGS84 UTM zone 28S',
            32729 => 'WGS84 UTM zone 29S',
            32730 => 'WGS84 UTM zone 30S',
            32731 => 'WGS84 UTM zone 31S',
            32732 => 'WGS84 UTM zone 32S',
            32733 => 'WGS84 UTM zone 33S',
            32734 => 'WGS84 UTM zone 34S',
            32735 => 'WGS84 UTM zone 35S',
            32736 => 'WGS84 UTM zone 36S',
            32737 => 'WGS84 UTM zone 37S',
            32738 => 'WGS84 UTM zone 38S',
            32739 => 'WGS84 UTM zone 39S',
            32740 => 'WGS84 UTM zone 40S',
            32741 => 'WGS84 UTM zone 41S',
            32742 => 'WGS84 UTM zone 42S',
            32743 => 'WGS84 UTM zone 43S',
            32744 => 'WGS84 UTM zone 44S',
            32745 => 'WGS84 UTM zone 45S',
            32746 => 'WGS84 UTM zone 46S',
            32747 => 'WGS84 UTM zone 47S',
            32748 => 'WGS84 UTM zone 48S',
            32749 => 'WGS84 UTM zone 49S',
            32750 => 'WGS84 UTM zone 50S',
            32751 => 'WGS84 UTM zone 51S',
            32752 => 'WGS84 UTM zone 52S',
            32753 => 'WGS84 UTM zone 53S',
            32754 => 'WGS84 UTM zone 54S',
            32755 => 'WGS84 UTM zone 55S',
            32756 => 'WGS84 UTM zone 56S',
            32757 => 'WGS84 UTM zone 57S',
            32758 => 'WGS84 UTM zone 58S',
            32759 => 'WGS84 UTM zone 59S',
            32760 => 'WGS84 UTM zone 60S',
            32767 => 'User Defined',
        },
    },
    3073 => 'PCSCitation',
    3074 => {
        Name => 'Projection',
        PrintConv => {
            # epsg_proj
            10101 => 'Alabama CS27 East',
            10102 => 'Alabama CS27 West',
            10131 => 'Alabama CS83 East',
            10132 => 'Alabama CS83 West',
            10201 => 'Arizona Coordinate System east',
            10202 => 'Arizona Coordinate System Central',
            10203 => 'Arizona Coordinate System west',
            10231 => 'Arizona CS83 east',
            10232 => 'Arizona CS83 Central',
            10233 => 'Arizona CS83 west',
            10301 => 'Arkansas CS27 North',
            10302 => 'Arkansas CS27 South',
            10331 => 'Arkansas CS83 North',
            10332 => 'Arkansas CS83 South',
            10401 => 'California CS27 I',
            10402 => 'California CS27 II',
            10403 => 'California CS27 III',
            10404 => 'California CS27 IV',
            10405 => 'California CS27 V',
            10406 => 'California CS27 VI',
            10407 => 'California CS27 VII',
            10431 => 'California CS83 1',
            10432 => 'California CS83 2',
            10433 => 'California CS83 3',
            10434 => 'California CS83 4',
            10435 => 'California CS83 5',
            10436 => 'California CS83 6',
            10501 => 'Colorado CS27 North',
            10502 => 'Colorado CS27 Central',
            10503 => 'Colorado CS27 South',
            10531 => 'Colorado CS83 North',
            10532 => 'Colorado CS83 Central',
            10533 => 'Colorado CS83 South',
            10600 => 'Connecticut CS27',
            10630 => 'Connecticut CS83',
            10700 => 'Delaware CS27',
            10730 => 'Delaware CS83',
            10901 => 'Florida CS27 East',
            10902 => 'Florida CS27 West',
            10903 => 'Florida CS27 North',
            10931 => 'Florida CS83 East',
            10932 => 'Florida CS83 West',
            10933 => 'Florida CS83 North',
            11001 => 'Georgia CS27 East',
            11002 => 'Georgia CS27 West',
            11031 => 'Georgia CS83 East',
            11032 => 'Georgia CS83 West',
            11101 => 'Idaho CS27 East',
            11102 => 'Idaho CS27 Central',
            11103 => 'Idaho CS27 West',
            11131 => 'Idaho CS83 East',
            11132 => 'Idaho CS83 Central',
            11133 => 'Idaho CS83 West',
            11201 => 'Illinois CS27 East',
            11202 => 'Illinois CS27 West',
            11231 => 'Illinois CS83 East',
            11232 => 'Illinois CS83 West',
            11301 => 'Indiana CS27 East',
            11302 => 'Indiana CS27 West',
            11331 => 'Indiana CS83 East',
            11332 => 'Indiana CS83 West',
            11401 => 'Iowa CS27 North',
            11402 => 'Iowa CS27 South',
            11431 => 'Iowa CS83 North',
            11432 => 'Iowa CS83 South',
            11501 => 'Kansas CS27 North',
            11502 => 'Kansas CS27 South',
            11531 => 'Kansas CS83 North',
            11532 => 'Kansas CS83 South',
            11601 => 'Kentucky CS27 North',
            11602 => 'Kentucky CS27 South',
            11631 => 'Kentucky CS83 North',
            11632 => 'Kentucky CS83 South',
            11701 => 'Louisiana CS27 North',
            11702 => 'Louisiana CS27 South',
            11731 => 'Louisiana CS83 North',
            11732 => 'Louisiana CS83 South',
            11801 => 'Maine CS27 East',
            11802 => 'Maine CS27 West',
            11831 => 'Maine CS83 East',
            11832 => 'Maine CS83 West',
            11900 => 'Maryland CS27',
            11930 => 'Maryland CS83',
            12001 => 'Massachusetts CS27 Mainland',
            12002 => 'Massachusetts CS27 Island',
            12031 => 'Massachusetts CS83 Mainland',
            12032 => 'Massachusetts CS83 Island',
            12101 => 'Michigan State Plane East',
            12102 => 'Michigan State Plane Old Central',
            12103 => 'Michigan State Plane West',
            12111 => 'Michigan CS27 North',
            12112 => 'Michigan CS27 Central',
            12113 => 'Michigan CS27 South',
            12141 => 'Michigan CS83 North',
            12142 => 'Michigan CS83 Central',
            12143 => 'Michigan CS83 South',
            12201 => 'Minnesota CS27 North',
            12202 => 'Minnesota CS27 Central',
            12203 => 'Minnesota CS27 South',
            12231 => 'Minnesota CS83 North',
            12232 => 'Minnesota CS83 Central',
            12233 => 'Minnesota CS83 South',
            12301 => 'Mississippi CS27 East',
            12302 => 'Mississippi CS27 West',
            12331 => 'Mississippi CS83 East',
            12332 => 'Mississippi CS83 West',
            12401 => 'Missouri CS27 East',
            12402 => 'Missouri CS27 Central',
            12403 => 'Missouri CS27 West',
            12431 => 'Missouri CS83 East',
            12432 => 'Missouri CS83 Central',
            12433 => 'Missouri CS83 West',
            12501 => 'Montana CS27 North',
            12502 => 'Montana CS27 Central',
            12503 => 'Montana CS27 South',
            12530 => 'Montana CS83',
            12601 => 'Nebraska CS27 North',
            12602 => 'Nebraska CS27 South',
            12630 => 'Nebraska CS83',
            12701 => 'Nevada CS27 East',
            12702 => 'Nevada CS27 Central',
            12703 => 'Nevada CS27 West',
            12731 => 'Nevada CS83 East',
            12732 => 'Nevada CS83 Central',
            12733 => 'Nevada CS83 West',
            12800 => 'New Hampshire CS27',
            12830 => 'New Hampshire CS83',
            12900 => 'New Jersey CS27',
            12930 => 'New Jersey CS83',
            13001 => 'New Mexico CS27 East',
            13002 => 'New Mexico CS27 Central',
            13003 => 'New Mexico CS27 West',
            13031 => 'New Mexico CS83 East',
            13032 => 'New Mexico CS83 Central',
            13033 => 'New Mexico CS83 West',
            13101 => 'New York CS27 East',
            13102 => 'New York CS27 Central',
            13103 => 'New York CS27 West',
            13104 => 'New York CS27 Long Island',
            13131 => 'New York CS83 East',
            13132 => 'New York CS83 Central',
            13133 => 'New York CS83 West',
            13134 => 'New York CS83 Long Island',
            13200 => 'North Carolina CS27',
            13230 => 'North Carolina CS83',
            13301 => 'North Dakota CS27 North',
            13302 => 'North Dakota CS27 South',
            13331 => 'North Dakota CS83 North',
            13332 => 'North Dakota CS83 South',
            13401 => 'Ohio CS27 North',
            13402 => 'Ohio CS27 South',
            13431 => 'Ohio CS83 North',
            13432 => 'Ohio CS83 South',
            13501 => 'Oklahoma CS27 North',
            13502 => 'Oklahoma CS27 South',
            13531 => 'Oklahoma CS83 North',
            13532 => 'Oklahoma CS83 South',
            13601 => 'Oregon CS27 North',
            13602 => 'Oregon CS27 South',
            13631 => 'Oregon CS83 North',
            13632 => 'Oregon CS83 South',
            13701 => 'Pennsylvania CS27 North',
            13702 => 'Pennsylvania CS27 South',
            13731 => 'Pennsylvania CS83 North',
            13732 => 'Pennsylvania CS83 South',
            13800 => 'Rhode Island CS27',
            13830 => 'Rhode Island CS83',
            13901 => 'South Carolina CS27 North',
            13902 => 'South Carolina CS27 South',
            13930 => 'South Carolina CS83',
            14001 => 'South Dakota CS27 North',
            14002 => 'South Dakota CS27 South',
            14031 => 'South Dakota CS83 North',
            14032 => 'South Dakota CS83 South',
            14100 => 'Tennessee CS27',
            14130 => 'Tennessee CS83',
            14201 => 'Texas CS27 North',
            14202 => 'Texas CS27 North Central',
            14203 => 'Texas CS27 Central',
            14204 => 'Texas CS27 South Central',
            14205 => 'Texas CS27 South',
            14231 => 'Texas CS83 North',
            14232 => 'Texas CS83 North Central',
            14233 => 'Texas CS83 Central',
            14234 => 'Texas CS83 South Central',
            14235 => 'Texas CS83 South',
            14301 => 'Utah CS27 North',
            14302 => 'Utah CS27 Central',
            14303 => 'Utah CS27 South',
            14331 => 'Utah CS83 North',
            14332 => 'Utah CS83 Central',
            14333 => 'Utah CS83 South',
            14400 => 'Vermont CS27',
            14430 => 'Vermont CS83',
            14501 => 'Virginia CS27 North',
            14502 => 'Virginia CS27 South',
            14531 => 'Virginia CS83 North',
            14532 => 'Virginia CS83 South',
            14601 => 'Washington CS27 North',
            14602 => 'Washington CS27 South',
            14631 => 'Washington CS83 North',
            14632 => 'Washington CS83 South',
            14701 => 'West Virginia CS27 North',
            14702 => 'West Virginia CS27 South',
            14731 => 'West Virginia CS83 North',
            14732 => 'West Virginia CS83 South',
            14801 => 'Wisconsin CS27 North',
            14802 => 'Wisconsin CS27 Central',
            14803 => 'Wisconsin CS27 South',
            14831 => 'Wisconsin CS83 North',
            14832 => 'Wisconsin CS83 Central',
            14833 => 'Wisconsin CS83 South',
            14901 => 'Wyoming CS27 East',
            14902 => 'Wyoming CS27 East Central',
            14903 => 'Wyoming CS27 West Central',
            14904 => 'Wyoming CS27 West',
            14931 => 'Wyoming CS83 East',
            14932 => 'Wyoming CS83 East Central',
            14933 => 'Wyoming CS83 West Central',
            14934 => 'Wyoming CS83 West',
            15001 => 'Alaska CS27 1',
            15002 => 'Alaska CS27 2',
            15003 => 'Alaska CS27 3',
            15004 => 'Alaska CS27 4',
            15005 => 'Alaska CS27 5',
            15006 => 'Alaska CS27 6',
            15007 => 'Alaska CS27 7',
            15008 => 'Alaska CS27 8',
            15009 => 'Alaska CS27 9',
            15010 => 'Alaska CS27 10',
            15031 => 'Alaska CS83 1',
            15032 => 'Alaska CS83 2',
            15033 => 'Alaska CS83 3',
            15034 => 'Alaska CS83 4',
            15035 => 'Alaska CS83 5',
            15036 => 'Alaska CS83 6',
            15037 => 'Alaska CS83 7',
            15038 => 'Alaska CS83 8',
            15039 => 'Alaska CS83 9',
            15040 => 'Alaska CS83 10',
            15101 => 'Hawaii CS27 1',
            15102 => 'Hawaii CS27 2',
            15103 => 'Hawaii CS27 3',
            15104 => 'Hawaii CS27 4',
            15105 => 'Hawaii CS27 5',
            15131 => 'Hawaii CS83 1',
            15132 => 'Hawaii CS83 2',
            15133 => 'Hawaii CS83 3',
            15134 => 'Hawaii CS83 4',
            15135 => 'Hawaii CS83 5',
            15201 => 'Puerto Rico CS27',
            15202 => 'St Croix',
            15230 => 'Puerto Rico Virgin Is',
            15302 => 'Kentucky CS27',
            15303 => 'Kentucky CS83 North',
            15914 => 'BLM 14N feet',
            15915 => 'BLM 15N feet',
            15916 => 'BLM 16N feet',
            15917 => 'BLM 17N feet',
            16001 => 'UTM zone 1N',
            16002 => 'UTM zone 2N',
            16003 => 'UTM zone 3N',
            16004 => 'UTM zone 4N',
            16005 => 'UTM zone 5N',
            16006 => 'UTM zone 6N',
            16007 => 'UTM zone 7N',
            16008 => 'UTM zone 8N',
            16009 => 'UTM zone 9N',
            16010 => 'UTM zone 10N',
            16011 => 'UTM zone 11N',
            16012 => 'UTM zone 12N',
            16013 => 'UTM zone 13N',
            16014 => 'UTM zone 14N',
            16015 => 'UTM zone 15N',
            16016 => 'UTM zone 16N',
            16017 => 'UTM zone 17N',
            16018 => 'UTM zone 18N',
            16019 => 'UTM zone 19N',
            16020 => 'UTM zone 20N',
            16021 => 'UTM zone 21N',
            16022 => 'UTM zone 22N',
            16023 => 'UTM zone 23N',
            16024 => 'UTM zone 24N',
            16025 => 'UTM zone 25N',
            16026 => 'UTM zone 26N',
            16027 => 'UTM zone 27N',
            16028 => 'UTM zone 28N',
            16029 => 'UTM zone 29N',
            16030 => 'UTM zone 30N',
            16031 => 'UTM zone 31N',
            16032 => 'UTM zone 32N',
            16033 => 'UTM zone 33N',
            16034 => 'UTM zone 34N',
            16035 => 'UTM zone 35N',
            16036 => 'UTM zone 36N',
            16037 => 'UTM zone 37N',
            16038 => 'UTM zone 38N',
            16039 => 'UTM zone 39N',
            16040 => 'UTM zone 40N',
            16041 => 'UTM zone 41N',
            16042 => 'UTM zone 42N',
            16043 => 'UTM zone 43N',
            16044 => 'UTM zone 44N',
            16045 => 'UTM zone 45N',
            16046 => 'UTM zone 46N',
            16047 => 'UTM zone 47N',
            16048 => 'UTM zone 48N',
            16049 => 'UTM zone 49N',
            16050 => 'UTM zone 50N',
            16051 => 'UTM zone 51N',
            16052 => 'UTM zone 52N',
            16053 => 'UTM zone 53N',
            16054 => 'UTM zone 54N',
            16055 => 'UTM zone 55N',
            16056 => 'UTM zone 56N',
            16057 => 'UTM zone 57N',
            16058 => 'UTM zone 58N',
            16059 => 'UTM zone 59N',
            16060 => 'UTM zone 60N',
            16101 => 'UTM zone 1S',
            16102 => 'UTM zone 2S',
            16103 => 'UTM zone 3S',
            16104 => 'UTM zone 4S',
            16105 => 'UTM zone 5S',
            16106 => 'UTM zone 6S',
            16107 => 'UTM zone 7S',
            16108 => 'UTM zone 8S',
            16109 => 'UTM zone 9S',
            16110 => 'UTM zone 10S',
            16111 => 'UTM zone 11S',
            16112 => 'UTM zone 12S',
            16113 => 'UTM zone 13S',
            16114 => 'UTM zone 14S',
            16115 => 'UTM zone 15S',
            16116 => 'UTM zone 16S',
            16117 => 'UTM zone 17S',
            16118 => 'UTM zone 18S',
            16119 => 'UTM zone 19S',
            16120 => 'UTM zone 20S',
            16121 => 'UTM zone 21S',
            16122 => 'UTM zone 22S',
            16123 => 'UTM zone 23S',
            16124 => 'UTM zone 24S',
            16125 => 'UTM zone 25S',
            16126 => 'UTM zone 26S',
            16127 => 'UTM zone 27S',
            16128 => 'UTM zone 28S',
            16129 => 'UTM zone 29S',
            16130 => 'UTM zone 30S',
            16131 => 'UTM zone 31S',
            16132 => 'UTM zone 32S',
            16133 => 'UTM zone 33S',
            16134 => 'UTM zone 34S',
            16135 => 'UTM zone 35S',
            16136 => 'UTM zone 36S',
            16137 => 'UTM zone 37S',
            16138 => 'UTM zone 38S',
            16139 => 'UTM zone 39S',
            16140 => 'UTM zone 40S',
            16141 => 'UTM zone 41S',
            16142 => 'UTM zone 42S',
            16143 => 'UTM zone 43S',
            16144 => 'UTM zone 44S',
            16145 => 'UTM zone 45S',
            16146 => 'UTM zone 46S',
            16147 => 'UTM zone 47S',
            16148 => 'UTM zone 48S',
            16149 => 'UTM zone 49S',
            16150 => 'UTM zone 50S',
            16151 => 'UTM zone 51S',
            16152 => 'UTM zone 52S',
            16153 => 'UTM zone 53S',
            16154 => 'UTM zone 54S',
            16155 => 'UTM zone 55S',
            16156 => 'UTM zone 56S',
            16157 => 'UTM zone 57S',
            16158 => 'UTM zone 58S',
            16159 => 'UTM zone 59S',
            16160 => 'UTM zone 60S',
            16200 => 'Gauss Kruger zone 0',
            16201 => 'Gauss Kruger zone 1',
            16202 => 'Gauss Kruger zone 2',
            16203 => 'Gauss Kruger zone 3',
            16204 => 'Gauss Kruger zone 4',
            16205 => 'Gauss Kruger zone 5',
            17348 => 'Map Grid of Australia 48',
            17349 => 'Map Grid of Australia 49',
            17350 => 'Map Grid of Australia 50',
            17351 => 'Map Grid of Australia 51',
            17352 => 'Map Grid of Australia 52',
            17353 => 'Map Grid of Australia 53',
            17354 => 'Map Grid of Australia 54',
            17355 => 'Map Grid of Australia 55',
            17356 => 'Map Grid of Australia 56',
            17357 => 'Map Grid of Australia 57',
            17358 => 'Map Grid of Australia 58',
            17448 => 'Australian Map Grid 48',
            17449 => 'Australian Map Grid 49',
            17450 => 'Australian Map Grid 50',
            17451 => 'Australian Map Grid 51',
            17452 => 'Australian Map Grid 52',
            17453 => 'Australian Map Grid 53',
            17454 => 'Australian Map Grid 54',
            17455 => 'Australian Map Grid 55',
            17456 => 'Australian Map Grid 56',
            17457 => 'Australian Map Grid 57',
            17458 => 'Australian Map Grid 58',
            18031 => 'Argentina 1',
            18032 => 'Argentina 2',
            18033 => 'Argentina 3',
            18034 => 'Argentina 4',
            18035 => 'Argentina 5',
            18036 => 'Argentina 6',
            18037 => 'Argentina 7',
            18051 => 'Colombia 3W',
            18052 => 'Colombia Bogota',
            18053 => 'Colombia 3E',
            18054 => 'Colombia 6E',
            18072 => 'Egypt Red Belt',
            18073 => 'Egypt Purple Belt',
            18074 => 'Extended Purple Belt',
            18141 => 'New Zealand North Island Nat Grid',
            18142 => 'New Zealand South Island Nat Grid',
            19900 => 'Bahrain Grid',
            19905 => 'Netherlands E Indies Equatorial',
            19912 => 'RSO Borneo',
            19926 => 'Stereo 70',
            32767 => 'User Defined',
        },
    },
    3075 => {
        Name => 'ProjCoordTrans',
        PrintConv => {
            # geo_ctrans
            1 => 'Transverse Mercator',
            2 => 'Transverse Mercator Modified Alaska',
            3 => 'Oblique Mercator',
            4 => 'Oblique Mercator Laborde',
            5 => 'Oblique Mercator Rosenmund',
            6 => 'Oblique Mercator Spherical',   # not advisable
            7 => 'Mercator',
            8 => 'Lambert Conf Conic 2SP',
            9 => 'Lambert Conf Conic 1SP',
            10 => 'Lambert Azim Equal Area',
            11 => 'Albers Equal Area',
            12 => 'Azimuthal Equidistant',
            13 => 'Equidistant Conic',
            14 => 'Stereographic',
            15 => 'Polar Stereographic',
            16 => 'Oblique Stereographic',   # not advisable
            17 => 'Equirectangular',
            18 => 'Cassini Soldner',
            19 => 'Gnomonic',
            20 => 'Miller Cylindrical',
            21 => 'Orthographic',
            22 => 'Polyconic',
            23 => 'Robinson',
            24 => 'Sinusoidal',
            25 => 'VanDerGrinten',
            26 => 'New Zealand Map Grid',
            27 => 'Transverse Mercator South Orientated',
            28 => 'Cylindrical Equal Area',
            32767 => 'User Defined',
        },
    },
    3076 => {
        Name => 'ProjLinearUnits',
        SeparateTable => 'Units',
        PrintConv => \%epsg_units,
    },
    3077 => 'ProjLinearUnitSize',
    3078 => 'ProjStdParallel1',
    3079 => 'ProjStdParallel2',
    3080 => 'ProjNatOriginLong',
    3081 => 'ProjNatOriginLat',
    3082 => 'ProjFalseEasting',
    3083 => 'ProjFalseNorthing',
    3084 => 'ProjFalseOriginLong',
    3085 => 'ProjFalseOriginLat',
    3086 => 'ProjFalseOriginEasting',
    3087 => 'ProjFalseOriginNorthing',
    3088 => 'ProjCenterLong',
    3089 => 'ProjCenterLat',
    3090 => 'ProjCenterEasting',
    3091 => 'ProjCenterNorthing',
    3092 => 'ProjScaleAtNatOrigin',
    3093 => 'ProjScaleAtCenter',
    3094 => 'ProjAzimuthAngle',
    3095 => 'ProjStraightVertPoleLong',
    3096 => 'ProjRectifiedGridAngle',
    4096 => {
        Name => 'VerticalCSType',
        SeparateTable => 'VerticalCS',
        PrintConv => \%epsg_vertcs,
    },
    4097 => 'VerticalCitation',
    4098 => {
        Name => 'VerticalDatum',
        SeparateTable => 'VerticalCS',
        PrintConv => \%epsg_vertcs,
    },
    4099 => {
        Name => 'VerticalUnits',
        SeparateTable => 'Units',
        PrintConv => \%epsg_units,
    },
#
# ChartTiff extensions (ref 2)
#
    47001 => {
        Name => 'ChartFormat',
        PrintConv => {
            47500 => 'General',
            47501 => 'Coastal',
            47502 => 'Harbor',
            47503 => 'SailingInternational',
            47504 => 'SmallCraft Route',
            47505 => 'SmallCraftArea',
            47506 => 'SmallCraftFolio',
            47507 => 'Topographic',
            47508 => 'Recreation',
            47509 => 'Index',
            47510 => 'Inset',
        },
    },
    47002 => 'ChartSource',
    47003 => 'ChartSourceEdition',
    47004 => 'ChartSourceDate',
    47005 => 'ChartCorrDate',
    47006 => 'ChartCountryOrigin',
    47007 => 'ChartRasterEdition',
    47008 => {
        Name => 'ChartSoundingDatum',
        PrintConv => {
            47600 => 'Equatorial Spring Low Water',
            47601 => 'Indian Spring Low Water',
            47602 => 'Lowest Astronomical Tide',
            47603 => 'Lowest Low Water',
            47604 => 'Lowest Normal Low Water',
            47605 => 'Mean Higher High Water',
            47606 => 'Mean High Water',
            47607 => 'Mean High Water Springs',
            47608 => 'Mean Lower Low Water',
            47609 => 'Mean Lower Low Water Springs',
            47610 => 'Mean Low Water',
            47611 => 'Mean Sea Level',
            47612 => 'Tropic Higher High Water',
            47613 => 'Tropic Lower Low Water',
        },
    },
    47009 => {
        Name => 'ChartDepthUnits',
        SeparateTable => 'Units',
        PrintConv => \%epsg_units,
    },
    47010 => 'ChartMagVar',
    47011 => 'ChartMagVarYear',
    47012 => 'ChartMagVarAnnChange',
    47013 => 'ChartWGSNSShift',
    47015 => 'InsetNWPixelX',
    47016 => 'InsetNWPixelY',
    47017 => 'ChartContourInterval',
);

#------------------------------------------------------------------------------
# Inputs: 0) ExifTool object ref
# Notes: byte order must be set before calling this routine
sub ProcessGeoTiff($)
{
    my $et = shift;
    my $dirData = $et->GetValue('GeoTiffDirectory', 'ValueConv') or return;

    # avoid re-processing if another EXIF directory is found
    $$et{DidGeoTiff} and $$et{DidGeoTiff} eq $dirData and return;
    $$et{DidGeoTiff} = $dirData;

    my $doubleData = $et->GetValue('GeoTiffDoubleParams', 'ValueConv');
    my $asciiData = $et->GetValue('GeoTiffAsciiParams', 'ValueConv');
    my $verbose = $et->Options('Verbose');

    if (length($$dirData) >= 8 and
        length($$dirData) >= 8 * (Get16u($dirData,6) + 1))
    {
        my $version    = Get16u($dirData,0);
        my $revision   = Get16u($dirData,2);
        my $minorRev   = Get16u($dirData,4);
        my $numEntries = Get16u($dirData,6);

        if ($verbose) {
            $$et{INDENT} .= '| ';
            $et->VerboseDir('GeoTiff',$numEntries);
        }
        # generate version number tag (not a real GeoTiff tag)
        my $tagTable = GetTagTable("Image::ExifTool::GeoTiff::Main");
        my $tagInfo = $et->GetTagInfo($tagTable, 1);
        $tagInfo and $et->FoundTag($tagInfo,"$version.$revision.$minorRev");

        my $i;
        for ($i=0; $i<$numEntries; ++$i) {
            my $pt = 8 * ($i + 1);
            my $tag    = Get16u($dirData, $pt);
            $tagInfo   = $et->GetTagInfo($tagTable, $tag) or next;
            my $loc    = Get16u($dirData, $pt+2);
            my $count  = Get16u($dirData, $pt+4);
            my $offset = Get16u($dirData, $pt+6);
            my $format = $geoTiffFormat{$loc};
            my ($val, $dataPt);
            if (not $format) {
                $et->Warn("Unknown GeoTiff location ($loc) for $$tagInfo{Name}");
                next;
            } elsif ($format eq 'double') {     # in the double parms
                $dataPt = $doubleData;
            } elsif ($format eq 'string') {     # in the ASCII parms
                $dataPt = $asciiData;
            } elsif ($format eq 'int16u') {     # in the GeoTiffDirectory data
                $dataPt = $dirData;
                unless ($loc) {                 # is value is stored in offset?
                    $count = 1;                 # (implied by location of 0)
                    $offset = ($pt + 6) / 2;    # offset of the "offset" value
                }
            }
            my $size = Image::ExifTool::FormatSize($format);
            if (not $dataPt or length($$dataPt) < $size*($offset+$count)) {
                $et->Warn("Missing $format data for $$tagInfo{Name}");
                next;
            }
            $offset *= $size;
            $val = Image::ExifTool::ReadValue($dataPt, $offset, $format,
                                              $count, length($$dataPt)-$offset);
            # remove trailing terminator (NULL or '|') from string value
            $val =~ s/(\0|\|)$// if $format eq 'string';
            $verbose and $et->VerboseInfo($tag, $tagInfo,
                'Table'  => $tagTable,
                'Index'  => $i,
                'Value'  => $val,
                'DataPt' => $dataPt,
                'Start'  => $offset,
                'Format' => $format,
                'Count'  => $count,
                'Size'   => $count * $size,
            );
            $et->FoundTag($tagInfo, $val);
        }
        if ($verbose) {
            $$et{INDENT} =~ s/..$//;
        }
    } else {
        $et->Warn('Bad GeoTIFF directory');
    }
    # extract block tags only if requested
    unless ($$et{OPTIONS}{RequestAll}) {
        $et->DeleteTag('GeoTiffDirectory')    unless $$et{REQ_TAG_LOOKUP}{geotiffdirectory};
        $et->DeleteTag('GeoTiffDoubleParams') unless $$et{REQ_TAG_LOOKUP}{geotiffdoubleparams};
        $et->DeleteTag('GeoTiffAsciiParams')  unless $$et{REQ_TAG_LOOKUP}{geotiffasciiparams};
    }
}


1;  #end

__END__

=head1 NAME

Image::ExifTool::GeoTiff - Read GeoTiff meta information

=head1 SYNOPSIS

This module is loaded automatically by Image::ExifTool when required.

=head1 DESCRIPTION

This module contains definitions required by Image::ExifTool to interpret
GeoTiff meta information.  GeoTiff information is used in images to specify
exact geometric mappings used to transform the image to real world
coordinates.

=head1 AUTHOR

Copyright 2003-2025, Phil Harvey (philharvey66 at gmail.com)

This library is free software; you can redistribute it and/or modify it
under the same terms as Perl itself.

=head1 REFERENCES

=over 4

=item ftp://ftp.remotesensing.org/geotiff/libgeotiff/libgeotiff-1.1.4.tar.gz

=item http://www.charttiff.com/whitepapers.shtml

=back

=head1 SEE ALSO

L<Image::ExifTool::TagNames/GeoTiff Tags>,
L<Image::ExifTool(3pm)|Image::ExifTool>

=cut
