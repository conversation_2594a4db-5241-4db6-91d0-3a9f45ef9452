import javax.swing.*;
import java.awt.*;
import java.awt.event.*;
import java.io.*;
import java.nio.file.*;
import java.util.*;
import java.util.List;
import java.util.concurrent.*;
import java.util.stream.*;

public class MetadataRestorerGUI extends JFrame {
    private JTextField inputField, outputField;
    private JButton browseInputBtn, browseOutputBtn, startBtn, pauseBtn, cancelBtn;
    private JProgressBar progressBar;
    private JLabel statusLabel;

    private volatile boolean paused = false;
    private volatile boolean cancelled = false;

    private static final String[] MEDIA_EXTS = {".jpg", ".jpeg", ".png", ".heic", ".mp4", ".mov", ".avi", ".mkv"};
    private static final String EXIFTOOL = "exiftool.exe"; // assumes exiftool.exe in same folder as program
    // Alternative: use full path like "C:\\Program Files\\ExifTool\\exiftool.exe"

    public MetadataRestorerGUI() {
        setTitle("Google Takeout Metadata Restorer");
        setSize(600, 250);
        setDefaultCloseOperation(EXIT_ON_CLOSE);
        setLayout(new GridBagLayout());

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.fill = GridBagConstraints.HORIZONTAL;

        inputField = new JTextField();
        outputField = new JTextField();
        browseInputBtn = new JButton("Browse");
        browseOutputBtn = new JButton("Browse");
        startBtn = new JButton("Start");
        pauseBtn = new JButton("Pause");
        cancelBtn = new JButton("Cancel");
        progressBar = new JProgressBar(0, 100);
        statusLabel = new JLabel("Idle...");

        // Input folder
        gbc.gridx = 0; gbc.gridy = 0; gbc.gridwidth = 1;
        add(new JLabel("Takeout Folder:"), gbc);
        gbc.gridx = 1; gbc.gridwidth = 2;
        add(inputField, gbc);
        gbc.gridx = 3; gbc.gridwidth = 1;
        add(browseInputBtn, gbc);

        // Output folder
        gbc.gridx = 0; gbc.gridy = 1; gbc.gridwidth = 1;
        add(new JLabel("Output Folder:"), gbc);
        gbc.gridx = 1; gbc.gridwidth = 2;
        add(outputField, gbc);
        gbc.gridx = 3; gbc.gridwidth = 1;
        add(browseOutputBtn, gbc);

        // Progress
        gbc.gridx = 0; gbc.gridy = 2; gbc.gridwidth = 4;
        add(progressBar, gbc);

        // Status
        gbc.gridy = 3;
        add(statusLabel, gbc);

        // Buttons
        JPanel buttonPanel = new JPanel();
        buttonPanel.add(startBtn);
        buttonPanel.add(pauseBtn);
        buttonPanel.add(cancelBtn);
        gbc.gridy = 4;
        add(buttonPanel, gbc);

        // Action Listeners
        browseInputBtn.addActionListener(e -> chooseFolder(inputField));
        browseOutputBtn.addActionListener(e -> chooseFolder(outputField));
        startBtn.addActionListener(e -> startProcessing());
        pauseBtn.addActionListener(e -> togglePause());
        cancelBtn.addActionListener(e -> cancelled = true);
    }

    private void chooseFolder(JTextField field) {
        JFileChooser chooser = new JFileChooser();
        chooser.setFileSelectionMode(JFileChooser.DIRECTORIES_ONLY);
        if (chooser.showOpenDialog(this) == JFileChooser.APPROVE_OPTION) {
            field.setText(chooser.getSelectedFile().getAbsolutePath());
        }
    }

    private void startProcessing() {
        String inputPath = inputField.getText().trim();
        String outputPath = outputField.getText().trim();

        if (inputPath.isEmpty() || outputPath.isEmpty()) {
            JOptionPane.showMessageDialog(this, "Please select both input and output folders.");
            return;
        }

        System.out.println("DEBUG: Starting processing...");
        System.out.println("DEBUG: Input path: " + inputPath);
        System.out.println("DEBUG: Output path: " + outputPath);

        cancelled = false;
        paused = false;

        new Thread(() -> processAll(inputPath, outputPath)).start();
    }

    private void togglePause() {
        paused = !paused;
        pauseBtn.setText(paused ? "Resume" : "Pause");
    }

    private void processAll(String inputPath, String outputPath) {
        try {
            System.out.println("=== STARTING PROCESSING ===");
            System.out.println("Input path: " + inputPath);
            System.out.println("Output path: " + outputPath);

            Path root = Paths.get(inputPath);
            Path output = Paths.get(outputPath);
            Files.createDirectories(output);

            // Collect all media files
            List<Path> allFiles;
            try (Stream<Path> stream = Files.walk(root)) {
                allFiles = stream.filter(Files::isRegularFile)
                        .filter(p -> Arrays.stream(MEDIA_EXTS).anyMatch(ext -> p.toString().toLowerCase().endsWith(ext)))
                        .collect(Collectors.toList());
            }

            int total = allFiles.size();
            System.out.println("DEBUG: Found " + total + " media files");

            // List first few files for debugging
            for (int i = 0; i < Math.min(5, total); i++) {
                System.out.println("DEBUG: File " + (i+1) + ": " + allFiles.get(i));
            }
            if (total > 5) {
                System.out.println("DEBUG: ... and " + (total-5) + " more files");
            }

            if (total == 0) {
                SwingUtilities.invokeLater(() -> JOptionPane.showMessageDialog(this, "No media files found."));
                return;
            }

            for (int i = 0; i < total; i++) {
                if (cancelled) break;

                // Handle pause
                while (paused && !cancelled) {
                    Thread.sleep(200);
                }

                Path file = allFiles.get(i);
                System.out.println("\nDEBUG: --- Processing file " + (i+1) + "/" + total + " ---");
                System.out.println("DEBUG: Source: " + file.toString());

                Path relPath = root.relativize(file);
                Path outFile = output.resolve(relPath);
                System.out.println("DEBUG: Destination: " + outFile.toString());

                Files.createDirectories(outFile.getParent());
                Files.copy(file, outFile, StandardCopyOption.REPLACE_EXISTING);
                System.out.println("DEBUG: File copied successfully");

                // Find matching JSON - handle both directions of matching
                String baseName = file.getFileName().toString();
                String baseNameWithoutExt = baseName.substring(0, baseName.lastIndexOf('.'));

                System.out.println("DEBUG: Looking for JSON files for: " + baseName);
                System.out.println("DEBUG: Base name without extension: " + baseNameWithoutExt);
                System.out.println("DEBUG: Searching in directory: " + file.getParent().toString());

                boolean foundJson = false;
                int jsonCount = 0;

                try (DirectoryStream<Path> dirStream = Files.newDirectoryStream(file.getParent(), "*.json")) {
                    for (Path json : dirStream) {
                        String jsonName = json.getFileName().toString();
                        String jsonNameWithoutExt = jsonName.substring(0, jsonName.lastIndexOf('.'));

                        boolean isMatch = false;
                        String matchReason = "";

                        // Case 1: JSON starts with full media filename (e.g., IMG123.jpg.json)
                        if (jsonName.startsWith(baseName)) {
                            isMatch = true;
                            matchReason = "JSON starts with full media filename";
                        }
                        // Case 2: Try progressive truncation (both directions)
                        else {
                            // Try truncating media filename and compare with JSON
                            for (int truncate = 1; truncate <= 5 && !isMatch; truncate++) {
                                if (baseNameWithoutExt.length() > truncate) {
                                    String truncatedMedia = baseNameWithoutExt.substring(0, baseNameWithoutExt.length() - truncate);
                                    if (jsonNameWithoutExt.startsWith(truncatedMedia) && truncatedMedia.length() > 10) {
                                        isMatch = true;
                                        matchReason = "JSON matches media filename truncated by " + truncate + " chars";
                                        break;
                                    }
                                }
                            }

                            // Try truncating JSON filename and compare with media
                            for (int truncate = 1; truncate <= 5 && !isMatch; truncate++) {
                                if (jsonNameWithoutExt.length() > truncate) {
                                    String truncatedJson = jsonNameWithoutExt.substring(0, jsonNameWithoutExt.length() - truncate);
                                    if (baseNameWithoutExt.startsWith(truncatedJson) && truncatedJson.length() > 10) {
                                        isMatch = true;
                                        matchReason = "Media matches JSON filename truncated by " + truncate + " chars";
                                        break;
                                    }
                                }
                            }
                        }

                        if (isMatch) {
                            jsonCount++;
                            System.out.println("DEBUG: ✓ Found JSON #" + jsonCount + ": " + jsonName + " for file: " + baseName);
                            System.out.println("DEBUG:   Match reason: " + matchReason);

                            // Show what's in the JSON file
                            showJsonContent(json.toAbsolutePath().toString());

                            // Convert Google Takeout JSON to ExifTool format and apply
                            convertAndApplyMetadata(json.toAbsolutePath().toString(), outFile.toAbsolutePath().toString());
                            foundJson = true;
                        }
                    }
                } catch (Exception e) {
                    System.out.println("DEBUG: Error searching for JSON files: " + e.getMessage());
                }

                if (!foundJson) {
                    System.out.println("DEBUG: ✗ No JSON found for: " + baseName);
                }

                final int currentIndex = i + 1;
                final String fileName = file.getFileName().toString();
                int progress = (int) ((currentIndex / (double) total) * 100);
                SwingUtilities.invokeLater(() -> {
                    progressBar.setValue(progress);
                    statusLabel.setText("Processed " + currentIndex + "/" + total + ": " + fileName);
                });
            }

            System.out.println("\n=== PROCESSING COMPLETE ===");
            System.out.println("Status: " + (cancelled ? "CANCELLED" : "COMPLETED"));

            SwingUtilities.invokeLater(() -> {
                statusLabel.setText(cancelled ? "❌ Cancelled" : "✅ Done!");
                JOptionPane.showMessageDialog(this, cancelled ? "Processing cancelled." : "All files processed successfully.");
            });

        } catch (Exception e) {
            e.printStackTrace();
            SwingUtilities.invokeLater(() -> JOptionPane.showMessageDialog(this, "Error: " + e.getMessage()));
        }
    }

    private void convertAndApplyMetadata(String jsonFile, String mediaFile) {
        try {
            System.out.println("DEBUG: === CONVERTING GOOGLE TAKEOUT JSON ===");

            // Read and parse the Google Takeout JSON
            String jsonContent = new String(Files.readAllBytes(Paths.get(jsonFile)));

            // Extract metadata using simple string parsing (avoiding JSON library dependency)
            String photoTakenTimestamp = extractSimpleValue(jsonContent, "photoTakenTime.*?\"timestamp\"\\s*:\\s*\"([^\"]+)\"");
            String creationTimestamp = extractSimpleValue(jsonContent, "creationTime.*?\"timestamp\"\\s*:\\s*\"([^\"]+)\"");
            String latitude = extractSimpleValue(jsonContent, "geoData.*?\"latitude\"\\s*:\\s*([^,}\\s]+)");
            String longitude = extractSimpleValue(jsonContent, "geoData.*?\"longitude\"\\s*:\\s*([^,}\\s]+)");
            String title = extractSimpleValue(jsonContent, "\"title\"\\s*:\\s*\"([^\"]+)\"");
            String description = extractSimpleValue(jsonContent, "\"description\"\\s*:\\s*\"([^\"]*)\"");

            System.out.println("DEBUG: Extracted metadata:");
            System.out.println("DEBUG:   photoTakenTime: " + photoTakenTimestamp);
            System.out.println("DEBUG:   creationTime: " + creationTimestamp);
            System.out.println("DEBUG:   latitude: " + latitude);
            System.out.println("DEBUG:   longitude: " + longitude);
            System.out.println("DEBUG:   title: " + title);

            // Apply metadata using individual ExifTool commands
            applyMetadataWithExifTool(mediaFile, photoTakenTimestamp, creationTimestamp, latitude, longitude, title, description);

        } catch (Exception e) {
            System.err.println("DEBUG: Error converting metadata: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private String extractJsonValue(String json, String... keys) {
        try {
            String current = json;
            for (int i = 0; i < keys.length; i++) {
                String key = keys[i];

                if (i == keys.length - 1) {
                    // Last key - extract the actual value
                    // Try quoted string first
                    String pattern = "\"" + key + "\"\\s*:\\s*\"([^\"]+)\"";
                    java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern);
                    java.util.regex.Matcher m = p.matcher(current);
                    if (m.find()) {
                        return m.group(1);
                    }

                    // Try unquoted number/boolean
                    pattern = "\"" + key + "\"\\s*:\\s*([^,}\\s]+)";
                    p = java.util.regex.Pattern.compile(pattern);
                    m = p.matcher(current);
                    if (m.find()) {
                        return m.group(1);
                    }

                    return null;
                } else {
                    // Intermediate key - extract the object/array content
                    String pattern = "\"" + key + "\"\\s*:\\s*\\{([^}]+)\\}";
                    java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern);
                    java.util.regex.Matcher m = p.matcher(current);
                    if (m.find()) {
                        current = "{" + m.group(1) + "}";
                    } else {
                        return null;
                    }
                }
            }
            return current;
        } catch (Exception e) {
            System.out.println("DEBUG: JSON parsing error for keys " + java.util.Arrays.toString(keys) + ": " + e.getMessage());
            return null;
        }
    }

    private String extractSimpleValue(String json, String pattern) {
        try {
            java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern, java.util.regex.Pattern.DOTALL);
            java.util.regex.Matcher m = p.matcher(json);
            if (m.find()) {
                String value = m.group(1);
                System.out.println("DEBUG: Extracted '" + value + "' using pattern: " + pattern);
                return value;
            } else {
                System.out.println("DEBUG: No match for pattern: " + pattern);
                return null;
            }
        } catch (Exception e) {
            System.out.println("DEBUG: Error with pattern '" + pattern + "': " + e.getMessage());
            return null;
        }
    }

    private void applyMetadataWithExifTool(String mediaFile, String photoTakenTimestamp, String creationTimestamp,
                                          String latitude, String longitude, String title, String description) {
        try {
            System.out.println("DEBUG: === APPLYING METADATA WITH EXIFTOOL ===");

            // Build ExifTool command with proper metadata tags
            java.util.List<String> command = new java.util.ArrayList<>();
            command.add(EXIFTOOL);
            command.add("-overwrite_original");

            // Use the photo taken time as the primary date (most important for sorting)
            String primaryTimestamp = photoTakenTimestamp != null ? photoTakenTimestamp : creationTimestamp;

            if (primaryTimestamp != null && !primaryTimestamp.equals("null")) {
                try {
                    // Convert Unix timestamp to ExifTool date format
                    long timestamp = Long.parseLong(primaryTimestamp);
                    java.time.Instant instant = java.time.Instant.ofEpochSecond(timestamp);
                    java.time.LocalDateTime dateTime = java.time.LocalDateTime.ofInstant(instant, java.time.ZoneOffset.UTC);
                    String exifDate = dateTime.format(java.time.format.DateTimeFormatter.ofPattern("yyyy:MM:dd HH:mm:ss"));

                    System.out.println("DEBUG: Setting date to: " + exifDate);

                    // Determine file type
                    String fileName = mediaFile.toLowerCase();
                    boolean isVideo = fileName.endsWith(".mp4") || fileName.endsWith(".mov") ||
                                    fileName.endsWith(".avi") || fileName.endsWith(".mkv");

                    if (isVideo) {
                        System.out.println("DEBUG: Applying video-specific metadata tags");
                        // Video file metadata tags
                        command.add("-QuickTime:CreateDate=" + exifDate);
                        command.add("-QuickTime:ModifyDate=" + exifDate);
                        command.add("-QuickTime:TrackCreateDate=" + exifDate);
                        command.add("-QuickTime:TrackModifyDate=" + exifDate);
                        command.add("-QuickTime:MediaCreateDate=" + exifDate);
                        command.add("-QuickTime:MediaModifyDate=" + exifDate);
                        command.add("-XMP:CreateDate=" + exifDate);
                        command.add("-XMP:ModifyDate=" + exifDate);
                        command.add("-XMP:DateTimeOriginal=" + exifDate);
                    } else {
                        System.out.println("DEBUG: Applying image-specific metadata tags");
                        // Image file metadata tags
                        command.add("-DateTimeOriginal=" + exifDate);
                        command.add("-CreateDate=" + exifDate);
                        command.add("-ModifyDate=" + exifDate);
                        command.add("-XMP:CreateDate=" + exifDate);
                        command.add("-XMP:DateTimeOriginal=" + exifDate);
                    }
                } catch (NumberFormatException e) {
                    System.out.println("DEBUG: Invalid timestamp: " + primaryTimestamp);
                }
            }

            // Set GPS coordinates if available
            if (latitude != null && longitude != null && !latitude.equals("0.0") && !longitude.equals("0.0")) {
                System.out.println("DEBUG: Setting GPS coordinates: " + latitude + ", " + longitude);
                command.add("-GPSLatitude=" + latitude);
                command.add("-GPSLongitude=" + longitude);
                command.add("-GPSLatitudeRef=" + (Double.parseDouble(latitude) >= 0 ? "N" : "S"));
                command.add("-GPSLongitudeRef=" + (Double.parseDouble(longitude) >= 0 ? "E" : "W"));
            }

            // Set title and description
            if (title != null && !title.isEmpty()) {
                command.add("-XMP:Title=" + title);
                command.add("-IPTC:ObjectName=" + title);
            }
            if (description != null && !description.isEmpty()) {
                command.add("-XMP:Description=" + description);
                command.add("-IPTC:Caption-Abstract=" + description);
            }

            command.add(mediaFile);

            System.out.println("DEBUG: ExifTool command: " + String.join(" ", command));

            ProcessBuilder pb = new ProcessBuilder(command);
            pb.redirectErrorStream(true);
            Process process = pb.start();

            // Read output
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    System.out.println("DEBUG: ExifTool: " + line);
                }
            }

            int exitCode = process.waitFor();
            System.out.println("DEBUG: ExifTool exit code: " + exitCode);

            if (exitCode == 0) {
                System.out.println("DEBUG: ✓ Metadata applied successfully!");
                verifyMetadata(mediaFile);
            } else {
                System.out.println("DEBUG: ✗ Failed to apply metadata");
            }

        } catch (Exception e) {
            System.err.println("DEBUG: Error applying metadata: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void runExifTool(String jsonFile, String mediaFile) {
        try {
            System.out.println("DEBUG: === RUNNING EXIFTOOL ===");

            // First check if ExifTool exists
            File exifToolFile = new File(EXIFTOOL);
            System.out.println("DEBUG: Checking for ExifTool at: " + exifToolFile.getAbsolutePath());
            if (!exifToolFile.exists()) {
                System.err.println("DEBUG: ERROR - ExifTool not found: " + EXIFTOOL);
                return;
            }
            System.out.println("DEBUG: ExifTool found!");

            System.out.println("DEBUG: JSON file: " + jsonFile);
            System.out.println("DEBUG: Media file: " + mediaFile);

            // Check if files exist
            if (!new File(jsonFile).exists()) {
                System.err.println("DEBUG: ERROR - JSON file does not exist: " + jsonFile);
                return;
            }
            if (!new File(mediaFile).exists()) {
                System.err.println("DEBUG: ERROR - Media file does not exist: " + mediaFile);
                return;
            }

            // Check for problematic characters in paths
            if (jsonFile.matches(".*[^\\x00-\\x7F].*") || mediaFile.matches(".*[^\\x00-\\x7F].*")) {
                System.out.println("DEBUG: Detected Unicode characters in file paths, using temp file approach");
                runExifToolWithTempFile(jsonFile, mediaFile);
                return;
            }

            ProcessBuilder pb = new ProcessBuilder(
                    EXIFTOOL,
                    "-json=" + jsonFile,
                    "-overwrite_original",
                    "-charset", "UTF8",  // Handle Unicode characters
                    "-v",  // Verbose output for debugging
                    mediaFile
            );
            pb.redirectErrorStream(true);

            // Set environment to handle Unicode
            pb.environment().put("LANG", "en_US.UTF-8");

            System.out.println("DEBUG: Command: " + String.join(" ", pb.command()));
            Process process = pb.start();

            // Read the output for debugging
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    System.out.println("DEBUG: ExifTool output: " + line);
                }
            }

            int exitCode = process.waitFor();
            System.out.println("DEBUG: ExifTool exit code: " + exitCode);
            if (exitCode == 0) {
                System.out.println("DEBUG: ✓ ExifTool succeeded!");

                // Verify metadata was actually written by reading it back
                verifyMetadata(mediaFile);
            } else {
                System.out.println("DEBUG: ✗ ExifTool failed with exit code: " + exitCode);
            }

        } catch (Exception e) {
            System.err.println("DEBUG: ExifTool exception: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void showJsonContent(String jsonFile) {
        try {
            System.out.println("DEBUG: === JSON CONTENT ===");
            String content = new String(Files.readAllBytes(Paths.get(jsonFile)));
            // Show first 500 characters to avoid too much output
            if (content.length() > 500) {
                System.out.println("DEBUG: JSON content (first 500 chars): " + content.substring(0, 500) + "...");
            } else {
                System.out.println("DEBUG: JSON content: " + content);
            }
        } catch (Exception e) {
            System.out.println("DEBUG: Error reading JSON file: " + e.getMessage());
        }
    }

    private void verifyMetadata(String mediaFile) {
        try {
            System.out.println("DEBUG: === VERIFYING OUTPUT FILE METADATA ===");
            System.out.println("DEBUG: Reading metadata from: " + mediaFile);

            // Check if it's a video file
            String fileName = mediaFile.toLowerCase();
            boolean isVideo = fileName.endsWith(".mp4") || fileName.endsWith(".mov") ||
                            fileName.endsWith(".avi") || fileName.endsWith(".mkv");

            ProcessBuilder pb;
            if (isVideo) {
                System.out.println("DEBUG: Checking video metadata tags");
                pb = new ProcessBuilder(
                        EXIFTOOL,
                        "-QuickTime:CreateDate",
                        "-QuickTime:ModifyDate",
                        "-QuickTime:TrackCreateDate",
                        "-QuickTime:MediaCreateDate",
                        "-XMP:CreateDate",
                        "-XMP:DateTimeOriginal",
                        "-GPS*",
                        "-s",  // Short tag names
                        mediaFile
                );
            } else {
                System.out.println("DEBUG: Checking image metadata tags");
                pb = new ProcessBuilder(
                        EXIFTOOL,
                        "-DateTimeOriginal",
                        "-CreateDate",
                        "-ModifyDate",
                        "-GPS*",
                        "-XMP:*",
                        "-EXIF:*",
                        "-s",  // Short tag names
                        mediaFile
                );
            }
            pb.redirectErrorStream(true);

            Process process = pb.start();

            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
            }

            int exitCode = process.waitFor();
            if (exitCode == 0) {
                String metadata = output.toString().trim();
                if (metadata.isEmpty()) {
                    System.out.println("DEBUG: ⚠️ NO METADATA FOUND in output file!");
                } else {
                    System.out.println("DEBUG: ✓ Metadata found in output file:");
                    System.out.println("DEBUG: " + metadata.replace("\n", "\nDEBUG: "));
                }
            } else {
                System.out.println("DEBUG: ✗ Failed to read metadata, exit code: " + exitCode);
            }

        } catch (Exception e) {
            System.out.println("DEBUG: Error verifying metadata: " + e.getMessage());
        }
    }

    private void runExifToolWithTempFile(String jsonFile, String mediaFile) {
        Path tempDir = null;
        try {
            // Create temporary directory
            tempDir = Files.createTempDirectory("exiftool_temp");

            // Copy JSON to temp location with simple name
            Path tempJson = tempDir.resolve("metadata.json");
            Files.copy(Paths.get(jsonFile), tempJson, StandardCopyOption.REPLACE_EXISTING);

            System.out.println("DEBUG: Using temp JSON file: " + tempJson.toString());

            ProcessBuilder pb = new ProcessBuilder(
                    EXIFTOOL,
                    "-json=" + tempJson.toString(),
                    "-overwrite_original",
                    "-v",
                    mediaFile
            );
            pb.redirectErrorStream(true);

            System.out.println("DEBUG: Command: " + String.join(" ", pb.command()));
            Process process = pb.start();

            // Read the output for debugging
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    System.out.println("DEBUG: ExifTool output: " + line);
                }
            }

            int exitCode = process.waitFor();
            System.out.println("DEBUG: ExifTool exit code: " + exitCode);
            if (exitCode == 0) {
                System.out.println("DEBUG: ✓ ExifTool succeeded with temp file!");
            } else {
                System.out.println("DEBUG: ✗ ExifTool failed with exit code: " + exitCode);
            }

        } catch (Exception e) {
            System.err.println("DEBUG: ExifTool temp file error: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // Clean up temp directory
            if (tempDir != null) {
                try {
                    Files.walk(tempDir)
                        .sorted((a, b) -> b.compareTo(a)) // Delete files before directories
                        .forEach(path -> {
                            try {
                                Files.delete(path);
                            } catch (Exception e) {
                                // Ignore cleanup errors
                            }
                        });
                } catch (Exception e) {
                    // Ignore cleanup errors
                }
            }
        }
    }

    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            MetadataRestorerGUI gui = new MetadataRestorerGUI();
            gui.setVisible(true);
        });
    }
}
