#------------------------------------------------------------------------------
# File:         cs.pm
#
# Description:  ExifTool Czech language translations
#
# Notes:        This file generated automatically by Image::ExifTool::TagInfoXML
#------------------------------------------------------------------------------

package Image::ExifTool::Lang::cs;

use strict;
use vars qw($VERSION);

$VERSION = '1.07';

%Image::ExifTool::Lang::cs::Translate = (
   'AEMeteringMode' => {
      PrintConv => {
        'Multi-segment' => 'Multi segment',
      },
    },
   'AEProgramMode' => {
      PrintConv => {
        'Landscape' => 'Krajina',
        'Macro' => 'Makro',
        'Portrait' => 'Portrét',
      },
    },
   'AFPoint' => {
      PrintConv => {
        'None' => 'Žádná',
      },
    },
   'AFPointBrightness' => {
      PrintConv => {
        'Normal' => 'Normální',
      },
    },
   'AFPointSelectionMethod' => {
      PrintConv => {
        'Normal' => 'Normální',
      },
    },
   'AFPointsInFocus' => {
      PrintConv => {
        'None' => 'Žádná',
      },
    },
   'APEVersion' => 'APE verze',
   'ActiveD-Lighting' => {
      PrintConv => {
        'Low' => 'Méně',
        'Normal' => 'Normální',
      },
    },
   'ActiveD-LightingMode' => {
      PrintConv => {
        'Low' => 'Méně',
        'Normal' => 'Normální',
      },
    },
   'AdultContentWarning' => {
      PrintConv => {
        'Unknown' => 'Neznámý',
      },
    },
   'Annotations' => 'Poznámky Photoshop',
   'Aperture' => 'Clona',
   'ApertureValue' => 'Clona',
   'Artist' => 'Autor',
   'AssistButtonFunction' => {
      PrintConv => {
        'Normal' => 'Normální',
      },
    },
   'Author' => 'Autor',
   'AuthorsPosition' => 'Pozice autora',
   'AutoLightingOptimizer' => {
      PrintConv => {
        'Low' => 'Méně',
      },
    },
   'AutoRotate' => {
      PrintConv => {
        'None' => 'Žádná',
        'Rotate 180' => '180° (dolů/vpravo)',
        'Rotate 270 CW' => '90° po směru HR (vlevo/dolů)',
        'Rotate 90 CW' => '90° ptoti směru HR (vpravo/nahoru)',
        'n/a' => 'Neznámý',
      },
    },
   'BadFaxLines' => 'Špatné faxové řádky',
   'BannerImageType' => {
      PrintConv => {
        'None' => 'Žádná',
      },
    },
   'BatteryLevel' => 'Stav baterie',
   'BitsPerSample' => 'Počet bitů na složku',
   'BlurWarning' => {
      PrintConv => {
        'None' => 'Žádná',
      },
    },
   'Brightness' => 'Jas',
   'BrightnessValue' => 'Jas',
   'By-line' => 'Autor',
   'CFAPattern' => 'CFA matrice',
   'CFAPattern2' => 'CFA matice 2',
   'CFARepeatPatternDim' => 'Velikost berevné matice CFA',
   'CPUType' => {
      PrintConv => {
        'None' => 'Žádná',
      },
    },
   'CalibrationIlluminant1' => {
      PrintConv => {
        'Cloudy' => 'Zataženo',
        'Cool White Fluorescent' => 'Studená zářivka',
        'Day White Fluorescent' => 'Denní zářivka',
        'Daylight' => 'Denní světlo',
        'Daylight Fluorescent' => 'Denní světlo',
        'Fine Weather' => 'Slunečno',
        'Flash' => 'Blesk',
        'Fluorescent' => 'Žárovka',
        'ISO Studio Tungsten' => 'Studiová světla',
        'Other' => 'Jiné osvětlení',
        'Shade' => 'Stíny',
        'Standard Light A' => 'Standardní světlo A',
        'Standard Light B' => 'Standardní světlo B',
        'Standard Light C' => 'Standardní světlo C',
        'Tungsten (Incandescent)' => 'Zářivka',
        'Unknown' => 'Neznámý',
        'White Fluorescent' => 'Bílá zářivka',
      },
    },
   'CalibrationIlluminant2' => {
      PrintConv => {
        'Cloudy' => 'Zataženo',
        'Cool White Fluorescent' => 'Studená zářivka',
        'Day White Fluorescent' => 'Denní zářivka',
        'Daylight' => 'Denní světlo',
        'Daylight Fluorescent' => 'Denní světlo',
        'Fine Weather' => 'Slunečno',
        'Flash' => 'Blesk',
        'Fluorescent' => 'Žárovka',
        'ISO Studio Tungsten' => 'Studiová světla',
        'Other' => 'Jiné osvětlení',
        'Shade' => 'Stíny',
        'Standard Light A' => 'Standardní světlo A',
        'Standard Light B' => 'Standardní světlo B',
        'Standard Light C' => 'Standardní světlo C',
        'Tungsten (Incandescent)' => 'Zářivka',
        'Unknown' => 'Neznámý',
        'White Fluorescent' => 'Bílá zářivka',
      },
    },
   'CameraOrientation' => {
      Description => 'Orientace',
      PrintConv => {
        'Horizontal (normal)' => '0° (nahoru/vlevo)',
        'Rotate 270 CW' => '90° po směru HR (vlevo/dolů)',
        'Rotate 90 CW' => '90° ptoti směru HR (vpravo/nahoru)',
      },
    },
   'CanonExposureMode' => {
      PrintConv => {
        'Aperture-priority AE' => 'Priorita clony',
        'Manual' => 'Manuální',
        'Shutter speed priority AE' => 'Priorita času',
      },
    },
   'Caption-Abstract' => 'Popisek',
   'CaptionWriter' => 'Autor popisku',
   'CaptureXResolutionUnit' => {
      PrintConv => {
        'um' => 'µm (mikrometr)',
      },
    },
   'CaptureYResolutionUnit' => {
      PrintConv => {
        'um' => 'µm (mikrometr)',
      },
    },
   'Categories' => 'Kategorie',
   'Category' => 'Kategorie',
   'CellLength' => 'Délka buňky',
   'CellWidth' => 'Šířka buňky',
   'CenterWeightedAreaSize' => {
      PrintConv => {
        'Average' => 'Průměr',
      },
    },
   'ChrominanceNR_TIFF_JPEG' => {
      PrintConv => {
        'Low' => 'Méně',
      },
    },
   'ChrominanceNoiseReduction' => {
      PrintConv => {
        'Low' => 'Méně',
      },
    },
   'City' => 'Město',
   'CleanFaxData' => 'Čistá fax data',
   'ColorEffect' => {
      PrintConv => {
        'Sepia' => 'Sépie',
      },
    },
   'ColorFilter' => 'Barevný filtr',
   'ColorMap' => 'Barevná mapa',
   'ColorMode' => {
      PrintConv => {
        'Landscape' => 'Krajina',
        'Normal' => 'Normální',
        'Portrait' => 'Portrét',
      },
    },
   'ColorMoireReductionMode' => {
      PrintConv => {
        'Low' => 'Méně',
      },
    },
   'ColorResponseUnit' => 'Odpovídající barevná jednotka',
   'ColorSpace' => {
      Description => 'Barevný prostor',
      PrintConv => {
        'ICC Profile' => 'ICC Profil',
        'Uncalibrated' => 'Nekalibrován',
      },
    },
   'ColorTemperature' => 'Teplota barev',
   'ColorTone' => {
      PrintConv => {
        'Normal' => 'Normální',
      },
    },
   'CommanderGroupAMode' => {
      PrintConv => {
        'Manual' => 'Manuální',
      },
    },
   'CommanderGroupBMode' => {
      PrintConv => {
        'Manual' => 'Manuální',
      },
    },
   'CommanderInternalFlash' => {
      PrintConv => {
        'Manual' => 'Manuální',
      },
    },
   'Comment' => 'Komentář',
   'ComponentsConfiguration' => 'Určení složek',
   'CompressedBitsPerPixel' => 'Komprimační mod',
   'Compression' => {
      Description => 'Kompresní algoritmus',
      PrintConv => {
        'JPEG' => 'JPEG komprese',
        'JPEG (old-style)' => 'JPEG (pův. verze)',
        'Kodak DCR Compressed' => 'Kodak DCR komprese',
        'Kodak KDC Compressed' => 'Kodak KDC komprese',
        'Next' => 'Kódování NeXT 2-bit',
        'Nikon NEF Compressed' => 'Nikon NEF komprese',
        'None' => 'Žádná',
        'Pentax PEF Compressed' => 'Pentax PEF komprese',
        'SGILog' => 'Kódování SGI 32-bit Log Luminance',
        'SGILog24' => 'Kódování SGI 24-bit Log Luminance',
        'Sony ARW Compressed' => 'Sony ARW komprese',
        'Thunderscan' => 'Kódování ThunderScan 4-bit',
        'Uncompressed' => 'Bez komprese',
      },
    },
   'CompressionType' => {
      PrintConv => {
        'None' => 'Žádná',
      },
    },
   'ConsecutiveBadFaxLines' => 'Sekvence vadných faxových řádků',
   'Contrast' => {
      Description => 'Kontrast',
      PrintConv => {
        'High' => 'Více',
        'Low' => 'Méně',
        'Normal' => 'Normální',
      },
    },
   'ConversionLens' => {
      PrintConv => {
        'Macro' => 'Makro',
      },
    },
   'Copyright' => 'Držitel práv',
   'CopyrightNotice' => 'Oznámení o autorských právech',
   'CopyrightStatus' => {
      PrintConv => {
        'Unknown' => 'Neznámý',
      },
    },
   'Country' => 'Země',
   'Country-PrimaryLocationName' => 'Země',
   'CreateDate' => 'Datum a čas generování digitálních dat',
   'CreationDate' => 'Datum vytvoření',
   'Credit' => 'Kredit',
   'CropUnit' => {
      PrintConv => {
        'inches' => 'Palce',
      },
    },
   'CropUnits' => {
      PrintConv => {
        'inches' => 'Palce',
      },
    },
   'CustomRendered' => {
      Description => 'Zpracování obrazu',
      PrintConv => {
        'Custom' => 'Uživatelské zpracování',
        'Normal' => 'Normální proces',
      },
    },
   'DataImprint' => {
      PrintConv => {
        'None' => 'Žádná',
      },
    },
   'DateCreated' => 'Datum vytvoření',
   'DateTime' => 'Datum a čas změny souboru',
   'DateTimeOriginal' => 'Datum a čas vzniku originálních dat',
   'Description' => 'Popis',
   'DeviceSettingDescription' => 'Popis nastavení zařízení',
   'DialDirectionTvAv' => {
      PrintConv => {
        'Normal' => 'Normální',
      },
    },
   'DigitalZoom' => {
      Description => 'Digitální přiblížení',
      PrintConv => {
        'None' => 'Žádná',
      },
    },
   'DigitalZoomRatio' => 'Digitální zoom',
   'Directory' => 'Umístění souboru',
   'DisplaySize' => {
      PrintConv => {
        'Normal' => 'Normální',
      },
    },
   'DisplayUnits' => {
      PrintConv => {
        'inches' => 'Palce',
      },
    },
   'DisplayXResolutionUnit' => {
      PrintConv => {
        'um' => 'µm (mikrometr)',
      },
    },
   'DisplayYResolutionUnit' => {
      PrintConv => {
        'um' => 'µm (mikrometr)',
      },
    },
   'DisplayedUnitsX' => {
      PrintConv => {
        'inches' => 'Palce',
      },
    },
   'DisplayedUnitsY' => {
      PrintConv => {
        'inches' => 'Palce',
      },
    },
   'DjVuVersion' => 'DjVu verze',
   'DocumentName' => 'Jméno dokumentu',
   'DotRange' => 'Bodová rozteč',
   'DriveMode' => 'Režim spouště',
   'ETTLII' => {
      PrintConv => {
        'Average' => 'Průměr',
      },
    },
   'EasyMode' => {
      PrintConv => {
        'Landscape' => 'Krajina',
        'Macro' => 'Makro',
        'Manual' => 'Manuální',
        'Night' => 'Noční foto',
        'Portrait' => 'Portrét',
      },
    },
   'Emphasis' => {
      PrintConv => {
        'None' => 'Žádná',
      },
    },
   'ExifImageHeight' => 'Výška',
   'ExifImageWidth' => 'Šířka',
   'ExifOffset' => 'Ukazatel Exif IFD',
   'ExifToolVersion' => 'ExifTool verze',
   'ExifVersion' => 'Exif verze',
   'ExpandFilm' => 'Ext. film',
   'ExpandFilterLens' => 'Ext. filtr objektivu',
   'ExpandFlashLamp' => 'Ext. světlo blesku',
   'ExpandLens' => 'Ext. objektiv',
   'ExpandScanner' => 'Ext. skener',
   'ExpandSoftware' => 'Ext. Software',
   'ExposureCompensation' => 'Korekce expozice',
   'ExposureIndex' => 'Index expozice',
   'ExposureMode' => {
      Description => 'Mód expozice',
      PrintConv => {
        'Aperture Priority' => 'Priorita clony',
        'Aperture-priority AE' => 'Priorita clony',
        'Auto' => 'Automatická expozice',
        'Auto bracket' => 'Auto braketing',
        'Landscape' => 'Krajina',
        'Manual' => 'Manuální expozice',
        'Portrait' => 'Portrét',
        'Shutter Priority' => 'Priorita času',
        'Shutter speed priority AE' => 'Priorita času',
      },
    },
   'ExposureModeInManual' => {
      PrintConv => {
        'Center-weighted average' => 'Zvýrazněný střed',
        'Partial metering' => 'Blokové',
        'Spot metering' => 'Středový bod',
      },
    },
   'ExposureProgram' => {
      Description => 'Expoziční mod',
      PrintConv => {
        'Action (High speed)' => 'Akční program (ovlivněný čas závěrky)',
        'Aperture Priority' => 'Priorita clony',
        'Aperture-priority AE' => 'Priorita clony',
        'Bulb' => 'Žárovka',
        'Creative (Slow speed)' => 'Kreativní program (ovlivněná hloubka ostrosti)',
        'Landscape' => 'Krajina',
        'Manual' => 'Manuální',
        'Not Defined' => 'Nedefinovaný',
        'Portrait' => 'Portrét',
        'Program AE' => 'Normální program',
        'Shutter Priority' => 'Priorita času',
        'Shutter speed priority AE' => 'Priorita času',
      },
    },
   'ExposureTime' => 'Expoziční čas',
   'ExposureTime2' => 'Expoziční čas 2',
   'FNumber' => 'F hodnota',
   'FaceOrientation' => {
      PrintConv => {
        'Horizontal (normal)' => '0° (nahoru/vlevo)',
        'Rotate 180' => '180° (dolů/vpravo)',
        'Rotate 270 CW' => '90° po směru HR (vlevo/dolů)',
        'Rotate 90 CW' => '90° ptoti směru HR (vpravo/nahoru)',
      },
    },
   'FaxProfile' => {
      PrintConv => {
        'Unknown' => 'Neznámý',
      },
    },
   'FaxRecvParams' => 'Parametry příjemce faxu',
   'FaxRecvTime' => 'Čas příjmu faxu',
   'FaxSubAddress' => 'Sub adresa faxu',
   'FileFormat' => 'Formát',
   'FileModifyDate' => 'Datum úpravy',
   'FileName' => 'Jméno',
   'FileSize' => 'Velikost',
   'FileSource' => {
      Description => 'Zdroj dat',
      PrintConv => {
        'Digital Camera' => 'Digitální fotoaparát',
        'Film Scanner' => 'Filmový skener',
        'Reflection Print Scanner' => 'Skener',
      },
    },
   'FileType' => 'Typ',
   'Filename' => 'Jméno',
   'FillOrder' => {
      Description => 'Pořadí výplně',
      PrintConv => {
        'Normal' => 'Normální',
      },
    },
   'FilterEffect' => {
      PrintConv => {
        'None' => 'Žádná',
      },
    },
   'FilterEffectMonochrome' => {
      PrintConv => {
        'None' => 'Žádná',
      },
    },
   'Flash' => {
      Description => 'Blesk',
      PrintConv => {
        'Auto, Did not fire' => 'Blesk nepoužit, auto mod',
        'Auto, Did not fire, Red-eye reduction' => 'Auto mod, nepoužit, redukce červených očí',
        'Auto, Fired' => 'Blesk použit, auto mod',
        'Auto, Fired, Red-eye reduction' => 'Blesk použit, auto mod, redukce červených očí',
        'Auto, Fired, Red-eye reduction, Return detected' => 'Blesk použit, auto mod, redukce červených očí, odraz detekován',
        'Auto, Fired, Red-eye reduction, Return not detected' => 'Blesk použit, auto mod, redukce červených očí, odraz nezjištěn',
        'Auto, Fired, Return detected' => 'Blesk použit, auto mod, odraz detekován',
        'Auto, Fired, Return not detected' => 'Blesk použit, auto mod, odraz nedetekován',
        'Did not fire' => 'Blesk ne',
        'Fired' => 'Blesk ano',
        'Fired, Red-eye reduction' => 'Blesk použit, redukce červených očí',
        'Fired, Red-eye reduction, Return detected' => 'Blesk použit, redukce červených očí, odraz detekován',
        'Fired, Red-eye reduction, Return not detected' => 'Blesk použit, redukce červených očí, odraz nezjištěn',
        'Fired, Return detected' => 'Odraz strobozáblesků detekován',
        'Fired, Return not detected' => 'Odraz strobozáblesků nezjištěn',
        'No Flash' => 'Blesk ne',
        'No flash function' => 'Blesk nezjištěn',
        'Off' => 'Blesk nepoužit, vynucený mod',
        'Off, Did not fire' => 'Blesk nepoužit, vynucený mod',
        'Off, Did not fire, Return not detected' => 'Blesk vypnut, bez záblesku, odraz nezachycen',
        'Off, No flash function' => 'Neaktivní, bez funkce blesku',
        'Off, Red-eye reduction' => 'Neaktivní, redukce červených očí',
        'On' => 'Blesk použit, vynucený mod',
        'On, Did not fire' => 'Blesk zapnut, nepoužit',
        'On, Fired' => 'Blesk použit, vynucený mod',
        'On, Red-eye reduction' => 'Blesk použit, vynucený mod, redukce červených očí',
        'On, Red-eye reduction, Return detected' => 'Blesk použit, vynucený mod, redukce červených očí, odraz detekován',
        'On, Red-eye reduction, Return not detected' => 'Blesk použit, vynucený mod, redukce červených očí, odraz nezjištěn',
        'On, Return detected' => 'Blesk použit, vynucený mod, odraz detekován',
        'On, Return not detected' => 'Blesk použit, vynucený mod, odraz nezjištěn',
      },
    },
   'FlashControlMode' => {
      PrintConv => {
        'Manual' => 'Manuální',
      },
    },
   'FlashDevice' => {
      PrintConv => {
        'None' => 'Žádná',
      },
    },
   'FlashEnergy' => 'Síla záblesku',
   'FlashExposureComp' => 'Kompenzace blesku',
   'FlashGroupAControlMode' => {
      PrintConv => {
        'Manual' => 'Manuální',
      },
    },
   'FlashGroupBControlMode' => {
      PrintConv => {
        'Manual' => 'Manuální',
      },
    },
   'FlashGroupCControlMode' => {
      PrintConv => {
        'Manual' => 'Manuální',
      },
    },
   'FlashIntensity' => {
      PrintConv => {
        'Normal' => 'Normální',
      },
    },
   'FlashMode' => {
      PrintConv => {
        'Normal' => 'Normální',
        'Unknown' => 'Neznámý',
      },
    },
   'FlashModel' => {
      PrintConv => {
        'None' => 'Žádná',
      },
    },
   'FlashOptions' => {
      PrintConv => {
        'Normal' => 'Normální',
      },
    },
   'FlashOptions2' => {
      PrintConv => {
        'Normal' => 'Normální',
      },
    },
   'FlashType' => {
      PrintConv => {
        'None' => 'Žádná',
      },
    },
   'FlashpixVersion' => 'Podporovaná verze Flashpix',
   'FocalLength' => 'Ohnisková vzdálenost',
   'FocalLength35efl' => 'Ohnisková vzdálenost',
   'FocalLengthIn35mmFormat' => 'Přepočtená ohnisková vzdálenost (35mm)',
   'FocalPlaneResolutionUnit' => {
      Description => 'Jednotka rozlišení senzoru',
      PrintConv => {
        'None' => 'Žádná',
        'inches' => 'Palce',
        'um' => 'µm (mikrometr)',
      },
    },
   'FocalPlaneXResolution' => 'Horizontální rozlišení senzoru',
   'FocalPlaneYResolution' => 'Vertikální rozlišení senzoru',
   'Focus' => {
      PrintConv => {
        'Manual' => 'Manuální',
      },
    },
   'FocusContinuous' => {
      PrintConv => {
        'Manual' => 'Manuální',
      },
    },
   'FocusMode' => {
      Description => 'Ostření',
      PrintConv => {
        'Macro' => 'Makro',
        'Manual' => 'Manuální',
        'Normal' => 'Normální',
      },
    },
   'FocusMode2' => {
      PrintConv => {
        'Manual' => 'Manuální',
      },
    },
   'FocusModeSetting' => {
      PrintConv => {
        'Manual' => 'Manuální',
      },
    },
   'FocusRange' => {
      PrintConv => {
        'Macro' => 'Makro',
        'Manual' => 'Manuální',
        'Normal' => 'Normální',
      },
    },
   'FocusTrackingLockOn' => {
      PrintConv => {
        'Normal' => 'Normální',
      },
    },
   'FrameRate' => 'Snímkovací frekvence',
   'FrameSize' => 'Velikost snímku',
   'FreeByteCounts' => 'Počet volných bytů',
   'FreeOffsets' => 'Volná datová pozice',
   'GIFVersion' => 'GIF verze',
   'GPSAltitude' => 'Nadmořská výška',
   'GPSAltitudeRef' => {
      Description => 'Nadmořská výška',
      PrintConv => {
        'Above Sea Level' => 'Nadmořská výška',
        'Below Sea Level' => 'Nadmořská výška (záporná hodnota)',
      },
    },
   'GPSAreaInformation' => 'Název GPS oblasti',
   'GPSDOP' => 'Přesnost měření',
   'GPSDateStamp' => 'GPS Datum',
   'GPSDateTime' => 'GPS čas (atomový čas)',
   'GPSDestBearing' => 'Azimut cíle',
   'GPSDestBearingRef' => {
      Description => 'Reference azimutu cíle.',
      PrintConv => {
        'Magnetic North' => 'Magnetický směr',
        'True North' => 'Geografický směr',
      },
    },
   'GPSDestDistance' => 'Vzdálenost k cíli',
   'GPSDestDistanceRef' => {
      Description => 'Reference vzdálenosti cíle',
      PrintConv => {
        'Kilometers' => 'Kilometry',
        'Miles' => 'Míle',
        'Nautical Miles' => 'Uzle',
      },
    },
   'GPSDestLatitude' => 'Zeměpisná šířka cíle',
   'GPSDestLatitudeRef' => {
      Description => 'Reference pro zeměpisnou šířku cíle',
      PrintConv => {
        'North' => 'Severní šířka',
        'South' => 'Jižní šířka',
      },
    },
   'GPSDestLongitude' => 'Zeměpisná délka cíle',
   'GPSDestLongitudeRef' => {
      Description => 'Reference pro zeměpisnou délku cíle',
      PrintConv => {
        'East' => 'Východní délka',
        'West' => 'Západní délka',
      },
    },
   'GPSDifferential' => {
      Description => 'GPS rozdílová korekce',
      PrintConv => {
        'Differential Corrected' => 'Započítaná rozdílová korekce',
        'No Correction' => 'Měření bez korekce',
      },
    },
   'GPSImgDirection' => 'Orientace obrázku',
   'GPSImgDirectionRef' => {
      Description => 'Reference k orientaci obrázku',
      PrintConv => {
        'Magnetic North' => 'Magnetický směr',
        'True North' => 'Geografický směr',
      },
    },
   'GPSInfo' => 'IFD ukazatel v GPS informacích',
   'GPSLatitude' => 'Zeměpisná šířka',
   'GPSLatitudeRef' => {
      Description => 'Severní nebo Jižní šířka',
      PrintConv => {
        'North' => 'Severní šířka',
        'South' => 'Jižní šířka',
      },
    },
   'GPSLongitude' => 'Zeměpisná délka',
   'GPSLongitudeRef' => {
      Description => 'Východní nebo západní délka',
      PrintConv => {
        'East' => 'Východní délka',
        'West' => 'Západní délka',
      },
    },
   'GPSMapDatum' => 'Geodetická data',
   'GPSMeasureMode' => {
      Description => 'Mod GPS',
      PrintConv => {
        '2-D' => '2-dimenzionální měření',
        '2-Dimensional' => '2-dimenzionální měření',
        '2-Dimensional Measurement' => '2-dimenzionální měření',
        '3-D' => '3-dimenzionální měření',
        '3-Dimensional' => '3-dimenzionální měření',
        '3-Dimensional Measurement' => '3-dimenzionální měření',
      },
    },
   'GPSProcessingMethod' => 'Název procesní metody GPS',
   'GPSSatellites' => 'GPS satelity využité při měření',
   'GPSSpeed' => 'Rychlost GPS přijímače',
   'GPSSpeedRef' => {
      Description => 'Jednotka rychlosti',
      PrintConv => {
        'km/h' => 'Kilometry za hodinu',
        'knots' => 'Uzle',
        'mph' => 'Míle za hodinu',
      },
    },
   'GPSStatus' => {
      Description => 'Stav GPS přijímače',
      PrintConv => {
        'Measurement Active' => 'Probíhá měření',
        'Measurement Void' => 'Vzájemné měření',
      },
    },
   'GPSTimeStamp' => 'GPS čas (atomový čas)',
   'GPSTrack' => 'Směr pohybu',
   'GPSTrackRef' => {
      Description => 'Reference pro směr pohybu',
      PrintConv => {
        'Magnetic North' => 'Magnetický směr',
        'True North' => 'Geografický směr',
      },
    },
   'GPSVersionID' => 'Verze GPS TAGu',
   'GainControl' => {
      Description => 'Míra jasu',
      PrintConv => {
        'High gain down' => 'Silné zeslabení',
        'High gain up' => 'Silné zesílení',
        'Low gain down' => 'Slabé zeslabení',
        'Low gain up' => 'Slabé zesílení',
        'None' => 'Žádná',
      },
    },
   'Gradation' => 'Pusobivy',
   'GrayResponseCurve' => 'Šedá referenční křivka',
   'GrayResponseUnit' => {
      Description => 'Jednotka odezvy šedé',
      PrintConv => {
        '0.0001' => 'Číslo udávající tisíce jednotek',
        '0.001' => 'Číslo udávající stovky jednotek',
        '0.1' => 'Číslo udávající desítky jednotek',
        '1e-05' => 'Číslo udávající desetitisíce jednotek',
        '1e-06' => 'Číslo udávající statisíce jednotek',
      },
    },
   'HalftoneHints' => 'Půltóny',
   'Headline' => 'Titulek',
   'HighISONoiseReduction' => {
      PrintConv => {
        'Low' => 'Méně',
        'Normal' => 'Normální',
      },
    },
   'HostComputer' => 'Host',
   'Hue' => 'Odstín',
   'ICCProfile' => 'ICC-Profil',
   'IPTC-NAA' => 'IPTC-NAA metadata',
   'ISO' => 'Citlivost ISO',
   'ISOSetting' => {
      PrintConv => {
        'Manual' => 'Manuální',
      },
    },
   'ImageDescription' => 'Popis obrázku',
   'ImageHeight' => 'Výška',
   'ImageHistory' => 'Historie obrázku',
   'ImageNumber' => 'Číslo obrázku',
   'ImageOrientation' => {
      PrintConv => {
        'Portrait' => 'Portrét',
      },
    },
   'ImageQuality' => {
      PrintConv => {
        'Normal' => 'Normální',
      },
    },
   'ImageSize' => 'Velikost snímku',
   'ImageSourceData' => 'Zdrojová data obrázku',
   'ImageTone' => {
      PrintConv => {
        'Landscape' => 'Krajina',
        'Portrait' => 'Portrét',
      },
    },
   'ImageUniqueID' => 'Jedinečné ID obrázku',
   'ImageWidth' => 'Šířka',
   'Index' => 'Nápověda',
   'InkNames' => 'Název náplně',
   'InkSet' => 'Inkoustová sada',
   'Instructions' => 'Pokyny',
   'InternalFlash' => {
      PrintConv => {
        'Fired' => 'Blesk ano',
        'Manual' => 'Manuální',
        'No' => 'Blesk ne',
      },
    },
   'InteropIndex' => {
      Description => 'Identifikace',
      PrintConv => {
        'R03 - DCF option file (Adobe RGB)' => 'R03: DCF option file (Adobe RGB)',
        'R98 - DCF basic file (sRGB)' => 'R98: DCF basic file (sRGB)',
        'THM - DCF thumbnail file' => 'THM: DCF thumbnail file',
      },
    },
   'InteropOffset' => 'Značka součinnosti',
   'InteropVersion' => 'Verze kompatibility',
   'JFIFVersion' => 'JFIF verze',
   'JPEGQuality' => {
      PrintConv => {
        'Standard' => 'Normální',
      },
    },
   'Keyword' => 'Klíčová slova',
   'Keywords' => 'Klíčová slova',
   'LeafData' => 'Leaf data',
   'Lens' => 'Objektiv',
   'LensInfo' => 'Informace o optice',
   'LicenseType' => {
      PrintConv => {
        'Unknown' => 'Neznámý',
      },
    },
   'LightSource' => {
      Description => 'Zdroj světla',
      PrintConv => {
        'Cloudy' => 'Zataženo',
        'Cool White Fluorescent' => 'Studená zářivka',
        'Day White Fluorescent' => 'Denní zářivka',
        'Daylight' => 'Denní světlo',
        'Daylight Fluorescent' => 'Denní světlo',
        'Fine Weather' => 'Slunečno',
        'Flash' => 'Blesk',
        'Fluorescent' => 'Žárovka',
        'ISO Studio Tungsten' => 'Studiová světla',
        'Other' => 'Jiné osvětlení',
        'Shade' => 'Stíny',
        'Standard Light A' => 'Standardní světlo A',
        'Standard Light B' => 'Standardní světlo B',
        'Standard Light C' => 'Standardní světlo C',
        'Tungsten (Incandescent)' => 'Zářivka',
        'Unknown' => 'Neznámý',
        'White Fluorescent' => 'Bílá zářivka',
      },
    },
   'Lightness' => 'Jas',
   'Location' => 'Lokalita',
   'LoopStyle' => {
      PrintConv => {
        'Normal' => 'Normální',
      },
    },
   'LuminanceNoiseReduction' => {
      PrintConv => {
        'Low' => 'Méně',
      },
    },
   'MIEVersion' => 'MIE verze',
   'Macro' => {
      PrintConv => {
        'Macro' => 'Makro',
        'Manual' => 'Manuální',
        'Normal' => 'Normální',
      },
    },
   'MacroMode' => {
      PrintConv => {
        'Macro' => 'Makro',
        'Normal' => 'Normální',
      },
    },
   'Make' => 'Výrobce',
   'MakerNote' => 'Privátní data výrobce',
   'MakerNotes' => 'Poznámka výrobce',
   'ManualFlashOutput' => {
      PrintConv => {
        'Low' => 'Méně',
      },
    },
   'MaxAperture' => 'Maximální clona+C1233 objektivu',
   'MaxApertureValue' => 'Max clona objektivu',
   'MaxSampleValue' => 'Max. hodnota vzorku',
   'MediaType' => {
      PrintConv => {
        'Normal' => 'Normální',
      },
    },
   'Metering' => {
      PrintConv => {
        'Spot' => 'Středový bod',
      },
    },
   'MeteringMode' => {
      Description => 'Režim měření expozice',
      PrintConv => {
        'Average' => 'Průměr',
        'Center-weighted average' => 'Zvýrazněný střed',
        'Multi-segment' => 'Multi segment',
        'Multi-spot' => 'Vícebodové',
        'Other' => 'Jiné',
        'Partial' => 'Blokové',
        'Spot' => 'Středový bod',
        'Unknown' => 'Neznámý',
      },
    },
   'MeteringMode2' => {
      PrintConv => {
        'Multi-segment' => 'Multi segment',
      },
    },
   'MeteringMode3' => {
      PrintConv => {
        'Multi-segment' => 'Multi segment',
      },
    },
   'MinSampleValue' => 'Min. hodnota vzorku',
   'MinoltaQuality' => {
      PrintConv => {
        'Normal' => 'Normální',
      },
    },
   'Model' => 'Typ fotoaparátu',
   'Model2' => 'Typ zařízení (2)',
   'ModifiedPictureStyle' => {
      PrintConv => {
        'Landscape' => 'Krajina',
        'None' => 'Žádná',
        'Portrait' => 'Portrét',
      },
    },
   'ModifiedSharpnessFreq' => {
      PrintConv => {
        'Low' => 'Méně',
      },
    },
   'ModifiedToneCurve' => {
      PrintConv => {
        'Manual' => 'Manuální',
      },
    },
   'ModifiedWhiteBalance' => {
      PrintConv => {
        'Cloudy' => 'Zataženo',
        'Daylight' => 'Denní světlo',
        'Daylight Fluorescent' => 'Denní světlo',
        'Flash' => 'Blesk',
        'Fluorescent' => 'Žárovka',
        'Shade' => 'Stíny',
        'Tungsten' => 'Zářivka',
      },
    },
   'ModifyDate' => 'Datum a čas změny souboru',
   'MonochromeFilterEffect' => {
      PrintConv => {
        'None' => 'Žádná',
      },
    },
   'MonochromeToningEffect' => {
      PrintConv => {
        'None' => 'Žádná',
      },
    },
   'NEFCompression' => {
      PrintConv => {
        'Uncompressed' => 'Bez komprese',
      },
    },
   'Noise' => 'Šum',
   'NoiseFilter' => {
      PrintConv => {
        'Low' => 'Méně',
      },
    },
   'NoiseReduction' => {
      Description => 'Potlačení šumu',
      PrintConv => {
        'Normal' => 'Normální',
      },
    },
   'NumberofInks' => 'Číslo náplně',
   'ObjectFileType' => {
      PrintConv => {
        'None' => 'Žádná',
        'Unknown' => 'Neznámý',
      },
    },
   'OldSubfileType' => 'Typ podsekce',
   'Opto-ElectricConvFactor' => 'Optoel. konverzní faktor (OECF)',
   'Orientation' => {
      Description => 'Orientace',
      PrintConv => {
        'Horizontal (normal)' => '0° (nahoru/vlevo)',
        'Mirror horizontal' => '0° (nahoru/vpravo)',
        'Mirror horizontal and rotate 270 CW' => '90° po směru HR (vlevo/nahoru)',
        'Mirror horizontal and rotate 90 CW' => '90° ptoti směru HR (vpravo/dolů)',
        'Mirror vertical' => '180° (dolů/vlevo)',
        'Rotate 180' => '180° (dolů/vpravo)',
        'Rotate 270 CW' => '90° po směru HR (vlevo/dolů)',
        'Rotate 90 CW' => '90° ptoti směru HR (vpravo/nahoru)',
      },
    },
   'PEFVersion' => 'PEF verze',
   'Padding' => 'Náhradní znaky',
   'PageName' => 'Jméno stránky',
   'PageNumber' => 'Číslo stránky',
   'PhotoEffectsType' => {
      PrintConv => {
        'None' => 'Žádná',
      },
    },
   'PhotometricInterpretation' => {
      Description => 'Pixelové schéma',
      PrintConv => {
        'BlackIsZero' => 'Černá je nula',
        'Color Filter Array' => 'CFA (Color Filter Matrix)',
        'Pixar LogL' => 'CIE Log2(L) (Log luminance)',
        'Pixar LogLuv' => 'CIE Log2(L)(u\',v\') (Log luminance and chrominance)',
        'RGB Palette' => 'Barevné schema',
        'Transparency Mask' => 'Průhlednost',
        'WhiteIsZero' => 'Bílá je nula',
      },
    },
   'PhotoshopAnnotations' => 'Poznámky Photoshop',
   'PictureFinish' => {
      PrintConv => {
        'Portrait' => 'Portrét',
      },
    },
   'PictureMode' => {
      PrintConv => {
        'Aperture-priority AE' => 'Priorita clony',
        'Landscape' => 'Krajina',
        'Macro' => 'Makro',
        'Manual' => 'Manuální',
        'Portrait' => 'Portrét',
        'Shutter speed priority AE' => 'Priorita času',
      },
    },
   'PictureMode2' => {
      PrintConv => {
        'Aperture Priority' => 'Priorita clony',
        'Manual' => 'Manuální',
        'Shutter Speed Priority' => 'Priorita času',
      },
    },
   'PictureStyle' => {
      PrintConv => {
        'Landscape' => 'Krajina',
        'None' => 'Žádná',
        'Portrait' => 'Portrét',
      },
    },
   'PixelUnits' => {
      PrintConv => {
        'Unknown' => 'Neznámý',
      },
    },
   'PlanarConfiguration' => {
      Description => 'Uspořádání obrazových dat',
      PrintConv => {
        'Chunky' => 'Chunky Format (prokládaný)',
        'Planar' => 'Planární (dvojrozměrný)',
      },
    },
   'Predictor' => {
      Description => 'Predikce',
      PrintConv => {
        'Horizontal differencing' => 'Horizontální diferenciace',
        'None' => 'Bez predikce',
      },
    },
   'PreviewColorSpace' => {
      PrintConv => {
        'Unknown' => 'Neznámý',
      },
    },
   'PreviewImage' => 'Náhled',
   'PreviewQuality' => {
      PrintConv => {
        'Normal' => 'Normální',
      },
    },
   'PrimaryChromaticities' => 'Chromatičnost primárních barev',
   'ProgramLine' => {
      PrintConv => {
        'Normal' => 'Normální',
      },
    },
   'ProgramMode' => {
      PrintConv => {
        'None' => 'Žádná',
        'Portrait' => 'Portrét',
      },
    },
   'Province-State' => 'Stát/provincie',
   'Quality' => {
      PrintConv => {
        'Low' => 'Méně',
        'Normal' => 'Normální',
      },
    },
   'QualityMode' => {
      PrintConv => {
        'Normal' => 'Normální',
      },
    },
   'RAFVersion' => 'RAF verze',
   'Rating' => 'Hodnocení',
   'RatingPercent' => 'Hodnocení v procentech',
   'RawJpgQuality' => {
      PrintConv => {
        'Normal' => 'Normální',
      },
    },
   'RecordMode' => {
      Description => 'Režim záznamu',
      PrintConv => {
        'Aperture Priority' => 'Priorita clony',
        'Manual' => 'Manuální',
        'Shutter Priority' => 'Priorita času',
      },
    },
   'RecordingMode' => {
      PrintConv => {
        'Landscape' => 'Krajina',
        'Manual' => 'Manuální',
        'Portrait' => 'Portrét',
      },
    },
   'ReferenceBlackWhite' => 'Černý a bílý referenční bod',
   'RelatedImageFileFormat' => 'Obrazový formát',
   'RelatedImageHeight' => 'Výška obrázku',
   'RelatedImageWidth' => 'Šířka obrázku',
   'RelatedSoundFile' => 'Audio soubor',
   'ResampleParamsQuality' => {
      PrintConv => {
        'Low' => 'Méně',
      },
    },
   'ResolutionUnit' => {
      Description => 'Jednotka X a Y rozlišení',
      PrintConv => {
        'None' => 'Žádná',
        'cm' => 'Pixely/cm',
        'inches' => 'Palce',
      },
    },
   'RetouchHistory' => {
      PrintConv => {
        'None' => 'Žádná',
      },
    },
   'Rotation' => {
      PrintConv => {
        'Horizontal' => '0° (nahoru/vlevo)',
        'Horizontal (normal)' => '0° (nahoru/vlevo)',
        'Rotate 180' => '180° (dolů/vpravo)',
        'Rotate 270 CW' => '90° po směru HR (vlevo/dolů)',
        'Rotate 90 CW' => '90° ptoti směru HR (vpravo/nahoru)',
        'Rotated 180' => '180° (dolů/vpravo)',
        'Rotated 270 CW' => '90° po směru HR (vlevo/dolů)',
        'Rotated 90 CW' => '90° ptoti směru HR (vpravo/nahoru)',
      },
    },
   'RowsPerStrip' => 'Počet řádek v části',
   'SPIFFVersion' => 'SPIFF verze',
   'SVGVersion' => 'SVG verze',
   'SampleFormat' => {
      Description => 'Formát vzorku',
      PrintConv => {
        'Complex int' => 'Komplexní číslo',
        'Float' => 'Desetinná čárka',
        'Signed' => 'Záporné číslo',
        'Undefined' => 'Nedefinované',
        'Unsigned' => 'Kladné číslo',
      },
    },
   'SamplesPerPixel' => 'Počet složek',
   'Saturation' => {
      Description => 'Saturace',
      PrintConv => {
        'High' => 'Vysoká',
        'Low' => 'Nízká',
        'Normal' => 'Normální',
      },
    },
   'SceneCaptureType' => {
      Description => 'Typ scény',
      PrintConv => {
        'Landscape' => 'Krajina',
        'Night' => 'Noční foto',
        'Portrait' => 'Portrét',
      },
    },
   'SceneMode' => {
      PrintConv => {
        'Aperture Priority' => 'Priorita clony',
        'Landscape' => 'Krajina',
        'Macro' => 'Makro',
        'Manual' => 'Manuální',
        'Normal' => 'Normální',
        'Portrait' => 'Portrét',
        'Shutter Priority' => 'Priorita času',
        'Spot' => 'Středový bod',
        'Sunset' => 'Západ',
      },
    },
   'SceneModeUsed' => {
      PrintConv => {
        'Aperture Priority' => 'Priorita clony',
        'Landscape' => 'Krajina',
        'Macro' => 'Makro',
        'Manual' => 'Manuální',
        'Portrait' => 'Portrét',
        'Shutter Priority' => 'Priorita času',
      },
    },
   'SceneSelect' => {
      PrintConv => {
        'Night' => 'Noční foto',
      },
    },
   'SceneType' => {
      Description => 'Typ scény',
      PrintConv => {
        'Directly photographed' => 'Přímo pořízený snímek',
      },
    },
   'SecurityClassification' => {
      Description => 'Bezpečnostní klasifikace',
      PrintConv => {
        'Confidential' => 'Důvěrný',
        'Restricted' => 'Vyhrazený',
        'Secret' => 'Tajný',
        'Top Secret' => 'Velmi tajný',
        'Unclassified' => 'Neurčeno',
      },
    },
   'SelfTimerMode' => 'Samospoušť',
   'SensingMethod' => {
      Description => 'Metoda měření',
      PrintConv => {
        'Color sequential area' => 'Barevný sekvenční plošný sensor',
        'Color sequential linear' => 'Barevný sekvenčné-lineární senzor',
        'Monochrome area' => 'Monochromatický senzor',
        'Monochrome linear' => 'Monochromatický lineární senzor',
        'Not defined' => 'Nedefinovaný',
        'One-chip color area' => 'Jednočipový barevný senzor',
        'Three-chip color area' => 'Tříčipový barevný senzor',
        'Trilinear' => 'Třílineární senzor',
        'Two-chip color area' => 'Dvoučipový barevný senzor',
      },
    },
   'SequentialShot' => {
      PrintConv => {
        'None' => 'Žádná',
      },
    },
   'SerialNumber' => 'ID fotoaparátu',
   'SetButtonCrossKeysFunc' => {
      PrintConv => {
        'Normal' => 'Normální',
      },
    },
   'ShadingCompensation' => 'Kompenzace stínování',
   'Sharpness' => {
      Description => 'Doostření',
      PrintConv => {
        'Hard' => 'Silné',
        'Normal' => 'Normální',
        'Soft' => 'Lehké',
      },
    },
   'SharpnessFrequency' => {
      PrintConv => {
        'Low' => 'Méně',
      },
    },
   'ShootingMode' => {
      Description => 'Režim focení',
      PrintConv => {
        'Aperture Priority' => 'Priorita clony',
        'Macro' => 'Makro',
        'Manual' => 'Manuální',
        'Normal' => 'Normální',
        'Portrait' => 'Portrét',
        'Shutter Priority' => 'Priorita času',
        'Spot' => 'Středový bod',
      },
    },
   'ShutterMode' => {
      PrintConv => {
        'Aperture Priority' => 'Priorita clony',
      },
    },
   'ShutterSpeed' => 'Expoziční čas',
   'ShutterSpeedValue' => 'Čas závěrky',
   'SlowShutter' => {
      PrintConv => {
        'None' => 'Žádná',
      },
    },
   'Software' => 'Použitý software',
   'Source' => 'Zdroj',
   'SpatialFrequencyResponse' => 'Spatial frequency response',
   'SpecialEffectsOpticalFilter' => {
      PrintConv => {
        'None' => 'Žádná',
      },
    },
   'SpectralSensitivity' => 'Spektrální citlivost',
   'State' => 'Stát',
   'StripByteCounts' => 'Bytů na komprimovanou část',
   'StripOffsets' => 'Pozice obrazových dat',
   'SubSecCreateDate' => 'Datum a čas generování digitálních dat',
   'SubSecDateTimeOriginal' => 'Datum a čas vzniku originálních dat',
   'SubSecModifyDate' => 'Datum a čas změny souboru',
   'SubSecTime' => 'DateTime 1/100 sekundy',
   'SubSecTimeDigitized' => 'DateTimeDigitized 1/100 sekund',
   'SubSecTimeOriginal' => 'DateTimeOriginal 1/100 sekund',
   'SubfileType' => 'Nový typ podsekce',
   'Subject' => 'Popis',
   'SubjectArea' => 'Pozice hlavního objektu',
   'SubjectDistance' => 'Vzdálenost objektu',
   'SubjectDistanceRange' => {
      Description => 'Rozsah vzdálenosti objektu',
      PrintConv => {
        'Close' => 'Blízký',
        'Distant' => 'Vzdálený',
        'Macro' => 'Makro',
        'Unknown' => 'Neznámý',
      },
    },
   'SubjectLocation' => 'Pozice hlavního objektu',
   'SubjectProgram' => {
      PrintConv => {
        'None' => 'Žádná',
        'Portrait' => 'Portrét',
      },
    },
   'Subsystem' => {
      PrintConv => {
        'Unknown' => 'Neznámý',
      },
    },
   'SupplementalCategories' => 'Doplňkové kategorie',
   'T4Options' => 'Plné bity',
   'T6Options' => 'Volby T6',
   'TargetPrinter' => 'Cílová tiskárna',
   'Teleconverter' => {
      PrintConv => {
        'None' => 'Žádná',
      },
    },
   'Thresholding' => 'Práh',
   'ThumbnailImage' => 'Náhled',
   'ThumbnailImageSize' => 'Velkost náhledu',
   'TileByteCounts' => 'Počet bytů prvku',
   'TileLength' => 'Délka prvku',
   'TileOffsets' => 'Offset prvku',
   'TileWidth' => 'Šířka prvku',
   'TimeScaleParamsQuality' => {
      PrintConv => {
        'Low' => 'Méně',
      },
    },
   'Title' => 'Název',
   'ToneCurve' => {
      PrintConv => {
        'Manual' => 'Manuální',
      },
    },
   'ToningEffect' => {
      PrintConv => {
        'None' => 'Žádná',
      },
    },
   'ToningEffectMonochrome' => {
      PrintConv => {
        'None' => 'Žádná',
      },
    },
   'TransferFunction' => 'Transfer funkce',
   'Transformation' => {
      PrintConv => {
        'Horizontal (normal)' => '0° (nahoru/vlevo)',
        'Mirror horizontal' => '0° (nahoru/vpravo)',
        'Mirror horizontal and rotate 270 CW' => '90° po směru HR (vlevo/nahoru)',
        'Mirror horizontal and rotate 90 CW' => '90° ptoti směru HR (vpravo/dolů)',
        'Mirror vertical' => '180° (dolů/vlevo)',
        'Rotate 180' => '180° (dolů/vpravo)',
        'Rotate 270 CW' => '90° po směru HR (vlevo/dolů)',
        'Rotate 90 CW' => '90° ptoti směru HR (vpravo/nahoru)',
      },
    },
   'TransmissionReference' => 'Reference přenosu',
   'Trapped' => {
      PrintConv => {
        'Unknown' => 'Neznámý',
      },
    },
   'Urgency' => 'Naléhavost',
   'UserComment' => 'Komentář',
   'UserDef1PictureStyle' => {
      PrintConv => {
        'Landscape' => 'Krajina',
        'Portrait' => 'Portrét',
      },
    },
   'UserDef2PictureStyle' => {
      PrintConv => {
        'Landscape' => 'Krajina',
        'Portrait' => 'Portrét',
      },
    },
   'UserDef3PictureStyle' => {
      PrintConv => {
        'Landscape' => 'Krajina',
        'Portrait' => 'Portrét',
      },
    },
   'VRDVersion' => 'VRD verze',
   'Version' => 'Verze',
   'VignetteControl' => {
      PrintConv => {
        'Normal' => 'Normální',
      },
    },
   'WBAdjLighting' => {
      PrintConv => {
        'Daylight (direct sunlight)' => 'Denní světlo (0)',
        'Daylight (shade)' => 'Denní světlo (1)',
        'Daylight (cloudy)' => 'Denní světlo (2)',
        'Flash' => 'Blesk',
        'None' => 'Žádná',
      },
    },
   'WhiteBalance' => {
      Description => 'Vyvážení bílé',
      PrintConv => {
        'Auto' => 'Automatické vyvážení bílé',
        'Black & White' => 'Černobílé foto',
        'Cloudy' => 'Zataženo',
        'Cool White Fluorescent' => 'Chladná bílá fluorescentní',
        'Custom 1' => 'VLASTNÍ1',
        'Custom 2' => 'VLASTNÍ2',
        'Custom 3' => 'VLASTNÍ3',
        'Custom 4' => 'VLASTNÍ4',
        'Day White Fluorescent' => 'Denní zářivka',
        'Daylight' => 'Denní světlo',
        'Daylight Fluorescent' => 'Denní světlo',
        'Flash' => 'Blesk',
        'Fluorescent' => 'Žárovka',
        'Manual' => 'Manuální',
        'Shade' => 'Stíny',
        'Tungsten' => 'Zářivka',
        'Unknown' => 'Neznámý',
        'White Fluorescent' => 'Bílá zářivka',
      },
    },
   'WhiteBalanceAdj' => {
      PrintConv => {
        'Cloudy' => 'Zataženo',
        'Daylight' => 'Denní světlo',
        'Flash' => 'Blesk',
        'Fluorescent' => 'Žárovka',
        'Shade' => 'Stíny',
        'Tungsten' => 'Zářivka',
      },
    },
   'WhiteBalanceMode' => {
      PrintConv => {
        'Unknown' => 'Neznámý',
      },
    },
   'WhiteBalanceSet' => {
      PrintConv => {
        'Cloudy' => 'Zataženo',
        'Daylight' => 'Denní světlo',
        'Daylight Fluorescent' => 'Denní světlo',
        'Flash' => 'Blesk',
        'Manual' => 'Manuální',
        'Shade' => 'Stíny',
        'Tungsten' => 'Zářivka',
        'White Fluorescent' => 'Bílá zářivka',
      },
    },
   'WhitePoint' => 'Chromatičnost bílého bodu',
   'Writer-Editor' => 'Autor popisku',
   'XMP' => 'XMP metadata',
   'XPAuthor' => 'Autor',
   'XPComment' => 'Komentář',
   'XPKeywords' => 'Klíčová slova',
   'XPSubject' => 'Popis',
   'XPTitle' => 'Název',
   'XPosition' => 'X-pozice',
   'XResolution' => 'Rozlišení obrázku na šířku',
   'YCbCrCoefficients' => 'Koeficienty transformační YCbCr matrice',
   'YCbCrPositioning' => {
      Description => 'Y a C pozice',
      PrintConv => {
        'Centered' => 'Centrované',
        'Co-sited' => 'Po stranách',
      },
    },
   'YCbCrSubSampling' => 'Vzorkovací poměr Y k C',
   'YPosition' => 'Y-pozice',
   'YResolution' => 'Rozlišení obrázku na výšku',
);

1;  # end


__END__

=head1 NAME

Image::ExifTool::Lang::cs.pm - ExifTool Czech language translations

=head1 DESCRIPTION

This file is used by Image::ExifTool to generate localized tag descriptions
and values.

=head1 AUTHOR

Copyright 2003-2025, Phil Harvey (philharvey66 at gmail.com)

This library is free software; you can redistribute it and/or modify it
under the same terms as Perl itself.

=head1 ACKNOWLEDGEMENTS

Thanks to Jens Duttke and Petr MichE<aacute>lek for providing this
translation.

=head1 SEE ALSO

L<Image::ExifTool(3pm)|Image::ExifTool>,
L<Image::ExifTool::TagInfoXML(3pm)|Image::ExifTool::TagInfoXML>

=cut
