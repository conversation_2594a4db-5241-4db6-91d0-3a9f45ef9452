#------------------------------------------------------------------------------
# File:         fr.pm
#
# Description:  ExifTool French language translations
#
# Notes:        This file generated automatically by Image::ExifTool::TagInfoXML
#------------------------------------------------------------------------------

package Image::ExifTool::Lang::fr;

use strict;
use vars qw($VERSION);

$VERSION = '1.37';

%Image::ExifTool::Lang::fr::Translate = (
   'AEAperture' => 'Ouverture AE',
   'AEApertureSteps' => 'Pas d\'ouverture de l\\’exposition automatique',
   'AEBAutoCancel' => {
      Description => 'Annulation bracketing auto',
      PrintConv => {
        'Off' => 'Arrêt',
        'On' => 'Marche',
      },
    },
   'AEBBracketValue' => 'Valeur du braquet AEB',
   'AEBSequence' => {
      Description => 'Séquence de bracketing AEB',
      PrintConv => {
        '+,0,-' => '+0',
        '-,0,+' => '-0',
      },
    },
   'AEBSequenceAutoCancel' => {
      Description => 'Séquence auto AEB/annuler',
      PrintConv => {
        '-,0,+/Disabled' => '-,0,+/Désactivé',
        '-,0,+/Enabled' => '-,0,+/Activé',
        '0,-,+/Disabled' => '0,-,+/Désactivé',
        '0,-,+/Enabled' => '0,-,+/Activé',
      },
    },
   'AEBShotCount' => 'Nombre de vues bracketées',
   'AEBXv' => 'Compensation d\'expo. auto en bracketing',
   'AECSnapshotDigitalGain' => 'Gain numérique de l\'instantané AEC',
   'AEExposureTime' => 'Temps d\'exposition AE',
   'AEExtra' => 'Suppléments AE',
   'AEInfo' => 'Info sur l\'exposition auto',
   'AELock' => {
      Description => 'Verrouillage AE',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'AELockMeterModeAfterFocus' => {
      PrintConv => {
        'Center-weighted' => 'Pondération centrale',
      },
    },
   'AEMaxAperture' => 'Ouverture maxi AE',
   'AEMaxAperture2' => 'Ouverture maxi AE (2)',
   'AEMeteringMode' => {
      Description => 'Mode de mesure AE',
      PrintConv => {
        'Center-weighted average' => 'Moyenne pondérée centrale',
        'Multi-segment' => 'Multizone',
      },
    },
   'AEMeteringSegments' => 'Segments de mesure AE',
   'AEMinAperture' => 'Ouverture mini AE',
   'AEMinExposureTime' => 'Temps d\'exposition mini AE',
   'AEProgramMode' => {
      Description => 'Mode programme AE',
      PrintConv => {
        'Av, B or X' => 'Av, B ou X',
        'Candlelight' => 'Bougie',
        'DOF Program' => 'Programme PdC',
        'DOF Program (P-Shift)' => 'Programme PdC (décalage P)',
        'Hi-speed Program' => 'Programme grande vitesse',
        'Hi-speed Program (P-Shift)' => 'Programme grande vitesse (décalage P)',
        'Kids' => 'Enfants',
        'Landscape' => 'Paysage',
        'M, P or TAv' => 'M, P ou TAv',
        'MTF Program' => 'Programme FTM',
        'MTF Program (P-Shift)' => 'Programme FTM (décalage P)',
        'Museum' => 'Musée',
        'Night Scene' => 'Nocturne',
        'Night Scene Portrait' => 'Portrait nocturne',
        'No Flash' => 'Sans flash',
        'Pet' => 'Animaux de compagnie',
        'Sunset' => 'Coucher de soleil',
        'Surf & Snow' => 'Surf et neige',
        'Sv or Green Mode' => 'Sv ou mode vert',
        'Text' => 'Texte',
      },
    },
   'AESetting' => {
      Description => 'Réglage d\'exposition automatique',
      PrintConv => {
        'AE Lock' => 'Verrouillage de l\'exposition automatique',
        'AE Lock + Exposure Comp.' => 'Verrouillage de l\'exposition automatique+Compensations d\'exposition',
        'Exposure Compensation' => 'Compensations d\'exposition',
        'No AE' => 'Pas d\'exposition automatique',
        'Normal AE' => 'Exposition automatique normale',
      },
    },
   'AEXv' => 'Compensation d\'exposition auto',
   'AE_ISO' => 'Sensibilité ISO AE',
   'AFAdjustment' => 'Ajustement AF',
   'AFAperture' => 'Ouverture de l\'autofocus',
   'AFArea' => 'Zone d\'autofocus',
   'AFAreaHeight' => 'Hauteur de la zone de l\'autofocus',
   'AFAreaHeights' => 'Hauteurs des zones de l\'autofocus',
   'AFAreaIllumination' => {
      Description => 'Illumination de la zone d\'autofocus',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'AFAreaMode' => {
      Description => 'Mode de la zone d\'autofocus',
      PrintConv => {
        '1-area' => 'Mise au point 1 zone',
        '1-area (high speed)' => 'Mise au point 1 zone (haute vitesse)',
        '225-area' => 'Zone 225',
        '23-area' => 'Zone 23',
        '3-area (center)?' => 'Mise au point 3 zones (au centre) ?',
        '3-area (high speed)' => 'Mise au point 3 zones (haute vitesse)',
        '3-area (left)?' => 'Mise au point 3 zones (à gauche) ?',
        '3-area (right)?' => 'Mise au point 3 zones (à droite) ?',
        '3D-tracking' => 'Suivi en 3D',
        '49-area' => 'Zone 49',
        '5-area' => 'Mise au point 5 zones',
        '9-area' => 'Mise au point 9 zones',
        'AF Point Expansion (4 point)' => 'Extension du point AF (4 points)',
        'AF Point Expansion (8 point)' => 'Extension du point AF (8 points)',
        'AF Point Expansion (surround)' => 'Extension du point AF (surround)',
        'Animal Eye Tracking' => 'Suivi de l\'œil des animaux',
        'Auto (Animals)' => 'Auto (Animaux)',
        'Auto (People)' => 'Auto (Personnes)',
        'Auto-area' => 'Zone auto',
        'Center' => 'Centré',
        'Contrast-detect' => 'Détection de contraste',
        'Contrast-detect (face priority)' => 'Détection de contraste (priorité au visage)',
        'Contrast-detect (normal area)' => 'Détection de contraste (zone normale)',
        'Contrast-detect (subject tracking)' => 'Détection de contraste (suivi du sujet)',
        'Contrast-detect (wide area)' => 'Détection de contraste (large zone)',
        'Default' => 'Par défaut',
        'Dynamic' => 'Dynamique',
        'Dynamic Area' => 'Zone dynamique',
        'Dynamic Area (21 points)' => 'Zone dynamique (21 points)',
        'Dynamic Area (25 points)' => 'Zone dynamique (25 points)',
        'Dynamic Area (3D-tracking)' => 'Zone dynamique (suivi 3D)',
        'Dynamic Area (49 points)' => 'Zone dynamique (49 points)',
        'Dynamic Area (51 points)' => 'Zone dynamique (51 points)',
        'Dynamic Area (51 points, 3D-tracking)' => 'Zone dynamique (51 points, suivi 3D)',
        'Dynamic Area (72 points)' => 'Zone dynamique (72 points)',
        'Dynamic Area (9 points)' => 'Zone dynamique (9 points)',
        'Dynamic Area (L)' => 'Zone dynamique (L)',
        'Dynamic Area (M)' => 'Zone dynamique (M)',
        'Dynamic Area (S)' => 'Zone dynamique (S)',
        'Dynamic Area (closest subject)' => 'Zone dynamique (sujet le plus proche)',
        'Dynamic Area (wide)' => 'Zone dynamique (large)',
        'Dynamic Area (wide, 3D-tracking)' => 'Zone dynamique (large, suivi 3D)',
        'Dynamic-area' => 'Zone dynamique',
        'Expanded Flexible Spot' => 'Spot flexible étendu',
        'Face + Tracking' => 'Visage + suivi',
        'Face Detect' => 'Détection de visage',
        'Face Detect (animal detect off)' => 'Détection de visage (détection d\'animaux désactivée)',
        'Face Detect (animal detect on)' => 'Détection de visage (détection d\'animaux activée)',
        'Face Detect AF' => 'Détection de visage',
        'Face Priority (41 points)' => 'Priorité au visage (41 points)',
        'Face Tracking' => 'Suivi du visage',
        'Face-priority AF' => 'Priorité aux visages AF',
        'Flexible Spot' => 'Spot flexible',
        'Flexizone Multi (49 point)' => 'Flexizone Multi (49 points)',
        'Flexizone Multi (9 point)' => 'Flexizone Multi (9 points)',
        'Flexizone Single' => 'Flexizone unique)',
        'Group Area' => 'Zone du groupe',
        'Group Area (HL)' => 'Zone du groupe (HL)',
        'Group Area (VL)' => 'Zone du groupe (HL)',
        'Group Dynamic' => 'Dynamique de groupe',
        'Large Zone AF' => 'Grande zone AF',
        'Local' => 'Locale',
        'Manual' => 'Manuelle',
        'Normal-area AF' => 'Zone normale AF',
        'Normal?' => 'Normale ?',
        'Off (Manual Focus)' => 'Désactivée (mise au point manuelle)',
        'Pinpoint' => 'Point de mire',
        'Pinpoint focus' => 'Point de mire de mise au point',
        'Selective (for Miniature effect)' => 'Sélective',
        'Single' => 'Simple',
        'Single Area' => 'Zone unique',
        'Single Area (wide)' => 'Zone unique (large)',
        'Single Point' => 'Point unique',
        'Single-point' => 'Point unique',
        'Single-point AF' => 'Mise au point automatique à point unique',
        'Spot Focusing' => 'Mise au point Spot',
        'Spot Mode Off' => 'Mode Spot désactivé',
        'Spot Mode On' => 'Mode Spot enclenché',
        'Subject Tracking (41 points)' => 'Suivi du sujet (41 points)',
        'Subject-tracking AF' => 'Suivi du sujet AF',
        'Touch' => 'Tactile',
        'Tracking' => 'Suivi',
        'Wide' => 'Large',
        'Wide (C1)' => 'Large (C1)',
        'Wide (C2)' => 'Large (C2)',
        'Wide (L)' => 'Large (L)',
        'Wide (L-animals)' => 'Large (L-animaux)',
        'Wide (L-people)' => 'Large (L-personnes)',
        'Wide (S)' => 'Large (S)',
        'Wide-area AF' => 'Zone large AF',
        'Wide/Tracking' => 'Large/Suivi',
        'n/a' => 'Non applicable',
      },
    },
   'AFAreaModeSetting' => {
      Description => 'Réglage du mode de la zone d\'autofocus',
      PrintConv => {
        '3D-tracking (11 points)' => 'Suivi 3D (11 points)',
        'Auto-area' => 'Zone automatique',
        'Center' => 'Centre',
        'Center (LA-EA4)' => 'Centre (LA-EA4)',
        'Closest Subject' => 'Sujet le plus proche',
        'Dynamic Area' => 'Zone dynamique',
        'Expanded Flexible Spot' => 'Spot flexible étendu',
        'Flexible Spot' => 'Spot flexible',
        'Flexible Spot (LA-EA4)' => 'Spot flexible (LA-EA4)',
        'Single Area' => 'Zone unique',
        'Wide' => 'Large',
      },
    },
   'AFAreaPointSize' => {
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'AFAreaWidth' => 'Largeur de la zone de l\'autofocus',
   'AFAreaWidths' => 'Largeurs des zones de l\'autofocus',
   'AFAreaXPosition' => 'Position horizontale Zone de l\'autofocus',
   'AFAreaXPosition1' => 'Position horizontale 1 Zone de l\'autofocus',
   'AFAreaXPositions' => 'Positions horizontales Zone de l\'autofocus',
   'AFAreaYPosition' => 'Position verticale Zone de l\'autofocus',
   'AFAreaYPosition1' => 'Position verticale 1 Zone de l\'autofocus',
   'AFAreaYPositions' => 'Positions verticales Zone de l\'autofocus',
   'AFAreaZoneSize' => {
      Description => 'Taille de la zone de l\'autofocus',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'AFAreas' => 'Zones d\'autofocus',
   'AFAssist' => {
      Description => 'Faisceau d\'assistance de l\'autofocus',
      PrintConv => {
        'Does not emit/Fires' => 'N\'émet pas/Se déclenche',
        'Emits/Does not fire' => 'Émet/Ne se déclenche pas',
        'Emits/Fires' => 'émet/Se déclenche',
        'Off' => 'Désactivé',
        'On' => 'Activé',
        'Only ext. flash emits/Fires' => 'Seul le flash ext. émet/se déclenche',
      },
    },
   'AFAssistBeam' => {
      Description => 'Faisceau d\'assistance de l\'autofocus',
      PrintConv => {
        'Does not emit' => 'Désactivé',
        'Emits' => 'Activé',
        'Only ext. flash emits' => 'Uniquement par flash ext.',
      },
    },
   'AFAssistLamp' => {
      Description => 'Lampe d\'assistance de l\'autofocus',
      PrintConv => {
        'Disabled and Not Required' => 'Désactivée et non requise',
        'Disabled but Required' => 'Désactivée mais requise',
        'Enabled but Not Used' => 'Activée mais non utilisée',
        'Fired' => 'Déclenchée',
      },
    },
   'AFButtonPressed' => {
      Description => 'Bouton Auto Focus pressé',
      PrintConv => {
        'No' => 'Non',
        'Yes' => 'Oui',
      },
    },
   'AFDefocus' => 'Défocalisation AF',
   'AFDuringLiveView' => {
      Description => 'AF pendant la visée directe',
      PrintConv => {
        'Disable' => 'Désactivé',
        'Enable' => 'Activé',
        'Live mode' => 'Mode visée directe',
        'Quick mode' => 'Mode rapide',
      },
    },
   'AFIlluminator' => {
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'AFImageHeight' => 'Hauteur de l\'image d\'Autofocus',
   'AFImageWidth' => 'Largeur de l\'image d\'Autofocus',
   'AFInfo' => 'Info autofocus',
   'AFInfo2' => 'Infos AF',
   'AFInfo2Version' => 'Version des infos de l\'Autofocus',
   'AFIntegrationTime' => 'Temps d\'intégration de l\'Autofocus',
   'AFMicroadjustment' => {
      Description => 'Micro-ajustement de l\'AF',
      PrintConv => {
        'Adjust all by same amount' => 'Ajuster idem tous obj',
        'Adjust by lens' => 'Ajuster par objectif',
        'Disable' => 'Désactivé',
      },
    },
   'AFMode' => 'Mode de l\'Autofocus',
   'AFOnAELockButtonSwitch' => {
      Description => 'Permutation touche AF/Mémo',
      PrintConv => {
        'Disable' => 'Désactivé',
        'Enable' => 'Activé',
      },
    },
   'AFOnButton' => {
      PrintConv => {
        'AE Lock (hold)' => 'Verrouillage de l\'exposition automatique (maintien)',
        'AWB Lock (hold)' => 'Verrouillage de la balance des blancs automatique (maintien)',
        'HDR Overlay' => 'Recouvrement HDR',
      },
    },
   'AFPoint' => {
      Description => 'Point de l\'Autofocus',
      PrintConv => {
        '(none)' => '(aucun)',
        'All Target' => 'Toutes les cibles',
        'Auto AF point selection' => 'Sélection automatique du point d\'autofocus',
        'Bottom' => 'En bas',
        'Bottom (horizontal)' => 'En bas (horizontal)',
        'Bottom (vertical)' => 'En bas (vertical)',
        'Bottom Assist-left' => 'En bas aide à gauche',
        'Bottom Assist-right' => 'En bas aide à droite',
        'Bottom Center' => 'En bas centré',
        'Bottom Left' => 'En bas à gauche',
        'Bottom Right' => 'En bas à droite',
        'Bottom-center (horizontal)' => 'En bas centré (horizontal)',
        'Bottom-center (vertical)' => 'En bas centré (vertical)',
        'Bottom-left' => 'En bas à gauche',
        'Bottom-left (horizontal)' => 'En bas à gauche (horizontal)',
        'Bottom-left (vertical)' => 'En bas à gauche (vertical)',
        'Bottom-right' => 'En bas à droite',
        'Bottom-right (horizontal)' => 'En bas à droite (horizontal)',
        'Bottom-right (vertical)' => 'En bas à droite (vertical)',
        'Center' => 'Au Centre',
        'Center (10)' => 'Au Centre (10)',
        'Center (11)' => 'Au Centre (11)',
        'Center (12)' => 'Au Centre (12)',
        'Center (14)' => 'Au Centre (14)',
        'Center (7)' => 'Au Centre (7)',
        'Center (9)' => 'Au Centre (9)',
        'Center (horizontal)' => 'Au Centre (horizontal)',
        'Center (vertical)' => 'Au Centre (vertical)',
        'Center F2.8' => 'Au Centre F2.8',
        'Center Left' => 'Au Centre à gauche',
        'Center Right' => 'Au Centre à droite',
        'Center Vertical' => 'Au Centre vertical',
        'Dynamic Single Target' => 'Cible unique dynamique',
        'E6 Center' => 'E6 Centré',
        'E6 Center F2.8' => 'E6 Centré F2.8',
        'E6 Center Vertical' => 'E6 Centré vertical',
        'Face Detect' => 'Détection de visage',
        'Far Left' => 'À l\'extrême-gauche',
        'Far Left (horizontal)' => 'À l\'extrême gauche (horizontal)',
        'Far Left (vertical)' => 'À l\'extrême gauche (vertical)',
        'Far Left/Right of Center' => 'À l\'extrême gauche/droite du centre',
        'Far Left/Right of Center/Bottom' => 'À l\'extrême gauche/droite du centre/en bas',
        'Far Right' => 'À l\'extrême-droit',
        'Far Right (horizontal)' => 'À l\'extrême droite (horizontal)',
        'Far Right (vertical)' => 'À l\'extrême droite (vertical)',
        'Left' => 'À gauche',
        'Left (horizontal)' => 'À gauche (horizontal)',
        'Left (or n/a)' => 'À gauche (ou non applicable)',
        'Left (vertical)' => 'À gauche (vertical)',
        'Lower Far Left' => 'En bas à l\'extrême gauche',
        'Lower Far Right' => 'En bas à l\'extrême droite',
        'Lower-left' => 'En bas à gauche',
        'Lower-left (horizontal)' => 'En bas à gauche (horizontal)',
        'Lower-left (vertical)' => 'En bas à gauche (vertical)',
        'Lower-middle' => 'En bas au centre',
        'Lower-right' => 'En bas à droite',
        'Lower-right (horizontal)' => 'En bas à droite (horizontal)',
        'Lower-right (vertical)' => 'En bas à droite (vertical)',
        'Manual AF point selection' => 'Sélection manuelle du point de mise au point',
        'Mid-left' => 'Au Milieu à gauche',
        'Mid-left (horizontal)' => 'Au Milieu (horizontal)',
        'Mid-left (vertical)' => 'Au Milieu (vertical)',
        'Mid-right' => 'Au milieu à droite',
        'Mid-right (horizontal)' => 'Au milieu à droite (horizontal)',
        'Mid-right (vertical)' => 'Au milieu à droite (vertical)',
        'Middle Horizontal' => 'Au milieu horizontal',
        'Near Left' => 'Près de la gauche',
        'Near Left/Right of Center' => 'Près de la gauche/droite du centre',
        'Near Right' => 'Près de la gauche',
        'Near Upper/Left' => 'Près du haut à gauche',
        'None' => 'Aucun',
        'None (MF)' => 'Aucun (MF)',
        'Right' => 'À droite',
        'Right (horizontal)' => 'À droite (horizontal)',
        'Right (vertical)' => 'À droite (vertical)',
        'Single Target' => 'Cible unique',
        'Top' => 'En haut',
        'Top (horizontal)' => 'En haut (horizontal)',
        'Top (vertical)' => 'En haut (vertical)',
        'Top Assist-left' => 'En haut aide à gauche',
        'Top Assist-right' => 'En haut aide à droite',
        'Top Center' => 'En haut au centre',
        'Top Left' => 'En haut à gauche',
        'Top Near-left' => 'En haut près de la gauche',
        'Top Near-right' => 'En haut près de la droite',
        'Top Right' => 'En haut à droite',
        'Top-center (horizontal)' => 'En haut au centre (horizontal)',
        'Top-center (vertical)' => 'En haut au centre (vertical)',
        'Top-left' => 'En haut à gauche',
        'Top-left (horizontal)' => 'En haut à gauche (horizontal)',
        'Top-left (vertical)' => 'En haut à gauche (vertical)',
        'Top-right' => 'En haut à droite',
        'Top-right (horizontal)' => 'En haut à droite (horizontal)',
        'Top-right (vertical)' => 'En haut à droite (vertical)',
        'Upper Far Left' => 'En haut à l\'extrême gauche',
        'Upper Far Right' => 'En haut à l\'extrême droite',
        'Upper Left' => 'En haut à gauche',
        'Upper Right' => 'En haut à droite',
        'Upper-left' => 'En haut à gauche',
        'Upper-left (horizontal)' => 'En haut à gauche (horizontal)',
        'Upper-left (vertical)' => 'En haut à gauche (vertical)',
        'Upper-middle' => 'En haut au centre',
        'Upper-right' => 'En haut à droite',
        'Upper-right (horizontal)' => 'En haut à droite (horizontal)',
        'Upper-right (vertical)' => 'En haut à droite (vertical)',
        'n/a' => 'Non applicable',
      },
    },
   'AFPointActivationArea' => {
      Description => 'Zone d\'activation des points de l\'autofocus',
      PrintConv => {
        'Automatic expanded (max. 13)' => 'Expansion auto (13 max.)',
        'Expanded (TTL. of 7 AF points)' => 'Expansion (TTL 7 collimat.)',
        'Single AF point' => 'Un seul collimateur AF',
      },
    },
   'AFPointAreaExpansion' => {
      Description => 'Extension de la zone d\'autofocus',
      PrintConv => {
        'Disable' => 'Désactivé',
        'Enable' => 'Activé',
        'Left/right AF points' => 'Activé (gauche/droite collimateurs autofocus d\'assistance)',
        'Surrounding AF points' => 'Activée (Collimateurs autofocus d\'assistance environnants)',
      },
    },
   'AFPointAutoSelection' => {
      Description => 'Sélection des collimateurs automatique',
      PrintConv => {
        'Control-direct:disable/Main:disable' => 'Contrôle rapide-Directe:désactivé/Principale:désactivé',
        'Control-direct:disable/Main:enable' => 'Contrôle rapide-Directe:désactivé/Principale:activé',
        'Control-direct:enable/Main:enable' => 'Contrôle rapide-Directe:activé/Principale:activé',
      },
    },
   'AFPointBrightness' => {
      Description => 'Intensité d\'illumination de l\'autofocus',
      PrintConv => {
        'Brighter' => 'Forte',
        'Normal' => 'Normale',
      },
    },
   'AFPointDisplayDuringFocus' => {
      Description => 'Affichage des points d\'autofocus pendant la mise au point',
      PrintConv => {
        'All (constant)' => 'Tous (constant)',
        'Disable display' => 'Désactiver l\'affichage',
        'Disabled' => 'Désactivé',
        'Off' => 'Désactivé',
        'On' => 'Activé',
        'On (when focus achieved)' => 'Activé (lorsque la mise au point est effectuée)',
        'Selected (constant)' => 'Sélectionné (constant)',
        'Selected (focused)' => 'Sélectionné (focalisé)',
        'Selected (pre-AF, focused)' => 'Sélectionné (pré-auto focus, focalisé)',
      },
    },
   'AFPointIllumination' => {
      Description => 'Illumination des points d\'autofocus',
      PrintConv => {
        'Brighter' => 'Plus brillant',
        'Off' => 'Désactivé',
        'On' => 'Activé',
        'On During Manual Focusing' => 'Activé pendant la mise au point manuelle',
        'On in Continuous Shooting Modes' => 'Activé en modes de prise de vue continue',
        'On in Continuous Shooting and Manual Focusing' => 'Activé en prise de vue continue et en mise au point manuelle',
        'On without dimming' => 'Activé sans atténuation',
      },
    },
   'AFPointMode' => {
      Description => 'Mode de mise au point automatique',
      PrintConv => {
        'Fixed Center' => 'Centre fixe',
        'Select' => 'Sélectionner',
      },
    },
   'AFPointPosition' => 'Position du point AF',
   'AFPointRegistration' => {
      Description => 'Enregistrement du point AF',
      PrintConv => {
        'Automatic' => 'Auto',
        'Bottom' => 'En bas',
        'Center' => 'Au centre',
        'Extreme Left' => 'À l\'extrême gauche',
        'Extreme Right' => 'À l\'extrême droite',
        'Left' => 'À gauche',
        'Right' => 'À droite',
        'Top' => 'En haut',
      },
    },
   'AFPointSelected' => {
      Description => 'Point AF sélectionné',
      PrintConv => {
        'Automatic Tracking AF' => 'AF en suivi auto',
        'Bottom' => 'Bas',
        'Center' => 'Centre',
        'Face Detect AF' => 'AF en reconnaissance de visage',
        'Fixed Center' => 'Fixe au centre',
        'Left' => 'Gauche',
        'Lower-left' => 'Bas gauche',
        'Lower-right' => 'Bas droit',
        'Mid-left' => 'Milieu gauche',
        'Mid-right' => 'Milieu droit',
        'Right' => 'Droit',
        'Top' => 'Haut',
        'Upper-left' => 'Haut gauche',
        'Upper-right' => 'Haut droite',
        'n/a' => 'Non applicable',
      },
    },
   'AFPointSelected2' => 'Point AF sélectionné 2',
   'AFPointSelection' => 'Méthode sélect. collimateurs AF',
   'AFPointSelectionMethod' => {
      Description => 'Méthode sélection collim. AF',
      PrintConv => {
        'Multi-controller direct' => 'Multicontrôleur direct',
        'Normal' => 'Normale',
        'Quick Control Dial direct' => 'Molette AR directe',
      },
    },
   'AFPointSpotMetering' => {
      Description => 'Nombre collimateurs/mesure spot',
      PrintConv => {
        '11/Active AF point' => '11/collimateur AF actif',
        '11/Center AF point' => '11/collimateur AF central',
        '45/Center AF point' => '45/collimateur AF central',
        '9/Active AF point' => '9/collimateur AF actif',
      },
    },
   'AFPointsInFocus' => {
      Description => 'Points d\'autofocus dans le focus',
      PrintConv => {
        '(none)' => '(aucun)',
        'All' => 'Tous',
        'All 11 Points' => 'Tous les 11 points',
        'Bottom' => 'En bas',
        'Bottom Near-left' => 'En bas près de la gauche',
        'Bottom Near-right' => 'En bas près de la droite',
        'Bottom, Center' => 'En Bas, centré',
        'Bottom-center' => 'En Bas, centré',
        'Bottom-left' => 'En bas à gauche',
        'Bottom-right' => 'En bas à droite',
        'Center' => 'Au centre',
        'Center (horizontal)' => 'Au centre (horizontal)',
        'Center (vertical)' => 'Au centre (vertical)',
        'Center+Right' => 'Au centre et à droite)',
        'Far Left' => 'À l\'extrême gauche',
        'Far Right' => 'À l\'extrême droite',
        'Fixed Center or Multiple' => 'Centre fixe ou multiple',
        'Left' => 'À gauche',
        'Left+Center' => 'À Gauche et au centre',
        'Left+Right' => 'À Gauche et à droite',
        'Lower Near-left' => 'En bas près de la gauche',
        'Lower Near-right' => 'En bas près de la droite',
        'Lower-left' => 'En bas à gauche',
        'Lower-left, Bottom' => 'En Bas à gauche et en bas',
        'Lower-left, Mid-left' => 'En Bas à gauche et au milieu à gauche',
        'Lower-middle' => 'En bas au milieu',
        'Lower-right' => 'En bas à droite',
        'Lower-right, Bottom' => 'En bas à droite et en bas',
        'Lower-right, Mid-right' => 'En bas à droite et au milieu à droite',
        'Mid-left' => 'Au milieu à gauche',
        'Mid-left, Center' => 'Au milieu et au centre',
        'Mid-right' => 'Au milieu à droite',
        'Mid-right, Center' => 'Au milieu à droite et au centre',
        'Near-left' => 'Près de la gauche',
        'Near-right' => 'Près de la droite',
        'None' => 'Aucun',
        'None (MF)' => 'Aucun (MF)',
        'Right' => 'À droite',
        'Top' => 'En haut',
        'Top Near-left' => 'En haut près de la gauche',
        'Top Near-right' => 'En haut près de la droite',
        'Top, Center' => 'En haut, au centre',
        'Top-center' => 'En haut, au centre',
        'Top-left' => 'En haut à gauche',
        'Top-right' => 'En haut à droite',
        'Upper Near-left' => 'En haut près de la gauche',
        'Upper Near-right' => 'En haut près de la doite',
        'Upper-left' => 'En haut à gauche',
        'Upper-left, Mid-left' => 'En haut à gauche, au milieu à gauche',
        'Upper-left, Top' => 'En haut à gauche, en haut',
        'Upper-middle' => 'En haut au milieu',
        'Upper-right' => 'En haut à droite',
        'Upper-right, Mid-right' => 'En haut à droite, au milieu à droit',
        'Upper-right, Top' => 'En haut à droite, en haut',
      },
    },
   'AFPointsInFocus1D' => 'Points d\'autofocus dans le focus 1D',
   'AFPointsInFocus5D' => {
      Description => 'Points d\'autofocus dans le focus 5D',
      PrintConv => {
        '(none)' => '(aucun)',
        'AI Servo1' => 'IA Servo 1',
        'AI Servo2' => 'IA Servo 2',
        'AI Servo3' => 'IA Servo 3',
        'AI Servo4' => 'IA Servo 4',
        'AI Servo5' => 'IA Servo 5',
        'AI Servo6' => 'IA Servo 6',
        'Bottom' => 'En bas',
        'Center' => 'Au centre',
        'Left' => 'À gauche',
        'Lower-left' => 'En bas à gauche',
        'Lower-right' => 'En bas à droite',
        'Right' => 'À droite',
        'Top' => 'En haut',
        'Upper-left' => 'En haut à gauche',
        'Upper-right' => 'En haut à droite',
      },
    },
   'AFPointsSelected' => {
      Description => 'Points d\'Autofocus sélectionnés',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'AFPointsUnknown1' => {
      PrintConv => {
        'All' => 'Tous',
        'Central 9 points' => '9 points centraux',
      },
    },
   'AFPointsUnknown2' => 'Points AF inconnus 2',
   'AFPointsUsed' => {
      Description => 'Points de mise au point automatique utilisés',
      PrintConv => {
        '(none)' => '(aucun)',
        'All 11 Points' => 'Tous les 11 points',
        'Bottom' => 'En bas',
        'Center' => 'Au centre',
        'Far Left' => 'À l\'extrême gauche',
        'Far Right' => 'À l\'extrême droite',
        'Left' => 'À gauche',
        'Lower Far Left' => 'À l\'extrême bas gauche',
        'Lower Far Right' => 'À l\'extrême bas droite',
        'Lower-left' => 'En bas à gauche',
        'Lower-middle' => 'Au milieu inférieur',
        'Lower-right' => 'En bas à droite',
        'Mid-left' => 'Au milieu gauche',
        'Mid-right' => 'Au milieu droit',
        'Near Left' => 'Près de la gauche',
        'Near Right' => 'Près de la droite',
        'Right' => 'À droite',
        'Top' => 'En haut',
        'Upper Far Left' => 'À l\'extrême haut gauche',
        'Upper Far Right' => 'À l\'extrême haut droit',
        'Upper-left' => 'En haut à gauche',
        'Upper-middle' => 'Au milieu supérieur',
        'Upper-right' => 'En haut à droite',
      },
    },
   'AFPredictor' => 'Prédicteur AF',
   'AFResponse' => 'Réponse AF',
   'AFSearch' => {
      Description => 'Recherche de l\'autofocus',
      PrintConv => {
        'Not Ready' => 'Non prête',
        'Ready' => 'Prête',
      },
    },
   'AFTracking' => {
      Description => 'Suivi de la mise au point automatique',
      PrintConv => {
        'Face tracking' => 'Suivi du visage',
        'Lock On AF' => 'Verrouillage de la mise au point automatique',
        'Off' => 'Désactivé',
      },
    },
   'AFTrackingSensitivity' => 'Sensibilité du suivi de la mise au point automatique',
   'AFType' => {
      Description => 'Type de mise au point automatique',
      PrintConv => {
        '15-point' => '15 points',
        '19-point' => '19 points',
        '79-point' => '79 points',
      },
    },
   'AIColorUsage' => 'Usage des couleurs AIM',
   'AICreatorVersion' => 'Version créateur AIM',
   'AIFCSummary' => 'Résumé AIM',
   'AIFileFormat' => 'Format fichier AIM',
   'AINumLayers' => 'Calques AIM',
   'AIRulerUnits' => {
      Description => 'Unités règles AIM',
      PrintConv => {
        'Centimeters' => 'Centimètres',
        'Inches' => 'Pouces',
        'Millimeters' => 'Milimètres',
      },
    },
   'AIServoContinuousShooting' => 'Priorité vit. méca. AI Servo',
   'AIServoImagePriority' => {
      Description => '1er Servo Ai/2e priorité déclenchement',
      PrintConv => {
        '1: AF, 2: Drive speed' => 'Priorité AF/Priorité cadence vues',
        '1: AF, 2: Tracking' => 'Priorité AF/Priorité suivi AF',
        '1: Release, 2: Drive speed' => 'Déclenchement/Priorité cadence vues',
      },
    },
   'AIServoTrackingMethod' => {
      Description => 'Méthode de suivi autofocus AI Servo',
      PrintConv => {
        'Continuous AF track priority' => 'Priorité suivi AF en continu',
        'Main focus point priority' => 'Priorité point AF principal',
      },
    },
   'AIServoTrackingSensitivity' => {
      Description => 'Sensibili. de suivi AI Servo',
      PrintConv => {
        'Fast' => 'Rapide',
        'Medium Fast' => 'Moyenne rapide',
        'Medium Slow' => 'Moyenne lent',
        'Moderately fast' => 'Moyennement rapide',
        'Moderately slow' => 'Moyennement lent',
        'Slow' => 'Lent',
      },
    },
   'APEVersion' => 'Version APE',
   'APS-CSizeCapture' => {
      Description => 'Capture de la taille APS-C',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'ARMIdentifier' => 'Identificateur ARM',
   'ARMVersion' => 'Version ARM',
   'AToB0' => 'A à B0',
   'AToB1' => 'A à B1',
   'AToB2' => 'A à B2',
   'Acceleration' => 'Accélération',
   'AccelerationTracking' => 'Suivi de l\'accélération',
   'AccelerationVector' => 'Vecteurs d\'accélération',
   'AccelerometerX' => 'Accéléromètre X',
   'AccelerometerY' => 'Accéléromètre Y',
   'AccelerometerZ' => 'Accéléromètre Z',
   'AccessoryType' => 'Type d\'accessoire',
   'ActionAdvised' => {
      Description => 'Action conseillée',
      PrintConv => {
        'Object Append' => 'Ajouter un objet',
        'Object Kill' => 'Détruire un objet',
        'Object Reference' => 'Référencer un objet',
        'Object Replace' => 'Remplacer un objet',
        'Ojbect Append' => 'Ajout d\'objet',
      },
    },
   'ActiveArea' => 'Zone active',
   'ActiveD-Lighting' => {
      Description => 'D-Lighting actif',
      PrintConv => {
        'Extra High' => 'Extra haut',
        'Extra High 1' => 'Extra haut 1',
        'Extra High 2' => 'Extra haut 2',
        'Extra High 3' => 'Extra haut 3',
        'Extra High 4' => 'Extra haut 4',
        'High' => 'Haut',
        'Low' => 'Bas',
        'Normal' => 'Normale',
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'ActiveD-LightingMode' => {
      PrintConv => {
        'Low' => 'Bas',
        'Normal' => 'Normale',
        'Off' => 'Désactivé',
      },
    },
   'AddAspectRatioInfo' => {
      Description => 'Info ratio d\'aspect ajouté',
      PrintConv => {
        'Off' => 'Désactivé',
      },
    },
   'AddOriginalDecisionData' => {
      Description => 'Aj. données décis. origine',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'AdditionalModelInformation' => 'Modèle d\'Information additionnel',
   'Address' => 'Adresse',
   'AdjustmentMode' => 'Mode de réglage',
   'AdultContentWarning' => {
      PrintConv => {
        'Unknown' => 'Inconnu',
      },
    },
   'AdvancedFilter' => {
      PrintConv => {
        'Hi Key' => 'Tons clairs',
        'Low Key' => 'Tons sombres',
        'Pop Color' => 'Couleur pop',
        'Soft Focus' => 'Flou artistique',
        'Toy Camera' => 'Caméra jouet',
      },
    },
   'AdvancedRaw' => {
      Description => 'RAW avancé',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'AdvancedSceneMode' => {
      Description => 'Mode Scène avancé',
      PrintConv => {
        'Aerial Photo / Underwater / Multi-aspect' => 'Photo aérienne / Sous-marine / Multi-aspect',
        'Beach' => 'Plage',
        'Bleach Bypass' => 'Traitement sans blanchiment',
        'Cinema' => 'Cinéma',
        'Color Select' => 'Désaturation partielle',
        'Creative Macro' => 'Macro créative',
        'Creative Night Scenery' => 'Scène nocturne créative',
        'Creative Portrait' => 'Portrait créatif',
        'Creative Scenery' => 'Scènes créatives',
        'Creative Sports' => 'Sports créatifs',
        'Cross Process' => 'Traitement croisé',
        'Dynamic Art' => 'Art dynamique',
        'Dynamic Monochrome' => 'Monochrome dynamique',
        'Elegant' => 'Élegant',
        'Expressive' => 'Expressif',
        'Fantasy' => 'Fantaisie',
        'Fireworks' => 'Feux d\'artifices',
        'Flower' => 'Fleur',
        'HDR Art' => 'HDR artistique',
        'HDR B&W' => 'HDR N&B',
        'Handheld Night Shot' => 'Photo nocturne à main levée',
        'High Dynamic' => 'Haute dynamique',
        'High Key' => 'Tons clairs',
        'High Sensitivity' => 'Haute Sensibilité',
        'High-speed Burst (shot 1)' => 'Rafale haute vitesse (prise de vue 1)',
        'High-speed Burst (shot 2)' => 'Rafale haute vitesse (prise de vue 2)',
        'High-speed Burst (shot 3)' => 'Rafale haute vitesse (prise de vue 3)',
        'Impressive Art' => 'Impressionisme',
        'Indoor Portrait' => 'Portrait en intérieur',
        'Indoor Sports' => 'Sports en intérieur',
        'Low Key' => 'Tons sombres',
        'Miniature' => 'Effet miniature',
        'Off' => 'Désactivé',
        'Old Days' => 'Vieux jours',
        'Outdoor Portrait' => 'Portrait en extérieur',
        'Outdoor Sports' => 'Sports en extérieur',
        'Retro' => 'Rétro',
        'Rough Monochrome' => 'Monochrome brut',
        'Sepia' => 'Sépia',
        'Silky Monochrome' => 'Monochrome soyeux',
        'Snow' => 'Neige',
        'Soft' => 'Mise au point douce',
        'Star' => 'Filtre étoile',
        'Starry Sky' => 'Ciel étoilé',
        'Sunshine' => 'Ensoleillée',
        'Toy Effect' => 'Effet jouet',
        'Toy Pop' => 'Jouet Pop',
      },
    },
   'AdvancedSceneType' => 'Type de scène avancé',
   'Advisory' => 'Adversité',
   'AlarmUID' => 'UID Alarme',
   'AmbienceSelection' => {
      Description => 'Sélecteur d\'ambiance',
      PrintConv => {
        'Brighter' => 'Plus lumineux',
        'Cool' => 'Frais',
        'Darker' => 'Plus sombre',
        'Soft' => 'Douce',
        'Vivid' => 'Éclatant',
        'Warm' => 'Chaud',
      },
    },
   'AmbientTemperature' => 'Température ambiante',
   'AmbientTemperatureFahrenheit' => 'Température ambiante en Fahrenheit',
   'AnalogBalance' => 'Balance analogique',
   'AngleAxis' => 'Angle de l\'axe',
   'AngleInfoRoll' => 'Angle de roulis',
   'AngleInfoYaw' => 'Angle de lacet',
   'AngleNumber' => 'Numéro de l\'angle',
   'AngleOfAttack' => 'Angle d\\attaque',
   'AngleToNorth' => 'Angle par rapport au Nord',
   'AngularPosition' => 'Position angulaire',
   'AngularUnitKind' => 'Type d\'unité angulaire',
   'AngularVelocity' => 'Vélocité angulaire',
   'Annotations' => 'Annotations Photoshop',
   'Anti-Blur' => {
      Description => 'Anti-flou',
      PrintConv => {
        'Off' => 'Désactivé',
        'On (Continuous)' => 'Activé (continu)',
        'On (Shooting)' => 'Activé (Prise de vue)',
        'n/a' => 'Non applicable',
      },
    },
   'AntiAliasStrength' => 'Puissance relative du filtre anticrénelage de l\'appareil',
   'Aperture' => 'Ouverture',
   'ApertureDisplayed' => 'Ouverture affichée',
   'ApertureRange' => {
      Description => 'Régler gamme d\'ouvertures',
      PrintConv => {
        'Disable' => 'Désactivé',
        'Enable' => 'Activée',
      },
    },
   'ApertureRingUse' => {
      Description => 'Utilisation de la bague de diaphragme',
      PrintConv => {
        'Permitted' => 'Autorisée',
        'Prohibited' => 'Interdite',
      },
    },
   'ApertureValue' => 'Ouverture',
   'ApplicationRecordVersion' => 'Version d\'enregistrement de l\'application',
   'ApplyShootingMeteringMode' => {
      Description => 'Appliquer mode de prise de vue/de mesure',
      PrintConv => {
        'Disable' => 'Désactivé',
        'Enable' => 'Activée',
      },
    },
   'ApproximateFNumber' => 'Diaphragme ƒ aproximatif',
   'ArtFilter' => {
      PrintConv => {
        'Cross Process' => 'Traitement croisé',
        'Cross Process II' => 'Traitement croisé II',
        'Partial Color' => 'Couleur partielle',
        'Partial Color II' => 'Couleur partielle II',
        'Partial Color III' => 'Couleur partielle III',
        'Soft Focus' => 'Flou artistique',
        'Soft Focus 2' => 'Flou artistique 2',
      },
    },
   'ArtFilterEffect' => {
      PrintConv => {
        'Cross Process' => 'Traitement croisé',
        'Cross Process II' => 'Traitement croisé II',
        'Partial Color' => 'Couleur partielle',
        'Partial Color II' => 'Couleur partielle II',
        'Partial Color III' => 'Couleur partielle III',
        'Soft Focus' => 'Flou artistique',
        'Soft Focus 2' => 'Flou artistique 2',
      },
    },
   'ArtMode' => {
      PrintConv => {
        'Art HDR' => 'HDR artistique',
        'Light Tone' => 'Tonalité claire',
        'Painting' => 'Peinture',
        'Toy Camera' => 'Caméra jouet',
      },
    },
   'Artist' => 'Artiste',
   'Artist2' => 'Artiste 2',
   'ArtworkCopyrightNotice' => 'Notice copyright de l\'Illustration',
   'ArtworkCreator' => 'Créateur de l\'Illustration',
   'ArtworkDateCreated' => 'Date de création de l\'Illustration',
   'ArtworkSource' => 'Source de l\'Illustration',
   'ArtworkSourceInventoryNo' => 'No d\'Inventaire du source de l\'Illustration',
   'ArtworkTitle' => 'Titre de l\'Illustration',
   'AsShotICCProfile' => 'Profil ICC à la prise de vue',
   'AsShotNeutral' => 'Balance neutre à la prise de vue',
   'AsShotPreProfileMatrix' => 'Matrice de pré-profil à la prise de vue',
   'AsShotProfileName' => 'Nom du profil du cliché',
   'AsShotWhiteXY' => 'Balance blanc X-Y à la prise de vue',
   'AspectRatio' => {
      Description => 'Ratio d\'aspect',
      PrintConv => {
        '16:9, 525 line, NTSC' => '16:9, 525 lignes, NTSC',
        '16:9, 625 line, PAL' => '16:9, 625 lignes, PAL',
        '3:2 (APS-C crop)' => '3:2 (recadrage APS-C)',
        '3:2 (APS-H crop)' => '3:2 (recadrage APS-H))',
        '4:3 crop' => '4:3 recadré',
        '4:3, 525 line, NTSC, CCIR601' => '4:3, 525 lignes, NTSC, CCIR601',
        '4:3, 625 line, PAL, CCIR601' => '4:3, 625 lignes, PAL, CCIR601',
      },
    },
   'AspectRatioType' => {
      Description => 'Type de ratio d\'aspect',
      PrintConv => {
        'Fixed' => 'Fixe',
        'Free Resizing' => 'Redimensionnement libre',
        'Keep Aspect Ratio' => 'Conserver le ratio d\'aspect',
      },
    },
   'AssignFuncButton' => {
      Description => 'Changer fonct. touche FUNC.',
      PrintConv => {
        'Exposure comp./AEB setting' => 'Correct. expo/réglage AEB',
        'Image jump with main dial' => 'Saut image par molette principale',
        'Image quality' => 'Changer de qualité',
        'LCD brightness' => 'Luminosité LCD',
        'Live view function settings' => 'Réglages Visée par l’écran',
      },
    },
   'AssignMB-D17FuncButtonPlusDials' => {
      PrintConv => {
        'Active D-Lighting' => 'D-Lighting actif',
      },
    },
   'AssignMB-D18FuncButtonPlusDials' => {
      PrintConv => {
        'Active D-Lighting' => 'D-Lighting actif',
      },
    },
   'AssignMovieRecordButton' => {
      PrintConv => {
        'HDR Overlay' => 'Recouvrement HDR',
      },
    },
   'AssistButtonFunction' => {
      Description => 'Touche de fonction rapide',
      PrintConv => {
        'Av+/- (AF point by QCD)' => 'Av+/- (AF par mol. AR)',
        'FE lock' => 'Mémo expo. au flash',
        'Normal' => 'Normale',
        'Select HP (while pressing)' => 'Sélect. HP (en appuyant)',
        'Select Home Position' => 'Sélect. position origine',
      },
    },
   'Audio' => {
      PrintConv => {
        'No' => 'Non',
        'Stereo' => 'Stéréo',
        'Yes' => 'Oui',
      },
    },
   'AudioBitRateControlMode' => 'Mode de contrôle du Bitrate audio',
   'AudioBitrate' => 'Bitrate audio',
   'AudioBitrateMode' => {
      Description => 'Mode Bitrate audio',
      PrintConv => {
        'Fixed' => 'Fixe',
      },
    },
   'AudioButton' => {
      PrintConv => {
        'HDR Overlay' => 'Recouvrement HD',
      },
    },
   'AudioButtonPlaybackMode' => {
      PrintConv => {
        'HDR Overlay' => 'Recouvrement HD',
      },
    },
   'AudioDuration' => 'Durée audio',
   'AudioOutcue' => 'Queue audio',
   'AudioSamplingRate' => 'Taux d\'échantillonnage audio',
   'AudioSamplingResolution' => 'Résolution d\'échantillonnage audio',
   'AudioType' => {
      Description => 'Type audio',
      PrintConv => {
        'Mono Actuality' => 'Actualité (audio mono (1 canal)',
        'Mono Music' => 'Musique, transmise par elle-même (audio mono (1 canal)',
        'Mono Question and Answer Session' => 'Question et réponse (audio mono (1 canal)',
        'Mono Raw Sound' => 'Son brut (audio mono (1 canal)',
        'Mono Response to a Question' => 'Réponse à une question (audio mono (1 canal)',
        'Mono Scener' => 'Scener (audio mono (1 canal)',
        'Mono Voicer' => 'Voix (audio mono (1 canal)',
        'Mono Wrap' => 'Wrap (audio mono (1 canal)',
        'Stereo Actuality' => 'Actualité (audio stéréo (2 canaux)',
        'Stereo Music' => 'Musique, transmise par elle-même (audio stéréo (2 canaux)',
        'Stereo Question and Answer Session' => 'Question et réponse (audio stéréo (2 canaux)',
        'Stereo Raw Sound' => 'Son brut (audio stéréo (2 canaux)',
        'Stereo Response to a Question' => 'Réponse à une question (audio stéréo (2 canaux)',
        'Stereo Scener' => 'Scener (audio stéréo (2 canaux)',
        'Stereo Voicer' => 'Voix (audio stéréo (2 canaux)',
        'Stereo Wrap' => 'Wrap (audio stéréo (2 canaux)',
        'Text Only' => 'Texte seul (pas de données d\'objet)',
      },
    },
   'Author' => 'Auteur',
   'AuthorsPosition' => 'Titre du créateur',
   'AutoAperture' => {
      Description => 'Auto-diaph',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'AutoBracket' => 'Auto bracketing',
   'AutoBracketSet' => {
      PrintConv => {
        'Active D-Lighting' => 'D-Lighting actif',
      },
    },
   'AutoBracketing' => {
      Description => 'Bracketing auto',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'AutoBracketingSet' => {
      PrintConv => {
        'Active D-Lighting' => 'D-Lighting actif',
      },
    },
   'AutoDistortionControl' => {
      Description => 'Contrôle de distorsion automatique',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
        'On (underwater)' => 'Activé (sous l\'eau)',
      },
    },
   'AutoExposureBracketing' => {
      Description => 'Bracketing d\'exposition automatique',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
        'On (shot 1)' => 'Activé (prise de vue 1)',
        'On (shot 2)' => 'Activé (prise de vue 2)',
        'On (shot 3)' => 'Activé (prise de vue 3)',
      },
    },
   'AutoFP' => {
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'AutoFocus' => {
      Description => 'Auto-Focus',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'AutoISO' => {
      Description => 'ISO auto',
      PrintConv => {
        'High Speed' => 'Haute vitesse',
        'Off' => 'Désactivé',
        'On' => 'Activé',
        'On (anti-shake)' => 'Activé (anti-vibration)',
        'On (high sensitivity)' => 'Activé (haute sensibilité)',
      },
    },
   'AutoLightingOptimizer' => {
      Description => 'Correction auto de luminosité',
      PrintConv => {
        'Disable' => 'Désactivé',
        'Enable' => 'Actif',
        'Low' => 'Faible',
        'Off' => 'Désactivé',
        'Strong' => 'Importante',
        'n/a' => 'Non applicable',
      },
    },
   'AutoLightingOptimizerOn' => {
      PrintConv => {
        'No' => 'Non',
        'Yes' => 'Oui',
      },
    },
   'AutoPortraitFramed' => {
      Description => 'Autoportrait encadré',
      PrintConv => {
        'No' => 'Non',
      },
    },
   'AutoRedEye' => {
      Description => 'Correction yeux-rouges automatique',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'AutoRotate' => {
      Description => 'Rotation automatique',
      PrintConv => {
        'None' => 'Aucune',
        'Rotate 180' => 'Rotation de 180°',
        'Rotate 270 CW' => 'Rotation antihoraire de 270°',
        'Rotate 90 CW' => 'Rotation antihoraire de 90°',
        'n/a' => 'Non applicable',
      },
    },
   'AuxiliaryImageType' => 'Type d\'image auxiliaire',
   'AuxiliaryLens' => 'Objectif Auxiliaire',
   'AvApertureSetting' => 'Réglage d\'ouverture Av',
   'AvSettingWithoutLens' => {
      Description => 'Réglage Av sans objectif',
      PrintConv => {
        'Disable' => 'Désactivé',
        'Enable' => 'Activé',
      },
    },
   'AverageFrameRate' => 'Fréquence d\'images moyenne',
   'BToA0' => 'B à A0',
   'BToA1' => 'B à A1',
   'BToA2' => 'B à A2',
   'BWMode' => {
      Description => 'Mode N&B',
      PrintConv => {
        '(none)' => '(aucun))',
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'BabyAge' => 'Âge du bébé',
   'BabyName' => 'Nom du bébé',
   'BackLight' => {
      Description => 'Contre-jour',
      PrintConv => {
        'Back Lit 1' => 'Rétroéclairage 1',
        'Back Lit 2' => 'Rétroéclairage 1',
        'Front Lit' => 'Lumière frontale',
      },
    },
   'BackgroundColorIndicator' => 'Indicateur de couleur d\'arrière-plan',
   'BackgroundColorValue' => 'Valeur de couleur d\'arrière-plan',
   'BackgroundTiling' => {
      PrintConv => {
        'No' => 'Non',
        'Yes' => 'Oui',
      },
    },
   'BadFaxLines' => 'Mauvaises lignes de Fax',
   'BannerImageType' => {
      PrintConv => {
        'None' => 'Aucune',
      },
    },
   'BannerImageURL' => 'URL de l\'image-bannière',
   'Barcode' => 'Code-barres',
   'BarcodeSymbology' => 'Symbologie du code-barres',
   'BarcodeValue' => 'Valeur du code-barres',
   'Barcodes' => 'Codes-barres',
   'BaseExposureCompensation' => 'Compensation d\'exposition de référence',
   'BaseISO' => 'ISO de référence',
   'BaseISODaylight' => 'ISO de référence Lumière du jour',
   'BaseISOFlash' => 'ISO de référence Flash',
   'BaseISOFluorescent' => 'ISO de référence Fluorescent',
   'BaseISOTungsten' => 'ISO de référence Tungstène',
   'BaseName' => 'Nom de référence',
   'BaseURL' => 'URL de référence',
   'BaselineExposure' => 'Exposition de référence',
   'BaselineNoise' => 'Bruit de référence',
   'BaselineSharpness' => 'Netteté de référence',
   'BatteryInfo' => 'Source d\'alimentation',
   'BatteryLevel' => {
      Description => 'Batterie',
      PrintConv => {
        'Full' => 'Totalement chargée',
        'Low' => 'Niveau bas',
        'Medium' => 'Niveau moyen',
        'Medium Low' => 'Niveau moyen bas',
        'Near Empty' => 'Presque vide',
        'Near Full' => 'Presque totalement chargée',
        'n/a' => 'Non applicable',
      },
    },
   'BatteryTemperature' => 'Température de la batterie',
   'BayerGreenSplit' => 'Séparation de vert Bayer',
   'Beep' => {
      PrintConv => {
        'High' => 'Bruyant',
        'Low' => 'Calme',
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'BeepVolume' => {
      Description => 'Volume du bip',
      PrintConv => {
        'Off' => 'Désactivé',
      },
    },
   'BestQualityScale' => 'Meilleure balance de qualité',
   'BestShotMode' => {
      Description => 'Meilleur mode de prise de vue',
      PrintConv => {
        'Anti Shake' => 'Anti-vibration',
        'Auction' => 'Enchères',
        'Auto Best Shot' => 'Meilleure prise de vue automatique',
        'Auto Framing' => 'Cadrage automatique',
        'Autumn Leaves' => 'Feuilles d\'automne',
        'Baby CS' => 'Bébé CS',
        'Backlight' => 'Contre-jour',
        'Beach' => 'Plage',
        'Blurred Background' => 'Arrière-plan flou',
        'Business Cards' => 'Cartes de visite',
        'Candlelight Portrait' => 'Portrait à la chandelle',
        'Child CS' => 'Enfant CS',
        'Child High Speed Movie' => 'Vidéo d\'enfant à grande vitesse',
        'Children' => 'Enfants',
        'Cross Filter' => 'Filtre transversal',
        'Dynamic Photo' => 'Photo dynamique',
        'Fashion Accessories' => 'Accessoires de mode',
        'Fireworks' => 'Feux d\'artifice',
        'Flower' => 'Fleur',
        'Food' => 'Alimentation',
        'For YouTube' => 'Pour YouTube',
        'For eBay' => 'Pour eBay',
        'HDR Art' => 'HDR artistique',
        'High Sensitivity' => 'Haute Sensibilité',
        'High Speed Anti Shake' => 'Anti-vibration à grande vitesse',
        'High Speed Best Selection' => 'Meilleure sélection à haute vitesse',
        'High Speed CS' => 'Haute vitesse CS',
        'High Speed Lighting' => 'Éclairage à haute vitesse',
        'High Speed Night Scene' => 'Scène nocturne à haute vitesse',
        'High Speed Night Scene and Portrait' => 'Scène nocturne à haute vitesse et Portrait',
        'High Speed Night Shot' => 'Photo nocturne à haute vitesse',
        'Interval Movie' => 'Film à intervalles',
        'Interval Snapshot' => 'Instantané à intervalle',
        'Lag Correction' => 'Correction du décalage',
        'Layout (2 images)' => 'Mise en page (2 images)',
        'Layout (3 images)' => 'Mise en page (3 images)',
        'Light Tone' => 'Tonalités claires',
        'Move In CS' => 'Entrer CS',
        'Move Out CS' => 'Sortir CS',
        'Movie' => 'Vidéo',
        'Multi SR Zoom' => 'Zoom SR multiple',
        'Multi-motion Image' => 'Image multi-mouvement',
        'Natural Green' => 'Vert naturel',
        'Night Scene' => 'Scène nocturne',
        'Night Scene Portrait' => 'Portrait de scène nocturne',
        'Off' => 'Désactivé',
        'Oil Painting' => 'Peinture à l\'huile',
        'Old Photo' => 'Vieille photo',
        'Party' => 'Fête',
        'Past Movie' => 'Vidéo passée',
        'People' => 'Personnes',
        'Pet' => 'Animal de compagnie',
        'Pet CS' => 'Animal de compagnie CS',
        'Pet High Speed Movie' => 'Film animalier à grande vitesse',
        'Portrait with Scenery' => 'Portrait avec paysage',
        'Pre-record Movie' => 'Pré-enregistrement de la vidéo',
        'Premium Auto' => 'Auto Premium',
        'Prerecord (Movie)' => 'Pré-enregistrement (Vidéo)',
        'Retro' => 'Rétro',
        'Scenery' => 'Paysage',
        'Self Slow Motion (behind)' => 'Ralenti automatique (arrière)',
        'Self Slow Motion (front)' => 'Ralenti automatique (avant)',
        'Self-portrait (1 person)' => 'Autoportrait (1 personne)',
        'Self-portrait (2 people)' => 'Autoportrait (2 personnes)',
        'Sepia' => 'Sépia',
        'Short Movie' => 'Court-métrage',
        'Silent' => 'Silencieux',
        'Slide Panorama' => 'Panorama de diapositives',
        'Slow Motion Swing (behind)' => 'Swing au ralenti (derrière)',
        'Slow Motion Swing (front)' => 'Swing au ralenti (avant)',
        'Snow' => 'Neige',
        'Soft Flowing Water' => 'Eau douce qui coule',
        'Soft Focus' => 'Flou artistique',
        'Splashing Water' => 'Éclaboussures d\'eau',
        'Sports High Speed Movie' => 'Film de sport à haute vitesse',
        'Sundown' => 'Crépuscule',
        'Swing Burst' => 'Swing éclaté',
        'Text' => 'Texte',
        'Toy Camera' => 'Caméra jouet',
        'Twilight' => 'Crépuscule',
        'Underwater' => 'Subaquatique',
        'Voice Recording' => 'Enregistrement de la voix',
        'Water Color' => 'Couleur de l\'eau',
        'White Board' => 'Tableau blanc',
        'Wide Shot' => 'Plan large',
      },
    },
   'BitDepthChroma' => 'Bits de profondeur de chrominance',
   'BitDepthLuma' => 'Bits de profondeur de luminance',
   'BitsPerComponent' => 'Bits par composante',
   'BitsPerExtendedRunLength' => 'Bits par « Run Length » étendue',
   'BitsPerRunLength' => 'Bits par « Run Length »',
   'BitsPerSample' => 'Nombre de bits par échantillon',
   'BlackLevel' => 'Niveau noir',
   'BlackLevel2' => 'Niveau de noir 2',
   'BlackLevelDeltaH' => 'Delta H du niveau noir',
   'BlackLevelDeltaV' => 'Delta V du niveau noir',
   'BlackLevelRepeatDim' => 'Dimension de répétition du niveau noir',
   'BlackMaskBottomBorder' => 'Bordure inférieure du masque noir',
   'BlackMaskLeftBorder' => 'Bordure gauche du masque noir',
   'BlackMaskRightBorder' => 'Bordure droite du masque noir',
   'BlackMaskTopBorder' => 'Bordure supérieure du masque noir',
   'BlackPoint' => 'Point noir',
   'BleachBypassToning' => {
      Description => 'Tonalité traitement sans blanchiment',
      PrintConv => {
        'Blue' => 'Bleu',
        'Green' => 'Vert',
        'Off' => 'Désactivé',
        'Purple' => 'Violet',
        'Red' => 'Rouge',
        'Yellow' => 'Jaune',
        'n/a' => 'Non applicable',
      },
    },
   'BlueAdjust' => 'Ajustement du bleu',
   'BlueBalance' => 'Balance bleue',
   'BlueMatrixColumn' => 'Colonne de la matrice bleue',
   'BlueSaturation' => 'Saturation du bleu',
   'BlueTRC' => 'Courbe de reproduction des tonalités bleues',
   'BlurWarning' => {
      Description => 'Alerte de flou',
      PrintConv => {
        'Blur Warning' => 'Alerte de flou',
        'None' => 'Aucune',
      },
    },
   'BoardTemperature' => 'Température de la carte',
   'BodyBatteryADLoad' => 'Tension accu boîtier en charge',
   'BodyBatteryADNoLoad' => 'Tension accu boîtier à vide',
   'BodyBatteryState' => {
      Description => 'État de accu boîtier',
      PrintConv => {
        'Almost Empty' => 'Presque vide',
        'Empty or Missing' => 'Vide ou absent',
        'Full' => 'Plein',
        'Running Low' => 'En baisse',
      },
    },
   'BodyFirmwareVersion' => 'Version du micrologiciel du boitier',
   'BracketMode' => {
      Description => 'Mode Bracket',
      PrintConv => {
        'Off' => 'Désactivé',
      },
    },
   'BracketProgram' => {
      PrintConv => {
        'N/A' => 'N/a',
      },
    },
   'BracketShot' => 'Cliché en bracketing',
   'BracketShotNumber' => {
      Description => 'Numéro de cliché en bracketing',
      PrintConv => {
        '1 of 3' => '1 sur 3',
        '1 of 5' => '1 sur 5',
        '2 of 3' => '2 sur 3',
        '2 of 5' => '2 sur 5',
        '3 of 3' => '3 sur 3',
        '3 of 5' => '3 sur 5',
        '4 of 5' => '4 sur 5',
        '5 of 5' => '5 sur 5',
        'n/a' => 'Non applicable',
      },
    },
   'BracketValue' => 'Valeur Bracket',
   'Brightness' => 'Luminosité',
   'BrightnessAdj' => 'Réglage de la luminosité',
   'BrightnessValue' => 'Luminosité',
   'BulbDuration' => 'Durée du pose longue',
   'BurstMode' => {
      Description => 'Mode Rafale',
      PrintConv => {
        'Infinite' => 'Infini',
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'BurstMode2' => 'Mode rafale',
   'BurstPrimary' => 'Rafale primaire',
   'BurstShot' => 'Prise de vue en rafale',
   'BurstSpeed' => 'Vitesse de rafale',
   'BurstUUID' => 'UUID de raffale',
   'ButtonFunctionControlOff' => {
      Description => 'Fonction de touche si Contrôle Rapide OFF',
      PrintConv => {
        'Disable main, Control, Multi-control' => 'Désactivée principale, Contrôle rapide, Multicontrôleur',
        'Normal (enable)' => 'Normale (activée)',
      },
    },
   'By-line' => 'Créateur',
   'By-lineTitle' => 'Fonction du créateur',
   'CFALayout' => {
      Description => 'Organisation CFA',
      PrintConv => {
        'Even columns offset down 1/2 row' => 'Organisation décalée A : les colonnes paires sont décalées vers le bas d\'une demi-rangée.',
        'Even columns offset up 1/2 row' => 'Organisation décalée B : les colonnes paires sont décalées vers le haut d\'une demi-rangée.',
        'Even rows offset left 1/2 column' => 'Organisation décalée D : les rangées paires sont décalées vers la gauche d\'une demi-colonne.',
        'Even rows offset right 1/2 column' => 'Organisation décalée C : les rangées paires sont décalées vers la droite d\'une demi-colonne.',
        'Rectangular' => 'Plan rectangulaire (ou carré)',
      },
    },
   'CFAPattern' => {
      Description => 'Matrice de filtrage couleur',
      PrintConv => {
        '[Blue,Green][Green,Red]' => '[Bleu,Vert][Vert,Rouge]',
        '[Green,Blue][Red,Green]' => '[Vert,Bleu][Rouge,Vert]',
        '[Green,Red][Blue,Green]' => '[Vert,Rouge][Bleu,Vert]',
        '[Red,Green][Green,Blue]' => '[Rouge,Vert][Vert,Bleu]',
        'n/a' => 'Non applicable',
      },
    },
   'CFAPattern2' => 'Modèle CFA 2',
   'CFAPlaneColor' => 'Couleur de plan CFA',
   'CFARepeatPatternDim' => 'Dimension du modèle de répétition CFA',
   'CHMVersion' => 'Version Mode CH',
   'CHModeShootingSpeed' => 'Mode CH Vitesse de prise de vue',
   'CMMFlags' => 'Indicateurs CMM',
   'CMSaturation' => 'Saturation CM',
   'CMYKEquivalent' => 'Equivalent CMJK',
   'CPUArchitecture' => {
      Description => 'Architecture processeur',
      PrintConv => {
        '32 bit' => '32 bits',
        '64 bit' => '64 bits',
      },
    },
   'CPUByteOrder' => 'Ordre de traitement des octets processeur',
   'CPUFirmwareVersion' => 'Version de firmware de CPU',
   'CPUType' => {
      PrintConv => {
        'None' => 'Aucune',
      },
    },
   'CR2CFAPattern' => {
      Description => 'Motif CR2 CFA',
      PrintConv => {
        '[Blue,Green][Green,Red]' => '[Bleu,Vert][Vert,Rouge]',
        '[Green,Blue][Red,Green]' => '[Vert,Bleu][Rouge,Vert]',
        '[Green,Red][Blue,Green]' => '[Vert,Rouge][Bleu,Vert]',
        '[Red,Green][Green,Blue]' => '[Rouge,Vert][Vert,Bleu]',
      },
    },
   'CalibrationDateTime' => 'Date et heure de calibration',
   'CalibrationIlluminant1' => {
      Description => 'Illuminant de calibration 1',
      PrintConv => {
        'Cloudy' => 'Temps nuageux',
        'Cool White Fluorescent' => 'Fluorescente type soft',
        'Day White Fluorescent' => 'Fluorescente type blanc',
        'Daylight' => 'Lumière du jour',
        'Daylight Fluorescent' => 'Fluorescente type jour',
        'Fine Weather' => 'Beau temps',
        'Fluorescent' => 'Fluorescente',
        'ISO Studio Tungsten' => 'Tungstène studio ISO',
        'Other' => 'Autre source de lumière',
        'Shade' => 'Ombre',
        'Standard Light A' => 'Lumière standard A',
        'Standard Light B' => 'Lumière standard B',
        'Standard Light C' => 'Lumière standard C',
        'Tungsten (Incandescent)' => 'Tungstène (lumière incandescente)',
        'Unknown' => 'Inconnue',
        'Warm White Fluorescent' => 'Fluorescent blanc chaud',
        'White Fluorescent' => 'Fluorescent blanc',
      },
    },
   'CalibrationIlluminant2' => {
      Description => 'Illuminant de calibration 2',
      PrintConv => {
        'Cloudy' => 'Temps nuageux',
        'Cool White Fluorescent' => 'Fluorescente type soft',
        'Day White Fluorescent' => 'Fluorescente type blanc',
        'Daylight' => 'Lumière du jour',
        'Daylight Fluorescent' => 'Fluorescente type jour',
        'Fine Weather' => 'Beau temps',
        'Fluorescent' => 'Fluorescente',
        'ISO Studio Tungsten' => 'Tungstène studio ISO',
        'Other' => 'Autre source de lumière',
        'Shade' => 'Ombre',
        'Standard Light A' => 'Lumière standard A',
        'Standard Light B' => 'Lumière standard B',
        'Standard Light C' => 'Lumière standard C',
        'Tungsten (Incandescent)' => 'Tungstène (lumière incandescente)',
        'Unknown' => 'Inconnue',
        'Warm White Fluorescent' => 'Fluorescent blanc chaud',
        'White Fluorescent' => 'Fluorescent blanc',
      },
    },
   'CameraCalibration' => 'Calibration de l\'appareil photo',
   'CameraCalibration1' => 'Calibration de l\'appareil photo 1',
   'CameraCalibration2' => 'Calibration de l\'appareil photo 2',
   'CameraCalibration3' => 'Calibration de l\'appareil photo 3',
   'CameraCalibrationSig' => 'Signature de calibration de l\'appareil photo',
   'CameraDateTime' => 'Date et heure de l\'appareil photo',
   'CameraDirection' => 'Direction de l\'appareil photo',
   'CameraE-mountVersion' => 'Version de l\'appareil photo à monture E',
   'CameraElevationAngle' => 'Angle d\'élémvation de l\'appareil photo',
   'CameraFilename' => 'Nom de fichier de l\'appareil photo',
   'CameraID' => 'Identifiant de l\'appareil photo',
   'CameraISO' => 'ISO de l\'appareil photo',
   'CameraIdentifier' => 'Identificateur de l\'appareil photo',
   'CameraImage' => 'Image de l\'appareil photo',
   'CameraInfoByteOrder' => 'Ordre des octets de l\'appareil photo',
   'CameraMakeModel' => 'Appareil photo Marque et Modèle',
   'CameraMaker' => 'Fabricant de l\'appareil photo',
   'CameraManufacturer' => 'Fabricant de l\'appareil photo',
   'CameraModel' => 'Modèle de l\'appareil photo',
   'CameraMotion' => 'Mouvement de l\'appareil photo',
   'CameraMove' => 'Mouvement de l\'appareil photo',
   'CameraName' => 'Nom de l\'appareil photo',
   'CameraOrientation' => {
      Description => 'Orientation de l\'appareil photo',
      PrintConv => {
        'Downwards' => 'Dirigé vers le bas',
        'Horizontal (normal)' => 'À l\'horizontal (normal)',
        'Normal' => 'Orientation Normale',
        'Rotate 180' => 'Pivoté de de 180°',
        'Rotate 270 CW' => 'Pivoté de 270° dans le sens antihoraire',
        'Rotate 90 CW' => 'Pivoté de 90° dans le sens antihoraire',
        'Rotate CCW' => 'Pivoté à droite',
        'Rotate CW' => 'Pivoté à gauche',
        'Tilt Downwards' => 'Incliné vers le bas',
        'Tilt Upwards' => 'Incliné vers le haut',
        'Upwards' => 'Dirigé vers le haut',
      },
    },
   'CameraPictureStyle' => {
      Description => 'Style d\'image de l\'appareil photo',
      PrintConv => {
        'Landscape' => 'Paysage',
        'Neutral' => 'Neutre',
        'User Defined 1' => 'Défini par l\'utilisateur 1',
        'User Defined 2' => 'Défini par l\'utilisateur 2',
        'User Defined 3' => 'Défini par l\'utilisateur 3',
      },
    },
   'CameraPitch' => 'Inclinaison de la caméra',
   'CameraSerialNumber' => 'Numéro de série de l\'appareil photo',
   'CameraSettings' => 'Réglages de l\'appareil',
   'CameraSettingsVersion' => 'Version des réglages de l\'appareil photo',
   'CameraTemperature' => 'Température de l\'appareil photo',
   'CameraType' => {
      Description => 'Type d\'appareil photo',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'CameraType2' => 'Type d\'appareil photo 2',
   'CameraYaw' => 'Lacet de la caméra',
   'Cameras' => 'Caméras',
   'CanonColorInfo1' => 'Info couleurs 1 Canon',
   'CanonColorInfo2' => 'Info couleurs 2 Canon',
   'CanonExposureMode' => {
      Description => 'Mode d\'exposition Canon',
      PrintConv => {
        'Aperture-priority AE' => 'Priorité à l\'ouverture AE',
        'Bulb' => 'Ampoule',
        'Depth-of-field AE' => 'Profondeur de champ AE',
        'Easy' => 'Simple',
        'Flexible-priority AE' => 'Priorité flexible AE',
        'Manual' => 'Manuel',
        'Program AE' => 'Programme d\'exposition automatique',
        'Shutter speed priority AE' => 'Priorité à la vitesse d\'obturation AE',
      },
    },
   'CanonFileDescription' => 'Description du fichier Canon',
   'CanonFileLength' => 'Longueur du fichier Canon',
   'CanonFirmwareVersion' => 'Version du firmware Canon',
   'CanonFlashInfo' => 'Info Flash Canon',
   'CanonFlashMode' => {
      Description => 'Mode Flash Canon',
      PrintConv => {
        'External flash' => 'Flash externe',
        'Off' => 'Désactivé',
        'On' => 'Activé',
        'Red-eye reduction' => 'Réduction yeux rouges',
        'Red-eye reduction (Auto)' => 'Réduction yeux rouges (Auto)',
        'Red-eye reduction (On)' => 'Réduction yeux rouges (Activée)',
        'Slow-sync' => 'Synchro lente',
        'n/a' => 'Non applicable',
      },
    },
   'CanonImageHeight' => 'Hauteur de l\'image Canon',
   'CanonImageSize' => {
      Description => 'Taille de l\'image Canon',
      PrintConv => {
        '1280x720 Movie' => 'Vidéo 1280x720',
        '1920x1080 Movie' => 'Vidéo 1920x1080',
        '4096x2160 Movie' => 'Vidéo 4096x2160',
        '640x480 Movie' => 'Vidéo 640x480',
        'Large' => 'Grande',
        'Medium' => 'Moyenne',
        'Medium 1' => 'Moyenne 1',
        'Medium 2' => 'Moyenne 2',
        'Medium 3' => 'Moyenne 3',
        'Medium Movie' => 'Vidéo moyenne',
        'Medium Widescreen' => 'Écran large moyen',
        'Postcard' => 'Carte postale',
        'Small' => 'Petite',
        'Small 1' => 'Petite 1',
        'Small 2' => 'Petite 2',
        'Small 3' => 'Petite 3',
        'Small Movie' => 'Petite vidéo',
        'Widescreen' => 'Écran large',
        'n/a' => 'Non applicable',
      },
    },
   'CanonImageType' => 'Type d\'image Canon',
   'CanonImageWidth' => 'Largeur de l\'image Canon',
   'CanonLogVersion' => {
      Description => 'Version du journal Canon',
      PrintConv => {
        'OFF' => 'Désactivé',
      },
    },
   'CanonModelID' => 'Identifiant du modèle Canon',
   'Caption-Abstract' => 'Légende / Description',
   'CaptionWriter' => 'Rédacteur',
   'CaptureXResolutionUnit' => {
      PrintConv => {
        'um' => 'µm (micromètre)',
      },
    },
   'CaptureYResolutionUnit' => {
      PrintConv => {
        'um' => 'µm (micromètre)',
      },
    },
   'CardiacBeatRejectionTechnique' => 'Technique de rejet du rythme cardiaque',
   'CardiacCyclePosition' => 'Position du cycle cardiaque',
   'CardiacFramingType' => 'Type de cadrage cardiaque',
   'CardiacNumberOfImages' => 'Nombre d\'images cardiaques',
   'CardiacPhases' => 'Phases cardiaques',
   'CardiacRRIntervalSpecified' => 'Intervalle cardiaque RR spécifié',
   'CardiacRepetitionTime' => 'Temps de répétition cardiaque',
   'CardiacSignalSource' => 'Source des signaux cardiaques',
   'CardiacSynchronizationTechnique' => 'Technique de synchronisation cardiaque',
   'CardiacTriggerSequence' => 'Séquence de déclenchement cardiaque',
   'CasioImageSize' => 'Taille de l\'image Casio',
   'CasioQuality' => {
      Description => 'Qualité Casio',
      PrintConv => {
        'Economy' => 'Économie',
        'Normal' => 'Normale',
      },
    },
   'Categories' => {
      Description => 'Catégories',
      PrintConv => {
        '(none)' => '(aucune)',
        'Events' => 'Événements',
        'People' => 'Personnes',
        'Scenery' => 'Paysage',
        'To Do' => 'À faire',
        'User 1' => 'Utilisateur 1',
        'User 2' => 'Utilisateur 2',
        'User 3' => 'Utilisateur 3',
      },
    },
   'Category' => 'Catégorie',
   'CellLength' => 'Longueur de la cellule',
   'CellWidth' => 'Largeur de la cellule',
   'CenterWeightedAreaSize' => {
      PrintConv => {
        'Average' => 'Moyenne',
      },
    },
   'Certificate' => 'Certificat',
   'CharTarget' => 'Cible caractère',
   'CharacterSet' => {
      Description => 'Jeu de caractères',
      PrintConv => {
        '191 characters ISO 8850-1' => '191 caractères ISO 8850-1',
        '38 characters ISO 646' => '38 caractères ISO 646',
        '65 characters ISO 646' => '65 caractères ISO 646',
        '95 characters ISO 646' => '95 caractères ISO 646',
        'Includes characters not ISO 2375 registered' => 'Comprend des caractères non enregistrés dans l\'ISO 2375',
        'Windows, Arabic' => 'Windows, Arabe',
        'Windows, Chinese (Simplified)' => 'Windows, Chinois (Simplifié)',
        'Windows, Cyrillic' => 'Windows, Cyrillique',
        'Windows, Greek' => 'Windows, Grec',
        'Windows, Hebrew' => 'Windows, Hébreux',
        'Windows, Japan (Shift - JIS X-0208)' => 'Windows, Japonais (Majuscule - JIS X-0208)',
        'Windows, Korea (Shift - KSC 5601)' => 'Windows, Coréen (Majuscule - KSC 5601)',
        'Windows, Latin2 (Eastern European)' => 'Windows, Latin2 (Europe de l\'Est)',
        'Windows, Turkish' => 'Windows, Turc',
      },
    },
   'CheckMark' => {
      Description => 'Coche',
      PrintConv => {
        'Clear' => 'Effacer',
      },
    },
   'ChromaBlurRadius' => 'Rayon de flou de chromatisme',
   'ChromaFormat' => 'Structures d\'échantillonnage de chrominance',
   'ChromaticAberrationCorrParams' => 'Paramètres de correction de l\'aberration chromatique',
   'ChromaticAberrationCorrection' => {
      Description => 'Correction de l\'aberration chromatique',
      PrintConv => {
        'No correction params available' => 'Aucun paramètre de correction disponible',
        'Off' => 'Désactivée',
        'On' => 'Activée',
      },
    },
   'ChromaticAberrationParams' => 'Paramètres d\'aberration chromatique',
   'ChromaticAdaptation' => 'Adaptation chromatique',
   'Chromaticity' => 'Chromaticité',
   'ChrominanceNR_TIFF_JPEG' => {
      PrintConv => {
        'Low' => 'Bas',
        'Off' => 'Désactivé',
      },
    },
   'ChrominanceNoiseReduction' => {
      PrintConv => {
        'Low' => 'Bas',
        'Off' => 'Désactivé',
      },
    },
   'CircleOfConfusion' => 'Cercle de confusion',
   'City' => 'Ville',
   'City2' => 'Ville 2',
   'CityName' => 'Nom de la ville',
   'Clarity' => 'Clarté',
   'Clarity2012' => 'Clarté 2012',
   'ClarityControl' => {
      Description => 'Contrôle de clarté',
      PrintConv => {
        'Off' => 'Désactivée',
      },
    },
   'ClassifyState' => 'État de classification',
   'CleanFaxData' => 'Données de Fax propres',
   'ClearRetouch' => {
      Description => 'Retouche claire',
      PrintConv => {
        'Off' => 'Désactivée',
        'On' => 'Activée',
      },
    },
   'ClearRetouchValue' => 'Valeur de retouche claire',
   'ClipPath' => 'Chemin d\'accès à l\'extrait',
   'ClipboardOrientation' => 'Orientation du presse-papier',
   'CodecDecodeAll' => {
      Description => 'Codec de décodage total',
      PrintConv => {
        'No' => 'Non',
        'Yes' => 'Oui',
      },
    },
   'CodedCharacterSet' => 'Codage du jeu de caractères',
   'CodedContentScanningKind' => {
      Description => 'Type d\'analyse du contenu codé',
      PrintConv => {
        'Interlaced' => 'Entrelacé',
        'Mixed' => 'Mixte',
        'Progressive' => 'Progressif',
        'Unknown' => 'Inconnu',
      },
    },
   'CollectionName' => 'Nom de collection',
   'Color' => {
      PrintConv => {
        'Sepia' => 'Sépia',
      },
    },
   'ColorAberrationControl' => {
      Description => 'Contrôle de l\'aberration de la couleur',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'ColorAdjustment' => 'Réglage des couleurs',
   'ColorAdjustmentMode' => {
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'ColorBW' => 'Couleur N&B',
   'ColorBalance' => 'Balance des couleurs',
   'ColorBalanceAdj' => {
      Description => 'Réglage de la balance des couleurs',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'ColorBalanceVersion' => 'Version de la Balance des couleurs',
   'ColorBitDepth' => 'Profondeur de bit des couleurs',
   'ColorBoostLevel' => 'Niveau de boost de couleur',
   'ColorBoostType' => {
      Description => 'Type de boost de couleur',
      PrintConv => {
        'People' => 'Personnes',
      },
    },
   'ColorBooster' => {
      Description => 'Booster de couleur',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'ColorCalibrationMatrix' => 'Matrice de calibration de la couleur',
   'ColorCharacterization' => 'Caractérisation de la couleur',
   'ColorCompensationFilter' => 'Filtre de compensation des couleurs',
   'ColorCompensationFilterCustom' => 'Filtre de compensation des couleurs personnalisé',
   'ColorCompensationFilterSet' => 'Réglage du filtre de compensation des couleurs',
   'ColorComponents' => 'Composants colorimétriques',
   'ColorControl' => 'Contrôle des couleurs',
   'ColorDataVersion' => 'Version des données de couleur',
   'ColorEffect' => {
      Description => 'Effet de couleurs',
      PrintConv => {
        'Black & White' => 'Noir et blanc',
        'Cool' => 'Frais',
        'Happy' => 'Joyeux',
        'Off' => 'Désactivé',
        'Sepia' => 'Sépia',
        'Vivid' => 'Éclatant',
        'Warm' => 'Chaud',
      },
    },
   'ColorFilter' => {
      Description => 'Filtre de couleur',
      PrintConv => {
        'Blue' => 'Bleu',
        'Full' => 'Plein',
        'Green' => 'Vert',
        'Off' => 'Désactivé',
        'Pink' => 'Rose',
        'Purple' => 'Violet',
        'Red' => 'Rouge',
        'Sepia' => 'Sépia',
        'Yellow' => 'Jaune',
      },
    },
   'ColorHue' => 'Teinte de couleur',
   'ColorInfo' => 'Info couleur',
   'ColorMap' => 'Charte de couleur',
   'ColorMatrix' => {
      Description => 'Matrice des couleurs',
      PrintConv => {
        'EOS Original' => 'Originale EOS',
        'Neutral' => 'Neutre',
      },
    },
   'ColorMatrix1' => 'Matrice de couleurs 1',
   'ColorMatrix2' => 'Matrice de couleurs 2',
   'ColorMatrix3' => 'Matrice de couleurs 3',
   'ColorMatrixA' => 'Matrice de couleurs A',
   'ColorMatrixA2' => 'Matrice de couleurs A2',
   'ColorMatrixAdobeRGB' => 'Matrice de couleurs AdobeRVB',
   'ColorMatrixB' => 'Matrice de couleurs B',
   'ColorMatrixB2' => 'Matrice de couleurs B2',
   'ColorMatrixNumber' => 'Numéro de la matrice de couleurs',
   'ColorMatrixSRGB' => 'Matrice de couleurs SRVB',
   'ColorMode' => {
      Description => 'Mode colorimétrique',
      PrintConv => {
        'Adobe RGB' => 'Adobe RVB',
        'Autumn Leaves' => 'Feuilles automne',
        'B & W' => 'N & B',
        'B&W' => 'N&B',
        'B&W Red Filter' => 'Filtre rouge N&B',
        'B&W Yellow Filter' => 'Filtre jaune N&B',
        'Bitmap' => 'Matriciel',
        'Black & White' => 'Noir & Blanc',
        'CMYK' => 'CMJN',
        'Clear' => 'Clair',
        'Color Palette' => 'Palette de couleurs',
        'Deep' => 'Profond',
        'Embed Adobe RGB' => 'Adobe RVB intégré',
        'Evening' => 'Soir',
        'FOV Classic Blue' => 'FOV Bleu classique',
        'Grayscale' => 'Niveaux de gris',
        'Indexed' => 'Indexé',
        'Indexed Color' => 'Couleurs indexées',
        'Landscape' => 'Paysage',
        'Light' => 'Lumière',
        'Multichannel' => 'Multicanal',
        'Natural' => 'Naturel',
        'Natural color' => 'Couleur naturelle',
        'Natural sRGB' => 'SRVB naturel',
        'Natural+ sRGB' => 'SRVB naturel+',
        'Neutral' => 'Neutre',
        'Neutral Color' => 'Couleur neutre',
        'Night Portrait' => 'Portrait nocturne',
        'Night Scene' => 'Scène nocturne',
        'Night View' => 'Vue nocturne',
        'Night View/Portrait' => 'Vue/Portrait nocturne',
        'Normal' => 'Normale',
        'Off' => 'Désactivé',
        'RGB' => 'RVB',
        'RGB Color' => 'Couleur RVB',
        'Saturated Color' => 'Couleurs saturées',
        'Solarization' => 'Solarisation',
        'Sunset' => 'Coucher de soleil',
        'Vivid' => 'Éclatant',
        'Vivid 2' => 'Éclatant 2',
        'Vivid color' => 'Couleurs éclatantes',
        'n/a' => 'Non applicable',
      },
    },
   'ColorMoireReduction' => {
      Description => 'Réduction du moiré des couleurs',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'ColorMoireReductionMode' => {
      Description => 'Mode de réduction du moiré de couleur',
      PrintConv => {
        'High' => 'Haut',
        'Low' => 'Bas',
        'Medium' => 'Moyen',
        'Off' => 'Désactivé',
      },
    },
   'ColorNoiseReduction' => 'Réduction du bruit de la couleur',
   'ColorNoiseReductionDetail' => 'Détail de la réduction du bruit de la couleur',
   'ColorNoiseReductionIntensity' => 'Réduction du bruit d\'intensité de la couleur',
   'ColorNoiseReductionSharpness' => 'Réduction du bruit de netteté de la couleur',
   'ColorNoiseReductionSmoothness' => 'Réduction du bruit lissé de la couleur',
   'ColorPalette' => 'Palette de couleur',
   'ColorRepresentation' => {
      Description => 'Représentation de couleur',
      PrintConv => {
        '3 Components, Frame Sequential in Multiple Objects' => 'Trois composantes, Vue séquentielle dans différents objets',
        '3 Components, Frame Sequential in One Object' => 'Trois composantes, Vue séquentielle dans un objet',
        '3 Components, Line Sequential' => 'Trois composantes, Ligne séquentielle',
        '3 Components, Pixel Sequential' => 'Trois composantes, Pixel séquentiel',
        '3 Components, Single Frame' => 'Trois composantes, Vue unique',
        '3 Components, Special Interleaving' => 'Trois composantes, Entrelacement spécial',
        '4 Components, Frame Sequential in Multiple Objects' => 'Quatre composantes, Vue séquentielle dans différents objets',
        '4 Components, Frame Sequential in One Object' => 'Quatre composantes, Vue séquentielle dans un objet',
        '4 Components, Line Sequential' => 'Quatre composantes, Ligne séquentielle',
        '4 Components, Pixel Sequential' => 'Quatre composantes, Pixel séquentiel',
        '4 Components, Single Frame' => 'Quatre composantes, Vue unique',
        '4 Components, Special Interleaving' => 'Quatre composantes, Entrelacement spécial',
        'Monochrome, Single Frame' => 'Monochrome, Vue unique',
        'No Image, Single Frame' => 'Pas d\'image, Vue unique',
      },
    },
   'ColorResponseUnit' => 'Unité de réponse de la couleur',
   'ColorSequence' => 'Séquence de couleur',
   'ColorSpace' => {
      Description => 'Espace colorimétrique',
      PrintConv => {
        'Adobe RGB' => 'Adobe RVB',
        'Adobe RGB (A700)' => 'Adobe RVB (A700)',
        'Adobe RGB (ICC)' => 'Adobe RVB (ICC)',
        'B&W' => 'N&B',
        'ICC Profile' => 'Profil ICC',
        'RGB' => 'RVB',
        'Uncalibrated' => 'Non calibré',
        'Wide Gamut RGB' => 'Wide Gamut RVB',
        'n/a' => 'Non applicable',
        'sRGB' => 'sRVB',
      },
    },
   'ColorSpaceData' => 'Espace de couleur des données',
   'ColorSpaceName' => 'Nom de l\'espace couleur',
   'ColorSpecApproximation' => {
      Description => 'Approximation de la spécification des couleurs',
      PrintConv => {
        'Accurate' => 'Précise',
        'Exceptional Quality' => 'Qualité exceptionnelle',
        'Not Specified' => 'Non spécifié',
        'Poor Quality' => 'Qualité médiocre',
        'Reasonable Quality' => 'Qualité raisonnable',
      },
    },
   'ColorTable' => 'Tableau de couleurs',
   'ColorTempAsShot' => 'Température couleur telle que capturée',
   'ColorTempAuto' => 'Température couleur Automatique',
   'ColorTempCloudy' => 'Température couleur Nuageux',
   'ColorTempCustom' => 'Température couleur personnalisée',
   'ColorTempCustom1' => 'Température couleur personnalisée 1',
   'ColorTempCustom2' => 'Température couleur personnalisée 2',
   'ColorTempDaylight' => 'Température couleur Lumière du jour',
   'ColorTempFlash' => 'Température couleur Flash',
   'ColorTempFluorescent' => 'Température couleurs Fluorescent',
   'ColorTempFluorescentD' => 'Température couleurs Fluorescent D',
   'ColorTempFluorescentN' => 'Température couleurs Fluorescent N',
   'ColorTempFluorescentW' => 'Température couleurs Fluorescent W',
   'ColorTempKelvin' => 'Température couleur Kelvin',
   'ColorTempMeasured' => 'Température couleur mesurée',
   'ColorTempPC1' => 'Température couleur PC 1',
   'ColorTempPC2' => 'Température couleur PC 2',
   'ColorTempPC3' => 'Température couleur PC 3',
   'ColorTempShade' => 'Température couleur Ombre',
   'ColorTempTungsten' => 'Température couleur Tungstène',
   'ColorTemperature' => 'Température couleur',
   'ColorTone' => {
      Description => 'Teinte couleur',
      PrintConv => {
        'Normal' => 'Normale',
      },
    },
   'ColorToneAuto' => {
      Description => 'Tonalité couleur Auto',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'ColorToneFaithful' => {
      Description => 'Tonalité couleur Fidèle',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'ColorToneLandscape' => {
      Description => 'Tonalité couleur Paysage',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'ColorToneMonochrome' => {
      Description => 'Tonalité couleur Monochrome',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'ColorToneNeutral' => {
      Description => 'Tonalité couleur Neutre',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'ColorTonePortrait' => {
      Description => 'Tonalité couleur Portrait',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'ColorToneStandard' => {
      Description => 'Tonalité couleur Standard',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'ColorToneUserDef1' => {
      Description => 'Tonalité couleur défini par l’utilisateur 1',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'ColorToneUserDef2' => {
      Description => 'Tonalité couleur défini par l’utilisateur 2',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'ColorToneUserDef3' => {
      Description => 'Tonalité couleur défini par l’utilisateur 3',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'ColorType' => {
      PrintConv => {
        'RGB' => 'RVB',
      },
    },
   'ColorantOrder' => 'Ordre de colorant',
   'ColorantTable' => 'Table de colorant',
   'ColorimetricReference' => 'Référence colorimétrique',
   'CommandDialsChangeMainSub' => {
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'CommandDialsMenuAndPlayback' => {
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'CommandDialsReverseRotation' => {
      PrintConv => {
        'No' => 'Non',
        'Yes' => 'Oui',
      },
    },
   'CommanderGroupAMode' => {
      PrintConv => {
        'Manual' => 'Manuelle',
        'Off' => 'Désactivé',
      },
    },
   'CommanderGroupBMode' => {
      PrintConv => {
        'Manual' => 'Manuelle',
        'Off' => 'Désactivé',
      },
    },
   'CommanderInternalFlash' => {
      PrintConv => {
        'Manual' => 'Manuelle',
        'Off' => 'Désactivé',
      },
    },
   'Comment' => 'Commentaire',
   'Comments' => 'Commentaires',
   'Compatibility' => 'Compatibilité',
   'CompatibleBrands' => 'Labels compatibles',
   'CompatibleFontName' => 'Nom de police compatible',
   'CompatibleVersion' => 'Version compatible',
   'Compilation' => {
      PrintConv => {
        'No' => 'Non',
        'Yes' => 'Oui',
      },
    },
   'ComponentBitDepth' => 'Profondeur de bits des composants',
   'ComponentsConfiguration' => {
      Description => 'Configuration des composants',
      PrintConv => {
        'Alpha (matte)' => 'Alpha (mat)',
        'Alpha, B, G, R' => 'Alpha, B, V, R',
        'Blue (B)' => 'Bleu (B)',
        'Chrominance (Cb, Cr, subsampled by two)' => 'Chrominance (Cb, Cr, sous-échantillonné par deux)',
        'Composite video' => 'Vidéo composite',
        'Depth (Z)' => 'Profondeur (Z)',
        'G' => 'V',
        'Green (G)' => 'Vert (V)',
        'R, G, B' => 'R, V, B',
        'R, G, B, Alpha' => 'R, V, B, Alpha',
        'Red (R)' => 'Rouge (R)',
        'User-defined 2 component element' => 'Élément à 2 composants défini par l\'utilisateur',
        'User-defined 3 component element' => 'Élément à 3 composants défini par l\'utilisateur',
        'User-defined 4 component element' => 'Élément à 4 composants défini par l\'utilisateur',
        'User-defined 5 component element' => 'Élément à 5 composants défini par l\'utilisateur',
        'User-defined 6 component element' => 'Élément à 6 composants défini par l\'utilisateur',
        'User-defined 7 component element' => 'Élément à 7 composants défini par l\'utilisateur',
        'User-defined 8 component element' => 'Élément à 8 composants défini par l\'utilisateur',
        'User-defined single component' => 'Composant unique défini par l\\utilisateur',
      },
    },
   'CompositeImage' => {
      Description => 'Image composite',
      PrintConv => {
        'Composite Image Captured While Shooting' => 'Image composite capturée pendant la prise de vue',
        'General Composite Image' => 'Image composite générale',
        'Not a Composite Image' => 'N\'est pas une image composite',
        'Unknown' => 'Inconnue',
      },
    },
   'CompressedBitsPerPixel' => 'Mode de compression de l\'image',
   'CompressedDataLength' => 'Longuuer des données compressées',
   'CompressedDataOffset' => 'Décalage des données compressées',
   'Compression' => {
      Description => 'Schéma de compression',
      PrintConv => {
        '4-Bit RLE' => '4 Bits RLE',
        '8-Bit RLE' => '8 Bits RLE',
        'Aperio JPEG 2000 RGB' => 'Aperio JPEG 2000 RVB',
        'Bitfields' => 'Champs de bits',
        'Deflated' => 'Deflaté',
        'Huffman-coded baseline JPEG' => 'JPEG de base à codage Huffman',
        'JBIG B&W' => 'JBIG N&B',
        'JBIG Color' => 'JBIG Couleur',
        'JPEG' => 'Compression JPEG',
        'JPEG (old-style)' => 'JPEG (ancien schéma)',
        'JPEG-LS' => 'JPEG LS',
        'Kodak DCR Compressed' => 'Compressé avec Kodak DCR',
        'Kodak KDC Compressed' => 'Compressé avec Kodak KDC',
        'Lossy JPEG' => 'JPEG avec pertes',
        'Microsoft Document Imaging (MDI) Binary Level Codec' => 'Microsoft Document Imaging (MDI) Codec de niveau binaire',
        'Microsoft Document Imaging (MDI) Progressive Transform Codec' => 'Microsoft Document Imaging (MDI) Codec de transformation progressive',
        'Microsoft Document Imaging (MDI) Vector' => 'Microsoft Document Imaging (MDI) Vectoriel',
        'Modified Huffman' => 'Huffman modifié',
        'Modified Modified READ' => 'LECTURE Modifiée Modifiée',
        'Modified READ' => 'LECTURE Modifiée',
        'Next' => 'Encodage NeXT 2 bits',
        'Nikon NEF Compressed' => 'Nikon NEF compressé',
        'None' => 'Aucun',
        'Packed RAW' => 'RAW encapsulé',
        'Panasonic RAW 1' => 'RAW 1 Panasonic',
        'Panasonic RAW 2' => 'RAW 2 Panasonic',
        'Panasonic RAW 3' => 'RAW 3 Panasonic',
        'Panasonic RAW 4' => 'RAW 4 Panasonic',
        'Pentax PEF Compressed' => 'Compression Pentax PEF',
        'PixarFilm' => 'Film Pixar',
        'PixarLog' => 'Journal Pixar',
        'RLE Encoding' => 'Encodage RLE',
        'SGILog' => 'Encodage Log luminance SGI 32 bits',
        'SGILog24' => 'Encodage Log luminance SGI 24 bits',
        'Samsung SRW Compressed' => 'Samsung SRW Compressé',
        'Samsung SRW Compressed 2' => 'Samsung SRW Compressé 2',
        'Sony ARW Compressed' => 'Sony ARW Compressé',
        'T4/Group 3 Fax' => 'T4/Groupe 3 Fax',
        'T6/Group 4 Fax' => 'T6/Groupe 4 Fax',
        'Thunderscan' => 'Encodage ThunderScan 4 bits',
        'Uncompressed' => 'Non compressé',
        'Uncompressed, interleaved, 8 bits per sample' => 'Non compressé, entrelacé, 8 bits par échantillon',
        'ZIP with prediction' => 'ZIP avec prédiction',
        'ZIP without prediction' => 'ZIP sans prédiction',
      },
    },
   'CompressionType' => {
      PrintConv => {
        'None' => 'Aucune',
      },
    },
   'ConditionalFEC' => 'Compensation exposition flash',
   'Confidence' => 'Confiance',
   'ConfidenceLevel' => 'Niveau de confiance',
   'ConnectionSpaceIlluminant' => 'Illuminant d\'espace de connexion',
   'ConsecutiveBadFaxLines' => 'Mauvaises lignes de Fax consécutives',
   'ConstantFrameRate' => {
      Description => 'Fréquence d\'images constante',
      PrintConv => {
        'Constant Frame Rate' => 'Fréquence d\'images constante',
        'Each Temporal Layer is Constant Frame Rate' => 'Chaque couche temporelle a une fréquence d\'images constante',
        'Unknown' => 'Inconnue',
      },
    },
   'ConstraintIndicatorFlags' => 'Indicateurs de contrainte',
   'ContentLocationCode' => 'Code du lieu du contenu',
   'ContentLocationName' => 'Nom du lieu du contenu',
   'ContinuousDrive' => {
      Description => 'Entraînement continu',
      PrintConv => {
        'Continuous' => 'Continu',
        'Continuous, High' => 'Continu, Haut',
        'Continuous, High+' => 'Continu, Haut+',
        'Continuous, Low' => 'Continu, bas',
        'Continuous, Silent' => 'Continu, silencieux',
        'Continuous, Speed Priority' => 'Continu, Priorité à la vitesse',
        'Movie' => 'Vidéo',
        'Silent Single' => 'Simple silencieux',
        'Single' => 'Simple',
        'Single, Silent' => 'Simple, Silencieux',
      },
    },
   'ContinuousShootingSpeed' => {
      Description => 'Vitesse de prise de vues en continu',
      PrintConv => {
        'Disable' => 'Désactivé',
        'Enable' => 'Activée',
      },
    },
   'ContinuousShotLimit' => {
      Description => 'Limiter nombre de vues en continu',
      PrintConv => {
        'Disable' => 'Désactivé',
        'Enable' => 'Activée',
      },
    },
   'Contrast' => {
      Description => 'Contraste',
      PrintConv => {
        '+1 (medium high)' => '+1 (moyen fort)',
        '+2 (high)' => '+2 (fort)',
        '+3 (very high)' => '+3 (très fort)',
        '+4 (maximum)' => '+4 (maximal)',
        '-1 (medium low)' => '-1 (moyen faible)',
        '-2 (low)' => '-2 (faible)',
        '-3 (very low)' => '-3 (très faible)',
        '-4 (minimum)' => '4 (minimal)',
        '0 (normal)' => '0 (Normal)',
        'Film Simulation' => 'Simulation de film',
        'High' => 'Fort',
        'Low' => 'Faible',
        'Medium High' => 'Moyen fort',
        'Medium Low' => 'Moyen faible',
        'Normal' => 'Normale',
        'n/a' => 'Non applicable',
      },
    },
   'ContrastAuto' => {
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'ContrastCurve' => 'Courbe de contraste',
   'ContrastFaithful' => {
      Description => 'Fidélité du contraste',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'ContrastHighlight' => 'Mise en lumière des contrastes',
   'ContrastLandscape' => {
      Description => 'Contraste Paysage',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'ContrastMode' => {
      Description => 'Mode Contraste',
      PrintConv => {
        'Cinema' => 'Cinéma',
        'Cross Process' => 'Traitement croisé',
        'Cross Process 2' => 'Traitement croisé 2',
        'Dynamic (B&W Film)' => 'Dynamique (Film N&B)',
        'Dynamic (Color Film)' => 'Dynamique (Film couleur)',
        'Dynamic Art (My Color)' => 'Art dynamique (My Color)',
        'Dynamic Mono' => 'Dynamique Mono',
        'Dynamic Monochrome' => 'Dynamique Monochrome',
        'Dynamic Monochrome 2' => 'Dynamique Monochrome 2',
        'Dynamic Range (film-like)' => 'Plage dynamique (similaire à celle d\'un film)',
        'Elegant (My Color)' => 'Élégant (My Color)',
        'Expressive' => 'Expressif',
        'Expressive 2' => 'Expressif 2',
        'Fantasy' => 'Fantaisie',
        'High' => 'Haut',
        'High Dynamic' => 'Haute dynamique',
        'High Dynamic 2' => 'Haute dynamique 2',
        'High Key 2' => 'Tons clairs 2',
        'Impressive Art' => 'Art impressionnant',
        'Impressive Art 2' => 'Art impressionnant 2',
        'Low' => 'Bas',
        'Low Key' => 'Tons sombres',
        'Low Key 2' => 'Tons sombres 2',
        'Match Filter Effects Toy' => 'Jouet à effets de filtres assortis',
        'Match Photo Style L. Monochrome' => 'Correspondre au style de photo L. Monochrome',
        'Medium High' => 'Moyennement élevé',
        'Medium Low' => 'Moyennement faible',
        'Nature (Color Film)' => 'Nature (film couleur)',
        'Nostalgic (Color Film)' => 'Nostalgique (Film couleur)',
        'Old Days' => 'Vieux jours',
        'Retro' => 'Rétro',
        'Retro (My Color)' => 'Rétro (My Color)',
        'Retro 2' => 'Rétro 2',
        'Sepia' => 'Sépia',
        'Smooth (B&W Film)' => 'Doux (Film Noir & Blanc)',
        'Smooth (Color Film) or Pure (My Color)' => 'Doux (Film couleur) ou Pur (My Color)',
        'Toy Effect' => 'Effet jouet',
        'Toy Effect 2' => 'Effet jouet 2',
        'Toy Pop' => 'Jouet Pop',
        'Vibrant (Color Film) or Expressive (My Color)' => 'Vibrant (Film couleur) ou expressif (My Color)',
      },
    },
   'ContrastMonochrome' => {
      Description => 'Contraste Monochrome',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'ContrastNeutral' => {
      Description => 'Contraste Neutre',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'ContrastPortrait' => {
      Description => 'Contraste Portrait',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'ContrastSetting' => 'Réglage du contraste',
   'ContrastShadow' => 'Contraste de l\'ombre',
   'ContrastStandard' => {
      Description => 'Contraste Standard',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'ContrastUserDef1' => {
      Description => 'Contraste défini par l\'utilisateur 1',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'ContrastUserDef2' => {
      Description => 'Contraste défini par l\'utilisateur 2',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'ContrastUserDef3' => {
      Description => 'Contraste défini par l\'utilisateur 3',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'Contributor' => 'Contributeur',
   'ControlMode' => {
      Description => 'Mode de contrôle',
      PrintConv => {
        'Camera Local Control' => 'Commande locale de la caméra',
        'Computer Remote Control' => 'Télécommande de l\'ordinateur',
        'n/a' => 'Non applicable',
      },
    },
   'ConversionLens' => {
      Description => 'Lentille de conversion',
      PrintConv => {
        'Off' => 'Désactivée',
        'Telephoto' => 'Téléobjectif',
        'Wide' => 'Grand angle',
      },
    },
   'Copyright' => 'Propriétaire du copyright',
   'CopyrightNotice' => 'Mention de copyright',
   'CopyrightStatus' => {
      PrintConv => {
        'Unknown' => 'Inconnu',
      },
    },
   'CoringFilter' => 'Filtre de carrotage',
   'CoringValues' => 'Valeurs du filtre de carrotage',
   'Country' => 'Pays',
   'Country-PrimaryLocationCode' => 'Code de pays ISO',
   'Country-PrimaryLocationName' => 'Pays',
   'CountryCode' => 'Code pays',
   'Coverage' => 'Couverture',
   'CreateDate' => 'Date de création des données numériques',
   'CreationDate' => 'Date de création',
   'CreativeStyle' => {
      Description => 'Style créatif',
      PrintConv => {
        'Adobe RGB' => 'Adobe RVB',
        'Autumn Leaves' => 'Feuilles d\'automne',
        'B&W' => 'N&B',
        'Clear' => 'Clair',
        'Deep' => 'Profond',
        'Landscape' => 'Paysage',
        'Light' => 'Lumière',
        'Neutral' => 'Neutre',
        'Night View/Portrait' => 'Vue/Portrait nocturne',
        'None' => 'Aucun',
        'Real' => 'Réel',
        'Sepia' => 'Sépia',
        'Sunset' => 'Coucher de soleil',
        'Vivid' => 'Éclatant',
        'Vivid 2' => 'Éclatant 2',
      },
    },
   'CreativeStyleSetting' => {
      Description => 'Réglage du style créatif',
      PrintConv => {
        'B&W' => 'N&B',
        'Landscape' => 'Paysage',
        'Sunset' => 'Coucher de soleil',
        'Vivid' => 'Éclatant',
      },
    },
   'CreativeStyleWasChanged' => {
      Description => 'Le style créatif a été modifié',
      PrintConv => {
        'No' => 'Non',
        'Yes' => 'Oui',
      },
    },
   'Creator' => 'Créateur',
   'CreatorAddress' => 'Adresse du créateur',
   'CreatorAppVersion' => 'Version de l\'application du créateur',
   'CreatorApplication' => 'Application du créateur',
   'CreatorBuildNumber' => 'Numéro de build du créateur',
   'CreatorBuildNumber2' => 'Numéro de build 2 du créateur',
   'CreatorCity' => 'Ville du créateur',
   'CreatorContactInfo' => 'Informations de contact du créateur',
   'CreatorCountry' => 'Pays du créateur',
   'CreatorIdentifier' => 'Identifiant du créateur',
   'CreatorMajorVersion' => 'Version majeure du créateur',
   'CreatorMinorVersion' => 'Version mineure du créateur',
   'CreatorName' => 'Nom du créateur',
   'CreatorPostalCode' => 'Code postal du créateur',
   'CreatorRegion' => 'Région du créateur',
   'CreatorRole' => 'Rôle du créateur',
   'CreatorSoftware' => 'Logiciel du créateur',
   'CreatorTool' => 'Outil de création',
   'CreatorWorkEmail' => 'Courriel professionnel du créateur',
   'CreatorWorkTelephone' => 'Téléphone professionnel du créateur',
   'CreatorWorkURL' => 'URL professionnelle du créateur',
   'Credit' => 'Fournisseur',
   'CropActive' => {
      PrintConv => {
        'No' => 'Non',
        'Yes' => 'Oui',
      },
    },
   'CropBottom' => 'Recadrage inférieur',
   'CropH' => 'Hauteur de recadrage',
   'CropHeight' => 'Hauteur de recadrage',
   'CropHiSpeed' => {
      Description => 'Recadrage haute vitesse',
      PrintConv => {
        '1.3x Crop' => 'Recadrage 1.3x',
        '1.3x Movie Crop' => 'Recadrage vidéo 1.3x',
        '1.4x Movie Crop' => 'Recadrage vidéo 1.4x',
        '1.5x Movie Crop' => 'Recadrage vidéo 1.5x',
        '16:9 Crop' => 'Recadrage 16:9',
        '1:1 Crop' => 'Recadrage 1:1',
        '2.7x Crop' => 'Recadrage 2.7x',
        '2.8x Movie Crop' => 'Recadrage vidéo 2.8x',
        '3:2 Crop' => 'Recadrage 3:2',
        '5:4 Crop' => 'Recadrage 5:4',
        'DX Crop' => 'Recadrage DX',
        'DX Movie Crop' => 'Recadrage vidéo DX',
        'DX Uncropped' => 'DX non recadré',
        'FX Uncropped' => 'FX non recadré',
        'Off' => 'Désactivé',
      },
    },
   'CropLeft' => 'Recadrage gauche',
   'CropMode' => {
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'CropOriginalHeight' => 'Hauteur original du recadrage ',
   'CropOriginalWidth' => 'Largeur originale du recadrage',
   'CropOutputHeight' => 'Hauteur du recadrage en sortie',
   'CropOutputHeightInches' => 'Hauteur du recadrage en sortie en pouces',
   'CropOutputPixels' => 'Pixels de recadrage en sortie',
   'CropOutputResolution' => 'Résolution du recadrage de la sortie',
   'CropOutputScale' => 'Échelle du recadrage en sortie',
   'CropOutputWidth' => 'Largeur du recadrage en sortie',
   'CropOutputWidthInches' => 'Largeur du recadrage en sortie en pouces',
   'CropRight' => 'Recadrage droite',
   'CropRightMargin' => 'Recadrage de la marge droite',
   'CropRotatedOriginalHeight' => 'Hauteur originale du recadrage de la rotation',
   'CropRotatedOriginalWidth' => 'Largeur originale du recadrage de la rotation',
   'CropRotation' => 'Recadrage de la rotation',
   'CropScaledResolution' => 'Résolution du recadrage mis à l\'échelle',
   'CropSourceResolution' => 'Résolution du recadrage de la source',
   'CropTop' => 'Recadrage supérieur',
   'CropTopMargin' => 'Recadrage de la marge supérieure',
   'CropUnit' => {
      Description => 'Unité de recadrage',
      PrintConv => {
        'inches' => 'Pouce',
      },
    },
   'CropUnits' => {
      PrintConv => {
        'inches' => 'Pouce',
      },
    },
   'CropW' => 'Largeur de recadrage',
   'CropWidth' => 'Largeur de recadrage',
   'CroppedImageHeight' => 'Hauteur de l\'image recadrée',
   'CroppedImageLeft' => 'Image recadrée gauche',
   'CroppedImageTop' => 'Image recadrée haut',
   'CroppedImageWidth' => 'Largeur de l\'image recadrée',
   'Cropping' => {
      Description => 'Recadrage',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'CurrentICCProfile' => 'Profil ICC actuel',
   'CurrentIPTCDigest' => 'Sommaire IPTC courant',
   'CurrentNumberInSequence' => 'Numéro actuel en séquence',
   'CurrentPatientLocation' => 'Emplacement actuel du patient',
   'CurrentPreProfileMatrix' => 'Matrice de pré-profil actuelle',
   'CurrentRepeatNumber' => 'Numéro de répétition actuel',
   'CurrentTime' => 'Heure courante',
   'CurrentUser' => 'Utilisateur courant',
   'CurrentVersion' => 'Version courante',
   'CursorSize' => 'Taille du curseur',
   'Curves' => {
      Description => 'Courbes',
      PrintConv => {
        'Off' => 'Désactivée',
        'On' => 'Activeé',
      },
    },
   'CustomRendered' => {
      Description => 'Traitement de l\'image',
      PrintConv => {
        'Custom' => 'Traitement personnalisé',
        'HDR (no original saved)' => 'HDR (pas d\'original enregistré)',
        'HDR (original saved)' => 'HDR (original enregistré)',
        'Normal' => 'Traitement normal',
        'Original (for HDR)' => 'Original (pour HDR)',
      },
    },
   'CustomSaturation' => 'Saturation personnalisée',
   'CustomSettingsAllDefault' => {
      Description => 'Réglages de saturation personnalisée par défaut',
      PrintConv => {
        'No' => 'Non',
        'Yes' => 'Oui',
      },
    },
   'D-LightingHQ' => {
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'D-LightingHQColorBoost' => 'D-Lighting HQ Boost couleur',
   'D-LightingHQHighlight' => 'D-Lighting HQ Boost éclairage',
   'D-LightingHQSelected' => {
      Description => 'D-Lighting haute Qualité sélectionné',
      PrintConv => {
        'No' => 'Non',
        'Yes' => 'Oui',
      },
    },
   'D-LightingHQShadow' => 'D-Lighting HQ Ombre',
   'D-LightingHS' => {
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'D-LightingHSAdjustment' => 'Réglage lumière du jour HS',
   'D-LightingHSColorBoost' => 'Lumière du jour HS boost couleur',
   'DECPosition' => {
      PrintConv => {
        'Contrast' => 'Contraste',
        'Exposure' => 'Exposition',
        'Filter' => 'Fitre',
      },
    },
   'DNGBackwardVersion' => 'Version DNG antérieure',
   'DNGLensInfo' => 'Distance focale minimale',
   'DNGVersion' => 'Version DNG',
   'DOF' => 'Profondeur de champ',
   'DSPFirmwareVersion' => 'Version de firmware de DSP',
   'DarkFocusEnvironment' => {
      Description => 'Environnement de la focalisation sur le noir',
      PrintConv => {
        'No' => 'Non',
        'Yes' => 'Oui',
      },
    },
   'DataCompressionMethod' => 'Fournisseur/propriétaire de l\'algorithme de compression de données',
   'DataDump' => 'Dump de données',
   'DataDump2' => 'Dump de données 2',
   'DataImprint' => {
      PrintConv => {
        'None' => 'Aucune',
        'Text' => 'Texte',
      },
    },
   'DataScaling' => 'Échelle des données',
   'DataType' => 'Type de données',
   'DateCreated' => 'Date de création',
   'DateDisplayFormat' => {
      Description => 'Format date',
      PrintConv => {
        'D/M/Y' => 'Jour/Mois/Année',
        'M/D/Y' => 'Mois/Jour/Année',
        'Y/M/D' => 'Année/Mois/Jour',
      },
    },
   'DateSent' => 'Date d\'envoi',
   'DateStampMode' => {
      Description => 'Mode Horodatage',
      PrintConv => {
        'Date & Time' => 'Date et heure',
        'Date Counter' => 'Compteur de date',
        'Off' => 'Désactivé',
      },
    },
   'DateTime' => 'Date de modification du fichier',
   'DateTimeCreated' => 'Date et heure de création',
   'DateTimeDigitized' => 'Date et heure de numérisation',
   'DateTimeOriginal' => 'Date de création des données originales',
   'DaylightSavings' => {
      Description => 'Heure d\'été',
      PrintConv => {
        'No' => 'Non',
        'Off' => 'Désactivée',
        'On' => 'Activée',
        'Yes' => 'Oui',
      },
    },
   'DecoderTableNumber' => 'Numéro de la table de décodage',
   'DefaultCropOrigin' => 'Origine du recadrage par défaut',
   'DefaultCropSize' => 'Taille du recadrage par défaut',
   'DefaultScale' => 'Echelle par défaut',
   'DeletedImageCount' => 'Compteur d\'images supprimées',
   'DependentImage1EntryNumber' => 'Numéro d\'entrée de l\'image dépendante 1',
   'DependentImage2EntryNumber' => 'Numéro d\'entrée de l\'image dépendante 2',
   'DestinationCity' => {
      Description => 'Ville de destination',
      PrintConv => {
        'Adelaide' => 'Adelaïde',
        'Algiers' => 'Alger',
        'Athens' => 'Athènes',
        'Beijing' => 'Pékin',
        'Cairo' => 'Le Caire',
        'Caracus' => 'Caracas',
        'Copenhagen' => 'Copenhague',
        'Jerusalem' => 'Jérusalem',
        'Kabul' => 'Kaboul',
        'Lisbon' => 'Lisbonne',
        'London' => 'Londre',
        'Manila' => 'Manille',
        'Moscow' => 'Moscou',
        'Noumea' => 'Nouméa',
        'Seoul' => 'Séoul',
        'Singapore' => 'Singapour',
        'Taipei' => 'Taïpei',
        'Tehran' => 'Téhéran',
        'Warsaw' => 'Varsovie',
        'Yangon' => 'Rangoun',
      },
    },
   'DestinationCityCode' => 'Code ville de destination',
   'DestinationDST' => {
      Description => 'Heure d\'été à destination',
      PrintConv => {
        'No' => 'Non',
        'Yes' => 'Oui',
      },
    },
   'DestinationID' => 'ID Destination',
   'DetailsOfCoefficients' => 'Détails des coefficients',
   'DetectedFaceBounds' => 'Limites de visage détectées',
   'DetectedFaceID' => 'ID Visage détecté',
   'DetectedFaceRollAngle' => 'Angle de roulis du visage détecté',
   'DetectedFaceYawAngle' => 'Angle de lacet du visage détecté',
   'Detector' => 'Détecteur',
   'DevelopmentDynamicRange' => 'Développement de la plage dynamique',
   'DeviceAttributes' => 'Attributs d\'appareil',
   'DeviceManufacturer' => 'Fabricant de l\'appareil',
   'DeviceManufacturerName' => 'Nom du fabricant de l\'appareil',
   'DeviceMfgDesc' => 'Description du fabricant de l\'appareil',
   'DeviceModel' => 'Modèle de l\'appareil',
   'DeviceModelDesc' => 'Description du modèle de l\'appareil',
   'DeviceName' => 'Nom de l\'appareil',
   'DeviceOrientation' => 'Orientation de l\'appareil',
   'DeviceParametersSets' => 'Jeux de paramètres de l\'appareil',
   'DeviceRelativeHeading' => 'Cap relatif de l\'appareil',
   'DeviceRelativePositionX' => 'Position relative en X de l\'appareil',
   'DeviceRelativePositionY' => 'Position relative en Y de l\'appareil',
   'DeviceRelativePositionZ' => 'Position relative en Z de l\'appareil',
   'DeviceRelativePositionalAccuracy' => 'Précision de la position relative de l\'appareil',
   'DeviceRelativeSpeed' => 'Vitesse relative de l\'appareil',
   'DeviceSequence' => 'Séquence de l\'appareil',
   'DeviceSerialNumber' => 'Numéro de série de l\'appareil',
   'DeviceSettingDescription' => 'Description des réglages de l\'appareil',
   'DeviceSettingDescriptionColumns' => 'Colonnes de description des paramètres de l\'appareil',
   'DeviceSettingDescriptionRows' => 'Lignes de description des paramètres de l\'appareil',
   'DeviceSettingDescriptionSettings' => 'Réglage de la description des paramètres de l\'appareil',
   'DeviceSettings' => 'Réglages de l\'appareil',
   'DeviceType' => {
      Description => 'Type d\'appareil',
      PrintConv => {
        'Cell Phone' => 'Téléphone portable',
        'Compact Digital Camera' => 'Appareil photo numérique compact',
        'HXM Video Camera' => 'Caméra vidéo HXM',
        'High-end NX Camera' => 'Appareil photo NX haut de gamme',
        'SMX Video Camera' => 'Caméra vidéo SMX',
      },
    },
   'DeviceUID' => 'UID de l\'appareil',
   'DeviceUsageDescription' => 'Description de l\'utilisation de l\'appareil',
   'DeviceVolume' => 'Volume de l\'appareil',
   'DialDirectionTvAv' => {
      Description => 'Sens rotation molette Tv/Av',
      PrintConv => {
        'Normal' => 'Normale',
        'Reversed' => 'Inversé',
      },
    },
   'DigitalCreationDate' => 'Date de création numérique',
   'DigitalCreationDateTime' => 'Date et heure de création numérique',
   'DigitalCreationTime' => 'Heure de création numérique',
   'DigitalFilter' => 'Filtre numérique',
   'DigitalFilter01' => {
      Description => 'Filtre numérique 01',
      PrintConv => {
        'Base Parameter Adjust' => 'Réglage des paramètres de base',
        'Bold Monochrome' => 'Monochrome gras',
        'Color Filter' => 'Filtre couleur',
        'Custom Filter' => 'Filtre personnalisé',
        'Extract Color' => 'Couleur de l\'extrait',
        'High Contrast' => 'Contraste élevé',
        'Invert Color' => 'Inversion de couleur',
        'Off' => 'Désactivé',
        'Posterization' => 'Postérisation',
        'Replace Color' => 'Remplacement de couleur',
        'Retro' => 'Rétro',
        'Shading' => 'Ombrage',
        'Sketch Filter' => 'Filtre d\'esquisse',
        'Slim' => 'Fin',
        'Soft Focus' => 'Flou artistique',
        'Starburst' => 'Éclats d\'étoiles',
        'Tone Expansion' => 'Expansion de la tonalité',
        'Toy Camera' => 'Caméra jouet',
        'Unicolor Bold' => 'Unicolore gras',
        'Water Color' => 'Aquarelle',
      },
    },
   'DigitalFilter02' => {
      Description => 'Filtre numérique 02',
      PrintConv => {
        'Base Parameter Adjust' => 'Réglage des paramètres de base',
        'Bold Monochrome' => 'Monochrome gras',
        'Color Filter' => 'Filtre couleur',
        'Custom Filter' => 'Filtre personnalisé',
        'Extract Color' => 'Couleur de l\'extrait',
        'High Contrast' => 'Contraste élevé',
        'Invert Color' => 'Inversion de couleur',
        'Off' => 'Désactivé',
        'Posterization' => 'Postérisation',
        'Replace Color' => 'Remplacement de couleur',
        'Retro' => 'Rétro',
        'Shading' => 'Ombrage',
        'Sketch Filter' => 'Filtre d\'esquisse',
        'Slim' => 'Fin',
        'Soft Focus' => 'Flou artistique',
        'Starburst' => 'Éclats d\'étoiles',
        'Tone Expansion' => 'Expansion de la tonalité',
        'Toy Camera' => 'Caméra jouet',
        'Unicolor Bold' => 'Unicolore gras',
        'Water Color' => 'Aquarelle',
      },
    },
   'DigitalFilter03' => {
      Description => 'Filtre numérique 03',
      PrintConv => {
        'Base Parameter Adjust' => 'Réglage des paramètres de base',
        'Bold Monochrome' => 'Monochrome gras',
        'Color Filter' => 'Filtre couleur',
        'Custom Filter' => 'Filtre personnalisé',
        'Extract Color' => 'Couleur de l\'extrait',
        'High Contrast' => 'Contraste élevé',
        'Invert Color' => 'Inversion de couleur',
        'Off' => 'Désactivé',
        'Posterization' => 'Postérisation',
        'Replace Color' => 'Remplacement de couleur',
        'Retro' => 'Rétro',
        'Shading' => 'Ombrage',
        'Sketch Filter' => 'Filtre d\'esquisse',
        'Slim' => 'Fin',
        'Soft Focus' => 'Flou artistique',
        'Starburst' => 'Éclats d\'étoiles',
        'Tone Expansion' => 'Expansion de la tonalité',
        'Toy Camera' => 'Caméra jouet',
        'Unicolor Bold' => 'Unicolore gras',
        'Water Color' => 'Aquarelle',
      },
    },
   'DigitalFilter04' => {
      Description => 'Filtre numérique 04',
      PrintConv => {
        'Base Parameter Adjust' => 'Réglage des paramètres de base',
        'Bold Monochrome' => 'Monochrome gras',
        'Color Filter' => 'Filtre couleur',
        'Custom Filter' => 'Filtre personnalisé',
        'Extract Color' => 'Couleur de l\'extrait',
        'High Contrast' => 'Contraste élevé',
        'Invert Color' => 'Inversion de couleur',
        'Off' => 'Désactivé',
        'Posterization' => 'Postérisation',
        'Replace Color' => 'Remplacement de couleur',
        'Retro' => 'Rétro',
        'Shading' => 'Ombrage',
        'Sketch Filter' => 'Filtre d\'esquisse',
        'Slim' => 'Fin',
        'Soft Focus' => 'Flou artistique',
        'Starburst' => 'Éclats d\'étoiles',
        'Tone Expansion' => 'Expansion de la tonalité',
        'Toy Camera' => 'Caméra jouet',
        'Unicolor Bold' => 'Unicolore gras',
        'Water Color' => 'Aquarelle',
      },
    },
   'DigitalFilter05' => {
      Description => 'Filtre numérique 05',
      PrintConv => {
        'Base Parameter Adjust' => 'Réglage des paramètres de base',
        'Bold Monochrome' => 'Monochrome gras',
        'Color Filter' => 'Filtre couleur',
        'Custom Filter' => 'Filtre personnalisé',
        'Extract Color' => 'Couleur de l\'extrait',
        'High Contrast' => 'Contraste élevé',
        'Invert Color' => 'Inversion de couleur',
        'Off' => 'Désactivé',
        'Posterization' => 'Postérisation',
        'Replace Color' => 'Remplacement de couleur',
        'Retro' => 'Rétro',
        'Shading' => 'Ombrage',
        'Sketch Filter' => 'Filtre d\'esquisse',
        'Slim' => 'Fin',
        'Soft Focus' => 'Flou artistique',
        'Starburst' => 'Éclats d\'étoiles',
        'Tone Expansion' => 'Expansion de la tonalité',
        'Toy Camera' => 'Caméra jouet',
        'Unicolor Bold' => 'Unicolore gras',
        'Water Color' => 'Aquarelle',
      },
    },
   'DigitalFilter06' => {
      Description => 'Filtre numérique 06',
      PrintConv => {
        'Base Parameter Adjust' => 'Réglage des paramètres de base',
        'Bold Monochrome' => 'Monochrome gras',
        'Color Filter' => 'Filtre couleur',
        'Custom Filter' => 'Filtre personnalisé',
        'Extract Color' => 'Couleur de l\'extrait',
        'High Contrast' => 'Contraste élevé',
        'Invert Color' => 'Inversion de couleur',
        'Off' => 'Désactivé',
        'Posterization' => 'Postérisation',
        'Replace Color' => 'Remplacement de couleur',
        'Retro' => 'Rétro',
        'Shading' => 'Ombrage',
        'Sketch Filter' => 'Filtre d\'esquisse',
        'Slim' => 'Fin',
        'Soft Focus' => 'Flou artistique',
        'Starburst' => 'Éclats d\'étoiles',
        'Tone Expansion' => 'Expansion de la tonalité',
        'Toy Camera' => 'Caméra jouet',
        'Unicolor Bold' => 'Unicolore gras',
        'Water Color' => 'Aquarelle',
      },
    },
   'DigitalFilter07' => {
      Description => 'Filtre numérique 07',
      PrintConv => {
        'Base Parameter Adjust' => 'Réglage des paramètres de base',
        'Bold Monochrome' => 'Monochrome gras',
        'Color Filter' => 'Filtre couleur',
        'Custom Filter' => 'Filtre personnalisé',
        'Extract Color' => 'Couleur de l\'extrait',
        'High Contrast' => 'Contraste élevé',
        'Invert Color' => 'Inversion de couleur',
        'Off' => 'Désactivé',
        'Posterization' => 'Postérisation',
        'Replace Color' => 'Remplacement de couleur',
        'Retro' => 'Rétro',
        'Shading' => 'Ombrage',
        'Sketch Filter' => 'Filtre d\'esquisse',
        'Slim' => 'Fin',
        'Soft Focus' => 'Flou artistique',
        'Starburst' => 'Éclats d\'étoiles',
        'Tone Expansion' => 'Expansion de la tonalité',
        'Toy Camera' => 'Caméra jouet',
        'Unicolor Bold' => 'Unicolore gras',
        'Water Color' => 'Aquarelle',
      },
    },
   'DigitalFilter08' => {
      Description => 'Filtre numérique 08',
      PrintConv => {
        'Base Parameter Adjust' => 'Réglage des paramètres de base',
        'Bold Monochrome' => 'Monochrome gras',
        'Color Filter' => 'Filtre couleur',
        'Custom Filter' => 'Filtre personnalisé',
        'Extract Color' => 'Couleur de l\'extrait',
        'High Contrast' => 'Contraste élevé',
        'Invert Color' => 'Inversion de couleur',
        'Off' => 'Désactivé',
        'Posterization' => 'Postérisation',
        'Replace Color' => 'Remplacement de couleur',
        'Retro' => 'Rétro',
        'Shading' => 'Ombrage',
        'Sketch Filter' => 'Filtre d\'esquisse',
        'Slim' => 'Fin',
        'Soft Focus' => 'Flou artistique',
        'Starburst' => 'Éclats d\'étoiles',
        'Tone Expansion' => 'Expansion de la tonalité',
        'Toy Camera' => 'Caméra jouet',
        'Unicolor Bold' => 'Unicolore gras',
        'Water Color' => 'Aquarelle',
      },
    },
   'DigitalFilter09' => {
      Description => 'Filtre numérique 09',
      PrintConv => {
        'Base Parameter Adjust' => 'Réglage des paramètres de base',
        'Bold Monochrome' => 'Monochrome gras',
        'Color Filter' => 'Filtre couleur',
        'Custom Filter' => 'Filtre personnalisé',
        'Extract Color' => 'Couleur de l\'extrait',
        'High Contrast' => 'Contraste élevé',
        'Invert Color' => 'Inversion de couleur',
        'Off' => 'Désactivé',
        'Posterization' => 'Postérisation',
        'Replace Color' => 'Remplacement de couleur',
        'Retro' => 'Rétro',
        'Shading' => 'Ombrage',
        'Sketch Filter' => 'Filtre d\'esquisse',
        'Slim' => 'Fin',
        'Soft Focus' => 'Flou artistique',
        'Starburst' => 'Éclats d\'étoiles',
        'Tone Expansion' => 'Expansion de la tonalité',
        'Toy Camera' => 'Caméra jouet',
        'Unicolor Bold' => 'Unicolore gras',
        'Water Color' => 'Aquarelle',
      },
    },
   'DigitalFilter10' => {
      Description => 'Filtre numérique 10',
      PrintConv => {
        'Base Parameter Adjust' => 'Réglage des paramètres de base',
        'Bold Monochrome' => 'Monochrome gras',
        'Color Filter' => 'Filtre couleur',
        'Custom Filter' => 'Filtre personnalisé',
        'Extract Color' => 'Couleur de l\'extrait',
        'High Contrast' => 'Contraste élevé',
        'Invert Color' => 'Inversion de couleur',
        'Off' => 'Désactivé',
        'Posterization' => 'Postérisation',
        'Replace Color' => 'Remplacement de couleur',
        'Retro' => 'Rétro',
        'Shading' => 'Ombrage',
        'Sketch Filter' => 'Filtre d\'esquisse',
        'Slim' => 'Fin',
        'Soft Focus' => 'Flou artistique',
        'Starburst' => 'Éclats d\'étoiles',
        'Tone Expansion' => 'Expansion de la tonalité',
        'Toy Camera' => 'Caméra jouet',
        'Unicolor Bold' => 'Unicolore gras',
        'Water Color' => 'Aquarelle',
      },
    },
   'DigitalFilter11' => {
      Description => 'Filtre numérique 11',
      PrintConv => {
        'Base Parameter Adjust' => 'Réglage des paramètres de base',
        'Bold Monochrome' => 'Monochrome gras',
        'Color Filter' => 'Filtre couleur',
        'Custom Filter' => 'Filtre personnalisé',
        'Extract Color' => 'Couleur de l\'extrait',
        'High Contrast' => 'Contraste élevé',
        'Invert Color' => 'Inversion de couleur',
        'Off' => 'Désactivé',
        'Posterization' => 'Postérisation',
        'Replace Color' => 'Remplacement de couleur',
        'Retro' => 'Rétro',
        'Shading' => 'Ombrage',
        'Sketch Filter' => 'Filtre d\'esquisse',
        'Slim' => 'Fin',
        'Soft Focus' => 'Flou artistique',
        'Starburst' => 'Éclats d\'étoiles',
        'Tone Expansion' => 'Expansion de la tonalité',
        'Toy Camera' => 'Caméra jouet',
        'Unicolor Bold' => 'Unicolore gras',
        'Water Color' => 'Aquarelle',
      },
    },
   'DigitalFilter12' => {
      Description => 'Filtre numérique 12',
      PrintConv => {
        'Base Parameter Adjust' => 'Réglage des paramètres de base',
        'Bold Monochrome' => 'Monochrome gras',
        'Color Filter' => 'Filtre couleur',
        'Custom Filter' => 'Filtre personnalisé',
        'Extract Color' => 'Couleur de l\'extrait',
        'High Contrast' => 'Contraste élevé',
        'Invert Color' => 'Inversion de couleur',
        'Off' => 'Désactivé',
        'Posterization' => 'Postérisation',
        'Replace Color' => 'Remplacement de couleur',
        'Retro' => 'Rétro',
        'Shading' => 'Ombrage',
        'Sketch Filter' => 'Filtre d\'esquisse',
        'Slim' => 'Fin',
        'Soft Focus' => 'Flou artistique',
        'Starburst' => 'Éclats d\'étoiles',
        'Tone Expansion' => 'Expansion de la tonalité',
        'Toy Camera' => 'Caméra jouet',
        'Unicolor Bold' => 'Unicolore gras',
        'Water Color' => 'Aquarelle',
      },
    },
   'DigitalFilter13' => {
      Description => 'Filtre numérique 13',
      PrintConv => {
        'Base Parameter Adjust' => 'Réglage des paramètres de base',
        'Bold Monochrome' => 'Monochrome gras',
        'Color Filter' => 'Filtre couleur',
        'Custom Filter' => 'Filtre personnalisé',
        'Extract Color' => 'Couleur de l\'extrait',
        'High Contrast' => 'Contraste élevé',
        'Invert Color' => 'Inversion de couleur',
        'Off' => 'Désactivé',
        'Posterization' => 'Postérisation',
        'Replace Color' => 'Remplacement de couleur',
        'Retro' => 'Rétro',
        'Shading' => 'Ombrage',
        'Sketch Filter' => 'Filtre d\'esquisse',
        'Slim' => 'Fin',
        'Soft Focus' => 'Flou artistique',
        'Starburst' => 'Éclats d\'étoiles',
        'Tone Expansion' => 'Expansion de la tonalité',
        'Toy Camera' => 'Caméra jouet',
        'Unicolor Bold' => 'Unicolore gras',
        'Water Color' => 'Aquarelle',
      },
    },
   'DigitalFilter14' => {
      Description => 'Filtre numérique 14',
      PrintConv => {
        'Base Parameter Adjust' => 'Réglage des paramètres de base',
        'Bold Monochrome' => 'Monochrome gras',
        'Color Filter' => 'Filtre couleur',
        'Custom Filter' => 'Filtre personnalisé',
        'Extract Color' => 'Couleur de l\'extrait',
        'High Contrast' => 'Contraste élevé',
        'Invert Color' => 'Inversion de couleur',
        'Off' => 'Désactivé',
        'Posterization' => 'Postérisation',
        'Replace Color' => 'Remplacement de couleur',
        'Retro' => 'Rétro',
        'Shading' => 'Ombrage',
        'Sketch Filter' => 'Filtre d\'esquisse',
        'Slim' => 'Fin',
        'Soft Focus' => 'Flou artistique',
        'Starburst' => 'Éclats d\'étoiles',
        'Tone Expansion' => 'Expansion de la tonalité',
        'Toy Camera' => 'Caméra jouet',
        'Unicolor Bold' => 'Unicolore gras',
        'Water Color' => 'Aquarelle',
      },
    },
   'DigitalFilter15' => {
      Description => 'Filtre numérique 15',
      PrintConv => {
        'Base Parameter Adjust' => 'Réglage des paramètres de base',
        'Bold Monochrome' => 'Monochrome gras',
        'Color Filter' => 'Filtre couleur',
        'Custom Filter' => 'Filtre personnalisé',
        'Extract Color' => 'Couleur de l\'extrait',
        'High Contrast' => 'Contraste élevé',
        'Invert Color' => 'Inversion de couleur',
        'Off' => 'Désactivé',
        'Posterization' => 'Postérisation',
        'Replace Color' => 'Remplacement de couleur',
        'Retro' => 'Rétro',
        'Shading' => 'Ombrage',
        'Sketch Filter' => 'Filtre d\'esquisse',
        'Slim' => 'Fin',
        'Soft Focus' => 'Flou artistique',
        'Starburst' => 'Éclats d\'étoiles',
        'Tone Expansion' => 'Expansion de la tonalité',
        'Toy Camera' => 'Caméra jouet',
        'Unicolor Bold' => 'Unicolore gras',
        'Water Color' => 'Aquarelle',
      },
    },
   'DigitalFilter16' => {
      Description => 'Filtre numérique 16',
      PrintConv => {
        'Base Parameter Adjust' => 'Réglage des paramètres de base',
        'Bold Monochrome' => 'Monochrome gras',
        'Color Filter' => 'Filtre couleur',
        'Custom Filter' => 'Filtre personnalisé',
        'Extract Color' => 'Couleur de l\'extrait',
        'High Contrast' => 'Contraste élevé',
        'Invert Color' => 'Inversion de couleur',
        'Off' => 'Désactivé',
        'Posterization' => 'Postérisation',
        'Replace Color' => 'Remplacement de couleur',
        'Retro' => 'Rétro',
        'Shading' => 'Ombrage',
        'Sketch Filter' => 'Filtre d\'esquisse',
        'Slim' => 'Fin',
        'Soft Focus' => 'Flou artistique',
        'Starburst' => 'Éclats d\'étoiles',
        'Tone Expansion' => 'Expansion de la tonalité',
        'Toy Camera' => 'Caméra jouet',
        'Unicolor Bold' => 'Unicolore gras',
        'Water Color' => 'Aquarelle',
      },
    },
   'DigitalFilter17' => {
      Description => 'Filtre numérique 17',
      PrintConv => {
        'Base Parameter Adjust' => 'Réglage des paramètres de base',
        'Bold Monochrome' => 'Monochrome gras',
        'Color Filter' => 'Filtre couleur',
        'Custom Filter' => 'Filtre personnalisé',
        'Extract Color' => 'Couleur de l\'extrait',
        'High Contrast' => 'Contraste élevé',
        'Invert Color' => 'Inversion de couleur',
        'Off' => 'Désactivé',
        'Posterization' => 'Postérisation',
        'Replace Color' => 'Remplacement de couleur',
        'Retro' => 'Rétro',
        'Shading' => 'Ombrage',
        'Sketch Filter' => 'Filtre d\'esquisse',
        'Slim' => 'Fin',
        'Soft Focus' => 'Flou artistique',
        'Starburst' => 'Éclats d\'étoiles',
        'Tone Expansion' => 'Expansion de la tonalité',
        'Toy Camera' => 'Caméra jouet',
        'Unicolor Bold' => 'Unicolore gras',
        'Water Color' => 'Aquarelle',
      },
    },
   'DigitalFilter18' => {
      Description => 'Filtre numérique 18',
      PrintConv => {
        'Base Parameter Adjust' => 'Réglage des paramètres de base',
        'Bold Monochrome' => 'Monochrome gras',
        'Color Filter' => 'Filtre couleur',
        'Custom Filter' => 'Filtre personnalisé',
        'Extract Color' => 'Couleur de l\'extrait',
        'High Contrast' => 'Contraste élevé',
        'Invert Color' => 'Inversion de couleur',
        'Off' => 'Désactivé',
        'Posterization' => 'Postérisation',
        'Replace Color' => 'Remplacement de couleur',
        'Retro' => 'Rétro',
        'Shading' => 'Ombrage',
        'Sketch Filter' => 'Filtre d\'esquisse',
        'Slim' => 'Fin',
        'Soft Focus' => 'Flou artistique',
        'Starburst' => 'Éclats d\'étoiles',
        'Tone Expansion' => 'Expansion de la tonalité',
        'Toy Camera' => 'Caméra jouet',
        'Unicolor Bold' => 'Unicolore gras',
        'Water Color' => 'Aquarelle',
      },
    },
   'DigitalFilter19' => {
      Description => 'Filtre numérique 19',
      PrintConv => {
        'Base Parameter Adjust' => 'Réglage des paramètres de base',
        'Bold Monochrome' => 'Monochrome gras',
        'Color Filter' => 'Filtre couleur',
        'Custom Filter' => 'Filtre personnalisé',
        'Extract Color' => 'Couleur de l\'extrait',
        'High Contrast' => 'Contraste élevé',
        'Invert Color' => 'Inversion de couleur',
        'Off' => 'Désactivé',
        'Posterization' => 'Postérisation',
        'Replace Color' => 'Remplacement de couleur',
        'Retro' => 'Rétro',
        'Shading' => 'Ombrage',
        'Sketch Filter' => 'Filtre d\'esquisse',
        'Slim' => 'Fin',
        'Soft Focus' => 'Flou artistique',
        'Starburst' => 'Éclats d\'étoiles',
        'Tone Expansion' => 'Expansion de la tonalité',
        'Toy Camera' => 'Caméra jouet',
        'Unicolor Bold' => 'Unicolore gras',
        'Water Color' => 'Aquarelle',
      },
    },
   'DigitalFilter20' => {
      Description => 'Filtre numérique 20',
      PrintConv => {
        'Base Parameter Adjust' => 'Réglage des paramètres de base',
        'Bold Monochrome' => 'Monochrome gras',
        'Color Filter' => 'Filtre couleur',
        'Custom Filter' => 'Filtre personnalisé',
        'Extract Color' => 'Couleur de l\'extrait',
        'High Contrast' => 'Contraste élevé',
        'Invert Color' => 'Inversion de couleur',
        'Off' => 'Désactivé',
        'Posterization' => 'Postérisation',
        'Replace Color' => 'Remplacement de couleur',
        'Retro' => 'Rétro',
        'Shading' => 'Ombrage',
        'Sketch Filter' => 'Filtre d\'esquisse',
        'Slim' => 'Fin',
        'Soft Focus' => 'Flou artistique',
        'Starburst' => 'Éclats d\'étoiles',
        'Tone Expansion' => 'Expansion de la tonalité',
        'Toy Camera' => 'Caméra jouet',
        'Unicolor Bold' => 'Unicolore gras',
        'Water Color' => 'Aquarelle',
      },
    },
   'DigitalGain' => 'Gain numérique',
   'DigitalImageGUID' => 'GUID de l\'image numérique',
   'DigitalSourceFileType' => 'Type du fichier de la source numérique',
   'DigitalZoom' => {
      Description => 'Zoom numérique',
      PrintConv => {
        'None' => 'Aucune',
        'Off' => 'Désactivé',
      },
    },
   'DigitalZoomOn' => {
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'DigitalZoomRatio' => 'Rapport de zoom numérique',
   'Directory' => 'Répertoire',
   'DirectoryIndex' => 'Index du répertoire',
   'DirectoryIndex2' => 'Index du répertoire 2',
   'DirectoryNumber' => 'Numéro du répertoire',
   'DisplaySize' => {
      PrintConv => {
        'Normal' => 'Normale',
      },
    },
   'DisplayUnits' => {
      PrintConv => {
        'inches' => 'Pouce',
      },
    },
   'DisplayXResolutionUnit' => {
      PrintConv => {
        'um' => 'µm (micromètre)',
      },
    },
   'DisplayYResolutionUnit' => {
      PrintConv => {
        'um' => 'µm (micromètre)',
      },
    },
   'DisplayedUnitsX' => {
      PrintConv => {
        'inches' => 'Pouce',
      },
    },
   'DisplayedUnitsY' => {
      PrintConv => {
        'inches' => 'Pouce',
      },
    },
   'DistortionCompensation' => {
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'DistortionControl' => {
      Description => 'Contrôle de la distorsion',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'DistortionCorrParams' => 'Paramètres de correction de la distorsion',
   'DistortionCorrParamsNumber' => {
      Description => 'Numéro du paramètre de correction de la distorsion',
      PrintConv => {
        '16 (Full-frame)' => '16 (plein cadre)',
      },
    },
   'DistortionCorrParamsPresent' => {
      Description => 'Paramètre de correction de la distorsion présent',
      PrintConv => {
        'No' => 'Non',
        'Yes' => 'Oui',
      },
    },
   'DistortionCorrection' => {
      Description => 'Correction de la distorsion',
      PrintConv => {
        'Applied' => 'Appliquée',
        'Auto fixed by lens' => 'Correction automatique réalisée par l\'objectif',
        'No correction params available' => 'Aucun paramètre de correction disponible',
        'None' => 'Aucune',
        'Off' => 'Désactivée',
        'On' => 'Activée',
      },
    },
   'DistortionCorrection2' => {
      Description => 'Correction de la distorsion 2',
      PrintConv => {
        'Off' => 'Désactivée',
        'On' => 'Activée',
      },
    },
   'DistortionCorrectionSetting' => {
      Description => 'Réglage de la correction de la distorsion',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
        'n/a' => 'Non applicable',
      },
    },
   'DistortionEffect' => {
      PrintConv => {
        'Shot Settings' => 'Paramétrage prise de vue',
      },
    },
   'DjVuVersion' => 'Version DjVu',
   'DocumentHistory' => 'Historique du document',
   'DocumentName' => 'Nom du document',
   'DocumentNotes' => 'Remarques sur le document',
   'DotRange' => 'Étendue de points',
   'DriveMode' => {
      Description => 'Mode de prise de vue',
      PrintConv => {
        'Burst' => 'Rafale',
        'Continuous' => 'Continu',
        'Continuous - HDR' => 'Continu - HDR',
        'Continuous High' => 'Continu (ultrarapide)',
        'Continuous Shooting' => 'Prise de vues en continu',
        'HDR Manual' => 'HDR Manuel',
        'HDR Strong 1' => 'HDR fort 1',
        'HDR Strong 2' => 'HDR fort 2',
        'HDR Strong 3' => 'HDR fort 3',
        'Multiple Exposure' => 'Exposition multiple',
        'No Timer' => 'Pas de retardateur',
        'Off' => 'Désactivé',
        'Remote Commander' => 'Télécommande',
        'Remote Continuous Shooting' => 'Prise de vue continue télécommandée',
        'Remote Control' => 'Télécommande',
        'Remote Control (3 s delay)' => 'Télécommande (retard 3 s)',
        'Self-Timer 2 sec' => 'Retardateur (2 sec)',
        'Self-timer' => 'Retardateur',
        'Self-timer (12 s)' => 'Retardateur (12 s)',
        'Self-timer (2 s)' => 'Retardateur (2 s)',
        'Self-timer 10 sec' => 'Retardateur 10 sec',
        'Self-timer 2 sec, Mirror Lock-up' => 'Retardateur 2 sec, Blocage du miroir',
        'Self-timer Operation' => 'Opération avec retardateur',
        'Shutter Button' => 'Déclencheur',
        'Single' => 'Simple',
        'Single Exposure' => 'Exposition unique',
        'Single Frame' => 'Image unique',
        'Single Shot' => 'Prise de vue unique',
        'Single-Frame Bracketing' => 'Bracketing sur image unique',
        'Single-frame' => 'Image unqiue',
        'Single-frame Bracketing' => 'Bracketing sur image unique',
        'Single-frame Exposure Bracketing' => 'Bracketing d\'exposition sur image unique',
        'Single-frame Shooting' => 'Prise de vue unique',
        'Speed Priority Continuous' => 'Priorité à la vitesse continue',
        'UHS continuous' => 'UHS continu',
        'Video' => 'Vidéo',
        'White Balance Bracketing' => 'Bracketing de la balance des blancs',
        'White Balance Bracketing High' => 'Bracketing de la balance des blancs Haut',
        'White Balance Bracketing Low' => 'Bracketing de la balance des blancs Bas',
        'n/a' => 'Non applicable',
      },
    },
   'DriveMode2' => {
      Description => 'Exposition multiple',
      PrintConv => {
        'Single Frame' => 'Vue par vue',
        'Single-frame' => 'Vue par vue',
      },
    },
   'DriveModeSetting' => {
      PrintConv => {
        'Single Frame' => 'Vue par vue',
      },
    },
   'DriveSpeed' => {
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'DronePitch' => 'Inclinaison du drone',
   'DroneQuaternion' => 'Quaternion du drone',
   'DroneRoll' => 'Roulis du drone',
   'DroneYaw' => 'Lacet du drone',
   'Duration' => 'Durée',
   'DynamicRange' => {
      Description => 'Plage dynamique',
      PrintConv => {
        'Wide' => 'Large',
      },
    },
   'DynamicRangeExpansion' => {
      Description => 'Expansion de la dynamique',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'DynamicRangeOptimizer' => {
      Description => 'Optimiseur Dyna',
      PrintConv => {
        'Advanced Auto' => 'Avancé Auto',
        'Advanced Lv1' => 'Avancé Niv1',
        'Advanced Lv2' => 'Avancé Niv2',
        'Advanced Lv3' => 'Avancé Niv3',
        'Advanced Lv4' => 'Avancé Niv4',
        'Advanced Lv5' => 'Avancé Niv5',
        'Auto' => 'Auto.',
        'Off' => 'Désactivé',
        'n/a' => 'Non applicable',
      },
    },
   'DynamicRangeSetting' => {
      Description => 'Réglage de la plage dynamique',
      PrintConv => {
        'Film Simulation' => 'Simulation de film',
        'Manual' => 'Manuel',
        'Wide1 (230%)' => 'Large1 (230%)',
        'Wide2 (400%)' => 'Large2 (400%)',
      },
    },
   'E-DialInProgram' => {
      Description => 'Programme E-Dial In',
      PrintConv => {
        'P Shift' => 'Décalage P',
        'Tv or Av' => 'Tv ou Av',
      },
    },
   'ETTLII' => {
      Description => 'E TTL II',
      PrintConv => {
        'Average' => 'Moyenne',
        'Evaluative' => 'Évaluative',
      },
    },
   'EVStepInfo' => 'Info de pas IL',
   'EVSteps' => {
      Description => 'Pas IL',
      PrintConv => {
        '1/2 EV Steps' => 'Pas de 1/2 IL',
        '1/3 EV Steps' => 'Pas de 1/3 IL',
      },
    },
   'EasyExposureCompensation' => {
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'EasyMode' => {
      Description => 'Mode simple',
      PrintConv => {
        'Beach' => 'Plage',
        'Best Image Selection' => 'Meilleure sélection d\'image',
        'Black & White' => 'Noir et blanc',
        'Blur Reduction' => 'Réduction de l\'effet de flou',
        'Color Accent' => 'Accentuation des couleurs',
        'Color Swap' => 'Permutation des couleurs',
        'Creative Auto' => 'Auto créatif',
        'Creative Light Effect' => 'Effet de lumière créatif',
        'Digital Macro' => 'Macro numérique',
        'Discreet' => 'Discret',
        'Easy' => 'Simple',
        'Face Self-timer' => 'Retardateur de visage',
        'Fast shutter' => 'Obturateur rapide',
        'Fireworks' => 'Feu d\'artifice',
        'Fisheye Effect' => 'Effet Fisheye',
        'Flash Off' => 'Flash désactivé',
        'Foliage' => 'Feuillage',
        'Food' => 'Alimentation',
        'Full auto' => 'Totalement automatique',
        'Gray Scale' => 'Niveaux de gris',
        'HDR Art Bold' => 'HDR artistique Gras',
        'HDR Art Standard' => 'HDR artistique standard',
        'HDR Art Vivid' => 'HDR artistique éclatante',
        'Handheld Night Scene' => 'Scène nocturne à main levée',
        'High Dynamic Range' => 'Plage dynamique élevée',
        'High-speed Burst' => 'Raffale haute vitesse',
        'High-speed Burst HQ' => 'Raffale HQ haute vitesse',
        'Indoor' => 'Intérieur',
        'Kids & Pets' => 'Enfants & animaux',
        'Landscape' => 'Paysage',
        'Live View Control' => 'Contrôle du Live View',
        'Long Shutter' => 'Obturateur long',
        'Low Light' => 'Faible luminosité',
        'Low Light 2' => 'Faible luminosité 2',
        'Manual' => 'Manuel',
        'Miniature Effect' => 'Effet miniature',
        'Movie Digest' => 'Résumé du film',
        'Movie Snap' => 'Instantané de film',
        'Neutral' => 'Neutre',
        'Night' => 'Nuit',
        'Night 2' => 'Nuit 2',
        'Night Scene' => 'Scène nocturne',
        'Night Snapshot' => 'Prise de vue nocturne',
        'Night+' => 'Nuit+',
        'Nostalgic' => 'Nostalgique',
        'Pan focus' => 'Mise au point panoramique',
        'Poster Effect' => 'Effet poster',
        'Quick Shot' => 'Prise de vue rapide',
        'Scene Intelligent Auto' => 'Scène Intelligente Auto',
        'Sepia' => 'Sépia',
        'Slow shutter' => 'Obturateur à vitesse lente',
        'Smile' => 'Sourire',
        'Smooth Skin' => 'Peau lisse',
        'Snow' => 'Neige',
        'Soft Focus' => 'Flou artistique',
        'Sports' => 'Sport',
        'Spotlight' => 'Projecteur',
        'Sunset' => 'Coucher de soleil',
        'Super Macro' => 'Super macro',
        'Super Night' => 'Super Nuit',
        'Super Vivid' => 'Super éclatant',
        'Toy Camera Effect' => 'Effet caméra jouet',
        'Underwater' => 'Subaquatique',
        'Vivid' => 'Éclatant',
        'Wink Self-timer' => 'Minuteur clignotant',
        'Zoom Blur' => 'Flou de zoom',
      },
    },
   'EdgeNoiseReduction' => {
      Description => 'Réduction du bruit de fond',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'EditStatus' => 'Statut d\'édition',
   'EditorialUpdate' => {
      Description => 'Mise à jour éditoriale',
      PrintConv => {
        'Additional language' => 'Langues supplémentaires',
      },
    },
   'EffectiveLV' => 'Indice de lumination effectif',
   'EffectiveMaxAperture' => 'Ouverture effective maxi de l\'Objectif',
   'ElectronicFrontCurtainShutter' => {
      Description => 'Volet électronique du rideau avant',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'Emphasis' => {
      PrintConv => {
        'None' => 'Aucune',
      },
    },
   'EncodingProcess' => {
      Description => 'Procédé de codage',
      PrintConv => {
        'Baseline DCT, Huffman coding' => 'Baseline DCT, codage Huffman',
        'Extended sequential DCT, Huffman coding' => 'Extended sequential DCT, codage Huffman',
        'Extended sequential DCT, arithmetic coding' => 'Extended sequential DCT, codage arithmétique',
        'Lossless, Differential Huffman coding' => 'Lossless, codage Huffman différentiel',
        'Lossless, Huffman coding' => 'Lossless, codage Huffman',
        'Lossless, arithmetic coding' => 'Lossless, codage arithmétique',
        'Lossless, differential arithmetic coding' => 'Lossless, codage arithmétique différentiel',
        'Progressive DCT, Huffman coding' => 'Progressive DCT, codage Huffman',
        'Progressive DCT, arithmetic coding' => 'Progressive DCT, codage arithmétique',
        'Progressive DCT, differential Huffman coding' => 'Progressive DCT, codage Huffman différentiel',
        'Progressive DCT, differential arithmetic coding' => 'Progressive DCT, codage arithmétique différentiel',
        'Sequential DCT, differential Huffman coding' => 'Sequential DCT, codage Huffman différentiel',
        'Sequential DCT, differential arithmetic coding' => 'Sequential DCT, codage arithmétique différentiel',
      },
    },
   'Encryption' => 'Chiffrage',
   'EndPoints' => 'Points de terminaison',
   'EnhanceDarkTones' => {
      Description => 'Améliorer les tons foncés',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'Enhancement' => {
      Description => 'Amélioration',
      PrintConv => {
        'Blue' => 'Bleu',
        'Flesh Tones' => 'Tons chair',
        'Green' => 'Vert',
        'Off' => 'Désactivé',
        'Red' => 'Rouge',
        'Scenery' => 'Paysage',
        'Underwater' => 'Subaquatique',
      },
    },
   'Enhancer' => 'Amplificateur',
   'EnhancerValues' => 'Valeurs de l\'amplificateur',
   'EnvelopeNumber' => 'Numéro d\'enveloppe',
   'EnvelopePriority' => {
      Description => 'Priorité d\'enveloppe',
      PrintConv => {
        '0 (reserved)' => '0 (réservé pour utilisation future)',
        '1 (most urgent)' => '1 (très urgent)',
        '5 (normal urgency)' => '5 (normalement urgent)',
        '8 (least urgent)' => '8 (moins urgent)',
        '9 (user-defined priority)' => '9 (priorité définie par l\'utilisateur)',
      },
    },
   'EnvelopeRecordVersion' => 'Version d\'enregistrement de l\'enveloppe',
   'EquipmentVersion' => 'Version de l\'équipement',
   'Error' => 'Erreur',
   'Event' => 'Evenement',
   'ExcursionTolerance' => {
      Description => 'Tolérance d\'excursion ',
      PrintConv => {
        'Allowed' => 'Possible',
        'Not Allowed' => 'Non permis (défaut)',
      },
    },
   'ExifByteOrder' => {
      Description => 'Ordre des octets Exif',
      PrintConv => {
        'Big-endian (Motorola, MM)' => 'Big-endian (Motorola, MM',
      },
    },
   'ExifCameraInfo' => 'Info d\'appareil photo Exif',
   'ExifImageHeight' => 'Hauteur de l\'image',
   'ExifImageWidth' => 'Largeur de l\'image',
   'ExifOffset' => 'Pointeur Exif IFD',
   'ExifToolVersion' => 'Version ExifTool',
   'ExifUnicodeByteOrder' => 'Ordre des octets Unicode Exif',
   'ExifVersion' => 'Version Exif',
   'ExitPupilPosition' => 'Position de la pupille de sortie',
   'ExpandFilm' => 'Extension film',
   'ExpandFilterLens' => 'Objectif à filtre extensible',
   'ExpandFlashLamp' => 'Extension de lampe flash',
   'ExpandLens' => 'Extension d\'objectif',
   'ExpandScanner' => 'Extension Scanner',
   'ExpandSoftware' => 'Extension logiciel',
   'ExpirationDate' => 'Date d\'expiration',
   'ExpirationTime' => 'Heure d\'expiration',
   'Exposure' => 'Exposition',
   'Exposure2012' => 'Exposition 2012',
   'ExposureAdj' => 'Réglage de l\'exposition',
   'ExposureAdj2' => 'Réglage de l\'exposition 2',
   'ExposureAdjust' => 'Réglage de l\'exposition',
   'ExposureBias' => 'Réglage du biais',
   'ExposureBracketStepSize' => 'Intervalle de bracketing d\'exposition',
   'ExposureBracketValue' => 'Valeur Bracketing Expo',
   'ExposureCompensation' => 'Décalage d\'exposition',
   'ExposureCount' => 'Nombre d\'expositions',
   'ExposureDelayMode' => {
      Description => 'Mode de retardement d\'exposition',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'ExposureDifference' => 'Correction d\'exposition',
   'ExposureIndex' => 'Indice d\'exposition',
   'ExposureLevelIncrements' => {
      Description => 'Paliers de réglage d\'expo',
      PrintConv => {
        '1-stop set, 1/3-stop comp.' => 'Réglage 1 valeur, correction 1/3 val.',
        '1/2 Stop' => 'Palier 1/2',
        '1/2-stop set, 1/2-stop comp.' => 'Réglage 1/2 valeur, correction 1/2 val.',
        '1/3 Stop' => 'Palier 1/3',
        '1/3-stop set, 1/3-stop comp.' => 'Réglage 1/3 valeur, correction 1/3 val.',
      },
    },
   'ExposureMode' => {
      Description => 'Mode d\'exposition',
      PrintConv => {
        'Aperture Priority' => 'Priorité à l\'ouverture',
        'Aperture-priority AE' => 'Priorité ouverture',
        'Auto' => 'Exposition automatique',
        'Auto bracket' => 'Bracketting auto',
        'Bulb' => 'Pose B',
        'Landscape' => 'Paysage',
        'Manual' => 'Exposition manuelle',
        'Night Scene / Twilight' => 'Nocturne',
        'Night View/Portrait' => 'Vue/Portrait nocturne',
        'Shutter Priority' => 'Priorité à l\'obturateur',
        'Shutter speed priority AE' => 'Priorité vitesse',
        'Sunset' => 'Coucher de soleil',
        'n/a' => 'Non applicable',
      },
    },
   'ExposureModeInManual' => {
      Description => 'Mode d\'exposition manuelle',
      PrintConv => {
        'Center-weighted average' => 'Moyenne pondérée centrale',
        'Evaluative metering' => 'Mesure évaluative',
        'Partial metering' => 'Mesure partielle',
        'Specified metering mode' => 'Mode de mesure spécifié',
        'Spot metering' => 'Mesure spot',
      },
    },
   'ExposureProgram' => {
      Description => 'Programme d\'exposition',
      PrintConv => {
        'Action (High speed)' => 'Programme action (orienté grandes vitesses d\'obturation)',
        'Aperture Priority' => 'Priorité à l\'ouverture',
        'Aperture-priority AE' => 'Priorité à l\'ouverture AE',
        'Creative (Slow speed)' => 'Programme créatif (orienté profondeur de champ)',
        'High Contrast Monochrome' => 'Monochrome à fort contraste',
        'Landscape' => 'Paysage',
        'Manual' => 'Manuel',
        'Night view' => 'Vue nocturne',
        'Night view/portrait' => 'Vue/Portrait nocturne',
        'Not Defined' => 'Non défini',
        'Pop Color' => 'Couleur pop',
        'Portrait' => 'Mode portrait',
        'Posterization' => 'Postérisation',
        'Program AE' => 'Programme normal',
        'Retro Photo' => 'Photo rétro',
        'Shutter Priority' => 'Priorité à l\'obturateur',
        'Shutter speed priority AE' => 'Priorité vitesse',
        'Shutter/aperture priority AE' => 'Priorité à l\'obturateur/l\'ouverture AE',
        'Sunset' => 'Coucher de soleil',
        'Toy Camera' => 'Caméra jouet',
      },
    },
   'ExposureStandardAdjustment' => 'Réglage de la norme d\'exposition ',
   'ExposureTime' => 'Temps de pose',
   'ExposureTime2' => 'Temps de pose 2',
   'ExposureTuning' => 'Réglage de l\'exposition',
   'ExposureWarning' => {
      Description => 'Alerte d\'exposition',
      PrintConv => {
        'Bad exposure' => 'Mauvaise exposition',
        'Good' => 'Bonne exposition',
      },
    },
   'ExtendedWBDetect' => {
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'Extender' => {
      Description => 'Extension',
      PrintConv => {
        'None' => 'Aucune',
        'Olympus EX-25 Extension Tube' => 'Tube d\'extension Olympus EX-25',
        'Olympus Zuiko Digital EC-14 1.4x Teleconverter' => 'Téléconvertisseur Olympus Zuiko Digital EC-14 1.4x',
        'Olympus Zuiko Digital EC-20 2.0x Teleconverter' => 'Téléconvertisseur Olympus Zuiko Digital EC-20 2.0x',
      },
    },
   'ExtenderFirmwareVersion' => 'Numéro de série du micrologiciel de l\'extension',
   'ExtenderSerialNumber' => 'Numéro de série de l\'extension',
   'ExtenderStatus' => {
      Description => 'État de l\'extension',
      PrintConv => {
        'Attached' => 'Attaché',
        'Not attached' => 'Non attaché',
        'Removed' => 'Retiré',
      },
    },
   'ExternalFlash' => {
      Description => 'Flash externe',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'ExternalFlashBounce' => {
      Description => 'Réflexion flash externe',
      PrintConv => {
        'Bounce' => 'Avec réflecteur',
        'Bounce or Off' => 'Avec réflecteur ou Désactivé',
        'No' => 'Non',
        'Yes' => 'Oui',
        'n/a' => 'Non applicable',
      },
    },
   'ExternalFlashExposureComp' => {
      Description => 'Compensation d\'exposition flash externe',
      PrintConv => {
        '-0.5' => '-0.5 IL',
        '-1.0' => '-1.0 IL',
        '-1.5' => '-1.5 IL',
        '-2.0' => '-2.0 IL',
        '-2.5' => '-2.5 IL',
        '-3.0' => '-3.0 IL',
        '0.0' => '0.0 IL',
        '0.5' => '0.5 IL',
        '1.0' => '1.0 IL',
        'n/a' => 'Non applicable',
        'n/a (Manual Mode)' => 'Non applicable (Mode manuel)',
      },
    },
   'ExternalFlashFirmware' => {
      Description => 'Micrologiciel Flash externe',
      PrintConv => {
        '1.01 (SB-800 or Metz 58 AF-1)' => '1.01 (SB-800 ou Metz 58 AF-1)',
        '3.01 (SU-800 Remote Commander)' => '3.01 (SU-800 Commande à distance)',
        'n/a' => 'Non applicable',
      },
    },
   'ExternalFlashFlags' => {
      Description => 'Indicateurs de Flash externe',
      PrintConv => {
        '(none)' => '(aucun)',
        'Bounce Flash' => 'Flash à rebond',
        'Dome Diffuser' => 'Diffuseur à dôme',
        'Fired' => 'Déclenché',
        'Wide Flash Adapter' => 'Adaptateur pour flash large',
      },
    },
   'ExternalFlashGuideNumber' => 'Numéro du guide du flash externe',
   'ExternalFlashMode' => {
      Description => 'Mode du flash externe',
      PrintConv => {
        'Not Connected' => 'Non connecté',
        'Off' => 'Désactivé',
        'On, Auto' => 'Activé, auto',
        'On, Contrast-control Sync' => 'Activé, Synchro du contrôle du contraste',
        'On, Flash Problem' => 'Activé, problème de flash',
        'On, High-speed Sync' => 'Activé, synchro haute vitesse',
        'On, Manual' => 'Activé, manuel',
        'On, P-TTL Auto' => 'Activé, auto P-TTL',
        'On, Wireless' => 'Activé, sans cordon',
        'On, Wireless, High-speed Sync' => 'Activé, sans cordon, synchro haute vitesse',
        'n/a - Off-Auto-Aperture' => 'Non applicable - Ouverture automatique désactivé',
      },
    },
   'ExternalFlashReadyState' => {
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'ExternalFlashZoom' => 'Zoom du flash externe',
   'ExtraSamples' => 'Echantillons supplémentaires',
   'FLIR_Unknown' => 'FLIR Inconnu',
   'FNumber' => 'Diaphragme ƒ',
   'FOV' => 'Champ de vision',
   'FPFVersion' => 'Version FPF',
   'FSType' => 'Type FS',
   'Fac100Per' => 'Visages 100 personnes',
   'Fac170Per' => 'Visages 170 personnes',
   'Fac18Per' => 'Visages 18 personnes',
   'Face10Position' => 'Position visage 10',
   'Face10Size' => 'Taille visage 10',
   'Face11Position' => 'Position visage 11',
   'Face11Size' => 'Taille visage 11',
   'Face12Position' => 'Position visage 12',
   'Face12Size' => 'Taille visage 12',
   'Face13Position' => 'Position visage 13',
   'Face13Size' => 'Taille visage 13',
   'Face14Position' => 'Position visage 14',
   'Face14Size' => 'Taille visage 14',
   'Face15Position' => 'Position visage 15',
   'Face15Size' => 'Taille visage 15',
   'Face16Position' => 'Position visage 16',
   'Face16Size' => 'Taille visage 16',
   'Face17Position' => 'Position visage 17',
   'Face17Size' => 'Taille visage 17',
   'Face18Position' => 'Position visage 18',
   'Face18Size' => 'Taille visage 18',
   'Face19Position' => 'Position visage 19',
   'Face19Size' => 'Taille visage 19',
   'Face1Birthday' => 'Anniversaire Visage 1',
   'Face1Category' => {
      Description => 'Catégorie visage 1',
      PrintConv => {
        'Family' => 'Famille',
        'Friend' => 'Ami',
        'Partner' => 'Conjoint',
      },
    },
   'Face1Name' => 'Nom visage 1',
   'Face1Position' => 'Position visage 1',
   'Face1Size' => 'Taille visage 1',
   'Face20Position' => 'Position visage 20',
   'Face20Size' => 'Taille visage 20',
   'Face21Position' => 'Position visage 21',
   'Face21Size' => 'Taille visage 21',
   'Face22Position' => 'Position visage 22',
   'Face22Size' => 'Taille visage 22',
   'Face23Position' => 'Position visage 23',
   'Face23Size' => 'Taille visage 23',
   'Face24Position' => 'Position visage 24',
   'Face24Size' => 'Taille visage 24',
   'Face25Position' => 'Position visage 25',
   'Face25Size' => 'Taille visage 25',
   'Face26Position' => 'Position visage 26',
   'Face26Size' => 'Taille visage 26',
   'Face27Position' => 'Position visage 27',
   'Face27Size' => 'Taille visage 27',
   'Face28Position' => 'Position visage 28',
   'Face28Size' => 'Taille visage 28',
   'Face29Position' => 'Position visage 29',
   'Face29Size' => 'Taille visage 29',
   'Face2Birthday' => 'Anniversaire Visage 2',
   'Face2Category' => {
      Description => 'Catégorie visage 2',
      PrintConv => {
        'Family' => 'Famille',
        'Friend' => 'Ami',
        'Partner' => 'Conjoint',
      },
    },
   'Face2Name' => 'Nom visage 2',
   'Face2Position' => 'Position visage 2',
   'Face2Size' => 'Taille visage 2',
   'Face30Position' => 'Position visage 30',
   'Face30Size' => 'Taille visage 30',
   'Face31Position' => 'Position visage 31',
   'Face31Size' => 'Taille visage 31',
   'Face32Position' => 'Position visage 32',
   'Face32Size' => 'Taille visage 32',
   'Face3Birthday' => 'Anniversaire Visage 3',
   'Face3Category' => {
      Description => 'Catégorie visage 3',
      PrintConv => {
        'Family' => 'Famille',
        'Friend' => 'Ami',
        'Partner' => 'Conjoint',
      },
    },
   'Face3Name' => 'Nom visage 3',
   'Face3Position' => 'Position visage 3',
   'Face3Size' => 'Taille visage 3',
   'Face4Birthday' => 'Anniversaire Visage 4',
   'Face4Category' => {
      Description => 'Catégorie visage 4',
      PrintConv => {
        'Family' => 'Famille',
        'Friend' => 'Ami',
        'Partner' => 'Conjoint',
      },
    },
   'Face4Name' => 'Nom visage 4',
   'Face4Position' => 'Position visage 4',
   'Face4Size' => 'Taille visage 4',
   'Face5Birthday' => 'Anniversaire Visage 5',
   'Face5Category' => {
      Description => 'Catégorie visage 5',
      PrintConv => {
        'Family' => 'Famille',
        'Friend' => 'Ami',
        'Partner' => 'Conjoint',
      },
    },
   'Face5Name' => 'Nom visage 5',
   'Face5Position' => 'Position visage 5',
   'Face5Size' => 'Taille visage 5',
   'Face6Birthday' => 'Anniversaire Visage 6',
   'Face6Category' => {
      Description => 'Catégorie visage 6',
      PrintConv => {
        'Family' => 'Famille',
        'Friend' => 'Ami',
        'Partner' => 'Conjoint',
      },
    },
   'Face6Name' => 'Nom visage 6',
   'Face6Position' => 'Position visage 6',
   'Face6Size' => 'Taille visage 6',
   'Face7Birthday' => 'Anniversaire Visage 7',
   'Face7Category' => {
      Description => 'Catégorie visage 7',
      PrintConv => {
        'Family' => 'Famille',
        'Friend' => 'Ami',
        'Partner' => 'Conjoint',
      },
    },
   'Face7Name' => 'Nom visage 7',
   'Face7Position' => 'Position visage 7',
   'Face7Size' => 'Taille visage 7',
   'Face8Birthday' => 'Anniversaire Visage 8',
   'Face8Category' => {
      Description => 'Catégorie visage 8',
      PrintConv => {
        'Family' => 'Famille',
        'Friend' => 'Ami',
        'Partner' => 'Conjoint',
      },
    },
   'Face8Name' => 'Nom visage 8',
   'Face8Position' => 'Position visage 8',
   'Face8Size' => 'Taille visage 8',
   'Face9Position' => 'Position visage 9',
   'Face9Size' => 'Taille visage 9',
   'FaceDetect' => {
      Description => 'Détection du visage',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'FaceDetectArea' => 'Zone de détection du visage',
   'FaceDetectFrameCrop' => 'Recadrage du cadre de détection du visage',
   'FaceDetectFrameSize' => 'Taille du cadre de détection du visage',
   'FaceDetected' => 'Visage détecté',
   'FaceDetection' => {
      Description => 'Détection du visage',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'FaceElementTypes' => {
      Description => 'Types d\'éléments détectés',
      PrintConv => {
        'Aircraft Body' => 'Carlingue d\'avion',
        'Aircraft Cockpit' => 'Cockpit d\'avion',
        'Animal Body' => 'Corps d\'animal',
        'Animal Face' => 'Visage d\'animal',
        'Animal Head' => 'Tête d\'animal',
        'Animal Left Eye' => 'Œil gauche d\'animal',
        'Animal Right Eye' => 'Œil droit d\'animal',
        'Bike' => 'Vélo',
        'Bird Body' => 'Corps d\'oiseau',
        'Bird Head' => 'Tête d\'oiseau',
        'Bird Left Eye' => 'Œil gauche d\'oiseau',
        'Bird Right Eye' => 'Œil droit d\'oiseau',
        'Body' => 'Corps',
        'Body of Car' => 'Carrosserie de voiture',
        'Face' => 'Visage',
        'Front of Car' => 'Calandre de voiture',
        'Head' => 'Tête',
        'Left Eye' => 'Œil gauche',
        'Right Eye' => 'Œil droit',
        'Train Cockpit' => 'Poste de conduite de train',
        'Train Front' => 'Avant de train',
      },
    },
   'FaceID' => 'ID Visage',
   'FaceInfoLength' => 'Longueur des informations sur le visage',
   'FaceInfoOffset' => 'Décalage de l\'information sur le visage',
   'FaceInfoUnknown' => 'Infos visage inconnu',
   'FaceName' => 'Nom du visage',
   'FaceNumbers' => 'Numéros du visage',
   'FaceOrientation' => {
      Description => 'Orientation du visage',
      PrintConv => {
        'Horizontal (normal)' => '0° (haut/gauche)',
        'Rotate 180' => 'Tourné à 180°',
        'Rotate 270 CW' => 'Tourné à 270° dans le sens antihoraire',
        'Rotate 90 CW' => 'Tourné à 90° dans le sens antihoraire',
      },
    },
   'FacePosition' => 'Position du visage',
   'FacePositions' => 'Positions du visage',
   'FaceRecognition' => {
      Description => 'Reconnaissance faciale',
      PrintConv => {
        'Off' => 'Désactivée',
        'On' => 'Activée',
      },
    },
   'FaceTest2' => 'Test facial 2',
   'FaceTest8' => 'Test facial 8',
   'FaceWidth' => 'Largeur de visage',
   'FacesDetected' => {
      Description => 'Visages détectés',
      PrintConv => {
        'No' => 'Non',
        'Yes' => 'Oui',
        'n/a' => 'Non applicable',
      },
    },
   'FacesRecognized' => 'Visages reconnus',
   'FacetSequence' => 'Séquence de facette',
   'Fade' => 'Fondu',
   'Fade-InDuration' => 'Durée du fondu enchaîné',
   'Fade-InType' => 'Type du fondu enchaîné',
   'Fade-OutDuration' => 'Durée du fondu à la fermeture',
   'Fade-OutType' => 'Type du fondu à la fermeture',
   'FastSeek' => {
      PrintConv => {
        'No' => 'Non',
        'Yes' => 'Oui',
      },
    },
   'FaxProfile' => {
      PrintConv => {
        'Unknown' => 'Inconnu',
      },
    },
   'FaxRecvParams' => 'Paramètres de réception Fax',
   'FaxRecvTime' => 'Temps de réception Fax',
   'FaxSubAddress' => 'Sous-adresse Fax',
   'FieldOfView' => {
      PrintConv => {
        'Wide' => 'Large',
      },
    },
   'FileAccessDate' => 'Date d\'accès au fichier',
   'FileAttributes' => {
      Description => 'Attribut du fichier',
      PrintConv => {
        'Block' => 'Bloc',
        'Character' => 'Caractère',
        'Compressed' => 'Compressé',
        'Device' => 'Appareil',
        'Directory' => 'Répertoire',
        'Encrypted' => 'Crypté',
        'Encrypted?' => 'Crypté?',
        'Hidden' => 'Masqué',
        'Mux Block' => 'Blocs multipexés',
        'Mux Character' => 'Caractères multipexés',
        'Not Content Indexed' => 'Aucun contenu indexé',
        'Not indexed' => 'Non indexé',
        'Offline' => 'Hors ligne',
        'Read Only' => 'Lecture seule',
        'Read-only' => 'Lecture seule',
        'Regular' => 'Régulier',
        'Reparse Point' => 'Point de reparsage',
        'Reparse point' => 'Point de reparsage',
        'Set Group ID' => 'ID du jeu d\'ensemble',
        'Solaris Door' => 'Porte Solaris',
        'Solaris Shadow Inode' => 'Inode fantôme Solaris',
        'Sparse File' => 'Fichier Sparse',
        'Symbolic Link' => 'Lien symbolique',
        'System' => 'Système',
        'Temporary' => 'Temporaire',
        'Unknown' => 'Inconnu',
        'Volume Label' => 'Label du volume',
        'VxFS Compressed' => 'Compressé VxFS',
        'XENIX Named' => 'Nommage XENIX',
      },
    },
   'FileFormat' => {
      Description => 'Format du fichier',
      PrintConv => {
        'Compressed Binary File [.ZIP] (PKWare Inc)' => 'Fichier binaire compressé [.ZIP] (PKWare Inc)',
        'Digital Audio File [.WAV] (Microsoft & Creative Labs)' => 'Fichier audio numérique [.WAV] (Microsoft & Creative Labs)',
        'Hypertext Markup Language [.HTML] (The Internet Society)' => 'HyperText Markup Language [.HTML] (The Internet Society)',
        'Illustrator (Adobe Graphics data)' => 'Illustrator (Adobe Graphics data',
        'JPEG (lossy)' => 'JPEG (avec perte)',
        'JPEG (lossy/non-quantization toggled)' => 'JPEG (basculement entre perte et non-quantification)',
        'JPEG (non-quantization)' => 'JPEG (non-quantification)',
        'Tagged Image File Format (Adobe/Aldus Image data)' => 'Tagged Image File Format (Adobe/Aldus Image data',
        'Tape Archive [.TAR]' => 'Ape Archive [.TAR]',
      },
    },
   'FileGroupID' => 'ID du groupe de fichiers',
   'FileID' => 'ID du fichier',
   'FileIndex' => 'Index du fichier',
   'FileIndex2' => 'Index 2 du fichier',
   'FileInfo' => 'Infos Fichier',
   'FileInfoVersion' => 'Version des Infos Fichier',
   'FileInodeChangeDate' => 'Date de changement d\'inode du fichier',
   'FileInodeNumber' => 'Numéro d\'inode du fichier',
   'FileLength' => 'Longueur du fichier',
   'FileModifyDate' => 'Date de modification du fichier',
   'FileName' => 'Nom du fichier',
   'FileNameAsDelivered' => 'Nom du fichier tel que livré',
   'FileNameLength' => 'Longueur du nom du fichier',
   'FileNumber' => 'Numéro du fichier',
   'FileNumberMemory' => {
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'FileNumberSequence' => {
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'FileOS' => {
      Description => 'Système d\'exploitation des fichiers',
      PrintConv => {
        'OS/2 16-bit' => 'OS/2 16-bits',
        'OS/2 16-bit PM-16' => 'OS/2 16-bits PM-16',
        'OS/2 32-bit' => 'OS/2 32-bits',
        'OS/2 32-bit PM-32' => 'OS/2 32-bits PM-32',
        'Windows 16-bit' => 'Windows 16-bits',
        'Windows 32-bit' => 'Windows 32-bits',
        'Windows NT 32-bit' => 'Windows NT 32-bits',
      },
    },
   'FileOwner' => 'Propriétaire du fichier',
   'FilePath' => 'Chemin d\'accès au fichier',
   'FilePermissions' => 'Permissions du fichier',
   'FileProfileVersion' => 'Version du profil du fichier',
   'FileSecurityReport' => 'Rapport de sécurité du fichier',
   'FileSequence' => 'Séquence du fichier',
   'FileSetConsistencyFlag' => 'Drapeau de cohérence du jeu de fichiers',
   'FileSetID' => 'ID du jeu de fichiers',
   'FileSize' => 'Taille du fichier',
   'FileSizeBytes' => 'Taille du fichier en octets',
   'FileSource' => {
      Description => 'Source du fichier',
      PrintConv => {
        'Computer Graphics' => 'Infographie',
        'Digital Camera' => 'Appareil photo numérique',
        'Film Scanner' => 'Scanner de film',
        'Reflection Print Scanner' => 'Scanner par réflexion',
        'Sigma Digital Camera' => 'Appareil photo numérique Sigma',
        'Video Capture' => 'Capture vidéo',
      },
    },
   'FileSubtype' => 'Sous-type de fichier',
   'FileType' => 'Type de fichier',
   'FileTypeDescription' => 'Description du type de fichier',
   'FileTypeExtension' => 'Extension du type de fichiers',
   'FileURL' => 'URL du fichier',
   'FileUserID' => 'ID utilisateur de fichier',
   'FileVersion' => 'Version de format de fichier',
   'FileVersionNumber' => 'Numéro de version du fichier',
   'Filename' => 'Nom du fichier ',
   'Files' => 'Fichiers',
   'FillFlashAutoReduction' => {
      Description => 'Mesure E-TTL',
      PrintConv => {
        'Disable' => 'Désactivé',
        'Enable' => 'Activé',
      },
    },
   'FillMethod' => {
      Description => 'Méthode de remplissage',
      PrintConv => {
        'Bit Replication' => 'Réplication de bits',
        'Zero Fill' => 'Remplissage à zéro',
      },
    },
   'FillOrder' => {
      Description => 'Ordre de remplissage',
      PrintConv => {
        'Normal' => 'Normale',
        'Reversed' => 'Inversé',
      },
    },
   'FilmMode' => {
      Description => 'Mode vidéo',
      PrintConv => {
        'Dynamic (B&W)' => 'Vives (N & Bà)',
        'Dynamic (color)' => 'Couleurs vives',
        'Nature (color)' => 'Couleurs naturelles',
        'Smooth (B&W)' => 'Pastel (N & B)',
        'Smooth (color)' => 'Couleurs pastel',
        'Standard (B&W)' => 'Normales (N & B)',
        'Standard (color)' => 'Couleurs normales',
        'n/a' => 'Non applicable',
      },
    },
   'FilterEffect' => {
      Description => 'Effet de filtre',
      PrintConv => {
        'Cross Process' => 'Traitement croisé',
        'Dynamic Monochrome' => 'Monochrome dynamique',
        'Expressive' => 'Expressif',
        'Fantasy' => 'Fantaisie',
        'Green' => 'Vert',
        'High Dynamic' => 'Haute dynamique',
        'High Key' => 'Tons clairs',
        'Impressive Art' => 'Art impressionnant',
        'Low Key' => 'Tons sombres',
        'Miniature Effect' => 'Effet miniature',
        'None' => 'Aucun',
        'Off' => 'Désactivé',
        'Old Days' => 'Vieux jours',
        'One Point Color' => 'Couleur d\'un point',
        'Red' => 'Rouge',
        'Retro' => 'Rétro',
        'Rough Monochrome' => 'Monochrome brut',
        'Sepia' => 'Sépia',
        'Silky Monochrome' => 'Monochrome soyeux',
        'Soft Focus' => 'Flou artistique',
        'Star Filter' => 'Filtre d\'étoiles',
        'Sunshine' => 'Soleil',
        'Toy Effect' => 'Effet jouet',
        'Toy Pop' => 'Jouet Pop',
        'Yellow' => 'Jaune',
        'n/a' => 'Non applicable',
      },
    },
   'FilterEffectAuto' => {
      Description => 'Effet filtre Auto',
      PrintConv => {
        'Green' => 'Vert',
        'None' => 'Aucun',
        'Red' => 'Rouge',
        'Yellow' => 'Jaune',
        'n/a' => 'Non applicable',
      },
    },
   'FilterEffectFaithful' => {
      Description => 'Effet filtre Fidèle',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'FilterEffectLandscape' => {
      Description => 'Effet filtre Paysage',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'FilterEffectMonochrome' => {
      Description => 'Effet filtre Monochrome',
      PrintConv => {
        'Green' => 'Vert',
        'None' => 'Aucun',
        'Red' => 'Rouge',
        'Yellow' => 'Jaune',
        'n/a' => 'Non applicable',
      },
    },
   'FilterEffectNeutral' => {
      Description => 'Effet filtre Neutre',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'FilterEffectPortrait' => {
      Description => 'Effet filtre Portrait',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'FilterEffectStandard' => {
      Description => 'Effet filtre Standard',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'FilterEffectUserDef1' => {
      Description => 'Effet filtre définit par l\'utilisateur 1',
      PrintConv => {
        'Green' => 'Vert',
        'None' => 'Aucun',
        'Red' => 'Rouge',
        'Yellow' => 'Jaune',
        'n/a' => 'Non applicable',
      },
    },
   'FilterEffectUserDef2' => {
      Description => 'Effet filtre définit par l\'utilisateur 2',
      PrintConv => {
        'Green' => 'Vert',
        'None' => 'Aucun',
        'Red' => 'Rouge',
        'Yellow' => 'Jaune',
        'n/a' => 'Non applicable',
      },
    },
   'FilterEffectUserDef3' => {
      Description => 'Effet filtre définit par l\'utilisateur 3',
      PrintConv => {
        'Green' => 'Vert',
        'None' => 'Aucun',
        'Red' => 'Rouge',
        'Yellow' => 'Jaune',
        'n/a' => 'Non applicable',
      },
    },
   'FinderDisplayDuringExposure' => {
      Description => 'Affich. viseur pendant expo.',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'Firmware' => 'Micrologiciel',
   'Firmware2' => 'Micrologiciel 2',
   'FirmwareDate' => 'Date du micrologiciel',
   'FirmwareID' => 'Identifiant du micrologiciel',
   'FirmwareName' => 'Nom du micrologiciel',
   'FirmwareRevision' => 'Révision du micrologiciel',
   'FirmwareRevision2' => 'Version du micrologiciel 2',
   'FirmwareVersion' => 'Version du micrologiciel',
   'FirmwareVersion2' => 'Version du firmware 2',
   'FirmwareVersion3' => 'Version de micrologiciel 3',
   'FirmwareVersions' => 'Versions du micrologiciel',
   'FirstName' => 'Prénom',
   'FisheyeFilter' => {
      Description => 'Filtre Fisheye',
      PrintConv => {
        'Off' => 'Désactivé',
      },
    },
   'FixtureIdentifier' => 'Identificateur d\'installation',
   'Flash' => {
      Description => 'Flash ',
      PrintConv => {
        'Auto, Did not fire' => 'Flash non déclenché, mode auto',
        'Auto, Did not fire, Red-eye reduction' => 'Auto, flash non déclenché, mode réduction yeux rouges',
        'Auto, Fired' => 'Flash déclenché, mode auto',
        'Auto, Fired, Red-eye reduction' => 'Flash déclenché, mode auto, mode réduction yeux rouges, lumière renvoyée détectée',
        'Auto, Fired, Red-eye reduction, Return detected' => 'Flash déclenché, mode auto, lumière renvoyée détectée, mode réduction yeux rouges',
        'Auto, Fired, Red-eye reduction, Return not detected' => 'Flash déclenché, mode auto, lumière renvoyée non détectée, mode réduction yeux rouges',
        'Auto, Fired, Return detected' => 'Flash déclenché, mode auto, lumière renvoyée détectée',
        'Auto, Fired, Return not detected' => 'Flash déclenché, mode auto, lumière renvoyée non détectée',
        'Did not fire' => 'Flash non déclenché',
        'Fired' => 'Flash déclenché',
        'Fired, Red-eye reduction' => 'Flash déclenché, mode réduction yeux rouges',
        'Fired, Red-eye reduction, Return detected' => 'Flash déclenché, mode réduction yeux rouges, lumière renvoyée détectée',
        'Fired, Red-eye reduction, Return not detected' => 'Flash déclenché, mode réduction yeux rouges, lumière renvoyée non détectée',
        'Fired, Return detected' => 'Lumière renvoyée sur le capteur détectée',
        'Fired, Return not detected' => 'Lumière renvoyée sur le capteur non détectée',
        'No Flash' => 'Flash non déclenché',
        'No flash function' => 'Pas de fonction flash',
        'Off' => 'Désactivé',
        'Off, Did not fire' => 'Flash non déclenché, mode flash forcé',
        'Off, Did not fire, Return not detected' => 'Éteint, flash non déclenché, lumière renvoyée non détectée',
        'Off, No flash function' => 'Éteint, pas de fonction flash',
        'Off, Red-eye reduction' => 'Éteint, mode réduction yeux rouges',
        'On' => 'Activé',
        'On, Did not fire' => 'Hors service, flash non déclenché',
        'On, Fired' => 'Flash déclenché, mode flash forcé',
        'On, Red-eye reduction' => 'Flash déclenché, mode forcé, mode réduction yeux rouges',
        'On, Red-eye reduction, Return detected' => 'Flash déclenché, mode forcé, mode réduction yeux rouges, lumière renvoyée détectée',
        'On, Red-eye reduction, Return not detected' => 'Flash déclenché, mode forcé, mode réduction yeux rouges, lumière renvoyée non détectée',
        'On, Return detected' => 'Flash déclenché, mode flash forcé, lumière renvoyée détectée',
        'On, Return not detected' => 'Flash déclenché, mode flash forcé, lumière renvoyée non détectée',
      },
    },
   'FlashAction' => {
      Description => 'Action du flash',
      PrintConv => {
        'Did not fire' => 'N\'a pas déclenché',
        'External Flash Fired' => 'Flash externe déclenché',
        'External Flash, Did not fire' => 'Flash externe, n\'a pas déclenché',
        'External Flash, Fired' => 'Flash externe, déclenché',
        'Fired' => 'Déclenché',
        'Flash Fired' => 'Flash déclenché',
        'Wireless Controlled Flash Fired' => 'Flash commandé sans cordon déclenché',
      },
    },
   'FlashActionExternal' => {
      Description => 'Action Flash Externe',
      PrintConv => {
        'Did not fire' => 'N\'a pas déclenché',
        'Fired' => 'A déclenché',
        'Fired, HSS' => 'Déclenché HSS',
      },
    },
   'FlashActivity' => 'Activité du flash',
   'FlashAttributes' => {
      Description => 'Attribut du flash',
      PrintConv => {
        'ActionScript3' => 'Action Script 3',
        'HasMetadata' => 'A des métadonnées',
        'UseNetwork' => 'Utilise le réseau',
      },
    },
   'FlashBatteryLevel' => 'Niveau de batterie du flash',
   'FlashBias' => 'Décalage Flash',
   'FlashBits' => {
      Description => 'Bits de flash',
      PrintConv => {
        '(none)' => '(aucun)',
        '2nd-curtain sync used' => 'Synchronisation du 2ème rideau utilisée',
        'Built-in' => 'Intégré',
        'External' => 'Externe',
        'FP sync enabled' => 'Synchro FP activée',
        'FP sync used' => 'Synchro FP utilisée',
        'Manual' => 'Manuel',
      },
    },
   'FlashBurstPriority' => {
      Description => 'Priorité à la rafale de flash',
      PrintConv => {
        'Exposure' => 'Exposition',
        'Frame Rate' => 'Fréquence d\'images',
      },
    },
   'FlashButtonFunction' => {
      Description => 'Fonction du bouton de flash',
      PrintConv => {
        'ISO speed' => 'Vitesse ISO',
        'Raise built-in flash' => 'Relever le flash intégré',
      },
    },
   'FlashChargeLevel' => 'Niveau de charge du flash',
   'FlashColorFilter' => {
      Description => 'Filtre couleur du flash',
      PrintConv => {
        'Amber' => 'Ambré',
        'Blue' => 'Bleu',
        'FL-GL1 or SZ-2FL Fluorescent' => 'FL-GL1 ou SZ-2FL Fluorescent',
        'None' => 'Aucun',
        'Red' => 'Rouge',
        'TN-A1 or SZ-2TN Incandescent' => 'TN-A1 ou SZ-2TN Incandescent',
        'Yellow' => 'Jaune',
      },
    },
   'FlashCommanderMode' => {
      Description => 'Mode commandant du flash',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'FlashCompensation' => 'Compensation du flash',
   'FlashControl' => {
      Description => 'Contrôle du flash',
      PrintConv => {
        'ADI Flash' => 'Flash ADI',
        'Manual' => 'Manuel',
        'Pre-flash TTL' => 'Pré-flash TTL',
      },
    },
   'FlashControlBuilt-in' => {
      Description => 'Contrôle du flash intégré',
      PrintConv => {
        'Commander Mode' => 'Mode Maître',
        'Manual' => 'Manuel',
        'Repeating Flash' => 'Flash répétitif',
      },
    },
   'FlashControlMode' => {
      Description => 'Mode de Contrôle du Flash',
      PrintConv => {
        'Auto Aperture' => 'Ouverture automatique',
        'Auto External Flash' => 'Flash externe automatique',
        'Automatic' => 'Automatique',
        'GN (distance priority)' => 'GN (priorité à la distance)',
        'Manual' => 'Manuel',
        'Off' => 'Désactivé',
        'Repeating Flash' => 'Flash répétitif',
      },
    },
   'FlashCurtain' => {
      Description => 'Rideau du flash',
      PrintConv => {
        '1st' => '1er',
        '2nd' => '2ième',
        'n/a' => 'Non applicable',
      },
    },
   'FlashDefault' => {
      Description => 'Falsh par défaut',
      PrintConv => {
        'Fill Flash' => 'Flash d\'appoint',
      },
    },
   'FlashDevice' => {
      PrintConv => {
        'None' => 'Aucune',
      },
    },
   'FlashEnergy' => 'Énergie du flash',
   'FlashExposureBracketValue' => 'Valeur du bracketing d\'exposition au flash',
   'FlashExposureComp' => 'Compensation d\'exposition au flash',
   'FlashExposureCompSet' => 'Réglage de compensation d\'exposition au flash',
   'FlashExposureLock' => {
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'FlashFired' => {
      Description => 'Flash utilisé',
      PrintConv => {
        'External' => 'Externe',
        'False' => 'Faux',
        'Internal' => 'Interne',
        'No' => 'Non',
        'True' => 'Vrai',
        'Yes' => 'Oui',
      },
    },
   'FlashFiring' => {
      Description => 'Émission de l\'éclair',
      PrintConv => {
        'Does not fire' => 'Désactivé',
        'Fires' => 'Activé',
      },
    },
   'FlashFirmwareVersion' => 'Version du micrologiciel du flash',
   'FlashFocalLength' => 'Focale Flash',
   'FlashFunction' => {
      Description => 'Fonction flash',
      PrintConv => {
        'Bounce flash' => 'Flash à rebond',
        'Built-in flash' => 'Flash intégré',
        'False' => 'Faux',
        'Fill flash, ADI' => 'Flash d\'appoint, ADI',
        'Fill flash, Pre-flash TTL' => 'Flash d\'appoint, pré-flash TTL',
        'HSS' => 'Synchro Haute Vitesse (HSS)',
        'Manual' => 'Manuel',
        'No flash' => 'Pas de flash',
        'Rear sync, ADI' => 'Synchro arrière, ADI',
        'True' => 'Vrai',
        'Wireless' => 'Sans cordon',
      },
    },
   'FlashGNDistance' => {
      Description => 'Distance du flash GN',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'FlashGroupACompensation' => 'Compensation du groupe de flash A',
   'FlashGroupAControlMode' => {
      Description => 'Mode de contrôle du groupe de flash A',
      PrintConv => {
        'Auto Aperture' => 'Ouverture automatique',
        'Automatic' => 'Automatique',
        'GN (distance priority)' => 'GN (priorité à la distance)',
        'Manual' => 'Manuel',
        'Off' => 'Désactivé',
        'Repeating Flash' => 'Répétition du flash',
      },
    },
   'FlashGroupAOutput' => 'Sortie Flash Groupe A',
   'FlashGroupBCompensation' => 'Compensation du groupe de flash B',
   'FlashGroupBControlMode' => {
      Description => 'Mode de contrôle du groupe de flash B',
      PrintConv => {
        'Auto Aperture' => 'Ouverture automatique',
        'Automatic' => 'Automatique',
        'GN (distance priority)' => 'GN (priorité à la distance)',
        'Manual' => 'Manuel',
        'Off' => 'Désactivé',
        'Repeating Flash' => 'Répétition du flash',
      },
    },
   'FlashGroupBOutput' => 'Sortie Flash Groupe B',
   'FlashGroupCCompensation' => 'Compensation du groupe de flash C',
   'FlashGroupCControlMode' => {
      Description => 'Mode de contrôle du groupe de flash C',
      PrintConv => {
        'Auto Aperture' => 'Ouverture automatique',
        'Automatic' => 'Automatique',
        'GN (distance priority)' => 'GN (priorité à la distance)',
        'Manual' => 'Manuel',
        'Off' => 'Désactivé',
        'Repeating Flash' => 'Répétition du flash',
      },
    },
   'FlashGroupCOutput' => 'Sortie Flash Groupe C',
   'FlashGuideNumber' => 'Numéro du guide du flash',
   'FlashInfo' => 'Information flash',
   'FlashInfoVersion' => 'Version de l\'info Flash',
   'FlashIntensity' => {
      Description => 'Intensité du flash',
      PrintConv => {
        'High' => 'Haute',
        'Low' => 'Basse',
        'Normal' => 'Normale',
        'Strong' => 'Forte',
        'Weak' => 'Faible',
        'n/a' => 'Non applicable',
        'n/a (x4)' => 'Non applicable (x4)',
      },
    },
   'FlashLevel' => {
      Description => 'Niveau de flash',
      PrintConv => {
        'High' => 'Haut',
        'Low' => 'Bas',
        'n/a' => 'Non applicable',
      },
    },
   'FlashMeteringSegments' => 'Segments de mesure flash',
   'FlashMode' => {
      Description => 'Mode du flash',
      PrintConv => {
        '2nd Curtain' => 'Flash 2ème rideau',
        'Auto' => 'Flash Auto',
        'Auto, Did not fire' => 'Flash Auto, non déclenché',
        'Auto, Did not fire, Red-eye reduction' => 'Flash Auto, non déclenché, réduction yeux rouges',
        'Auto, Fired' => 'Flash Auto, déclenché',
        'Auto, Fired, Red-eye reduction' => 'Auto, flash déclenché, réduction yeux rouges',
        'Autoflash' => 'Flash auto (Autoflash)',
        'Did Not Fire' => 'Flash non-déclenché',
        'Disabled' => 'Flash désactivé',
        'External, Auto' => 'Flash externe, auto',
        'External, Contrast-control Sync' => 'Flash externe, synchro contrôle des contrastes',
        'External, Flash Problem' => 'Flash externe, problème de flash',
        'External, High-speed Sync' => 'Flash externe, synchro haute vitesse',
        'External, Manual' => 'Flash externe, manuel',
        'External, P-TTL Auto' => 'Flash externe, P-TTL',
        'External, Wireless' => 'Flash externe, sans cordon',
        'External, Wireless, High-speed Sync' => 'Flash externe, sans cordon, synchro haute vitesse',
        'Fill Flash' => 'Flash d\'appoint',
        'Fill flash' => 'Flash d\'appoint',
        'Fill-flash' => 'Flash d\'appoint',
        'Fill-in' => 'Flash d\'Appoint (Fill-in)',
        'Fired, Commander Mode' => 'Flash déclenché, Mode Maître',
        'Fired, External' => 'Flash déclenché, Exterieur',
        'Fired, Manual' => 'Flash déclenché, Manuel',
        'Fired, TTL Mode' => 'Flash déclenché, Mode TTL',
        'Flash Off' => 'Flash désactivé',
        'Force' => 'Flash forcé',
        'Forced On' => 'Flash activé forcé',
        'Internal' => 'Flash interne',
        'LED Light' => 'Flash LED',
        'Manual' => 'Flash manuel',
        'Normal' => 'Flash normale',
        'Not Ready' => 'Flash non prêt',
        'Off' => 'Flash désactivé',
        'Off, Did not fire' => 'Flash désactivé, non déclenché',
        'Off?' => 'Flash désactivé ?',
        'On' => 'Flash activé',
        'On, Did not fire' => 'Flash activé, non déclenché',
        'On, Did not fire, Wireless (Master)' => 'Flash activé, non déclenché, sans cordon (Maître)',
        'On, Fired' => 'Flash activé, déclenché',
        'On, Red-eye reduction' => 'Flash activé, réduction yeux rouges',
        'On, Slow-sync' => 'Flash activé, synchro lente',
        'On, Slow-sync, Red-eye reduction' => 'Flash activé, synchro lente, réduction yeux rouges',
        'On, Soft' => 'Flash activé, doux',
        'On, Trailing-curtain Sync' => 'Flash activé, synchro 2e rideau',
        'On, Wireless (Control)' => 'Flash activé, sans cordon (Contrôle)',
        'On, Wireless (Master)' => 'Flash activé, sans cordon (Maître)',
        'Rear Sync' => 'Flash synchro arrière',
        'Rear flash sync' => 'Flash synchro arrière',
        'Red eye' => 'Flash yeux rouge',
        'Red-Eye' => 'Flash yeux rouge',
        'Red-Eye?' => 'Flash yeux rouge ?',
        'Red-eye' => 'Flash yeux rouge',
        'Red-eye Reduction' => 'Flash yeux rouge, réduction yeux rouges',
        'Red-eye reduction' => 'Flash yeux rouge réduction yeux rouges',
        'Slow Sync' => 'Flash synchro lente',
        'Slow-sync' => 'Flash synchro lente',
        'Synchro, Red-eye reduction' => 'Flash synchro réduction yeux rouges',
        'Unknown' => 'Inconnu',
        'Wireless' => 'Flash sans cordon',
        'n/a - Off-Auto-Aperture' => 'Non applicable - Auto-diaph hors service',
      },
    },
   'FlashModel' => {
      Description => 'Modèle du Flash',
      PrintConv => {
        'None' => 'Aucun',
      },
    },
   'FlashOptions' => {
      Description => 'Options du flash',
      PrintConv => {
        'Auto, Red-eye reduction' => 'Auto, réduction yeux rouges',
        'Normal' => 'Normale',
        'Red-eye reduction' => 'Réduction yeux rouges',
        'Slow-sync' => 'Synchro lente',
        'Slow-sync, Red-eye reduction' => 'Synchro lente, réduction yeux rouges',
        'Trailing-curtain Sync' => 'Synchro 2e rideau',
        'Wireless (Control)' => 'Sans cordon (contrôleur)',
        'Wireless (Master)' => 'Sans cordon (maître)',
      },
    },
   'FlashOptions2' => {
      Description => 'Options de flash (2)',
      PrintConv => {
        'Auto, Red-eye reduction' => 'Auto, réduction yeux rouges',
        'Normal' => 'Normale',
        'Red-eye reduction' => 'Réduction yeux rouges',
        'Slow-sync' => 'Synchro lente',
        'Slow-sync, Red-eye reduction' => 'Synchro lente, réduction yeux rouges',
        'Trailing-curtain Sync' => 'Synchro 2e rideau',
        'Wireless (Control)' => 'Sans cordon (contrôleur)',
        'Wireless (Master)' => 'Sans cordon (maître)',
      },
    },
   'FlashOutput' => 'Puissance du flash',
   'FlashPower' => 'Puissance du flash',
   'FlashRedEyeMode' => {
      Description => 'Flash anti-yeux rouges',
      PrintConv => {
        'False' => 'Faux',
        'True' => 'Vrai',
      },
    },
   'FlashReturn' => {
      Description => 'Retour de flash',
      PrintConv => {
        'No return detection' => 'Pas de détection de retour',
        'Return detected' => 'Retour détecté',
        'Return not detected' => 'Retour non détecté',
        'Subject Inside Flash Range' => 'Sujet en porté du flash',
        'Subject Outside Flash Range' => 'Sujet hors de porté du flash',
      },
    },
   'FlashSerialNumber' => 'Numéro de série du flash',
   'FlashSetting' => 'Réglages Flash',
   'FlashSource' => {
      Description => 'Source du flash',
      PrintConv => {
        'External' => 'Externe',
        'Internal' => 'Interne',
        'None' => 'Aucun',
      },
    },
   'FlashStatus' => {
      Description => 'État du flash',
      PrintConv => {
        'Built-in' => 'Flash intégré',
        'Built-in Flash Fired' => 'Flash intégré déclenché',
        'Built-in Flash Inhibited' => 'Flash intégré inhibé',
        'Built-in Flash present' => 'Flash intégré présent',
        'External' => 'Flash externe',
        'External Flash Fired' => 'Flash externe déclenché',
        'External Flash present' => 'Flash externe présent',
        'External, Did not fire' => 'Flash externe, non déclenché',
        'External, Fired' => 'Flash externe, déclenché',
        'Flash Inhibited' => 'Flash inhibé',
        'Internal, Did not fire' => 'Flash interne, non déclenché',
        'Internal, Did not fire (0x08)' => 'Flash interne, non déclenché (0x08)',
        'Internal, Fired' => 'Flash interne, déclenché',
        'No Flash present' => 'Aucun flash présent',
        'None' => 'Aucun',
        'Off' => 'Désactivé',
        'Off (1)' => 'Désactivé (1)',
      },
    },
   'FlashSyncSpeedAv' => {
      Description => 'Vitesse synchro en mode Av',
      PrintConv => {
        '1/200 Fixed' => '1/200 fixe',
        '1/250 Fixed' => '1/250 fixe',
        '1/300 Fixed' => '1/300 fixe',
      },
    },
   'FlashType' => {
      Description => 'Type de flash',
      PrintConv => {
        'Built-In Flash' => 'Intégré',
        'External' => 'Externe',
        'None' => 'Aucune',
      },
    },
   'FlashWarning' => {
      Description => 'Avertissement de flash',
      PrintConv => {
        'No' => 'Non',
        'Off' => 'Désactivé',
        'On' => 'Activé',
        'Yes (flash required but disabled)' => 'Oui (flash requis mais désactivé)',
      },
    },
   'FlashpixVersion' => 'Version Flashpix',
   'FlexibleSpotPosition' => 'Position flexible du spot',
   'FlickerReduce' => {
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'FlightDegree' => 'Degré en vol',
   'FlightPitchDegree' => 'Degré d\'inclinaison en vol',
   'FlightRollDegree' => 'Degré de roulis en vol',
   'FlightSpeed' => 'Vitesse en vol',
   'FlightXSpeed' => 'Vitesse horizontale (X) en vol',
   'FlightYSpeed' => 'Vitesse verticale (Y) en vol',
   'FlightYawDegree' => 'Degré de lacet en vol',
   'FlightZSpeed' => 'Vitesse en profondeur (Z) du vol',
   'FlipAngle' => 'Angle de retournement',
   'FlipHorizontal' => {
      Description => 'Retournement horizontal',
      PrintConv => {
        'No' => 'Non',
        'Yes' => 'Oui',
      },
    },
   'FlipStatus' => 'État du retournement',
   'FocalLength' => 'Focale de l\'objectif',
   'FocalLength35efl' => 'Focale de l\'objectif',
   'FocalLength35mm' => 'Distance focale 35 mm',
   'FocalLengthIn35mmFormat' => 'Longueur de focale équivalente à un film 35 mm',
   'FocalPlaneAFPointArea' => 'Plan focal de la zone de mise au point automatique',
   'FocalPlaneAFPointLocation1' => 'Localisation de la zone de mise au point automatique du plan focal 1',
   'FocalPlaneAFPointLocation10' => 'Localisation de la zone de mise au point automatique du plan focal 10',
   'FocalPlaneAFPointLocation11' => 'Localisation de la zone de mise au point automatique du plan focal 11',
   'FocalPlaneAFPointLocation12' => 'Localisation de la zone de mise au point automatique du plan focal 12',
   'FocalPlaneAFPointLocation13' => 'Localisation de la zone de mise au point automatique du plan focal 13',
   'FocalPlaneAFPointLocation14' => 'Localisation de la zone de mise au point automatique du plan focal 14',
   'FocalPlaneAFPointLocation15' => 'Localisation de la zone de mise au point automatique du plan focal 15',
   'FocalPlaneAFPointLocation2' => 'Localisation de la zone de mise au point automatique du plan focal 2',
   'FocalPlaneAFPointLocation3' => 'Localisation de la zone de mise au point automatique du plan focal 3',
   'FocalPlaneAFPointLocation4' => 'Localisation de la zone de mise au point automatique du plan focal 4',
   'FocalPlaneAFPointLocation5' => 'Localisation de la zone de mise au point automatique du plan focal 5',
   'FocalPlaneAFPointLocation6' => 'Localisation de la zone de mise au point automatique du plan focal 6',
   'FocalPlaneAFPointLocation7' => 'Localisation de la zone de mise au point automatique du plan focal 7',
   'FocalPlaneAFPointLocation8' => 'Localisation de la zone de mise au point automatique du plan focal 8',
   'FocalPlaneAFPointLocation9' => 'Localisation de la zone de mise au point automatique du plan focal 9',
   'FocalPlaneAFPointsUsed' => {
      Description => 'Points de mise au point automatique du plan focal utilisés',
      PrintConv => {
        '(none)' => 'Aucun',
      },
    },
   'FocalPlaneDiagonal' => 'Diagonale du plan focal',
   'FocalPlaneResolutionUnit' => {
      Description => 'Unité de résolution de plan focal',
      PrintConv => {
        'None' => 'Aucune',
        'inches' => 'Pouces',
        'um' => 'µm (micromètre)',
      },
    },
   'FocalPlaneXResolution' => 'Résolution du plan focal horizontal',
   'FocalPlaneXSize' => 'Taille du plan focal horizontal',
   'FocalPlaneXUnknown' => 'Plan focal inconnu',
   'FocalPlaneYResolution' => 'Résolution du plan focal vertical',
   'FocalPlaneYSize' => 'Taille du plan focal vertical',
   'FocalType' => {
      Description => 'Type de focal',
      PrintConv => {
        'Fixed' => 'Fixe',
      },
    },
   'FocalUnits' => 'Unités focales',
   'Focus' => {
      Description => 'Mise au point',
      PrintConv => {
        'Auto-focus Didn\'t Lock' => 'Mise au point automatique non verrouillée',
        'Auto-focus Locked' => 'Mise au point automatique verrouillée',
        'Manual' => 'Manuelle',
      },
    },
   'FocusContinuous' => {
      Description => 'Mise au point continue',
      PrintConv => {
        'Continuous' => 'Continue',
        'Manual' => 'Manuelle',
        'Single' => 'Simple',
      },
    },
   'FocusDistance' => 'Distance focale',
   'FocusDistance2' => 'Distance focale 2',
   'FocusDistanceLower' => 'Distance focale inférieure',
   'FocusDistanceRange' => 'Plage de distance focale',
   'FocusDistanceRangeWidth' => 'Largeur de la plage de distance focale',
   'FocusDistanceUpper' => 'Distance focale supérieure',
   'FocusFrameSize' => 'Taille focale de mise au point',
   'FocusInfoVersion' => 'Version infos de focus',
   'FocusLocation' => 'Localisation du focus',
   'FocusLocked' => {
      Description => 'Focus verrouillé',
      PrintConv => {
        'Continuous Focus' => 'Focus continu',
        'Manual Focus' => 'Focus manuel',
        'No' => 'Non',
        'Yes' => 'Oui',
      },
    },
   'FocusMode' => {
      Description => 'Mode de mise au point',
      PrintConv => {
        '(none)' => '(aucun)',
        'AF sensor' => 'Capteur AF',
        'AF-A (Focus-priority)' => 'AF-A (Priorité à la mise au point)',
        'AF-A (Release-priority)' => 'AF-A (Priorité à la libération)',
        'AF-C' => 'AF-C (prise de vue en rafale)',
        'AF-C (Focus-priority)' => 'AF-C (Priorité à la mise au point)',
        'AF-C (Release-priority)' => 'AF-C (Priorité à la libération)',
        'AF-S' => 'AF-S (prise de vue unique)',
        'AF-S (Focus-priority)' => 'AF-S (Priorité à la mise au point)',
        'AF-S (Release-priority)' => 'AF-S (Priorité à la libération)',
        'Auto, Continuous' => 'Auto, continue',
        'Auto, Focus button' => 'Bouton autofocus',
        'Continuous' => 'Auto, continue',
        'Continuous AF' => 'Auto, continue AF',
        'Contrast-detect (Focus-priority)' => 'Détection du contraste (Priorité à la mise au point)',
        'Contrast-detect (Release-priority)' => 'Détection du contraste (Priorité au déclenchement)',
        'Custom' => 'Personalisé',
        'Face Detect' => 'Détection de visage',
        'Face detect' => 'Détection de visage',
        'Focus Lock' => 'Verrouillage de la mise au point',
        'Infinity' => 'Infini',
        'Live View Magnification Frame' => 'Cadre d\'agrandissement Live View',
        'Manual' => 'Manuelle',
        'Manual Focus (3)' => 'Mise au point manuelle (3)',
        'Manual Focus (6)' => 'Mise au point manuelle (6)',
        'Movie' => 'Vidéo',
        'Movie Servo AF' => 'Vidéo Servo AF',
        'Movie Snap Focus' => 'Vidéo Snap Focus',
        'Multi-Area Auto Focus' => 'Mise au point automatique multizone',
        'Normal' => 'Normale',
        'One-shot AF' => 'Mise au point automatique en une seule prise',
        'Pan Focus' => 'Hyperfocale',
        'Permanent-AF' => 'Permanent AF',
        'Pinpoint AF' => 'Point de mire AF',
        'Semi-manual' => 'Semi-manuelle',
        'Sequential shooting AF' => 'Prise de vue séquentielle en mise au point automatique',
        'Single' => 'Simple',
        'Single AF' => 'Mise au point automatique unique',
        'Single-Area Auto Focus' => 'Mise au point automatique à zone unique',
        'Snap' => 'Instantané',
        'Starry Sky AF' => 'Ciel étoilé AF',
        'Subject Tracking' => 'Suivi du sujet',
        'Tracking Contrast-detect (Focus-priority)' => 'Suivi de la détection du contraste (priorité à la mise au point)',
        'n/a' => 'Non applicable',
      },
    },
   'FocusMode2' => {
      Description => 'Mode de mise au point 2',
      PrintConv => {
        'AF-C' => 'AF-C (prise de vue en rafale)',
        'AF-S' => 'AF-S (prise de vue unique)',
        'Manual' => 'Manuelle',
      },
    },
   'FocusModeSetting' => {
      PrintConv => {
        'AF-C' => 'AF-C (prise de vue en rafale)',
        'AF-S' => 'AF-S (prise de vue unique)',
        'Manual' => 'Manuelle',
      },
    },
   'FocusPixel' => 'Pixels de mise au point',
   'FocusPosition' => 'Distance de mise au point',
   'FocusPosition2' => 'Distance 2 de mise au point',
   'FocusProcess' => {
      Description => 'Processus de mise au point',
      PrintConv => {
        'AF Not Used' => 'Autofocus non utilisé',
        'AF Used' => 'Autofocus utilisé',
      },
    },
   'FocusRange' => {
      Description => 'Plage de mise au point',
      PrintConv => {
        'Close' => 'Près',
        'Far Range' => 'Distance lointaine',
        'Infinity' => 'Infini',
        'Manual' => 'Manuelle',
        'Middle Range' => 'Distance moyenne',
        'Normal' => 'Normale',
        'Not Known' => 'Non connue',
        'Pan Focus' => 'Hyperfocale',
        'Super Macro' => 'Super macro',
        'Very Close' => 'Très près',
      },
    },
   'FocusRangeIndex' => {
      Description => 'Index de la plage de mise au point',
      PrintConv => {
        '0 (very close)' => '0 (très proche)',
        '1 (close)' => '1 (proche)',
        '6 (far)' => '6 (loin)',
        '7 (very far)' => '7 (très loin)',
      },
    },
   'FocusSetting' => 'Réglage de la mise au point',
   'FocusStepCount' => 'Nombre de pas du focus',
   'FocusTrackingLockOn' => {
      PrintConv => {
        'Normal' => 'Normale',
        'Off' => 'Désactivé',
      },
    },
   'FocusWarning' => {
      Description => 'Alerte de mise au point',
      PrintConv => {
        'Good' => 'Bonne focalisation',
        'Out of focus' => 'Pas de focalisation',
      },
    },
   'FocusingScreen' => 'Ecran de mise au point',
   'ForwardMatrix1' => 'Matrice de déplacement 1',
   'ForwardMatrix2' => 'Matrice de déplacement 2',
   'ForwardMatrix3' => 'Matrice de déplacement 3',
   'ForwardTo' => 'Déplacer vers',
   'FrameNumber' => 'Numéro de vue',
   'FrameRate' => 'Vitesse',
   'FrameSize' => 'Taille du cadre',
   'FreeByteCounts' => 'Nombre d\'octets libres',
   'FreeBytes' => 'Octets libres',
   'FreeOffsets' => 'Offsets libres',
   'FujiFlashMode' => {
      Description => 'Mode Flash Fuji',
      PrintConv => {
        'Commander' => 'Commandeur',
        'External' => 'Externe',
        'Flash Commander' => 'Commandeur de Flash',
        'High Speed Sync (HSS)' => 'Synchronisation haute vitesse (HSS)',
        'Manual' => 'Manuel',
        'Not Attached' => 'Non attaché',
        'Off' => 'Désactivé',
        'On' => 'Activé',
        'Red-eye reduction' => 'Réduction yeux rouges',
        'TTL - Red-eye Flash - 1st Curtain (front)' => 'TTL - Flash yeux rouges - 1er rideau (avant)',
        'TTL - Red-eye Flash - 2nd Curtain (rear)' => 'TTL - Flash yeux rouges - 2eme rideau (arrière)',
        'TTL Auto - 1st Curtain (front)' => 'TTL Auto - 1er rideau (avant)',
        'TTL Auto - 2nd Curtain (rear)' => 'TTL Auto - 2eme rideau (arrière)',
        'TTL Auto - Did not fire' => 'TTL Auto - Non déclenché',
        'TTL Auto - Red-eye Flash - 1st Curtain (front)' => 'TTL Auto - 1er rideau (avant)',
        'TTL Auto - Red-eye Flash - 2nd Curtain (rear)' => 'TTL Auto - 2eme rideau (arrière)',
        'TTL Slow - 1st Curtain (front)' => 'TTL Lent - 1er rideau (avant',
        'TTL Slow - 2nd Curtain (rear)' => 'TTL Lent - 2eme rideau (arrière)',
        'TTL Slow - Red-eye Flash - 1st Curtain (front)' => 'TTL Lent - Flash yeux rouges - 1er rideau (avant',
        'TTL Slow - Red-eye Flash - 2nd Curtain (rear)' => 'TTL Lent - Flash yeux rouges - 2eme rideau (arrière)',
      },
    },
   'FujiLayout' => 'Mise en page Fuji',
   'FujiModel' => 'Modèle Fuji',
   'FujiModel2' => 'Modèle Fuji2',
   'FullImageSize' => 'Taille complète de l\'image',
   'FullName' => 'Nom complet',
   'Func1Button' => {
      PrintConv => {
        'HDR Overlay' => 'Recouvrement HDR',
      },
    },
   'Func1ButtonPlaybackMode' => {
      PrintConv => {
        'HDR Overlay' => 'Recouvrement HDR',
      },
    },
   'Func1ButtonPlusDials' => {
      PrintConv => {
        'Active D-Lighting' => 'D-Lighting actif',
      },
    },
   'Func2Button' => {
      PrintConv => {
        'HDR Overlay' => 'Recouvrement HDR',
      },
    },
   'Func2ButtonPlaybackMode' => {
      PrintConv => {
        'HDR Overlay' => 'Recouvrement HDR',
      },
    },
   'Func2ButtonPlusDials' => {
      PrintConv => {
        'Active D-Lighting' => 'D-Lighting actif',
      },
    },
   'Func3Button' => {
      PrintConv => {
        'HDR Overlay' => 'Recouvrement HDR',
      },
    },
   'Func3ButtonPlaybackMode' => {
      PrintConv => {
        'HDR Overlay' => 'Recouvrement HDR',
      },
    },
   'Func4Button' => {
      PrintConv => {
        'HDR Overlay' => 'Recouvrement HDR',
      },
    },
   'Func4ButtonPlaybackMode' => {
      PrintConv => {
        'HDR Overlay' => 'Recouvrement HDR',
      },
    },
   'FuncButton' => {
      PrintConv => {
        'Active D-Lighting' => 'D-Lighting actif',
      },
    },
   'FuncButtonPlusDials' => {
      PrintConv => {
        'Active D-Lighting' => 'D-Lighting actif',
      },
    },
   'FunctionButton' => {
      PrintConv => {
        'Active D-Lighting' => 'D-Lighting actif',
        'Center-weighted' => 'Pondération centrale',
      },
    },
   'GIFVersion' => 'Version GIF',
   'GPSAltitude' => 'Altitude GPS',
   'GPSAltitudeRef' => {
      Description => 'Référence de l\'altitude GPS',
      PrintConv => {
        'Above Sea Level' => 'Au-dessus du niveau de la mer',
        'Below Sea Level' => 'En-dessous du niveau de la mer',
      },
    },
   'GPSAreaInformation' => 'Nom de la zone GPS',
   'GPSCoordinates' => 'Coordonnées GPS',
   'GPSDOP' => 'Précision de la mesure',
   'GPSDataList' => 'Liste des données GPS',
   'GPSDataList2' => 'Liste des données GPS 2',
   'GPSDateStamp' => 'Horodatage GPS',
   'GPSDateTime' => 'Date/Heure GPS (horloge atomique)',
   'GPSDateTimeRaw' => 'Date/Heure GPS brute',
   'GPSDestAltitude' => 'Altitude GPS de la destination',
   'GPSDestBearing' => 'Azimut GPS de la destination',
   'GPSDestBearingRef' => {
      Description => 'Référence de l\'azimut GPS de la destination',
      PrintConv => {
        'Magnetic North' => 'Nord magnétique',
        'True North' => 'Nord vrai',
      },
    },
   'GPSDestDistance' => 'Distance GPS de la destination',
   'GPSDestDistanceRef' => {
      Description => 'Référence de la distance GPS de la destination',
      PrintConv => {
        'Kilometers' => 'Kilomètres',
        'Nautical Miles' => 'Milles nautiques',
      },
    },
   'GPSDestLatitude' => 'Latitude GPS de la destination',
   'GPSDestLatitudeRef' => {
      Description => 'Référence de la valeur GPS en latitude de la destination',
      PrintConv => {
        'North' => 'Nord',
        'South' => 'Sud',
      },
    },
   'GPSDestLongitude' => 'Longitude GPS de la destination',
   'GPSDestLongitudeRef' => {
      Description => 'Référence de la valeur GPS en longitude de la destination',
      PrintConv => {
        'East' => 'Est',
        'West' => 'Ouest',
      },
    },
   'GPSDifferential' => {
      Description => 'Différentiel GPS',
      PrintConv => {
        'Differential Corrected' => 'Différentiel corrigé',
        'No Correction' => 'Sans correction',
      },
    },
   'GPSFramingAltitude' => 'Altitude de cadrage GPS',
   'GPSFramingLatitude' => 'Latitude de cadrage GPS',
   'GPSFramingLongitude' => 'Longitude de cadrage GPS',
   'GPSHPositioningError' => 'Erreur horizontale de la position GPS',
   'GPSHorizontalAccuracy' => 'Précision GPS horizontale',
   'GPSImgDirection' => 'Direction GPS de la prise de vue',
   'GPSImgDirectionRef' => {
      Description => 'Référence de la direction GPS de la prise de vue',
      PrintConv => {
        'Magnetic North' => 'Nord magnétique',
        'True North' => 'Nord vrai',
      },
    },
   'GPSInfo' => 'Pointeur IFD d\'informations GPS',
   'GPSLatitude' => 'Latitude GPS',
   'GPSLatitudeRaw' => 'Latitude GPS brute',
   'GPSLatitudeRef' => {
      Description => 'Référence de la valeur GPS en latitude',
      PrintConv => {
        'North' => 'Nord',
        'South' => 'Sud',
      },
    },
   'GPSLog' => 'Journal GPS',
   'GPSLongitude' => 'Longitude GPS',
   'GPSLongitudeRaw' => 'Longitude GPS brute',
   'GPSLongitudeRef' => {
      Description => 'Référence de la valeur GPS en Longitude',
      PrintConv => {
        'East' => 'Est',
        'West' => 'Ouest',
      },
    },
   'GPSLongtitude' => 'Longitude GPS',
   'GPSMapDatum' => 'Données de surveillance géodésique utilisées',
   'GPSMeasureMode' => {
      Description => 'Mode de mesure GPS',
      PrintConv => {
        '2-D' => '2D',
        '2-Dimensional' => 'Mesure à deux dimensions',
        '2-Dimensional Measurement' => 'Mesure bidimensionnelle',
        '3-D' => '3D',
        '3-Dimensional' => 'Mesure à trois dimensions',
        '3-Dimensional Measurement' => 'Mesure tridimensionnelle',
        'No Measurement' => 'Aucune mesure',
      },
    },
   'GPSMode' => 'Mode GPS',
   'GPSPitch' => 'Tangage GPS',
   'GPSPosition' => 'Position GPS',
   'GPSProcessingMethod' => 'Nom de la méthode de traitement GPS',
   'GPSRoll' => 'Roulis GPS',
   'GPSSatellites' => 'Satellites GPS utilisés pour la mesure',
   'GPSSpeed' => 'Vitesse du récepteur GPS',
   'GPSSpeed3D' => 'Vitesse GPS 3D',
   'GPSSpeedAccuracy' => 'Précision de la vitesse du GPS',
   'GPSSpeedRaw' => 'Vitesse GPS brute',
   'GPSSpeedRef' => {
      Description => 'Référence de vitesse du GPS',
      PrintConv => {
        'km/h' => 'Kilomètres par heure',
        'knots' => 'nœuds',
        'mph' => 'Miles par heure',
      },
    },
   'GPSStatus' => {
      Description => 'État du récepteur GPS',
      PrintConv => {
        'Measurement Active' => 'Mesure active',
        'Measurement Void' => 'Mesure vide',
      },
    },
   'GPSTimeStamp' => 'Heure GPS (horloge atomique)',
   'GPSTrack' => 'Tracé GPS',
   'GPSTrackRaw' => 'Tracé GPS brut',
   'GPSTrackRef' => {
      Description => 'Référence pour du tracé GPS',
      PrintConv => {
        'Magnetic North' => 'Nord magnétique',
        'True North' => 'Nord vrai',
      },
    },
   'GPSVersionID' => 'Identifiant de la version GPS',
   'GTModelType' => {
      Description => 'Type de modèle GT',
      PrintConv => {
        'Geocentric' => 'Géocentrique',
        'Geographic' => 'Géographique',
        'Projected' => 'Projeté',
        'User Defined' => 'Défini par l\'utilisateur',
      },
    },
   'GainBase' => 'Gain de référence',
   'GainControl' => {
      Description => 'Contrôle du gain',
      PrintConv => {
        'High gain down' => 'Réduction du gain élevé',
        'High gain up' => 'Augmentation du gain élevé',
        'Low gain down' => 'Réduction du gain faible',
        'Low gain up' => 'Augmentation du gain faible',
        'None' => 'Aucun',
      },
    },
   'GammaBlackPoint' => 'Gamma point noir',
   'GammaBlue' => 'Gamma bleu',
   'GammaColorTone' => 'Gamma Tonalité de couleur',
   'GammaCompensatedValue' => 'Valeur de compensation gamma',
   'GammaContrast' => 'Gamma Contraste',
   'GammaCurveOutputRange' => 'Plage de sortie de la courbe gamma',
   'GammaEnable' => 'Activation du gamma',
   'GammaGreen' => 'Gamma vert',
   'GammaLinear' => {
      Description => 'Gamma linéaire',
      PrintConv => {
        'No' => 'Non',
        'Yes' => 'Oui',
      },
    },
   'Gapless' => {
      PrintConv => {
        'No' => 'Non',
        'Yes' => 'Oui',
      },
    },
   'GenProfileCompatibilityFlags' => {
      Description => 'Indicateurs de compatibilité du profil Générique',
      PrintConv => {
        '3D Main' => '3D principal',
        'Format Range Extensions' => 'Extensions de la plage de formats',
        'High Throughput' => 'Haut débit',
        'High Throughput Screen Content Coding Extensions' => 'Extensions du codage du contenu de l\'écrans à haut débit',
        'Main' => 'Principal',
        'Main 10' => 'Principal 10',
        'Main Still Picture' => 'Photo principale',
        'Multiview Main' => 'Multi-vues principale',
        'No Profile' => 'Aucun profil',
        'Scalable Format Range Extensions' => 'Extensions évolutives de la plage de formats',
        'Scalable Main' => 'Évolutif principal',
        'Screen Content Coding Extensions' => 'Extensions du codage du contenu de l\'écran',
      },
    },
   'GeneralLevelIDC' => 'Niveau général IDC',
   'GeneralProfileIDC' => {
      Description => 'Profil général IDC',
      PrintConv => {
        '3D Main' => '3D principal',
        'Format Range Extensions' => 'Extensions de la plage de formats',
        'High Throughput' => 'Haut débit',
        'High Throughput Screen Content Coding Extensions' => 'Extensions du codage du contenu de l\'écrans à haut débit',
        'Main' => 'Principal',
        'Main 10' => 'Principal 10',
        'Main Still Picture' => 'Photo principale',
        'Multiview Main' => 'Multi-vues principale',
        'No Profile' => 'Aucun profil',
        'Scalable Format Range Extensions' => 'Extensions évolutives de la plage de formats',
        'Scalable Main' => 'Évolutif principal',
        'Screen Content Coding Extensions' => 'Extensions du codage du contenu de l\'écran',
      },
    },
   'GeneralProfileSpace' => {
      Description => 'Espace du profil général',
      PrintConv => {
        'Conforming' => 'Conforme',
      },
    },
   'GeneralTierFlag' => {
      Description => 'Drapeau de niveau général',
      PrintConv => {
        'High Tier' => 'Niveau supérieur',
        'Main Tier' => 'Niveau principal',
      },
    },
   'GeoTiffAsciiParams' => 'Tag de paramètres Ascii GeoTiff',
   'GeoTiffDirectory' => 'Tag de répertoire de clé GeoTiff',
   'GeoTiffDoubleParams' => 'Tag de paramètres doubles GeoTiff',
   'GeographicalCoordinates' => 'Coordonnées géographiques',
   'Geography' => 'Géographie',
   'Geolocation' => 'Géolocalisation',
   'GeologicalContext' => 'Contexte géologique',
   'GeologicalContextBed' => 'Lit du contexte géologique',
   'GeologicalContextFormation' => 'Formation du contexte géologique',
   'GeologicalContextGroup' => 'Groupe du contexte géologique',
   'GeologicalContextID' => 'Identifiant du contexte géologique',
   'GeologicalContextMember' => 'Membre du contexte géologique',
   'GeometricDistortionParams' => 'Paramètres de la distorsion géométrique',
   'GeometricMaximumDistortion' => 'Distorsion géométrique maximale',
   'GeometricalProperties' => 'Propriétés géométriques',
   'GimbalDegree' => 'Degré du gimbal',
   'GimbalPitch' => 'Inclinaison du gimbal',
   'GimbalPitchDegree' => 'Degré d\'inclinaison du gimbal',
   'GimbalReverse' => 'Inversion du gimbal',
   'GimbalRoll' => 'Roulis du gimbal',
   'GimbalRollDegree' => 'Degré de roulis du gimbal',
   'GimbalYaw' => 'Lacet du gimbal',
   'GimbalYawDegree' => 'Degré de lacet du gimbal',
   'Good' => 'Bon',
   'GoogleBot' => 'Bot Google',
   'GoogleHostHeader' => 'En-tête de l\'hôte Google',
   'GooglePingMessage' => 'Message Ping Google',
   'GooglePingURL' => 'URL de ping Google',
   'GooglePlusUploadCode' => 'Code de téléchargement de Google Plus',
   'GoogleSourceData' => 'Données sources de Google',
   'GoogleStartTime' => 'Heure de début Google',
   'GoogleTrackDuration' => 'Durée du suivi Google',
   'Gradation' => {
      PrintConv => {
        'Auto-Override' => 'Priorité automatique',
        'High Key' => 'Tons clairs',
        'Low Key' => 'Tons sombres',
        'Normal' => 'Normale',
        'User-Selected' => 'Sélection utilisateur',
        'n/a' => 'Non applicable',
      },
    },
   'GrayResponseCurve' => 'Courbe de réponse grise',
   'GrayResponseUnit' => {
      Description => 'Unités des valeurs de réponses grises',
      PrintConv => {
        '0.0001' => '0.0001 - La valeur représente des millièmes',
        '0.001' => '0.001. La valeur représente des centièmes',
        '0.1' => '0.1. La valeur représente des dixièmes',
        '1e-05' => '1e-05 - LLa valeur représente des dix-millièmes',
        '1e-06' => '1e-06 - La valeur représente des cent-millièmes',
      },
    },
   'GrayTRC' => 'Courbe de reproduction des tonalités grises',
   'GreenAdjust' => 'Ajustement du vert',
   'GreenMatrixColumn' => 'Colonne de la matrice verte',
   'GreenTRC' => 'Courbe de reproduction des tonalités vertes',
   'GridDisplay' => {
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'GridFocalDistance' => 'Distance focale de la grille',
   'GridFrameOffsetVector' => 'Vecteur de décalage du cadre de la grille',
   'GridGuidesInfo' => 'Info sur les guides de la grille',
   'GridID' => 'Identifiant de la grille',
   'GridPeriod' => 'Période de quadrillage',
   'GridPitch' => 'Pas de la grille',
   'GridResolution' => 'Résolution de la grille',
   'GridSize' => 'Taille de la grille',
   'GridSpacingMaterial' => 'Matériau d\'espacement de la grille',
   'GridThickness' => 'Épaisseur de la grille',
   'GripBatteryADLoad' => 'Tension accu poignée en charge',
   'GripBatteryADNoLoad' => 'Tension accu poignée à vide',
   'GripBatteryState' => {
      Description => 'État de la batterie de la poignée',
      PrintConv => {
        'Almost Empty' => 'Presque vide',
        'Empty or Missing' => 'Vide ou absent',
        'Full' => 'Pleinz',
        'Running Low' => 'Niveau faible',
      },
    },
   'HCUsage' => 'Usage HC',
   'HDR' => {
      Description => 'HDR auto',
      PrintConv => {
        'Auto-align Off' => 'Alignement automatique désactivé',
        'Auto-align On' => 'Alignement automatique activé',
        'HDR Advanced' => 'HDR avancé',
        'HDR Auto' => 'HDR auto',
        'HDR image (fail 1)' => 'Image HDR (ratée 1)',
        'HDR image (fail 2)' => 'Image HDR (ratée 2)',
        'HDR image (good)' => 'Image HDR (bonne)',
        'Off' => 'Désactivée',
        'On' => 'Activée',
        'On (normal)' => 'Activée (normale)',
        'Uncorrected image' => 'Image incorrecte',
        'n/a' => 'Non applicable',
      },
    },
   'HDREffect' => {
      Description => 'Effet HDR',
      PrintConv => {
        'Art (bold)' => 'Art (gras)',
        'Art (embossed)' => 'Art (embossé)',
        'Art (vivid)' => 'Art (éclatant)',
        'Natural' => 'Naturel',
      },
    },
   'HDRImageType' => {
      Description => 'Type de l\'image HDR',
      PrintConv => {
        'HDR Image' => 'Image HDR',
        'Original Image' => 'Image orginale',
      },
    },
   'HDRInfoVersion' => 'Info version HDR',
   'HDRLevel' => {
      Description => 'Niveau HDR',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'HDRLevel2' => {
      Description => 'Niveau HDR 2',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'HDRSetting' => {
      Description => 'Réglage HDR',
      PrintConv => {
        'Off' => 'Désactivé',
        'On (Auto)' => 'Activé (Auto)',
        'On (Manual)' => 'Activé (Manuel)',
      },
    },
   'HDRShot' => {
      Description => 'Prise de vue HDR',
      PrintConv => {
        'Off' => 'Désactivée',
        'On' => 'Activée',
      },
    },
   'HDRSmoothing' => {
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'HEVCConfigurationVersion' => 'Version de la configuration HEVC',
   'HalftoneHints' => 'Indications sur les demi-teintes',
   'HandlerType' => {
      Description => 'Type de gestionnaire',
      PrintConv => {
        'Alias Data' => 'Données alias',
        'Audio Track' => 'Piste audio',
        'Camera Metadata' => 'Métadonnées d\'appareil photo',
        'Clock Reference' => 'Référence d\'horloge',
        'Data' => 'Données',
        'Hint Track' => 'Piste d\'indice',
        'MPEG-7 Stream' => 'Flux MPEG-7',
        'Metadata' => 'Métadonnées',
        'Metadata Tags' => 'Tags de métadonnées',
        'Non-Real Time Metadata' => 'Métadonnées non Temps réel',
        'Object Content' => 'Contenu d\'objet',
        'Object Descriptor' => 'Descripteur d\'objet',
        'Panasonic Static Metadata' => 'Métadonnées statiques Panasonic',
        'Picture' => 'Image',
        'Private' => 'Privé',
        'Scene Description' => 'Description de scène',
        'Subpicture' => 'Sous-image',
        'Subtitle' => 'Sous-titre',
        'Text' => 'Texte',
        'Time Code' => 'Code temporel',
        'Video Track' => 'Piste vidéo',
      },
    },
   'Headline' => 'Titre principal',
   'HierarchicalSubject' => 'Sujet hiérarchique',
   'HighISONoiseReduction' => {
      Description => 'Réduction du bruit en sensibilité ISO élevée',
      PrintConv => {
        'Active (Medium)' => 'Active (Moyenne)',
        'Active (Strong)' => 'Active (Forte)',
        'Active (Weak)' => 'Active (Faible)',
        'Auto' => 'Auto.',
        'High' => 'Forte',
        'Low' => 'Faible',
        'Medium' => 'Moyenne',
        'Medium High' => 'Moyennement élevée',
        'Medium Low' => 'Moyennement faible',
        'Minimal' => 'Minimale',
        'Normal' => 'Normale',
        'Off' => 'Désactivée',
        'On' => 'Activée',
        'Strong' => 'Importante',
        'Weak' => 'Faible',
        'Weakest' => 'La plus faible',
      },
    },
   'HighISONoiseReduction2' => {
      Description => 'Réduction du bruit en sensibilité ISO élevée 2',
      PrintConv => {
        'High' => 'Forte',
        'Low' => 'Faible',
        'Normal' => 'Normale',
        'Off' => 'Désactivée',
        'n/a' => 'Non applicable',
      },
    },
   'Highlight' => 'Mise en lumière',
   'HighlightTone' => {
      Description => 'Tonalité lumineuse',
      PrintConv => {
        '+1 (medium hard)' => '+1 (moyennement dure)',
        '+2 (hard)' => '+2 (dure)',
        '+3 (very hard)' => '+3 (très dure)',
        '+4 (hardest)' => '+4 (la plus dure)',
        '-1 (medium soft)' => '-1 (moyennement douce)',
        '-2 (soft)' => '-2 (douce)',
        '0 (normal)' => '0 (normale)',
      },
    },
   'HighlightTonePriority' => {
      Description => 'Priorité Tonalités lumineuses',
      PrintConv => {
        'Disable' => 'Désactivée',
        'Enable' => 'Activée',
        'Off' => 'Désactivée',
        'On' => 'Activée',
      },
    },
   'HighlightWarning' => {
      Description => 'Avertissement de mise en lumière',
      PrintConv => {
        'Disabled' => 'Désactivé',
        'No' => 'Non',
        'Yes' => 'Oui',
      },
    },
   'Highlights' => 'Lumières',
   'History' => 'Récapitulatif',
   'HometownCity' => {
      Description => 'Ville de résidence',
      PrintConv => {
        'Adelaide' => 'Adélaïde',
        'Algiers' => 'Alger',
        'Athens' => 'Athène',
        'Beijing' => 'Pékin',
        'Cairo' => 'Le Caire',
        'Caracus' => 'Caracas',
        'Jerusalem' => 'Jérusalem',
        'Kabul' => 'Kaboul',
        'Kathmandu' => 'Kathmandou',
        'Lisbon' => 'Lisbonne',
        'London' => 'Londre',
        'Male' => 'Malé',
        'Manila' => 'Manille',
        'Moscow' => 'Moscou',
        'Noumea' => 'Nouméa',
        'Seoul' => 'Séoul',
        'Singapore' => 'Singapour',
        'Tehran' => 'Téhéran',
        'Warsaw' => 'Varsovie',
        'Yangon' => 'Rangoun',
      },
    },
   'HometownCityCode' => 'Code ville de résidence',
   'HometownDST' => {
      Description => 'Heure d\'été de résidence',
      PrintConv => {
        'No' => 'Non',
        'Yes' => 'Oui',
      },
    },
   'HostComputer' => 'Ordinateur hôte',
   'Hue' => 'Nuance',
   'HueAdjustment' => 'Teinte',
   'HyperfocalDistance' => 'Distance hyperfocale',
   'ICCProfile' => 'Profil ICC',
   'ICCProfileName' => 'Nom du profil ICC',
   'ICC_Profile' => 'Profil de couleur ICC d\'entrée',
   'ID3Size' => 'Taille ID3',
   'IDCCreativeStyle' => {
      Description => 'Style créatif IDC',
      PrintConv => {
        'Adobe RGB' => 'Adobe RVB',
        'Autumn Leaves' => 'Feuilles d\'automne',
        'B&W' => 'N&B',
        'Camera Setting' => 'Réglage de l\'appareil photo',
        'Clear' => 'Clair',
        'Deep' => 'Profond',
        'Landscape' => 'Paysage',
        'Light' => 'Lumière',
        'Neutral' => 'Neutre',
        'Night View' => 'Vue nocturne',
        'Real' => 'Réel',
        'Sepia' => 'Sépia',
        'Sunset' => 'Coucher de soleil',
        'Vivid' => 'Éclatant',
      },
    },
   'IPTC-NAA' => 'Métadonnées IPTC-NAA',
   'IPTCBitsPerSample' => 'Nombre de bits par échantillon IPTC',
   'IPTCDigest' => 'Résumé IPTC',
   'IPTCImageHeight' => 'Hauteur de l\'image IPTC',
   'IPTCImageRotation' => {
      Description => 'Rotation de l\'image IPTC',
      PrintConv => {
        '0' => 'Pas de rotation',
        '180' => 'Rotation de 180 degrés',
        '270' => 'Rotation de 270 degrés',
        '90' => 'Rotation de 90 degrés',
      },
    },
   'IPTCImageWidth' => 'Largeur de l\'image IPTC',
   'IPTCLastEdited' => 'Dernière modification IPTC',
   'IPTCPictureNumber' => 'Numéro d\'image IPTC',
   'IPTCPixelHeight' => 'Hauteur du pixel IPTC',
   'IPTCPixelWidth' => 'Largeur du pixel IPTC',
   'ISO' => 'Sensibilité ISO',
   'ISO2' => 'ISO 2',
   'ISO3166CountryCode' => 'Code pays ISO 3166',
   'ISO639-1LanguageCode' => 'Code langue ISO 639-1',
   'ISO639CaptionsLanguageCode' => 'Code langue des sous-titres ISO 639',
   'ISO639TextLanguageCode' => 'Code langue du texte ISO 639',
   'ISOAuto' => {
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'ISOAutoMax' => 'ISO Auto maximum',
   'ISOAutoMin' => 'ISO Auto minimum',
   'ISOAutoOffset' => 'Décalage automatique de l\'ISO',
   'ISOAutoMinSpeed' => {
      PrintConv => {
        'Auto Fast' => 'Auto rapide',
        'Auto Slow' => 'Auto lent',
      },
    },
   'ISOExpansion' => {
      Description => 'Extension sensibilité ISO',
      PrintConv => {
        'Off' => 'Arrêt',
        'On' => 'Marche',
      },
    },
   'ISOExpansion2' => {
      PrintConv => {
        'Off' => 'Désactivé',
      },
    },
   'ISOFloor' => 'Seuil ISO',
   'ISOInfo' => 'Info ISO',
   'ISOSelection' => 'Choix ISO',
   'ISOSetting' => {
      Description => 'Réglage ISO',
      PrintConv => {
        'Manual' => 'Manuelle',
        'n/a' => 'Non applicable',
      },
    },
   'ISOSpeedExpansion' => {
      Description => 'Extension de sensibilité ISO',
      PrintConv => {
        'No' => 'Non',
        'Yes' => 'Oui',
      },
    },
   'ISOSpeedIncrements' => {
      Description => 'Incréments de sensibilité ISO',
      PrintConv => {
        '1/3 Stop' => 'Palier 1/3',
      },
    },
   'ISOSpeedRange' => {
      Description => 'Régler l\'extension de sensibilité ISO',
      PrintConv => {
        'Disable' => 'Désactivé',
        'Enable' => 'Activée',
      },
    },
   'IT8Header' => 'En-tête IT8',
   'IcingDetected' => {
      PrintConv => {
        'No' => 'Non',
        'Yes' => 'Oui',
        'n/a' => 'Non applicable',
      },
    },
   'Identifier' => 'Identifiant',
   'Illumination' => {
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'Image::ExifTool::Apple::Main' => 'Image::ExifTool::Apple::Principal',
   'Image::ExifTool::Apple::RunTime' => 'Image::ExifTool::Apple::Temps de fonctionnement',
   'Image::ExifTool::Canon::uuid' => 'Image::ExifTool::Canon::uuid',
   'Image::ExifTool::FLIR::GPS_UUID' => 'Image::ExifTool::FLIR::UUID du GPS',
   'ImageAdjustment' => 'Ajustement Image',
   'ImageAreaOffset' => 'Décalage de zone d\'image',
   'ImageAuthentication' => {
      Description => 'Authentication de l\'image',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'ImageBoardID' => 'Identifiant de la carte d\'images',
   'ImageBoundary' => 'Cadre Image',
   'ImageColor' => 'Couleur de l\'image',
   'ImageColorIndicator' => 'Indicateur de couleur de l\'image',
   'ImageColorValue' => 'Valeur de couleur de l\'image',
   'ImageCount' => 'Compteur d\'images',
   'ImageDataSize' => 'Taille de l\'image',
   'ImageDepth' => 'Profondeur de l\'image',
   'ImageDescription' => 'Description de l\'image',
   'ImageDustOff' => {
      Description => 'Dépoussiérage de l\'image',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'ImageEditCount' => 'Compteur de traitement de l\'image',
   'ImageEditing' => {
      Description => 'Traitement de l\'image',
      PrintConv => {
        'Cropped' => 'Recadré',
        'Digital Filter' => 'Filtre numérique',
        'Frame Synthesis?' => 'Synthèse de vue ?',
        'None' => 'Aucun',
      },
    },
   'ImageEffects' => {
      PrintConv => {
        'Cross Process' => 'Traitement croisé',
        'High Key' => 'Tons clairs',
        'Vivid' => 'Éclatant',
      },
    },
   'ImageFileSizeAsDelivered' => {
      Description => 'Taille du fichier image généré',
      PrintConv => {
        'Greater than 50 MB' => 'Supérieure à 50 Mo',
        'Up to 1 MB' => 'Jusqu\'à 1 Mo',
        'Up to 10 MB' => 'Jusqu\'à 10 Mo',
        'Up to 30 MB' => 'Jusqu\'à 30 Mo',
        'Up to 50 MB' => 'Jusqu\'à 50 Mo',
      },
    },
   'ImageGeneration' => {
      Description => 'Génération de l\'image',
      PrintConv => {
        'Original Image' => 'Image orginale',
        'Re-developed from RAW' => 'Redéveloppée à partir du RAW',
      },
    },
   'ImageHeight' => 'Hauteur de l\'image',
   'ImageHistory' => 'Historique de l\'image',
   'ImageID' => 'ID de l\'image',
   'ImageLayer' => 'Couche image',
   'ImageNumber' => 'Numéro de l\'image',
   'ImageNumber2' => 'Numéro de l\'image 2',
   'ImageOptimization' => 'Optimisation de l\'image',
   'ImageOrientation' => {
      Description => 'Orientation de l\'image',
      PrintConv => {
        'Landscape' => 'Paysage',
        'Square' => 'Carré',
      },
    },
   'ImagePixelDepth' => 'Profondeur en pixels de l\'image',
   'ImagePixelFormat' => {
      Description => 'Format des pixels de l\'image',
      PrintConv => {
        '2-byte short integer' => 'Nombre entier court de 2 octets',
        '4-byte float' => 'Nombre flottant de 4 octets',
        '4-byte long integer' => 'Nombre entier long de 4 octets',
        '8-byte double' => 'Nombre entier double de 8 octets',
      },
    },
   'ImageProcessing' => 'Retouche de l\'image',
   'ImageProcessingVersion' => 'Version du traitement d\'image',
   'ImageQuality' => {
      Description => 'Qualité de l\'image',
      PrintConv => {
        '4k Movie' => 'Film 4k',
        'Full HD Movie' => 'Vidéo Full HD',
        'High' => 'Haute',
        'JPEG Basic' => 'JPEG Basique',
        'JPEG Fine' => 'JPEG Fin',
        'Motion Picture' => 'Film d\'animation',
        'NEF (RAW) + JPEG Basic' => 'NEF (RAW) + JPEG basique',
        'NEF (RAW) + JPEG Fine' => 'NEF (RAW) + JPEG Fin',
        'NEF (RAW) + JPEG Norm' => 'NEF (RAW) + JPEG Normal',
        'Normal' => 'Normale',
        'Snap Shot' => 'Instantané',
        'TIF (RGB)' => 'TIF (RVB)',
        'Very High' => 'Très haute',
      },
    },
   'ImageQuality2' => 'Qualité de l\'image 2',
   'ImageReview' => {
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'ImageRotated' => {
      PrintConv => {
        'No' => 'Non',
        'Yes' => 'Oui',
      },
    },
   'ImageSize' => 'Taille de l\'image',
   'ImageSourceData' => 'Données source de l\'image',
   'ImageSpatialExtent' => 'Étendue spatiale de l\'image',
   'ImageStabilization' => {
      Description => 'Stabilisation de l\'image',
      PrintConv => {
        'Off' => 'Désactivée',
        'On' => 'Activée',
        'On (0x3f)' => 'Activée (0x3f)',
        'On (2)' => 'Activée (2)',
        'On (mode 1, continuous)' => 'Activée (mode 1, continu)',
        'On (mode 2, shooting only)' => 'Activée (mode 2, prise de vue uniquement)',
        'On, Body-only' => 'Activée, Corps seul',
        'On, Body-only Panning' => 'Activée, Panoramique du boitier seulement',
        'On, Mode 1' => 'Activée, Mode 1',
        'On, Mode 2' => 'Activée, Mode 2',
        'On, Mode 3' => 'Activée, Mode 3',
        'On, Mode 4' => 'Activée, Mode 4',
        'On, Optical' => 'Activée, Optique',
        'On, Optical Panning' => 'Activée, Panoramique optique',
        'Optical' => 'Optique',
        'Panning' => 'Panoramique',
        'Panning (2)' => 'Panoramique (2)',
        'Sensor-shift' => 'Déplacement du capteur',
        'Shoot Only' => 'Prise de vue uniquement',
        'Shoot Only (2)' => 'Prise de vue uniquement (2)',
        'Slow Shutter' => 'Obturateur à vitesse lente',
        'n/a' => 'Non applicable',
      },
    },
   'ImageStyle' => {
      PrintConv => {
        'Adobe RGB' => 'Adobe RVB',
        'B&W' => 'N&B',
        'Landscape' => 'Paysage',
        'Neutral' => 'Neutre',
        'Night View/Portrait' => 'Vue/Portrait nocturne',
        'Sunset' => 'Coucher de soleil',
        'Vivid' => 'Éclatant',
      },
    },
   'ImageTone' => {
      Description => 'Tonalité de l\'image',
      PrintConv => {
        'Bright' => 'Brillant',
        'Cross Processing' => 'Traitement croisé',
        'Landscape' => 'Paysage',
        'Natural' => 'Naturel',
      },
    },
   'ImageType' => 'Type de l\'image',
   'ImageUniqueID' => 'Identificateur unique de l\'image',
   'ImageWidth' => 'Largeur de l\'image',
   'Indexed' => 'Indexé',
   'InfoButtonWhenShooting' => {
      Description => 'Touche INFO au déclenchement',
      PrintConv => {
        'Displays camera settings' => 'Affiche les réglages en cours',
        'Displays shooting functions' => 'Affiche les fonctions',
      },
    },
   'InkNames' => 'Nom des encres',
   'InkSet' => 'Encrage',
   'IntegrationTime' => 'Temps d\'intégration',
   'IntellectualGenre' => 'Genre intellectuel',
   'IntelligentAuto' => {
      Description => 'Mode Auto intelligent',
      PrintConv => {
        'Advanced' => 'Avancé',
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'IntelligentContrast' => {
      Description => 'Contraste intelligent',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
        'n/a' => 'Non applicable',
      },
    },
   'IntelligentExposure' => {
      Description => 'Exposition intelligente',
      PrintConv => {
        'High' => 'Haute',
        'Low' => 'Basse',
        'Off' => 'Désactivée',
      },
    },
   'IntelligentResolution' => {
      Description => 'Résolution intelligente',
      PrintConv => {
        'Extended' => 'Étendue',
        'High' => 'Haute',
        'Low' => 'Basse',
        'Off' => 'Désactivée',
      },
    },
   'IntensityStereo' => {
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'InterchangeColorSpace' => {
      PrintConv => {
        'CMY (K) Device Dependent' => 'CMY(K) dépendant de l\'appareil',
        'RGB Device Dependent' => 'RVB dépendant de l\'appareil',
      },
    },
   'IntergraphMatrix' => 'Matrice intergraphe',
   'Interlace' => 'Entrelacement',
   'InternalFlash' => {
      Description => 'Zoom du flash interne',
      PrintConv => {
        'Commander Mode' => 'Mode Maître',
        'Fired' => 'Flash déclenché',
        'Manual' => 'Manuelle',
        'No' => 'Flash non déclenché',
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'InternalFlashMode' => {
      Description => 'Segment de mesure flash esclave 2',
      PrintConv => {
        'Did not fire, (Unknown 0xf4)' => 'Hors service (inconnue 0xF4)',
        'Did not fire, Auto' => 'Hors service, auto',
        'Did not fire, Auto, Red-eye reduction' => 'Hors service, auto, réduction yeux rouges',
        'Did not fire, Normal' => 'Hors service, normal',
        'Did not fire, Red-eye reduction' => 'Hors service, réduction yeux rouges',
        'Did not fire, Slow-sync' => 'Hors service, synchro lente',
        'Did not fire, Slow-sync, Red-eye reduction' => 'Hors service, synchro lente, réduction yeux rouges',
        'Did not fire, Trailing-curtain Sync' => 'Hors service, synchro 2e rideau',
        'Did not fire, Wireless (Control)' => 'Hors service, sans cordon (contrôleur)',
        'Did not fire, Wireless (Master)' => 'Hors service, sans cordon (maître)',
        'Fired' => 'Activé',
        'Fired, Auto' => 'En service, auto',
        'Fired, Auto, Red-eye reduction' => 'En service, auto, réduction yeux rouges',
        'Fired, Red-eye reduction' => 'En service, réduction yeux rouges',
        'Fired, Slow-sync' => 'En service, synchro lente',
        'Fired, Slow-sync, Red-eye reduction' => 'En service, synchro lente, réduction yeux rouges',
        'Fired, Trailing-curtain Sync' => 'En service, synchro 2e rideau',
        'Fired, Wireless (Control)' => 'En service, sans cordon (contrôleur)',
        'Fired, Wireless (Master)' => 'En service, sans cordon (maître)',
        'n/a - Off-Auto-Aperture' => 'Non applicable - Auto-diaph hors service',
      },
    },
   'InternalFlashStrength' => 'Segment de mesure flash esclave 4',
   'InternalIDNumber' => 'Numéro d\'identification interne',
   'InternalLensSerialNumber' => 'Numéro de série de l\'objectif interne',
   'InternalNDFilter' => 'Filtre ND interne',
   'InternalName' => 'Nom interne',
   'InternalSerialNumber' => 'Numéro de série interne',
   'InternalVersionNumber' => 'Numéro de version interne',
   'InteropIndex' => {
      Description => 'Identification d\'interopérabilité',
      PrintConv => {
        'R03 - DCF option file (Adobe RGB)' => 'R03: fichier d\'option DCF (Adobe RVB)',
        'R98 - DCF basic file (sRGB)' => 'R98: fichier de base DCF (sRVB)',
        'THM - DCF thumbnail file' => 'THM: fichier de vignette DCF',
      },
    },
   'InteropOffset' => 'Indicateur d\'interfonctionnement',
   'InteropVersion' => 'Version d\'interopérabilité',
   'IptcLastEdited' => 'Dernière édition IPTC',
   'JFIFVersion' => 'Version JFIF',
   'JPEG-HEIFSwitch' => {
      Description => 'Sélecteur JPEG-HEIF',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'JPEGACTables' => 'Tableaux AC JPEG',
   'JPEGDCTables' => 'Tableaux DC JPEG',
   'JPEGLosslessPredictors' => 'Prédicteurs JPEG sans perte',
   'JPEGPointTransforms' => 'Transformations de point JPEG',
   'JPEGProc' => 'Proc JPEG',
   'JPEGQTables' => 'Tableaux Q JPEG',
   'JPEGQuality' => {
      Description => 'Qualité',
      PrintConv => {
        'Basic' => 'Basique',
        'Extra Fine' => 'Extra fine',
        'High' => 'Haute',
        'Standard' => 'Normale',
        'Very High' => 'Trés haute',
        'n/a' => 'Non applicable',
        'n/a (Movie)' => 'Non applicable (vidéo)',
        'n/a (RAW only)' => 'Non applicable (RAW seulement)',
      },
    },
   'JPEGRestartInterval' => 'Intervalle de redémarrage JPEG',
   'JPEGTables' => 'Tableaux JPEG',
   'JobID' => 'ID de la tâche',
   'JpgFromRaw' => 'Jpeg à partir des données RAW',
   'JpgFromRawLength' => 'Longueur du Jpeg à partir des données RAW',
   'JpgFromRawStart' => 'Début du Jpeg à partir des données RAW',
   'JpgRecordedPixels' => {
      Description => 'Pixels enregistrés JPEG',
      PrintConv => {
        '10 MP' => '10 Mpx',
        '2 MP' => '2 Mpx',
        '6 MP' => '6 Mpx',
      },
    },
   'Keyword' => 'Mot-clé',
   'Keywords' => 'Mots-clés',
   'LC1' => 'Données d\'objectif',
   'LC10' => 'Données mv\' nv\'',
   'LC11' => 'Données AVC 1/EXP',
   'LC12' => 'Données mv1 Avminsif',
   'LC14' => 'Données UNT_12 UNT_6',
   'LC15' => 'Données d\'adaptation de flash incorporé',
   'LC2' => 'Code de distance',
   'LC3' => 'Valeur K',
   'LC4' => 'Données de correction d\'aberration à courte distance',
   'LC5' => 'Données de correction d\'aberration chromatique',
   'LC6' => 'Données d\'aberration d\'ouverture',
   'LC7' => 'Données de condition minimale de déclenchement AF',
   'LCDDisplayAtPowerOn' => {
      Description => 'État LCD lors de l\'allumage',
      PrintConv => {
        'Display' => 'Allumé',
        'Retain power off status' => 'État précédent',
      },
    },
   'LCDDisplayReturnToShoot' => {
      Description => 'Affich. LCD -> Prise de vues',
      PrintConv => {
        'Also with * etc.' => 'Aussi par * etc.',
        'With Shutter Button only' => 'Par déclencheur uniq.',
      },
    },
   'LCDIllumination' => {
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'LCDIlluminationDuringBulb' => {
      Description => 'Éclairage LCD pendant pose longue',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'LCDPanels' => {
      Description => 'Ecran LCD supérieur/arrière',
      PrintConv => {
        'ISO/File no.' => 'ISO/No. fichier',
        'ISO/Remain. shots' => 'ISO/Vues restantes',
        'Remain. shots/File no.' => 'Vues restantes/No. fichier',
        'Shots in folder/Remain. shots' => 'Vues dans dossier/Vues restantes',
      },
    },
   'LCHEditor' => {
      Description => 'Éditeur LCH',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'LameQuality' => 'Qualité Lame',
   'LameStereoMode' => {
      Description => 'Mode stéréo Lame',
      PrintConv => {
        'Dual Channels' => 'Doubles canaux',
        'Forced Joint Stereo' => 'Stéréo jointe forcée',
        'Intensity Stereo' => 'Stéréo Intensité',
        'Joint Stereo' => 'Stéréo jointe',
        'Stereo' => 'Stéréo',
      },
    },
   'Landmark' => 'Point de repère',
   'LandmarkCounter' => 'Compteur de points de repères',
   'Language' => 'Langage',
   'LanguageCode' => {
      Description => 'Code langue',
      PrintConv => {
        'Albanian' => 'Albanais',
        'Arabic' => 'Arabe',
        'Azeri' => 'Azéri',
        'Belarusian' => 'Belarusse',
        'Bulgarian' => 'Bulgare',
        'Byelorussian' => 'Biélorusse',
        'Chinese (Simplified)' => 'Chinois (Simplifié)',
        'Chinese (Traditional)' => 'Chinois (Traditionnel)',
        'Cornish' => 'Cornique',
        'Croato-Serbian (Latin)' => 'Serbo-Croate',
        'Czech' => 'Tchèque',
        'Danish' => 'Danois',
        'Dutch' => 'Flamand',
        'Dutch (Belgian)' => 'Flamand (Belge)',
        'English (Australian)' => 'Anglais (Australien)',
        'English (British)' => 'Anglais (Britanique)',
        'English (Canadian)' => 'Anglais (Canadien)',
        'English (U.S.)' => 'Anglais (U.S.)',
        'English (US)' => 'Anglais (US)',
        'Estonian' => 'Estonien',
        'Finnish' => 'Finnois',
        'French' => 'Français',
        'French (Belgian)' => 'Français (Belge)',
        'French (Canadian)' => 'Français (Canadien)',
        'French (Swiss)' => 'Français (Suisse)',
        'Gaelic' => 'Gaëlic',
        'Georgian' => 'Georgien',
        'German' => 'Allemand',
        'German (Austrian)' => 'Allemand (Autralien)',
        'German (Swiss)' => 'Allemand (Suisse)',
        'Greek' => 'Grecque',
        'Hebrew' => 'Hébreux',
        'Hungarian' => 'Hongrois',
        'Icelandic' => 'Islandais',
        'Indonesian' => 'Indonésien',
        'Italian' => 'Italien',
        'Italian (Swiss)' => 'Italian (Suisse)',
        'Japanese' => 'Japonnais',
        'Korean' => 'Coréen',
        'Latvian' => 'Letton',
        'Lithuanian' => 'Lithuanien',
        'Macedonian' => 'Macedonien',
        'Malay' => 'Malais',
        'Malaysian' => 'Malaysien',
        'Maltese' => 'Maltais',
        'Mongolian' => 'Mongolien',
        'Nepali' => 'Népalais',
        'Neutral' => 'Neutre',
        'Neutral 2' => 'Neutre 2',
        'None' => 'Aucune',
        'Norwegian (Bokmal)' => 'Norvégien (Bokmal)',
        'Norwegian (Bokml)' => 'Norvégien (Bokml)',
        'Norwegian (Nynorsk)' => 'Norvégien (Nynorsk)',
        'Oriya' => 'Odia',
        'Polish' => 'Polonais',
        'Portuguese' => 'Portugais',
        'Portuguese (Brazilian)' => 'Portugais (Brésilen)',
        'Process default' => 'Traitement par défaut',
        'Punjabi' => 'Penjabi',
        'Rhaeto-Romanic' => 'Rhaeto-Romain',
        'Romanian' => 'Roumain',
        'Russian' => 'Russe',
        'Serbo-Croatian (Cyrillic)' => 'Serbo-Croate (Cyrillique)',
        'Simplified Chinese' => 'Chinois simplifié',
        'Slovak' => 'Slovaque',
        'Slovenian' => 'Slovène',
        'Spanish (Castilian)' => 'Espagnol (Castillan)',
        'Spanish (Mexican)' => 'Espagnol (Mexicain)',
        'Spanish (Modern)' => 'Espagnol (Moderne)',
        'Swedish' => 'Suédois',
        'Tamil' => 'Tamoul',
        'Traditional Chinese' => 'Chinois traditionnel',
        'Turkish' => 'Turque',
        'Ukrainian' => 'Ukrainien',
        'Urdu' => 'Ourdou',
        'Uzbek' => 'Ouzbek',
        'Vietnamese' => 'Vietnamien',
        'Walon' => 'Wallon',
        'Welsh' => 'Gallois',
        'Zulu' => 'Zoulou',
      },
    },
   'LanguageIdentifier' => 'Identificateur de langue',
   'LastKeywordIPTC' => 'Dernier mot-clé IPTC',
   'LastKeywordXMP' => 'Dernier mot-clé XMP',
   'LateralChromaticAberration' => {
      Description => 'Aberration chromatique latérale',
      PrintConv => {
        'Off' => 'Désactivée',
        'n/a' => 'Non applicable',
      },
    },
   'LeafData' => 'Données Leaf',
   'Lens' => 'Objectif ',
   'Lens35efl' => 'Objectif 35 efl',
   'LensAFStopButton' => {
      Description => 'Bouton d\'arrêt de l\'autofocus de l\'objectif',
      PrintConv => {
        'AE lock' => 'Verrouillage de l\'exposition automatique',
        'AE lock while metering' => 'Verrouillage de l\'exposition automatique pendant le mesurage',
        'AF Stop' => 'Arrêt de l\'autofocus',
        'AF mode: ONE SHOT <-> AI SERVO' => 'Mode autofocus: UNE PRISE DE VUE <-> IA SERVO',
        'AF point: M -> Auto / Auto -> Ctr.' => 'Points autofocus: M -> Auto / Auto -> Ctr.',
        'AF point: M->Auto/Auto->ctr' => 'Points autofocus: M->Auto/Auto->ctr',
        'AF start' => 'Activation autofocus',
        'AF stop' => 'Arrêt de l\'autofocus',
        'IS start' => 'Activation stabilisation image',
        'Lock AE and start timer' => 'Verrouillage de l\'autofocus et démarrage du minuteur',
        'ONE SHOT <-> AI SERVO' => 'UNE PRISE DE VUE <-> IA SERVO',
        'One Shot <-> AI servo' => 'Une prise de vue <-> IA Servo',
        'Operate AF' => 'Exploitation de l\'autofocus',
        'Spot AF' => 'Spot de l\'autofocus',
        'Switch to registered AF point' => 'Passage au point d\'autofocus enregistré',
      },
    },
   'LensApertureRange' => 'Plage d\'ouverture de l\'objectif',
   'LensAttached' => {
      Description => 'Objectif fixé',
      PrintConv => {
        'No' => 'Non',
        'Yes' => 'Oui',
      },
    },
   'LensData' => 'Valeur K (LC3)',
   'LensDataVersion' => 'Version des Données Objectif',
   'LensDistortionParams' => 'Paramètres de distorsion de l\'objectif',
   'LensDriveNoAF' => {
      Description => 'Pilot. obj. si AF impossible',
      PrintConv => {
        'Focus search off' => 'Pas de recherche du point',
        'Focus search on' => 'Recherche du point',
      },
    },
   'LensE-mountVersion' => 'Version de l\'objectif à monture E',
   'LensFStops' => 'Nombre de diaphs de l\'objectif',
   'LensFirmwareVersion' => 'Version du micrologiciel de l\'objectif',
   'LensFocalLength' => 'Longueur de la focale de l\'objectif',
   'LensFocalRange' => 'Plage de focale de l\'objectif',
   'LensFocusFunctionButtons' => {
      Description => 'Boutons de mise au point de l\'objectif',
      PrintConv => {
        'AE Lock Only' => 'Verrouillage Exposition Auto uniquement',
        'AE/AF Lock' => 'Verrouillage AF/Exposition Auto',
        'AF Lock Only' => 'Verrouillage AF uniquement',
        'AF-Area Mode (Auto Area AF)' => 'AF-Mode zone (AF Zone auto)',
        'AF-Area Mode (Dynamic Area 152 Points)' => 'AF-Mode zone Zone-dynamique (152 points)',
        'AF-Area Mode (Dynamic Area 153 Points)' => 'AF-Mode zone Zone-dynamique (153 points)',
        'AF-Area Mode (Dynamic Area 25 Points)' => 'AF-Mode zone Zone-dynamique (25 points)',
        'AF-Area Mode (Dynamic Area 72 Points)' => 'AF-Mode zone Zone-dynamique (72 points)',
        'AF-Area Mode (Dynamic Area 9 Points)' => 'AF-Mode zone Zone-dynamique (9 points)',
        'AF-Area Mode (Group Area AF)' => 'AF-Mode zone (AF Zone-groupe)',
        'AF-Area Mode (Single)' => 'AF-Mode zone (Point unique)',
        'AF-Area Mode + AF-On (Auto Area AF)' => 'AF-Mode zone: AF Activé (AF Zone auto)',
        'AF-Area Mode + AF-On (Dynamic Area 152 Points)' => 'AF-Mode zone: AF Activé (AF Zone-dynamique (152 points)',
        'AF-Area Mode + AF-On (Dynamic Area 153 Points)' => 'AF-Mode zone: AF Activé (AF Zone-dynamique (153 points)',
        'AF-Area Mode + AF-On (Dynamic Area 25 Points)' => 'AF-Mode zone: AF Activé (AF Zone-dynamique (25 points)',
        'AF-Area Mode + AF-On (Dynamic Area 72 Points)' => 'AF-Mode zone: AF Activé (AF Zone-dynamique (72 points)',
        'AF-Area Mode + AF-On (Dynamic Area 9 Points)' => 'AF-Mode zone: AF Activé (AF Zone-dynamique (9 points)',
        'AF-Area Mode + AF-On (Group Area AF)' => 'AF-Mode zone: AF Activé (AF Zone-groupe)',
        'AF-Area Mode + AF-On (Single)' => 'AF-Mode zone: AF Activé (Unique)',
        'AF-Area Mode:  Single-point AF' => 'AF-Mode zone: AF Point unique',
        'AF-Area Mode: Auto area AF' => 'AF-Mode zone: AF Zone auto',
        'AF-Area Mode: Dynamic-area AF (21 points)' => 'AF-Mode zone: AF Zone-dynamique (21 points)',
        'AF-Area Mode: Dynamic-area AF (51 points)' => 'AF-Mode zone: AF Zone-dynamique (51 points)',
        'AF-Area Mode: Dynamic-area AF (9 points)' => 'AF-Mode zone: AF Zone-dynamique (9 points)',
        'AF-Area Mode: Group-area AF' => 'AF-Mode zone: AF Zone-groupe',
        'AF-On' => 'AF-Activé',
        'Disable Synchronized Release' => 'Désactiver la synchro de déclenchement',
        'Flash Disable/Enable' => 'Désactivation/activation du flash',
        'Preset Focus Point' => 'Point de mise au point prédéfini',
        'Preset focus Point' => 'Point de mise au point prédéfini',
        'Remote Release Only' => 'Déclenchement en télécommande seulement',
        'Sync Release (Master Only)' => 'Synchro déclenchement (Maître seulement)',
        'Sync Release (Remote Only)' => 'Synchro déclenchement (Télécommande seulement)',
      },
    },
   'LensFormat' => {
      Description => 'Format de l\'objectif',
      PrintConv => {
        'Full-frame' => 'Plein cadre',
        'Unknown' => 'Inconnu',
      },
    },
   'LensFunc1Button' => {
      PrintConv => {
        'HDR Overlay' => 'Recouvrement HDR',
      },
    },
   'LensFunc2Button' => {
      PrintConv => {
        'HDR Overlay' => 'Recouvrement HDR',
      },
    },
   'LensID' => 'Identifiant de l\'Objectif',
   'LensIDNumber' => 'Numéro de l\'objectif',
   'LensInfo' => 'Informations sur l\'objectif',
   'LensKind' => 'Type d\'objectif / version (LC0)',
   'LensMake' => 'Marque de l\'objectif',
   'LensMaker' => 'Fabricant de l\'objectif',
   'LensManualDistortionAmount' => 'Taux de distorsion manuel de l\'objectif',
   'LensManufacturer' => 'Fabricant de l\'objectif',
   'LensMaxApertureRange' => 'Plage d\'ouverture maximale de l\'objectif',
   'LensModel' => 'Modèle de l\'objectif',
   'LensModulationOptimizer' => {
      Description => 'Optimiseur de modulation de l\'objectif',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'LensMount' => {
      Description => 'Monture de l\'objectif',
      PrintConv => {
        'A-mount' => 'Monture A',
        'A-mount (3)' => 'Monture A (3)',
        'E-mount' => 'Monture E',
        'Unknown' => 'Inconnue',
      },
    },
   'LensMount2' => {
      Description => 'Monture de l\'objectif 2',
      PrintConv => {
        'A-mount (1)' => 'Monture A (1)',
        'A-mount (5)' => 'Monture A (5)',
        'E-mount' => 'Monture E',
        'Unknown' => 'Inconnue',
      },
    },
   'LensMountType' => {
      Description => 'Type de monture de l\'objectif',
      PrintConv => {
        'F-mount Lens' => 'Objectif à monture F',
        'Z-mount Lens' => 'Objectif à monture Z',
      },
    },
   'LensNumber' => 'Numéro de l\'objectif',
   'LensPartNumber' => 'Numéro de pièce de l\'objectif',
   'LensPositionAbsolute' => 'Position absolue de l\'objectif',
   'LensProfileChromaticAberrationScale' => 'Échelle d\'aberration chromatique du profil de l\'objectif',
   'LensProfileDigest' => 'Résumé du profil de l\'objectif',
   'LensProfileDistortionScale' => 'Échelle de distorsion du profil de l\'objectif',
   'LensProfileEnable' => 'Activation du profil de l\'objectif',
   'LensProfileFilename' => 'Nom du fichier du profil de l\'objectif',
   'LensProfileIsEmbedded' => 'Le profil de l\'objectif est intégré',
   'LensProfileName' => 'Nom du profil de l\'objectif',
   'LensProfileSetup' => 'Réglage du profil de l\'objectif',
   'LensProfileVignettingScale' => 'Échelle de vignettage du profil de l\'objectif',
   'LensProperties' => 'Propriétés de l\'objectif',
   'LensSegmentType' => 'Type de segment de l\'objectif',
   'LensSerialNumber' => 'Numéro de série de l\'objectif',
   'LensShading' => 'Teinte de l\'objectif',
   'LensShutterLock' => {
      Description => 'Verrouillage de l\'obturateur de l\'objectif',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'LensSpec' => 'Spécification de l\'objectif',
   'LensSpecFeatures' => 'Caractéristiques de l\'objectif',
   'LensType' => {
      Description => 'Type d\'objectif',
      PrintConv => {
        'None' => 'Aucun',
        'n/a' => 'Non applicable',
        'smc PENTAX-F 100-300mm F4.5-5.6 or Sigma Lens' => 'smc PENTAX-F 100-300mm F4.5-5.6 ou objectif Sigma',
        'smc PENTAX-F 28-80mm F3.5-4.5 or Tokina Lens' => 'smc PENTAX-F 28-80mm F3.5-4.5 ou objectif Tokina',
        'smc PENTAX-F 35-105mm F4-5.6 or Sigma or Tokina Lens' => 'smc PENTAX-F 35-105mm F4-5.6 ou objectif Sigma ou Tokina',
        'smc PENTAX-F 70-210mm F4-5.6 or Tokina or Takumar Lens' => 'smc PENTAX-F 70-210mm F4-5.6 or Tokina ou objectif Takumar',
        'smc PENTAX-F Macro 50mm F2.8 or Sigma Lens' => 'smc PENTAX-F Macro 50mm F2.8 ou objectif Sigma',
        'smc PENTAX-FA 28-200mm F3.8-5.6 AL[IF] or Tamron Lens' => 'smc PENTAX-FA 28-200mm F3.8-5.6 AL[IF] ou objectif Tamron',
        'smc PENTAX-FA 31mm F1.8 AL Limited' => 'smc PENTAX-FA 31mm F1.8 Limité AL',
      },
    },
   'LensType2' => 'Type d\'objectif 2',
   'LensType3' => 'Type d\'objectif 3',
   'LensTypeMake' => 'Marque du type d\'objectif',
   'LensTypeModel' => 'Modèle du type d\'objectif',
   'LensZoomPosition' => 'Position du zoom de l\'objectif',
   'LevelOrientation' => {
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'LicenseType' => {
      PrintConv => {
        'Unknown' => 'Inconnu',
      },
    },
   'LightReading' => 'Lecture de la lumière',
   'LightSource' => {
      Description => 'Source de lumière',
      PrintConv => {
        'Cloudy' => 'Nuageux',
        'Cool White Fluorescent' => 'Fluorescente type soft',
        'Custom 1-4' => 'Personnalisé 1-4',
        'Day White Fluorescent' => 'Jour blanc Fluorescent',
        'Daylight' => 'Lumière du jour',
        'Daylight Fluorescent' => 'Lumière du jour Fluorescente',
        'Evening Sunlight' => 'Soleil du soir',
        'Fine Weather' => 'Beau temps',
        'Fluorescent' => 'Fluorescente',
        'ISO Studio Tungsten' => 'Tungstène studio ISO',
        'One Touch White Balance' => 'Balance des blancs par simple pression',
        'Other' => 'Autre',
        'Shade' => 'Ombre',
        'Standard Light A' => 'Lumière standard A',
        'Standard Light B' => 'Lumière standard B',
        'Standard Light C' => 'Lumière standard C',
        'Tungsten (Incandescent)' => 'Tungstène (lumière incandescente)',
        'Unknown' => 'Inconnue',
        'Warm White Fluorescent' => 'Fluorescent blanc chaud',
        'White Fluorescent' => 'Fluorescent blanc',
      },
    },
   'LightSourceSpecial' => {
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'LightValue' => 'Luminosité',
   'Lightness' => 'Luminosité',
   'LinearResponseLimit' => 'Limite de réponse linéaire',
   'LinearizationTable' => 'Table de linéarisation',
   'Lit' => {
      PrintConv => {
        'No' => 'Non',
        'Yes' => 'Oui',
      },
    },
   'LithostratigraphicTerms' => 'Termes lithostratigraphiques',
   'LivePhotoVideoIndex' => 'Index vidéo Live Photo',
   'LivePhotoVitalityScore' => 'Note de vitalité Live Photo',
   'LivePhotoVitalityScoringVersion' => 'Version du score de vitalité Live Photo',
   'LiveViewAFMethod' => {
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'LiveViewAFSetting' => {
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'LiveViewExposureSimulation' => {
      Description => 'Simulation d\'exposition directe',
      PrintConv => {
        'Disable (LCD auto adjust)' => 'Désactivée (réglage écran auto)',
        'Enable (simulates exposure)' => 'Activée (simulation exposition)',
      },
    },
   'LiveViewFocusMode' => {
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'LiveViewMetering' => {
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'LiveViewShooting' => {
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'LocalizedCameraModel' => 'Nom localisé du modèle de l\'appareil',
   'Location' => 'Lieu',
   'LocationCreatedCity' => 'Localisation de la ville de création',
   'LocationCreatedCountryName' => 'Localisation du pays de création',
   'LocationCreatedProvinceState' => 'Localisation de la région/état de création',
   'LocationSets' => 'Jeux de localisation',
   'LocationShown' => 'Localisation indiquée',
   'LocationShownCity' => 'Localisation de la Ville indiquée',
   'LocationShownCountryName' => 'Localisation du nom du pays indiqué',
   'LocationShownProvinceState' => 'Localisation de la région/état indiqué',
   'LockMicrophoneButton' => {
      Description => 'Fonction de touche microphone',
      PrintConv => {
        'Protect (hold:record memo)' => 'Protéger (maintien: enregistrement sonore)',
        'Record memo (protect:disable)' => 'Enregistrement sonore (protéger: désactivée)',
      },
    },
   'LongExposureNoiseReduction' => {
      Description => 'Réduction du bruit en exposition longue',
      PrintConv => {
        'Off' => 'Désactivée',
        'Off (65535)' => 'Désactivée (65535)',
        'On' => 'Activée',
        'On (65535)' => 'Activée (65535)',
        'On (dark subtracted)' => 'Activée (obscurité soustraite)',
        'On (unused)' => 'Activée (non utilisée)',
        'n/a' => 'Non applicable',
      },
    },
   'LongExposureNoiseReduction2' => {
      Description => 'Réduction du bruit en exposition longue 2',
      PrintConv => {
        'Off' => 'Désactivée',
        'On' => 'Activée',
        'On (1D)' => 'Activée (1D)',
      },
    },
   'LookupTable' => 'Table de correspondance',
   'LoopStyle' => {
      PrintConv => {
        'Normal' => 'Normale',
      },
    },
   'LuminanceNoiseReduction' => {
      PrintConv => {
        'Low' => 'Bas',
        'Off' => 'Désactivé',
      },
    },
   'MCUVersion' => 'Version MCU',
   'MIEVersion' => 'Version MIE',
   'MIMEType' => 'Type MIME',
   'MPEGVideoRecodingDataset' => 'Jeu de données de recodage vidéo MPEG',
   'MPFVersion' => 'Version MPF',
   'MPImage' => 'Image MP',
   'MPImageFlags' => {
      Description => 'Indicateurs image MP',
      PrintConv => {
        'Dependent child image' => 'Image fille dépendante',
        'Dependent parent image' => 'Image parent dépendante',
        'Representative image' => 'Image représentative',
      },
    },
   'MPImageFormat' => 'Format de l\'image MP',
   'MPImageLength' => 'Longueur de l\'image MP',
   'MPImageStart' => 'Début de l\'image MP',
   'MPImageType' => {
      Description => 'Type de l\'image MP',
      PrintConv => {
        'Baseline MP Primary Image' => 'Image primaire MP de référence',
        'Large Thumbnail (VGA equivalent)' => 'Grande vignette (équivalent VGA)',
        'Large Thumbnail (full HD equivalent)' => 'Grande vignette (équivalent à full HD)',
        'Multi-frame Disparity' => 'Disparité multi-image',
        'Multi-frame Panorama' => 'Panorama multi-image',
        'Undefined' => 'Indéfini',
      },
    },
   'MSStereo' => {
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'Macro' => {
      PrintConv => {
        'Close Focus' => 'Gros plan',
        'Manual' => 'Manuel',
        'Normal' => 'Normale',
        'Off' => 'Désactivée',
        'On' => 'Activée',
        'Super Macro' => 'Super macro',
        'View' => 'Vue',
        'n/a' => 'Non applicable',
      },
    },
   'MacroMode' => {
      Description => 'Mode Macro',
      PrintConv => {
        'Macro Zoom' => 'Zoom Macro',
        'Normal' => 'Normale',
        'Off' => 'Désactivé',
        'On' => 'Activé',
        'Super Macro' => 'Super macro',
        'Tele-Macro' => 'Télé-Macro',
      },
    },
   'MagicFilter' => {
      PrintConv => {
        'Cross Process' => 'Traitement croisé',
        'Cross Process II' => 'Traitement croisé II',
        'Partial Color' => 'Couleur partielle',
        'Partial Color II' => 'Couleur partielle II',
        'Partial Color III' => 'Couleur partielle III',
        'Soft Focus' => 'Flou artistique',
        'Soft Focus 2' => 'Flou artistique 2',
      },
    },
   'MagnifiedView' => {
      Description => 'Agrandissement en lecture',
      PrintConv => {
        'Image playback only' => 'Lecture image uniquement',
        'Image review and playback' => 'Aff. inst. et lecture',
      },
    },
   'MainDialExposureComp' => {
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'MajorBrand' => {
      Description => 'Label majeur',
      PrintConv => {
        '3GPP (.3GP) Release 6 MBMS Extended Presentations' => '3GPP (.3GP) Version 6 Présentations étendues de MBMS',
        '3GPP (.3GP) Release 7 MBMS Extended Presentations' => '3GPP (.3GP) Version 7 Présentations étendues de MBMS',
        '3GPP Media (.3GP) Release 1 (probably non-existent)' => '3GPP Media (.3GP) Version 1 (probablement inexistante)',
        '3GPP Media (.3GP) Release 2 (probably non-existent)' => '3GPP Media (.3GP) Version 2 (probablement inexistante)',
        '3GPP Media (.3GP) Release 3 (probably non-existent)' => '3GPP Media (.3GP) Version 3 (probablement inexistante)',
        '3GPP Media (.3GP) Release 4' => '3GPP Media (.3GP) Version 4',
        '3GPP Media (.3GP) Release 5' => '3GPP Media (.3GP) Version 5',
        '3GPP Media (.3GP) Release 6 Streaming Servers' => '3GPP Media (.3GP) Version 6 Serveurs de streaming',
        '3GPP Media (.3GP) Release 7 Streaming Servers' => '3GPP Media (.3GP) Version 7 Serveurs de streaming',
        '3GPP Release 6 General Profile' => '3GPP Version 6 Profil général',
        '3GPP2 EZmovie for KDDI 3G cellphones' => '3GPP2 EZmovie pour les téléphones portables 3G de KDDI',
        '3GPP2 Media (.3G2) compliant with 3GPP2 C.S0050-0 V1.0' => '3GPP2 Media (.3G2)conforme à 3GPP2 C.S0050-0 V1.0',
        '3GPP2 Media (.3G2) compliant with 3GPP2 C.S0050-A V1.0.0' => '3GPP2 Media (.3G2) conforme à 3GPP2 C.S0050-A V1.0.0',
        '3GPP2 Media (.3G2) compliant with 3GPP2 C.S0050-B v1.0' => '3GPP2 Media (.3G2) conforme à 3GPP2 C.S0050-B v1.0',
        'AV1 Image File Format (.AVIF)' => 'Format de fichier d\'image AV1 (.AVIF)',
        'Apple iTunes AAC-LC (.M4B) Audio Book' => 'Apple iTunes AAC-LC (.M4B) Livre Audio',
        'Apple iTunes AAC-LC (.M4P) AES Protected Audio' => 'Apple iTunes AAC-LC (.M4P) Audio protégée par AES',
        'Apple iTunes Video (.M4V) Video' => 'Apple iTunes Video (.M4V) Vidéo',
        'Audible Enhanced Audiobook (.AAX)' => 'Livre audio enrichi Audible (.AAX)',
        'Audio Book for Adobe Flash Player 9+ (.F4B)' => 'Livre audio pour Adobe Flash Player 9+ (.F4B)',
        'Audio for Adobe Flash Player 9+ (.F4A)' => 'Audio pour Adobe Flash Player 9+ (.F4A)',
        'Canon Digital Camera' => 'Appareil photo numérique Canon',
        'Casio Digital Camera' => 'Appareil photo numérique Casio',
        'DMB MAF aud w/ HE-AAC v2 aud, MOT slides, DLS, JPG/PNG/MNG images' => 'DMB MAF aud avec HE-AAC v2 aud, diapositives MOT, DLS, images JPG/PNG/MNG',
        'DMB MAF aud with HE-AAC aud, JPG/PNG/MNG images' => 'DMB MAF aud avec HE-AAC aud, images JPG/PNG/MNG',
        'DMB MAF audio with ER-BSAC audio, JPG/PNG/MNG images' => 'DMB MAF audio avec ER-BSAC audio, images JPG/PNG/MNG',
        'DMB MAF supporting all the components defined in the specification' => 'DMB MAF prenant en charge tous les composants définis dans la spécification',
        'DMB MAF vid w/ AVC vid, ER-BSAC aud, BIFS, JPG/PNG/MNG images, TS' => 'DMB MAF vid avec vid AVC, ER-BSAC aud, BIFS, images JPG/PNG/MNG, TS',
        'DMB MAF vid w/ AVC vid, HE-AAC aud, BIFS, JPG/PNG/MNG images, TS' => 'DMB MAF vid avec vid AVC, HE-AAC aud, BIFS, images JPG/PNG/MNG, TS',
        'DMB MAF vid w/ AVC vid, HE-AAC v2 aud, BIFS, JPG/PNG/MNG images, TS' => 'DMB MAF vid avec vid AVC, HE-AAC v2 aud, BIFS, images JPG/PNG/MNG, TS',
        'DMB MAF w/ MPEG Layer II aud, MOT slides, DLS, JPG/PNG/MNG images' => 'DMB MAF avec couche MPEG II aud, diapositives MOT, DLS, images JPG/PNG/MNG',
        'DMB MAF, extending DA0A, with 3GPP timed text, DID, TVA, REL, IPMP' => 'DMB MAF, extension de dA0A, avec texte minuté 3GPP, DID, TVA, REL, IPMP',
        'DMB MAF, extending da1a, with 3GPP timed text, DID, TVA, REL, IPMP' => 'DMB MAF, extension de da1a, avec texte minuté 3GPP, DID, TVA, REL, IPMP',
        'DMB MAF, extending da2a, with 3GPP timed text, DID, TVA, REL, IPMP' => 'DMB MAF, extension de da2a, avec texte minuté 3GPP, DID, TVA, REL, IPMP',
        'DMB MAF, extending da3a w/ BIFS, 3GPP timed text, DID, TVA, REL, IPMP' => 'DMB MAF, extension de da3a, avec BIFS, texte minuté 3GPP, DID, TVA, REL, IPMP',
        'DMB MAF, extending dv1a, with 3GPP timed text, DID, TVA, REL, IPMP' => 'DMB MAF, extension de dv1a, avec texte minuté 3GPP, DID, TVA, REL, IPMP',
        'DMB MAF, extending dv2a, with 3GPP timed text, DID, TVA, REL, IPMP' => 'DMB MAF, extension de dv2a, avec texte minuté 3GPP, DID, TVA, REL, IPMP',
        'DMB MAF, extending dv3a, with 3GPP timed text, DID, TVA, REL, IPMP' => 'DMB MAF, extension de dv3a, avec texte minuté 3GPP, DID, TVA, REL, IPMP',
        'DVB (.DVB) over MPEG-2 Transport Stream' => 'DVB (.DVB) sur flux de transport MPEG-2',
        'DVB (.DVB) over RTP' => 'DVB (.DVB) sur RTP',
        'Digital Media Project' => 'Projet média numérique',
        'Dirac (wavelet compression), encapsulated in ISO base media (MP4)' => 'Dirac (compression en ondelettes), encapsulé en base ISO (MP4)',
        'H.264/MPEG-4 AVC (.MP4) Nero Cinema Profile' => 'H.264/MPEG-4 AVC (.MP4) Profil cinéma Nero',
        'H.264/MPEG-4 AVC (.MP4) Nero HDTV Profile' => 'H.264/MPEG-4 AVC (.MP4) Profil HDTV Nero',
        'H.264/MPEG-4 AVC (.MP4) Nero Mobile Profile' => 'H.264/MPEG-4 AVC (.MP4) Profil mobile Nero',
        'H.264/MPEG-4 AVC (.MP4) Nero Portable Profile' => 'H.264/MPEG-4 AVC (.MP4) Profil portable Nero',
        'H.264/MPEG-4 AVC (.MP4) Nero Standard Profile' => 'H.264/MPEG-4 AVC (.MP4) Profil standard Nero',
        'High Efficiency Image Format HEVC sequence (.HEICS)' => 'Sequence HEVC au format High Efficiency Image Format (.HEICS)',
        'High Efficiency Image Format HEVC still image (.HEIC)' => 'Image fixe HEVC au format High Efficiency Image Format (.HEIC)',
        'High Efficiency Image Format sequence (.HEIFS)' => 'Séquence au format High Efficiency Image Format (.HEIFS)',
        'High Efficiency Image Format still image (.HEIF)' => 'Image fixe au format High Efficiency Image Format (.HEIF)',
        'ISMACryp 2.0 Encrypted File' => 'Fichier crypté ISMACryp 2.0',
        'JPEG 2000 Compound Image (.JPM)' => 'Image composée JPEG 2000 (.JPM',
        'JPEG 2000 Compound Image (.JPM) [ISO 15444-6]' => 'Image composée JPEG 2000 (.JPM) [ISO 15444-6]',
        'JPEG 2000 Image (.JP2)' => 'Image JPEG 2000 Image (.JP2)',
        'JPEG 2000 Image (.JP2) [ISO 15444-1 ?]' => 'Image JPEG 2000 (.JP2) [ISO 15444-1 ?]',
        'JPEG 2000 with extensions (.JPX)' => 'Image JPEG 2000 avec extensions (.JPX)',
        'JPEG 2000 with extensions (.JPX) [ISO 15444-2]' => 'Image JPEG 2000 avec extensions (.JPX) [ISO 15444-2]',
        'JPEG XL Image (.JXL)' => 'Image JPEG XL (.JXL)',
        'MP4 Base w/ AVC ext [ISO 14496-12:2005]' => 'Base avec AVC ext [ISO 14496-12:2005]',
        'MP4 v2 [ISO 14496-14] Nero Digital AAC Audio' => 'MP4 v2 [ISO 14496-14] Audio AAC numérique Nero',
        'MPEG-4 (.MP4) Nero Cinema Profile' => 'MPEG-4 (.MP4) Profil cinéma Nero',
        'MPEG-4 (.MP4) Nero HDTV Profile' => 'MPEG-4 (.MP4) Profil HDTV Nero',
        'MPEG-4 (.MP4) Nero Mobile Profile' => 'MPEG-4 (.MP4) Profil mobile Nero',
        'MPEG-4 (.MP4) Nero Portable Profile' => 'MPEG-4 (.MP4) Profil portable Nero',
        'MPEG-4 (.MP4) Nero Standard Profile' => 'MPEG-4 (.MP4) Profil standard Nero',
        'MPEG-4 (.MP4) for SonyPSP' => 'MPEG-4 (.MP4) pour SonyPSP',
        'MPEG-4/3GPP Mobile Profile (.MP4/3GP) (for NTT)' => 'MPEG-4/3GPP Profil mobile (.MP4/3GP) (pour NTT)',
        'Motion JPEG 2000 [ISO 15444-3] General Profile' => 'Motion JPEG 2000 [ISO 15444-3] Profil général',
        'Motion JPEG 2000 [ISO 15444-3] Simple Profile' => 'Motion JPEG 2000 [ISO 15444-3] Profil simple',
        'Panasonic Digital Camera' => 'Appareil photo numérique Panasonic',
        'Photo Player, MAF [ISO/IEC 23000-3]' => 'Lecteur de photos, MAF [ISO/IEC 23000-3]',
        'Protected Video for Adobe Flash Player 9+ (.F4P)' => 'Vidéo protégée pour Adobe Flash Player 9+ (.F4P)',
        'Ross Video' => 'Vidéo Ross',
        'SD Memory Card Video' => 'Carte mémoire SD Vidéo',
        'Samsung stereoscopic, dual stream' => 'Samsung stéréoscopique, double flux',
        'Samsung stereoscopic, single stream' => 'Samsung stéréoscopique, flux unique',
        'Sony / Mobile QuickTime (.MQV) US Patent 7,477,830 (Sony Corp)' => 'Sony / Mobile QuickTime (.MQV) Brevet US 7.477.830 (Sony Corp)',
        'Unknown, from GPAC samples (prob non-existent)' => 'Inconnu, à partir d\'échantillons GPAC (probablement inexistant)',
        'Video for Adobe Flash Player 9+ (.F4V)' => 'Vidéo pour Adobe Flash Player 9+ (.F4V)',
      },
    },
   'MajorVersion' => 'Version majeure',
   'Make' => 'Fabricant',
   'MakeAndModel' => 'Fabricant et modèle',
   'MakerNote' => 'Note du fabricant',
   'MakerNoteApple' => 'Note du fabricant Apple',
   'MakerNoteCanon' => 'Note du fabricant Canon',
   'MakerNoteCasio' => 'Note du fabricant Casio',
   'MakerNoteCasio2' => 'Note du fabricant Casio 2',
   'MakerNoteDJI' => 'Note du fabricant DJI',
   'MakerNoteDJIInfo' => 'Note du fabricant Infos DJI',
   'MakerNoteFLIR' => 'Note du fabricant FLIR',
   'MakerNoteFujiFilm' => 'Note du fabricant FujiFilm',
   'MakerNoteGE' => 'Note du fabricant GE',
   'MakerNoteGE2' => 'Note du fabricant GE 2',
   'MakerNoteHP' => 'Note du fabricant HP',
   'MakerNoteHP2' => 'Note du fabricant HP 2',
   'MakerNoteHP4' => 'Note du fabricant HP 4',
   'MakerNoteHP6' => 'Note du fabricant HP 6',
   'MakerNoteHasselblad' => 'Note du fabricant Hasselblad',
   'MakerNoteISL' => 'Note du fabricant ISL',
   'MakerNoteJVC' => 'Note du fabricant JVC',
   'MakerNoteJVCText' => 'Note du fabricant JVC Texte',
   'MakerNoteKodak10' => 'Note du fabricant Kodak 10',
   'MakerNoteKodak11' => 'Note du fabricant Kodak 11',
   'MakerNoteKodak12' => 'Note du fabricant Kodak 12',
   'MakerNoteKodak1a' => 'Note du fabricant Kodak 1a',
   'MakerNoteKodak1b' => 'Note du fabricant Kodak 1b',
   'MakerNoteKodak2' => 'Note du fabricant Kodak 2',
   'MakerNoteKodak3' => 'Note du fabricant Kodak 3',
   'MakerNoteKodak4' => 'Note du fabricant Kodak 4',
   'MakerNoteKodak5' => 'Note du fabricant Kodak 5',
   'MakerNoteKodak6a' => 'Note du fabricant Kodak 6a',
   'MakerNoteKodak6b' => 'Note du fabricant Kodak 6b',
   'MakerNoteKodak7' => 'Note du fabricant Kodak 7',
   'MakerNoteKodak8a' => 'Note du fabricant Kodak 8a',
   'MakerNoteKodak8b' => 'Note du fabricant Kodak 8b',
   'MakerNoteKodak8c' => 'Note du fabricant Kodak 8c',
   'MakerNoteKodak9' => 'Note du fabricant Kodak 9',
   'MakerNoteKodakUnknown' => 'Note du fabricant Kodak Inconnue',
   'MakerNoteKyocera' => 'Note du fabricant Kyocera',
   'MakerNoteLeica' => 'Note du fabricant Leica',
   'MakerNoteLeica10' => 'Note du fabricant Leica 10',
   'MakerNoteLeica2' => 'Note du fabricant Leica 2',
   'MakerNoteLeica3' => 'Note du fabricant Leica 3',
   'MakerNoteLeica4' => 'Note du fabricant Leica 4',
   'MakerNoteLeica5' => 'Note du fabricant Leica 5',
   'MakerNoteLeica6' => 'Note du fabricant Leica 6',
   'MakerNoteLeica7' => 'Note du fabricant Leica 7',
   'MakerNoteLeica8' => 'Note du fabricant Leica 8',
   'MakerNoteLeica9' => 'Note du fabricant Leica 9',
   'MakerNoteMinolta' => 'Note du fabricant Minolta',
   'MakerNoteMinolta2' => 'Note du fabricant Minolta 2',
   'MakerNoteMinolta3' => 'Note du fabricant Minolta 3',
   'MakerNoteMotorola' => 'Note du fabricant Motorola',
   'MakerNoteNikon' => 'Note du fabricant Nikon',
   'MakerNoteNikon2' => 'Note du fabricant Nikon 2',
   'MakerNoteNikon3' => 'Note du fabricant Nikon 3',
   'MakerNoteNintendo' => 'Note du fabricant Nintendo',
   'MakerNoteOffset' => 'Note du fabricant Offset',
   'MakerNoteOlympus' => 'Note du fabricant Olympus',
   'MakerNoteOlympus2' => 'Note du fabricant Olympus 2',
   'MakerNoteOlympus3' => 'Note du fabricant Olympus 3',
   'MakerNotePanasonic' => 'Note du fabricant Panasonic',
   'MakerNotePanasonic2' => 'Note du fabricant Panasonic 2',
   'MakerNotePanasonic3' => 'Note du fabricant Panasonic 3',
   'MakerNotePentax' => 'Note du fabricant Pentax',
   'MakerNotePentax2' => 'Note du fabricant Pentax 2',
   'MakerNotePentax3' => 'Note du fabricant Pentax 3',
   'MakerNotePentax4' => 'Note du fabricant Pentax 4',
   'MakerNotePentax5' => 'Note du fabricant Pentax 5',
   'MakerNotePentax6' => 'Note du fabricant Pentax 6',
   'MakerNotePentaxUnknown' => 'Note du fabricant Pentax Inconnue',
   'MakerNotePhaseOne' => 'Note du fabricant PhaseOne',
   'MakerNoteReconyx' => 'Note du fabricant Reconyx',
   'MakerNoteReconyx2' => 'Note du fabricant Reconyx 2',
   'MakerNoteReconyx3' => 'Note du fabricant Reconyx 3',
   'MakerNoteRicoh' => 'Note du fabricant Ricoh',
   'MakerNoteRicoh2' => 'Note du fabricant Ricoh 2',
   'MakerNoteRicohPentax' => 'Note du fabricant Ricoh Pentax',
   'MakerNoteRicohText' => 'Note du fabricant Ricoh Texte',
   'MakerNoteSafety' => {
      Description => 'Sécurité de la note du fabricant',
      PrintConv => {
        'Safe' => 'Sûre',
        'Unsafe' => 'Pas sûre',
      },
    },
   'MakerNoteVersion' => 'Version des informations spécifiques fabricant',
   'MakerNotes' => 'Notes fabricant',
   'ManualFlashOutput' => {
      Description => 'Sortie de flash manuelle',
      PrintConv => {
        'Full' => 'Totale',
        'Low' => 'Basse',
        'Medium' => 'Moyenne',
        'n/a' => 'Non applicable',
      },
    },
   'ManualFlashStrength' => {
      Description => 'Force du flash manuel',
      PrintConv => {
        'n/a' => 'Non applicable',
        'n/a (x4)' => 'Non applicable (x4)',
      },
    },
   'ManualFocusDistance' => 'Distance de mise au point manuelle',
   'ManualFocusPointIllumination' => {
      Description => 'Illumination manuelle des points de mise au point',
      PrintConv => {
        'On' => 'Activé',
        'On During Focus Point Selection Only' => 'Activé uniquement pendant la sélection de la zone de mise au point',
      },
    },
   'ManualTv' => {
      Description => 'Régl. Tv/Av manuel pour exp. M',
      PrintConv => {
        'Tv=Control/Av=Main' => 'Tv=Contrôle rapide/Av=Principale',
        'Tv=Control/Av=Main w/o lens' => 'Tv=Contrôle rapide/Av=Principale sans objectif',
        'Tv=Main/Av=Control' => 'Tv=Principale/Av=Contrôle rapide',
        'Tv=Main/Av=Main w/o lens' => 'Tv=Principale/Av=Contrôle rapide sans objectif',
      },
    },
   'ManufactureDate' => 'Date de fabrication',
   'MarkBits' => 'Bits de marquage',
   'Marked' => 'Marqué',
   'MaskedAreas' => 'Zones masquées',
   'MasterDocumentID' => 'ID du document maître',
   'Matteing' => 'Matité',
   'MaxAperture' => 'Ouverture maximale',
   'MaxApertureAtMaxFocal' => 'Ouverture à la focale maxi',
   'MaxApertureAtMinFocal' => 'Ouverture à la focale mini',
   'MaxApertureValue' => 'Ouverture maximale de l\'objectif',
   'MaxAvailHeight' => 'Hauteur max Disponible',
   'MaxAvailWidth' => 'Largeur max Disponible',
   'MaxFocalLength' => 'Focale maxi',
   'MaxSampleValue' => 'Valeur maxi d\'échantillon',
   'MaxVal' => 'Valeur max',
   'MaximumDensityRange' => 'Etendue maximale de densité',
   'MeasuredEV' => 'EV mesuré',
   'MeasuredEV2' => 'EV 2 mesuré',
   'MeasuredEV3' => 'EV 3 mesuré',
   'MeasuredLV' => 'LV mesuré',
   'MeasuredRGGB' => 'RVVB mesuré',
   'MeasuredRGGBData' => 'Donnée RVVB mesurée',
   'Measurement' => 'Observateur de mesure',
   'MeasurementAccuracy' => 'Précision de la mesure',
   'MeasurementBacking' => 'Support de mesure',
   'MeasurementDeterminedBy' => 'Mesure déterminée par',
   'MeasurementDeterminedDate' => 'Date de détermination de la mesure',
   'MeasurementFlare' => 'Flare de mesure',
   'MeasurementGeometry' => {
      Description => 'Géométrie de mesure',
      PrintConv => {
        '0/45 or 45/0' => '0/45 ou 45/0',
        '0/d or d/0' => '0/d ou d/0',
      },
    },
   'MeasurementIlluminant' => 'Illuminant de mesure',
   'MeasurementObserver' => 'Observateur de mesure',
   'MediaBlackPoint' => 'Point noir moyen',
   'MediaDataOffset' => 'Décalage des données du média',
   'MediaDataSize' => 'Taille des données du média',
   'MediaGroupUUID' => 'UUID du groupe de médias',
   'MediaLanguageCode' => 'Code de la langue du média',
   'MediaLocation' => 'Emplacement du média',
   'MediaModifyDate' => 'Date de modification du média',
   'MediaType' => {
      PrintConv => {
        'Normal' => 'Normale',
      },
    },
   'MediaWhitePoint' => 'Point blanc moyen',
   'Megapixels' => 'Mégapixels',
   'MenuButtonDisplayPosition' => {
      Description => 'Position début touche menu',
      PrintConv => {
        'Previous' => 'Précédente',
        'Previous (top if power off)' => 'Précédente (Haut si dés.)',
        'Top' => 'Haut',
      },
    },
   'MenuButtonReturn' => {
      PrintConv => {
        'Previous' => 'Précédente',
        'Top' => 'Haut',
      },
    },
   'MetaFormat' => 'Format méta',
   'MetaImageSize' => 'Taille méta de l\'image',
   'MetaType' => 'Type méta',
   'MetaVersion' => 'Version méta',
   'MetadataDate' => 'Date des metadonnées',
   'Metering' => {
      Description => 'Mesurage',
      PrintConv => {
        'Center-weighted' => 'Pondération centrale',
        'Matrix' => 'Matrice',
      },
    },
   'MeteringMode' => {
      Description => 'Mode de mesure',
      PrintConv => {
        '8-segment' => '8 segments',
        'Average' => 'Moyenne',
        'Center-weighted average' => 'Moyenne pondérée centrale',
        'Default' => 'Par défaut',
        'Evaluative' => 'Évaluative',
        'Highlight' => 'Mise en lumière',
        'Multi-segment' => 'Multi-segments',
        'Multi-spot' => 'Multi-spots',
        'Other' => 'Autre',
        'Partial' => 'Partielle',
        'Pattern+AF' => 'Motif+AF',
        'Spot+Highlight control' => 'Spot+Mise en lumière contrôlée',
        'Spot+Shadow control' => 'Spot+Ombre contrôlée',
        'Unknown' => 'Inconnue',
      },
    },
   'MeteringMode2' => {
      Description => 'Mode de mesure 2',
      PrintConv => {
        'Average' => 'Moyenne',
        'Center-weighted average' => 'Moyenne pondérée centrale',
        'Highlight' => 'Mise en lumière',
        'Multi-segment' => 'Multi-segments',
        'Spot (Large)' => 'Spot (Grand)',
        'Spot (Standard)' => 'Spot (standard)',
      },
    },
   'MeteringMode3' => {
      Description => 'Mode de mesure (3)',
      PrintConv => {
        'Center-weighted average' => 'Moyenne pondérée centrale',
        'Multi-segment' => 'Multi-segments',
      },
    },
   'MeteringOffScaleIndicator' => {
      Description => 'Indicateur de mesure hors échelle',
      PrintConv => {
        'Out of Range' => 'Hors plage de valeurs',
        'Under/Over Range' => 'En dessous/au-dessus de la plage de valeurs',
        'Within Range' => 'Dans la plage de valeurs',
      },
    },
   'MeteringTime' => {
      Description => 'Temps de mesure',
      PrintConv => {
        'No Limit' => 'Non limité',
      },
    },
   'MicrophoneAttenuator' => {
      Description => 'Atténuateur de microphone',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'MicrophoneFrequencyResponse' => {
      Description => 'Réponse en fréquence du microphone',
      PrintConv => {
        'Vocal Range' => 'Bande vocale',
        'Wide Range' => 'Bande large',
      },
    },
   'MinAperture' => 'Ouverture minimale',
   'MinApertureValue' => 'Valeur d\'ouverture minimale',
   'MinFocalLength' => 'Focale mini',
   'MinFocusDistance' => 'Distance minimale de mise au point',
   'MinSampleValue' => 'Valeur mini d\'échantillon',
   'MinSpatialSegmentationIDC' => 'Segmentation spatiale minimale IDC',
   'MinoltaQuality' => {
      Description => 'Qualité Minolta',
      PrintConv => {
        'Economy' => 'Économie',
        'Extra fine' => 'Extra Fine',
        'Normal' => 'Normale',
        'Raw' => 'RAW',
      },
    },
   'MinorVersion' => 'Version mineure',
   'MirrorLockup' => {
      Description => 'Verrouillage du miroir',
      PrintConv => {
        'Disable' => 'Désactivé',
        'Enable' => 'Activé',
        'Enable: Down with Set' => 'Activé: Retour par touche SET',
      },
    },
   'ModDate' => 'Date de modification',
   'Model' => {
      Description => 'Modèle de l\'appareil photo',
      PrintConv => {
        'E-PL1s' => 'E-PL1',
      },
    },
   'Model2' => 'Modèle de l\'appareil photo (2)',
   'ModelAge' => 'Age du modèle de l\'appareil photo',
   'ModelAndVersion' => 'Modèle et version',
   'ModelID' => 'ID modèle',
   'ModelReleaseID' => 'ID de mise à disposition du modèle ',
   'ModelReleaseStatus' => {
      Description => 'État de mise à disposition ',
      PrintConv => {
        'Limited or Incomplete Model Releases' => 'Mise à disposition limitée ou incomplète du modèle',
        'None' => 'Aucun',
        'Not Applicable' => 'Non applicable',
        'Unlimited Model Releases' => 'Mise à disposition non limitée du modèle',
      },
    },
   'ModelReleaseYear' => 'Année de lancement du modèle',
   'ModelTiePoint' => 'Point d\'attache du modèle',
   'ModelTransform' => 'Transformation du modèle',
   'ModelType' => 'Type de modèle',
   'ModelYear' => 'Année du modèle',
   'ModelingFlash' => {
      Description => 'Modélisation du flash',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'ModifiedDigitalGain' => 'Gain numérique modifié',
   'ModifiedPictureStyle' => {
      Description => 'Style d\'image modifié',
      PrintConv => {
        'Adobe RGB' => 'Adobe RVB',
        'CM Set 1' => 'CM jeu 1',
        'CM Set 2' => 'CM jeu 2',
        'Faithful' => 'Fidèle',
        'Fine Detail' => 'Détails fins',
        'High Saturation' => 'Saturation élevée',
        'Landscape' => 'Paysage',
        'Low Saturation' => 'Saturation faible',
        'Neutral' => 'Neutre',
        'None' => 'Aucun',
        'User Def. 1' => 'Défini par l\'utilisateur 1',
        'User Def. 2' => 'Défini par l\'utilisateur 2',
        'User Def. 3' => 'Défini par l\'utilisateur 3',
        'n/a' => 'Non applicable',
      },
    },
   'ModifiedSaturation' => {
      Description => 'Saturation modifiée',
      PrintConv => {
        'CM1 (Red Enhance)' => 'CM1 (Amélioration rouge)',
        'CM2 (Green Enhance)' => 'CM2 (Amélioration verte)',
        'CM3 (Blue Enhance)' => 'CM3 (Amélioration bleue)',
        'CM4 (Skin Tones)' => 'CM4 (Tons de la chair)',
        'Off' => 'Désactivé',
      },
    },
   'ModifiedSensorRedLevel' => 'Niveau rouge modifié du capteur',
   'ModifiedSharpnessFreq' => {
      PrintConv => {
        'High' => 'Haut',
        'Highest' => 'Plus haut',
        'Low' => 'Doux',
        'n/a' => 'Non applicable',
      },
    },
   'ModifiedToneCurve' => {
      PrintConv => {
        'Manual' => 'Manuelle',
      },
    },
   'ModifiedWhiteBalance' => {
      PrintConv => {
        'Cloudy' => 'Temps nuageux',
        'Daylight' => 'Lumière du jour',
        'Daylight Fluorescent' => 'Fluorescente type jour',
        'Fluorescent' => 'Fluorescente',
        'Shade' => 'Ombre',
        'Tungsten' => 'Tungstène (lumière incandescente)',
      },
    },
   'ModifiedWhiteBalanceBlue' => 'Balance des blancs modifiée Bleue',
   'ModifiedWhiteBalanceRed' => 'Balance des blancs modifiée Rouge',
   'ModifyDate' => 'Date de modification du fichier',
   'MoireFilter' => {
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'MonochromeColor' => {
      Description => 'Couleur monochrome',
      PrintConv => {
        '(none)' => '(aucune)',
        'Blue' => 'Bleue',
        'Green' => 'Verte',
        'Normal' => 'Normale',
        'Purple' => 'Violette',
        'Sepia' => 'Sépia',
      },
    },
   'MonochromeFilterEffect' => {
      Description => 'Effet de filtre monochrome',
      PrintConv => {
        'Blue' => 'Bleu',
        'Green' => 'Vert',
        'Infrared' => 'Infrarouge',
        'None' => 'Aucun',
        'Off' => 'Désactivé',
        'Red' => 'Rouge',
        'Yellow' => 'Jaune',
      },
    },
   'MonochromeGrainEffect' => {
      Description => 'Effet de grain monochrome',
      PrintConv => {
        'High' => 'Haut',
        'Low' => 'Bas',
        'Off' => 'Désactivé',
      },
    },
   'MonochromeLinear' => {
      PrintConv => {
        'No' => 'Non',
        'Yes' => 'Oui',
      },
    },
   'MonochromeToningEffect' => {
      Description => 'Effet de tonalité monochrome',
      PrintConv => {
        'Blue' => 'Bleu',
        'Green' => 'Vert',
        'None' => 'Aucun',
        'Purple' => 'Violet',
        'Sepia' => 'Sépia',
      },
    },
   'MovieAF-OnButton' => {
      PrintConv => {
        'Flash Disable/Enable' => 'Flash désactivé/activé',
        'HDR Overlay' => 'Recouvrement HDR',
      },
    },
   'MovieAFAreaMode' => {
      PrintConv => {
        'HDR Overlay' => 'Recouvrement HDR',
        'Single' => 'Simple',
      },
    },
   'MovieAFTrackingSensitivity' => {
      Description => 'Sensibilité du suivi de la mise au point automatique pour les films',
      PrintConv => {
        '1 (High)' => '1 (Haute)',
        '7 (Low)' => '7 (Basse)',
      },
    },
   'MovieAutoDistortionControl' => {
      Description => 'Contrôle automatique de la distorsion pour les vidéo',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'MovieFunc1Button' => {
      PrintConv => {
        'HDR Overlay' => 'Recouvrement HDR',
      },
    },
   'MovieFunc3Button' => {
      PrintConv => {
        'HDR Overlay' => 'Recouvrement HDR',
      },
    },
   'MovieImageArea' => 'Zone de l\'image vidéo',
   'MovieMeteringMode' => {
      Description => 'Mode de mesure vidéo',
      PrintConv => {
        'Center' => 'Centre',
        'Highlight' => 'Mise en lumière',
        'Matrix' => 'Matrice',
      },
    },
   'MovieMultiSelector' => {
      PrintConv => {
        'HDR Overlay' => 'Recouvrement HDR',
      },
    },
   'MovieRecordButtonPlaybackMode' => {
      PrintConv => {
        'HDR Overlay' => 'Recouvrement HDR',
      },
    },
   'MovieSubjectDetection' => {
      PrintConv => {
        'People' => 'Personnes',
      },
    },
   'MultiExposure' => {
      Description => 'Infos Surimpression',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'MultiExposureAutoGain' => {
      Description => 'Auto-expo des surimpressions',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'MultiExposureMode' => {
      Description => 'Mode de surimpression',
      PrintConv => {
        'Off' => 'Désactivé',
      },
    },
   'MultiExposureShots' => 'Nombre de prises de vue',
   'MultiExposureVersion' => 'Version Surimpression',
   'MultiFrameNREffect' => {
      Description => 'Effet RB multi-photos',
      PrintConv => {
        'High' => 'Haut',
      },
    },
   'MultiFrameNoiseReduction' => {
      Description => 'Réduction du bruit multi-photos',
      PrintConv => {
        'None' => 'Aucune',
        'Off' => 'Désactivée',
        'On' => 'Activé(e)',
        'n/a' => 'Non applicable',
      },
    },
   'MultiSelectorPlaybackMode' => {
      PrintConv => {
        'HDR Overlay' => 'Recouvrement HDR',
      },
    },
   'MultiSelectorShootMode' => {
      PrintConv => {
        'HDR Overlay' => 'Recouvrement HDR',
      },
    },
   'MultipleExposureSet' => {
      Description => 'Exposition multiple',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'Mute' => {
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'MyColorMode' => {
      Description => 'Mode MyColor',
      PrintConv => {
        'B&W' => 'N&B',
        'Color Accent' => 'Accentuation de la couleur',
        'Color Swap' => 'Permutation des couleurs',
        'Custom' => 'Personnalisé',
        'Dark Skin Tone' => 'Teint de peau foncé',
        'Light Skin Tone' => 'Teint de peau clair',
        'Neutral' => 'Neutre',
        'Off' => 'Désactivé',
        'Positive Film' => 'Film positif',
        'Sepia' => 'Sépia',
        'Vivid' => 'Éclatant',
        'Vivid Blue' => 'Bleu éclatant',
        'Vivid Green' => 'Vert éclatant',
        'Vivid Red' => 'Rouge éclatant',
      },
    },
   'NDFilter' => {
      Description => 'Filtre ND',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
        'n/a' => 'Non applicable',
      },
    },
   'NEFBitDepth' => {
      Description => 'Profondeur de bits NEF',
      PrintConv => {
        'n/a (JPEG)' => 'Non applicable (JPEG)',
      },
    },
   'NEFCompression' => {
      Description => 'Compression NEF',
      PrintConv => {
        'High Efficiency' => 'Haute efficacité',
        'High Efficiency*' => 'Haute efficacité*',
        'Lossless' => 'Sans perte',
        'Lossy (type 1)' => 'Avec perte (type 1)',
        'Lossy (type 2)' => 'Avec perte (type 2)',
        'Small' => 'Petite',
        'Uncompressed' => 'Non compressé',
        'Uncompressed (reduced to 12 bit)' => 'Non compressé (réduit à 12 bits)',
      },
    },
   'NEFLinearizationTable' => 'Table de Linearization',
   'Name' => 'Nom',
   'NamedColor2' => 'Couleur nommée 2',
   'NativeDigest' => 'Sommaire natif',
   'NativeDisplayInfo' => 'Information sur l\'affichage natif',
   'NetExposureCompensation' => 'Compensation de l\'exposition',
   'NewsPhotoVersion' => 'Version d\'enregistrement news photo',
   'Nickname' => 'Surnom',
   'NikonCaptureData' => 'Données Nikon Capture',
   'NikonCaptureVersion' => 'Version Nikon Capture',
   'NikonMeteringMode' => {
      Description => 'Mode de mesure Nikon',
      PrintConv => {
        'Center' => 'Centre',
        'Highlight' => 'Mise en lumière',
        'Matrix' => 'Matrice',
      },
    },
   'Noise' => 'Bruit',
   'NoiseFilter' => {
      PrintConv => {
        'Low' => 'Bas',
        'Off' => 'Désactivé',
        'n/a' => 'Non applicable',
      },
    },
   'NoiseReduction' => {
      Description => 'Réduction du bruit',
      PrintConv => {
        '(none)' => '(aucune)',
        '+1 (medium strong)' => '+1 (moyennement forte)',
        '+2 (strong)' => '+2 (forte)',
        '+3 (very strong)' => '+3 (très forte)',
        '+4 (strongest)' => '+4 (la plus forte)',
        '-1 (medium weak)' => '-1 (moyennement faible)',
        '-2 (weak)' => '-2 (faible)',
        '-3 (very weak)' => '-3 (très faible)',
        '-4 (weakest)' => '-4 (la plus faible)',
        '0 (normal)' => '0 (normale)',
        'High (+1)' => 'Forte (+1)',
        'Highest (+2)' => 'La plus forte (+2)',
        'Low' => 'Faible',
        'Low (-1)' => 'Faible (-1)',
        'Lowest (-2)' => 'La plus faible (-2)',
        'Medium' => 'Moyenne',
        'Noise Filter' => 'Avec filtre anti-bruit',
        'Noise Filter (ISO Boost)' => 'Avec filtre anti-bruit (ISO Boost)',
        'Noise Reduction' => 'Avec réduction du bruit',
        'Normal' => 'Normale',
        'Off' => 'Désactivée',
        'On' => 'Activée',
        'Standard' => '±0 (normal)',
        'Strong' => 'Forte',
        'Weak' => 'Faible',
        'n/a' => 'Non applicable',
      },
    },
   'NoiseReduction2' => {
      Description => 'Réduction du bruit 2',
      PrintConv => {
        '(none)' => '(aucun)',
        'Noise Filter' => 'Filtre anti-bruit',
        'Noise Filter (ISO Boost)' => 'Filtre anti-bruit (Boost ISO)',
        'Noise Reduction' => 'Réduction du bruit',
      },
    },
   'NoiseReductionApplied' => 'Réduction de bruit appliquée',
   'NoiseReductionIntensity' => 'Intensité de réduction du bruit',
   'NoiseReductionMethod' => {
      Description => 'Méthode de réduction du bruit',
      PrintConv => {
        'Better Quality' => 'Meilleure qualité',
        'Better Quality 2013' => 'Meilleure qualité 2013',
        'Faster' => 'Plus rapide',
      },
    },
   'NoiseReductionSharpness' => 'Réduction de bruit netteté',
   'NominalMaxAperture' => 'Ouverture maxi nominal',
   'NominalMinAperture' => 'Ouverture mini nominal',
   'NumAFPoints' => 'Nombre de points de mise au point automatique',
   'NumChannels' => 'Nombre de canaux',
   'NumColors' => 'Nombre de couleurs',
   'NumFaceElements' => 'Nombre d\'éléments de visages',
   'NumFacePositions' => 'Nombre de positions de visage',
   'NumFonts' => 'Nombre de polices',
   'NumIndexEntries' => 'Nombre d\'entrées d\'index',
   'NumTemporalLayers' => 'Nombre de couches temporelles',
   'NumberOfImages' => 'Nombre d\'images',
   'NumberOfImagesArchived' => 'Nombre d\'images archivées',
   'NumberofInks' => 'Nombre d\'encres',
   'OECFColumns' => 'Colonnes OECF',
   'OECFNames' => 'Noms OECF',
   'OECFRows' => 'Lignes OECF',
   'OECFValues' => 'Valeurs OECF',
   'OPIProxy' => 'Proxy OPI',
   'ObjectAttributeReference' => 'Genre intellectuel',
   'ObjectCycle' => {
      Description => 'Cycle d\'objet',
      PrintConv => {
        'Both Morning and Evening' => 'Les deux',
        'Evening' => 'Soir',
        'Morning' => 'Matin',
      },
    },
   'ObjectFileType' => {
      PrintConv => {
        'None' => 'Aucune',
        'Unknown' => 'Inconnu',
      },
    },
   'ObjectName' => 'Titre',
   'ObjectPreviewData' => 'Données de la miniature de l\\aperçu',
   'ObjectPreviewFileFormat' => 'Format du fichier de la miniature de l\'aperçu',
   'ObjectPreviewFileVersion' => 'Version du format du fichier de la miniature de l\'aperçu',
   'ObjectTypeReference' => 'Référence du type d\'objet',
   'OffsetSchema' => 'Schéma de décalage',
   'OffsetTime' => 'Valeur du décalage UTC',
   'OffsetTimeDigitized' => 'Valeur du décalage UTC des données numériques',
   'OffsetTimeOriginal' => 'Valeur du décalage UTC des données originales',
   'OldSubfileType' => 'Type du sous-fichier',
   'OlympusImageHeight' => 'Hauteur d\'image Olympus',
   'OlympusImageWidth' => 'Largeur d\'image Olympus',
   'OneTouchWB' => {
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'OpticalZoom' => 'Zoom optique',
   'OpticalZoomCode' => 'Code du zoom optique',
   'OpticalZoomMode' => {
      Description => 'Mode du zoom optique',
      PrintConv => {
        'Extended' => 'Étendu',
        'Standard' => 'Normal',
      },
    },
   'OpticalZoomOn' => {
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'Opto-ElectricConvFactor' => 'Facteur de conversion optoélectrique',
   'Orientation' => {
      Description => 'Orientation de l\'image',
      PrintConv => {
        'Flipped left-right' => 'Retourné de gauche à droite',
        'Flipped left-right, then up-down' => 'Retourné de gauche à droite, puis de haut en bas',
        'Flipped up-down' => 'Retourné de haut en bas',
        'Horizontal' => 'Horizontale',
        'Horizontal (normal)' => 'Horizontale (normale)',
        'Mirror horizontal' => 'Mise en miroir horizontal',
        'Mirror horizontal and rotate 270 CW' => 'Mise en miroir horizontal et rotation antihoraire de 270°',
        'Mirror horizontal and rotate 90 CW' => 'Mise en miroir horizontal et rotation antihoraire de 90°',
        'Mirror vertical' => 'Mise en miroir vertical',
        'Rotate 180' => 'Rotation de 180°',
        'Rotate 270 CW' => 'Rotation antihoraire de 270°',
        'Rotate 90 CW' => 'Rotation antihoraire de 90°',
        'Same as source' => 'Identique à la source',
        'Tiled' => 'Carrelée',
        'Vertical' => 'Verticale',
      },
    },
   'Orientation2' => {
      PrintConv => {
        'Horizontal (normal)' => 'Horizontale (normale)',
        'Rotate 180' => 'Rotation de 180°',
        'Rotate 270 CW' => 'Rotation antihoraire de 270°',
        'Rotate 90 CW' => 'Rotation antihoraire de 90°',
      },
    },
   'OriginalDecisionDataOffset' => 'Décalage des données de décision originales',
   'OriginalFileName' => 'Nom du fichier original',
   'OriginalRawFileData' => 'Données du fichier RAW d\'origine',
   'OriginalRawFileDigest' => 'Digest du fichier RAW original',
   'OriginalRawFileName' => 'Nom du fichier RAW d\'origine',
   'OriginalRawFileType' => 'Type de fichier RAW d\'origine',
   'OriginalRawImage' => 'Image RAW originale',
   'OriginalTransmissionReference' => 'Identificateur de tâche',
   'OriginatingProgram' => 'Programme d\'origine',
   'OtherImage' => 'Autre image',
   'OutputLUT' => 'Sortie de la LUT (Table de correspondance)',
   'OutputResponse' => 'Réponse de sortie',
   'Owner' => 'Propriétaire',
   'OwnerID' => 'ID du propriétaire',
   'OwnerName' => 'Nom du propriétaire',
   'PDFVersion' => 'Version PDF',
   'PEFVersion' => 'Version PEF',
   'PF25MeteringMode' => 'Mode de mesure PF25',
   'Padding' => 'Remplissage',
   'PageName' => 'Nom de page',
   'PageNumber' => 'Page numéro',
   'PanasonicExifVersion' => 'Version Exif Panasonic',
   'PanasonicImageHeight' => 'Hauteur de l\'image Panasonic',
   'PanasonicImageWidth' => 'Largeur de l\'image Panasonic',
   'PanasonicRawVersion' => 'Version RAW Panasonic',
   'PanasonicTitle' => 'Titre',
   'PanoramaSize3D' => {
      PrintConv => {
        'Wide' => 'Large',
        'n/a' => 'Non applicable',
      },
    },
   'ParallelismType' => 'Type de parallélisme',
   'PentaxImageSize' => {
      Description => 'Taille de l\'image Pentax',
      PrintConv => {
        '2304x1728 or 2592x1944' => '2304 x 1728 ou 2592 x 1944',
        '2560x1920 or 2304x1728' => '2560 x 1920 ou 2304 x 1728',
        '2816x2212 or 2816x2112' => '2816 x 2212 ou 2816 x 2112',
        '3008x2008 or 3040x2024' => '3008 x 2008 ou 3040 x 2024',
        'Full' => 'Pleine',
      },
    },
   'PentaxModelID' => 'Modèle Pentax',
   'PentaxModelType' => 'Type de modèle Pentax',
   'PentaxVersion' => 'Version Pentax',
   'People' => 'Personnes',
   'PerChannelBlackLevel' => 'Niveau de noir par canal',
   'PeripheralLighting' => {
      Description => 'Correction éclairage périphérique',
      PrintConv => {
        'Off' => 'Désactiver',
        'On' => 'Activer',
      },
    },
   'Person' => 'Personne',
   'PersonInImage' => 'Personnes sur l\'Image',
   'PersonInImageCharacteristic' => 'Caractéristiques des personnes sur l\'image',
   'PhaseDetectAF' => 'Auto-Focus',
   'PhotoEffect' => {
      Description => 'Effet Photo',
      PrintConv => {
        'B&W' => 'N&B',
        'Custom' => 'Personnalisé',
        'My Color Data' => 'Données My Color',
        'Neutral' => 'Neutre',
        'Off' => 'Désactivé',
        'Sepia' => 'Sépia',
        'Smooth' => 'Doux',
        'Vivid' => 'Éclatant',
      },
    },
   'PhotoEffects' => {
      Description => 'Effets photo',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'PhotoEffectsBlue' => 'Effets photo bleu',
   'PhotoEffectsGreen' => 'Effets photo vert',
   'PhotoEffectsRed' => 'Effets photo rouge',
   'PhotoEffectsType' => {
      Description => 'Type d\'effet photo',
      PrintConv => {
        'B&W' => 'N&B',
        'None' => 'Aucune',
        'Sepia' => 'Sépia',
        'Tinted' => 'Teinté',
      },
    },
   'PhotoStyle' => {
      Description => 'Style de la photo',
      PrintConv => {
        'Natural' => 'Naturel',
        'Scenery' => 'Paysage',
        'Standard or Custom' => 'Standard ou personnalisé',
        'Vivid' => 'Éclatant',
      },
    },
   'PhotographicSensitivity' => 'Sensibilité photographique',
   'PhotometricInterpretation' => {
      Description => 'Interprétation photométrique',
      PrintConv => {
        'BlackIsZero' => 'Zéro pour noir',
        'Color Filter Array' => 'CFA (Matrice de filtres de couleurs)',
        'Pixar LogL' => 'CIE Log2(L) (Log luminance)',
        'Pixar LogLuv' => 'CIE Log2(L)(u\',v\') (Log luminance et chrominance)',
        'RGB' => 'RVB',
        'RGB Palette' => 'Palette RVB',
        'Transparency Mask' => 'Masque de transparence',
        'WhiteIsZero' => 'Zéro pour blanc',
      },
    },
   'PhotoshopAnnotations' => 'Annotations Photoshop',
   'PictureControl' => {
      Description => 'Optimisation de l\'image',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'PictureControlActive' => {
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'PictureControlAdjust' => {
      Description => 'Ajustement de l\'optimisation d\'image',
      PrintConv => {
        'Default Settings' => 'Paramètres par défault',
        'Full Control' => 'Réglages manuels',
        'Quick Adjust' => 'Réglages rapides',
      },
    },
   'PictureControlBase' => 'Optimisation de l\'image de base',
   'PictureControlName' => 'Optimisation de l\'image - Nom',
   'PictureControlQuickAdjust' => 'Optimisation de l\'image - Réglages rapides',
   'PictureControlVersion' => 'Optimisation de l\'image - Version',
   'PictureDescription' => 'Description de l\'image',
   'PictureDisplayRate' => 'Évaluation de l\'image',
   'PictureEffect' => {
      Description => 'Effet d\'image',
      PrintConv => {
        'HDR Painting' => 'Peinture HDR',
        'HDR Painting (high)' => 'Peinture HDR (haute)',
        'HDR Painting (low)' => 'Peinture HDR (basse)',
        'High Contrast Monochrome' => 'Monochrome à fort contraste',
        'Illustration (high)' => 'Illustration (haute)',
        'Illustration (low)' => 'Illustration (basse)',
        'Miniature (bottom)' => 'Miniature (en bas)',
        'Miniature (left)' => 'Miniature (à gauche)',
        'Miniature (middle horizontal)' => 'Miniature (milieu horizontal)',
        'Miniature (middle vertical)' => 'Miniature (milieu vertical)',
        'Miniature (right)' => 'Miniature (à droite)',
        'Miniature (top)' => 'Miniature (en haut)',
        'None' => 'Aucun',
        'Off' => 'Désactivé',
        'Partial Color (blue)' => 'Couleur partielle (bleue)',
        'Partial Color (green)' => 'Couleur partielle (verte)',
        'Partial Color (red)' => 'Couleur partielle (rouge)',
        'Partial Color (yellow)' => 'Couleur partielle (jaune)',
        'Pop Color' => 'Couleur pop',
        'Posterization' => 'Postérisation',
        'Posterization B/W' => 'Postérisation N&B',
        'Retro Photo' => 'Photo rétro',
        'Rich-tone Monochrome' => 'Monochrome riche en tonalités',
        'Soft Focus' => 'Flou artistique',
        'Soft Focus (high)' => 'Flou artistique (élevé)',
        'Soft Focus (low)' => 'Flou artistique (faible)',
        'Soft High Key' => 'Tons clairs logiciel',
        'Toy Camera' => 'Caméra jouet',
        'Toy Camera (cool)' => 'Caméra jouet (frais)',
        'Toy Camera (green)' => 'Caméra jouet (vert)',
        'Toy Camera (magenta)' => 'Caméra jouet (magenta)',
        'Toy Camera (normal)' => 'Caméra jouet (normal)',
        'Toy Camera (warm)' => 'Caméra jouet (chaud)',
        'Water Color' => 'Couleur de l\'eau',
        'Water Color 2' => 'Couleur de l\'eau 2',
      },
    },
   'PictureEffect2' => {
      Description => 'Effet d\'image 2',
      PrintConv => {
        'HDR Painting' => 'Peinture HDR',
        'High Contrast Monochrome' => 'Monochrome à fort contraste',
        'Off' => 'Désactivé',
        'Partial Color' => 'Couleur partielle',
        'Pop Color' => 'Couleur pop',
        'Posterization' => 'Postérisation',
        'Retro Photo' => 'Photo rétro',
        'Rich-tone Monochrome' => 'Monochrome riche en tonalités',
        'Soft Focus' => 'Flou artistique',
        'Soft High Key' => 'Tons clairs logiciel',
        'Toy Camera' => 'Caméra jouet',
        'Water Color' => 'Couleur de l\'eau',
      },
    },
   'PictureFinish' => {
      Description => 'Finition de l\'image',
      PrintConv => {
        'Adobe RGB' => 'Adobe RVB',
        'Adobe RGB (ICC)' => 'Adobe RVB (ICC)',
        'Evening Scene' => 'Scène du soir',
        'Natural' => 'Naturel',
        'Natural+' => 'Naturel+',
        'Night Scene' => 'Nocturne',
        'Wind Scene' => 'Scène venteuse',
      },
    },
   'PictureMode' => {
      Description => 'Mode de l\'image',
      PrintConv => {
        '1/2 EV steps' => 'Pas de 1/2 IL',
        '1/3 EV steps' => 'Pas de 1/3 IL',
        'Anti-blur' => 'Anti-flou',
        'Aperture Priority' => 'Priorité à l\'ouverture',
        'Aperture Priority, Off-Auto-Aperture' => 'Priorité à l\'ouverture (auto-diaph hors service)',
        'Aperture-priority AE' => 'Priorité à l\'ouverture AE',
        'Auto PICT (Landscape)' => 'Image Auto (Paysage)',
        'Auto PICT (Macro)' => 'Image Auto (Macro)',
        'Auto PICT (Portrait)' => 'Image Auto (Portrait)',
        'Auto PICT (Sport)' => 'Auto PICT (sport)',
        'Auto PICT (Standard)' => 'Image Auto (Standard)',
        'Autumn' => 'Automne',
        'Blur Reduction' => 'Réduction du flou',
        'Bulb' => 'Pose B',
        'Bulb, Off-Auto-Aperture' => 'Pose B (auto-diaph hors service)',
        'Candlelight' => 'Bougie',
        'DOF Program' => 'Programme PdC',
        'DOF Program (HyP)' => 'Programme PdC (Hyper-programme)',
        'Dark Pet' => 'Animal foncé',
        'Digital Filter' => 'Filtre numérique',
        'Fireworks' => 'Feux d\'artifice',
        'Flash X-Sync Speed AE' => 'Synchro X flash vitesse AE',
        'Food' => 'Nourriture',
        'Frame Composite' => 'Vue composite',
        'Green Mode' => 'Mode vert',
        'Half-length Portrait' => 'Portrait (buste)',
        'Hi-speed Program' => 'Programme grande vitesse',
        'Hi-speed Program (HyP)' => 'Programme grande vitesse (Hyper-programme)',
        'Kids' => 'Enfants',
        'Landscape' => 'Paysage',
        'Light Pet' => 'Animal clair',
        'MTF Program' => 'Programme FTM',
        'MTF Program (HyP)' => 'Programme FTM (Hyper-programme)',
        'Manual' => 'Manuel',
        'Manual, Off-Auto-Aperture' => 'Manuel (auto-diaph hors service)',
        'Medium Pet' => 'Animal demi-teintes',
        'Museum' => 'Musée',
        'Natural' => 'Naturel',
        'Natural Light' => 'Lumière naturelle',
        'Natural Light & Flash' => 'Lumière naturelle & Flash',
        'Natural Skin Tone' => 'Ton chair naturel',
        'Night Scene' => 'Scène nocturne',
        'Night Scene HDR' => 'Scène nocturne HDR',
        'Night Scene Portrait' => 'Scène portrait nocturne',
        'No Flash' => 'Sans flash',
        'Pet' => 'Animaux de compagnie',
        'Program' => 'Programme',
        'Program (HyP)' => 'Programme AE (Hyper-programme)',
        'Program AE' => 'Priorité vitesse',
        'Program Av Shift' => 'Décalage programme Av',
        'Program Tv Shift' => 'Décalage programme Tv',
        'Purple' => 'Violet',
        'Self Portrait' => 'Autoportrait',
        'Sensitivity Priority AE' => 'Priorité sensibilité AE',
        'Sepia' => 'Sépia',
        'Shutter & Aperture Priority AE' => 'Priorité à l\'obturateur et à l\'ouverture AE',
        'Shutter Speed Priority' => 'Priorité vitesse',
        'Shutter speed priority AE' => 'Priorité vitesse',
        'Snow' => 'Neige',
        'Soft' => 'Doux',
        'Sunset' => 'Coucher de soleil',
        'Surf & Snow' => 'Surf et neige',
        'Synchro Sound Record' => 'Enregistrement de son synchro',
        'Text' => 'Texte',
        'Underwater' => 'Sous-marine',
        'Vivid' => 'Éclatant',
      },
    },
   'PictureMode2' => {
      Description => 'Mode de l\'image 2',
      PrintConv => {
        'Aperture Priority' => 'Priorité à l\'ouverture',
        'Aperture Priority, Off-Auto-Aperture' => 'Priorité ouverture (auto-diaph hors service)',
        'Auto PICT' => 'Image auto',
        'Bulb' => 'Pose B',
        'Bulb, Off-Auto-Aperture' => 'Pose B (auto-diaph hors service)',
        'Flash X-Sync Speed AE' => 'Expo auto, vitesse de synchro flash X',
        'Green Mode' => 'Mode vert',
        'Manual' => 'Manuelle',
        'Manual, Off-Auto-Aperture' => 'Manuel (auto-diaph hors service)',
        'Program AE' => 'Programme AE',
        'Program Av Shift' => 'Décalage programme Av',
        'Program Tv Shift' => 'Décalage programme Tv',
        'Scene Mode' => 'Mode scène',
        'Sensitivity Priority AE' => 'Expo auto, priorité sensibilité',
        'Shutter & Aperture Priority AE' => 'Expo auto, priorité vitesse et ouverture',
        'Shutter Speed Priority' => 'Priorité vitesse',
      },
    },
   'PictureModeBWFilter' => {
      Description => 'Mode d\'image Filtre BW',
      PrintConv => {
        'Green' => 'Vert',
        'Neutral' => 'Neutre',
        'Red' => 'Rouge',
        'Yellow' => 'Jaune',
        'n/a' => 'Non applicable',
      },
    },
   'PictureModeEffect' => {
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'PictureModeTone' => {
      Description => 'Mode de tonalité de l\'image',
      PrintConv => {
        'Blue' => 'Bleu',
        'Green' => 'Vert',
        'Neutral' => 'Neutre',
        'Purple' => 'Violet',
        'Sepia' => 'Sépia',
        'n/a' => 'Non applicable',
      },
    },
   'PictureProfile' => {
      Description => 'Profil de l\'image ',
      PrintConv => {
        'Gamma Cine1 (PP5)' => 'Gamma Ciné1 (PP5)',
        'Gamma Cine2 (PP6)' => 'Gamma Ciné1 (PP6)',
        'Gamma Cine3' => 'Gamma Ciné3',
        'Gamma Cine4' => 'Gamma Ciné4',
        'Gamma ITU709 (PP3 or PP4)' => 'Gamma ITU709 (PP3 ou PP4)',
        'Gamma Movie (PP1)' => 'Gamma Vidéo (PP1)',
        'Gamma S-Log3 (PP8 or PP9)' => 'Gamma S-Log3 (PP8 ou PP9)',
        'Gamma Still - B&W/Sepia' => 'Gamma fixe - N&B/Sépia',
        'Gamma Still - Clear' => 'Gamma fixe - Clair',
        'Gamma Still - Deep' => 'Gamma fixe - Profond',
        'Gamma Still - Light' => 'Gamma fixe - Lumière',
        'Gamma Still - Night View/Portrait' => 'Gamma fixe - Vue/Portrait nocturne',
        'Gamma Still - Portrait' => 'Gamma fixe - Portrait',
        'Gamma Still - Real' => 'Gamma fixe - Réel',
        'Gamma Still - Standard/Neutral (PP2)' => 'Gamma fixe - Standard/Neutre (PP2)',
        'Gamma Still - Vivid' => 'Gamma fixe - Éclatant',
      },
    },
   'PictureStyle' => {
      Description => 'Style de l\'image',
      PrintConv => {
        'Adobe RGB' => 'Adobe RVB',
        'CM Set 1' => 'CM jeu 1',
        'CM Set 2' => 'CM jeu 2',
        'Custom' => 'Personnalisé',
        'Faithful' => 'Fidèle',
        'Fine Detail' => 'Détails fins',
        'High Saturation' => 'Saturation élevée',
        'Landscape' => 'Paysage',
        'Low Saturation' => 'Saturation faible',
        'Neutral' => 'Neutre',
        'None' => 'Aucun',
        'Shot Settings' => 'Paramétrage prise de vue',
        'Unknown?' => 'Inconnu ?',
        'User Def. 1' => 'Défini par l\'utilisateur 1',
        'User Def. 2' => 'Défini par l\'utilisateur 2',
        'User Def. 3' => 'Défini par l\'utilisateur 3',
        'n/a' => 'Non applicable',
      },
    },
   'PictureStylePC' => {
      Description => 'Style de l\'image PC',
      PrintConv => {
        'Adobe RGB' => 'Adobe RVB',
        'CM Set 1' => 'CM jeu 1',
        'CM Set 2' => 'CM jeu 2',
        'Faithful' => 'Fidèle',
        'Fine Detail' => 'Détails fins',
        'High Saturation' => 'Saturation élevée',
        'Landscape' => 'Paysage',
        'Low Saturation' => 'Saturation faible',
        'Neutral' => 'Neutre',
        'None' => 'Aucun',
        'User Def. 1' => 'Défini par l\'utilisateur 1',
        'User Def. 2' => 'Défini par l\'utilisateur 2',
        'User Def. 3' => 'Défini par l\'utilisateur 3',
        'n/a' => 'Non applicable',
      },
    },
   'PictureStyleUserDef' => {
      Description => 'Style de l\'image défini par l\'utilisateur',
      PrintConv => {
        'Adobe RGB' => 'Adobe RVB',
        'CM Set 1' => 'CM jeu 1',
        'CM Set 2' => 'CM jeu 2',
        'Faithful' => 'Fidèle',
        'Fine Detail' => 'Détails fins',
        'High Saturation' => 'Saturation élevée',
        'Landscape' => 'Paysage',
        'Low Saturation' => 'Saturation faible',
        'Neutral' => 'Neutre',
        'None' => 'Aucun',
        'User Def. 1' => 'Défini par l\'utilisateur 1',
        'User Def. 2' => 'Défini par l\'utilisateur 2',
        'User Def. 3' => 'Défini par l\'utilisateur 3',
        'n/a' => 'Non applicable',
      },
    },
   'PictureWizardMode' => {
      PrintConv => {
        'Landscape' => 'Paysage',
        'Vivid' => 'Éclatant',
        'n/a' => 'Non applicable',
      },
    },
   'Pitch' => {
      Description => 'Tangage',
      PrintConv => {
        'High' => 'Élevé',
        'Low' => 'Faible',
      },
    },
   'PitchAngle' => 'Angle de tangage',
   'PixelAspectRatio' => 'Ratio d\'aspect des pixels',
   'PixelIntensityRange' => 'Plage d\'intensité des pixels',
   'PixelScale' => 'Échelle en pixel',
   'PixelShiftInfo' => {
      Description => 'Informations sur le décalage des pixels',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'PixelUnits' => {
      PrintConv => {
        'Unknown' => 'Inconnu',
      },
    },
   'PlanarConfiguration' => {
      Description => 'Configuration planaire',
      PrintConv => {
        'Chunky' => 'Format Chunky (composants entrelacés)',
        'Planar' => 'Format Planaire (composants séparés)',
      },
    },
   'PostalCode' => 'Code Postal',
   'PowerSource' => {
      Description => 'Source d\'alimentation',
      PrintConv => {
        'Body Battery' => 'Accu boîtier',
        'External Power Supply' => 'Alimentation externe',
        'Grip Battery' => 'Accu poignée',
      },
    },
   'Predictor' => {
      Description => 'Prédicteur',
      PrintConv => {
        'Horizontal differencing' => 'Différentiation horizontale',
        'None' => 'Aucun schéma de prédicteur utilisé avant l\'encodage',
      },
    },
   'Preview0' => 'Aperçu 0',
   'Preview1' => 'Aperçu 1',
   'Preview2' => 'Aperçu 2',
   'PreviewApplicationName' => 'Nom de l\'application d\'aperçu',
   'PreviewApplicationVersion' => 'Version de l\'application d\'aperçu',
   'PreviewButton' => {
      PrintConv => {
        'Active D-Lighting' => 'D-Lighting actif',
      },
    },
   'PreviewButtonPlusDials' => {
      PrintConv => {
        'Active D-Lighting' => 'D-Lighting actif',
      },
    },
   'PreviewColorSpace' => {
      Description => 'Espace de couleur de l\'aperçu',
      PrintConv => {
        'Adobe RGB' => 'Adobe RVB',
        'Gray Gamma 2.2' => 'Gamma gris 2.2',
        'ProPhoto RGB' => 'Photo Pro Adobe RVB',
        'Unknown' => 'Inconnu',
        'sRGB' => 'sRVB',
      },
    },
   'PreviewDate' => 'Date de l\'aperçu',
   'PreviewDateTime' => 'Horodatage de l\'aperçu',
   'PreviewDuration' => 'Durée de l\'aperçu',
   'PreviewImage' => 'Aperçu de l\'image',
   'PreviewImage1' => 'Aperçu de l\'image 1',
   'PreviewImage2' => 'Aperçu de l\'image 2',
   'PreviewImageBorders' => 'Limites de l\'aperçu',
   'PreviewImageData' => 'Données de l\'aperçu',
   'PreviewImageHeight' => 'Hauteur de l\'aperçu',
   'PreviewImageLength' => 'Longueur de l\'aperçu',
   'PreviewImageName' => 'Nom de l\'aperçu',
   'PreviewImageSize' => 'Taille de l\'aperçu',
   'PreviewImageStart' => 'Début de l\'aperçu',
   'PreviewImageType' => 'Type de l\\image d\'aperçu',
   'PreviewImageValid' => {
      Description => 'Aperçu valide',
      PrintConv => {
        'No' => 'Non',
        'Yes' => 'Oui',
      },
    },
   'PreviewQuality' => {
      PrintConv => {
        'CRAW' => 'cRAW',
        'Economy' => 'Économie',
        'Light (RAW)' => 'Légère (RAW)',
        'Normal' => 'Normale',
        'Superfine' => 'Super fine',
        'n/a' => 'Non applicable',
      },
    },
   'PreviewSettingsDigest' => 'Digest des réglages d\'aperçu',
   'PreviewSettingsName' => 'Nom des réglages d\'aperçu',
   'PrimaryAFPoint' => {
      Description => 'Points de mise au point automatique utilisés',
      PrintConv => {
        '(none)' => '(aucun)',
        'Bottom' => 'En bas',
        'C6 (Center)' => 'C6 (Centre)',
        'Center' => 'Au centre',
        'D8 (Center)' => 'D8 (Centre)',
        'E8 (Center)' => 'E8 (Centre)',
        'E9 (Center)' => 'E9 (Centre)',
        'F11 (Center)' => 'F11 (Centre)',
        'F8 (Center)' => 'F8 (Centre)',
        'Far Left' => 'À l\'extrême gauche',
        'Far Right' => 'À l\'extrême droite',
        'Lower-left' => 'En bas à gauche',
        'Lower-right' => 'En bas à droite',
        'Mid-left' => 'Au milieu à gauche',
        'Mid-right' => 'Au milieu à droite',
        'Top' => 'En haut',
        'Upper-left' => 'En haut à gauche',
        'Upper-right' => 'En haut à droite',
      },
    },
   'PrimaryChromaticities' => 'Chromaticité des couleurs primaires',
   'PrimaryItemReference' => 'Référence de l\'élément principal',
   'PrimaryPlatform' => 'Plateforme primaire',
   'PrintIM' => 'Print IM',
   'PrintIMVersion' => 'Version Print IM',
   'PrintInfo' => 'Imprimer Info',
   'PrintInfo2' => 'Imprimer Info 2',
   'PrintStyle' => {
      Description => 'Style d\'impression',
      PrintConv => {
        'Centered' => 'Centré',
        'Size to Fit' => 'Adapter à la taille',
        'User Defined' => 'Défini par l\'utilisateur',
      },
    },
   'PrioritySetInAWB' => {
      Description => 'Priorité accordée à la balance automatique des blancs',
      PrintConv => {
        'Ambience' => 'Ambiance',
        'White' => 'Blanc',
      },
    },
   'PrioritySetupShutterRelease' => {
      PrintConv => {
        'AF' => 'Mise au point automatique',
      },
    },
   'ProcessingSoftware' => 'Logiciel de traitement',
   'Producer' => 'Producteur',
   'ProductID' => 'ID de produit',
   'ProductionCode' => 'L\'appareil est passé en SAV',
   'ProfileCMMType' => {
      Description => 'Type du profil CMM',
      PrintConv => {
        'Jetsoft Development' => 'Etsoft Development',
        'Modgraph, Inc.' => 'Odgraph, Inc.',
        'Philips Consumer Electronics Co.' => 'Hilips Consumer Electronics Co.',
        'none' => 'Aucun',
      },
    },
   'ProfileCalibrationSig' => 'Signature de calibration du profil',
   'ProfileClass' => {
      Description => 'Classe du profil',
      PrintConv => {
        'Abstract Profile' => 'Profil de résumé',
        'ColorSpace Conversion Profile' => 'Profil de conversion d\'espace de couleur',
        'DeviceLink Profile' => 'Profil de liaison',
        'Display Device Profile' => 'Profil d\'appareil d\'affichage',
        'Input Device Profile' => 'Profil d\'appareil d\'entrée',
        'NamedColor Profile' => 'Profil de couleur nommée',
        'Nikon Input Device Profile (NON-STANDARD!)' => 'Profil Nikon ("nkpf")',
        'Output Device Profile' => 'Profil d\'appareil de sortie',
      },
    },
   'ProfileConnectionSpace' => 'Espace de connexion du profil',
   'ProfileCopyright' => 'Copyright du profil',
   'ProfileCreator' => 'Créateur du profil',
   'ProfileDateTime' => 'Horodatage du profil',
   'ProfileDescription' => 'Description du profil',
   'ProfileDescriptionML' => 'Description ML du profil',
   'ProfileEmbedPolicy' => {
      Description => 'Règles d\'usage du profil incluses',
      PrintConv => {
        'Allow Copying' => 'Permet la copie',
        'Embed if Used' => 'Inclus si utilisé',
        'Never Embed' => 'Jamais inclus',
        'No Restrictions' => 'Pas de restriction',
      },
    },
   'ProfileFileSignature' => 'Signature du fichier du profil',
   'ProfileHueSatMapData1' => 'Données du profil teinte sat. 1',
   'ProfileHueSatMapData2' => 'Données du profil teinte sat. 2',
   'ProfileHueSatMapDims' => 'Divisions de teinte',
   'ProfileID' => 'ID du profil',
   'ProfileLookTableData' => 'Table de correspondance (LUT) du profil - Données',
   'ProfileLookTableDims' => 'Table de correspondance (LUT) du profil - Teintes',
   'ProfileLookTableEncoding' => {
      Description => 'Codage de la table de correspondance',
      PrintConv => {
        'Linear' => 'Linéaire',
      },
    },
   'ProfileName' => 'Nom du profil',
   'ProfileSequenceDesc' => 'Description de séquence du profil',
   'ProfileToneCurve' => 'Courbe de tonalité du profil',
   'ProfileType' => {
      Description => 'Type de profil',
      PrintConv => {
        'Group 3 FAX' => 'Fax Group 3',
        'Unspecified' => 'Non spécifié',
      },
    },
   'ProfileVersion' => 'Version du profil',
   'ProgramISO' => {
      Description => 'Programme ISO',
      PrintConv => {
        'Intelligent ISO' => 'ISO Intelligent',
        'n/a' => 'Non applicable',
      },
    },
   'ProgramLine' => {
      Description => 'Ligne de programme',
      PrintConv => {
        'Depth' => 'Priorité profondeur de champ',
        'Hi Speed' => 'Priorité grande vitesse',
        'MTF' => 'Priorité FTM',
        'Normal' => 'Normale',
      },
    },
   'ProgramMode' => {
      PrintConv => {
        'None' => 'Aucune',
        'Sunset' => 'Coucher de soleil',
        'Text' => 'Texte',
      },
    },
   'ProgramShift' => 'Décalage Programme',
   'ProgramVersion' => 'Version du programme',
   'Protect' => 'Protéger',
   'Province-State' => 'État / Région',
   'Publisher' => 'Editeur',
   'Quality' => {
      Description => 'Qualité',
      PrintConv => {
        '2 (Telephone)' => '2 (Téléphone)',
        '3 (Thumb)' => '3 (Pouce)',
        '6 (Xtreme)' => '6 (Extrême)',
        '7 (Insane)' => '7 (Folle)',
        '8 (BrainDead)' => '8 (Mort cérébrale)',
        'Basic' => 'Basique',
        'Best' => 'Meilleure',
        'Better' => 'Mieux',
        'CRAW' => 'cRAW',
        'Compressed RAW' => 'RAW compressé',
        'Compressed RAW + JPEG' => 'RAW compressé + JPEG',
        'Dynamic Pixel Shift' => 'Décalage dynamique des pixels',
        'Economy' => 'Économie',
        'Extra Fine' => 'Extra fine',
        'Good' => 'Bonne',
        'High' => 'Haute',
        'Light' => 'Légère',
        'Light (RAW)' => 'Légère (RAW)',
        'Low' => 'Basse',
        'Medium' => 'Moyenne',
        'Normal' => 'Normale',
        'RAW (pixel shift enabled)' => 'RAW (décalage des pixels activé)',
        'RAW + JPEG' => 'RAW+JPEG',
        'RAW + Light' => 'RAW + Légère',
        'Superfine' => 'Super fine',
        'Unstable/Experimental' => 'Instable/Expérimental',
        'n/a' => 'Non applicable',
      },
    },
   'Quality2' => 'Qualité 2',
   'QualityButton' => {
      PrintConv => {
        'HDR Overlay' => 'Recouvrement HDR',
      },
    },
   'QualityButtonPlaybackMode' => {
      PrintConv => {
        'HDR Overlay' => 'Recouvrement HDR',
      },
    },
   'QualityMode' => {
      Description => 'Qualité',
      PrintConv => {
        'Economy' => 'Économie',
        'Fine' => 'Haute',
        'Normal' => 'Normale',
      },
    },
   'QuantizationMethod' => {
      Description => 'Méthode de quantification',
      PrintConv => {
        'Color Space Specific' => 'Spécifique à l\'espace de couleur',
        'Compression Method Specific' => 'Spécifique à la méthode de compression',
        'Gamma Compensated' => 'Compensée gamma',
        'IPTC Ref B' => 'IPTC réf "B"',
        'Linear Density' => 'Densité linéaire',
        'Linear Dot Percent' => 'Pourcentage de point linéaire',
        'Linear Reflectance/Transmittance' => 'Réflectance/transmittance linéaire',
      },
    },
   'QuickAdjust' => 'Réglages rapides',
   'QuickControlDialInMeter' => {
      Description => 'Molette de contrôle rapide en mesure',
      PrintConv => {
        'AF point selection' => 'Sélection collimateur AF',
        'Exposure comp/Aperture' => 'Correction exposition/ouverture',
        'ISO speed' => 'Sensibilité ISO',
      },
    },
   'QuickShot' => {
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'QuietShutterShootingSpeed' => {
      PrintConv => {
        'Single' => 'Simple',
      },
    },
   'RAFVersion' => 'Version RAF',
   'RAWFileType' => {
      Description => 'Type de fichier RAW',
      PrintConv => {
        'Compressed RAW' => 'RAW compressé',
        'Lossless Compressed RAW' => 'RAW compressé sans perte',
        'Uncompressed RAW' => 'RAW non compressé',
        'n/a' => 'Non applicable',
      },
    },
   'RFLensType' => {
      Description => 'Type d\'objectif RF',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'ROMOperationMode' => 'Mode de fonctionnement de la ROM',
   'RasterPadding' => 'Remplissage raster',
   'RasterizedCaption' => 'Légende rastérisée',
   'Rating' => {
      Description => 'Classement',
      PrintConv => {
        'Clean' => 'Propre',
        'Explicit' => 'Explicite',
        'Explicit (old)' => 'Explicite (ancien)',
        'none' => 'aucun',
      },
    },
   'RatingPercent' => 'Rapport en pourcentage',
   'RawAndJpgRecording' => {
      Description => 'Enregistrement RAW et JPEG',
      PrintConv => {
        'JPEG (Best)' => 'JPEG (le meilleur)',
        'JPEG (Better)' => 'JPEG (meilleur)',
        'JPEG (Good)' => 'JPEG (bon)',
        'RAW (DNG, Best)' => 'RAW (DNG, le meilleur)',
        'RAW (DNG, Better)' => 'RAW (DNG, meilleur)',
        'RAW (DNG, Good)' => 'RAW (DNG, bon)',
        'RAW (PEF, Best)' => 'RAW (PEF, le meilleur)',
        'RAW (PEF, Better)' => 'RAW (PEF, meilleur)',
        'RAW (PEF, Good)' => 'RAW (PEF, bon)',
        'RAW+JPEG (DNG, Best)' => 'RAW+JPEG (DNG, le meilleur)',
        'RAW+JPEG (DNG, Better)' => 'RAW+JPEG (DNG, meilleur)',
        'RAW+JPEG (DNG, Good)' => 'RAW+JPEG (DNG, bon)',
        'RAW+JPEG (PEF, Best)' => 'RAW+JPEG (PEF, le meilleur)',
        'RAW+JPEG (PEF, Better)' => 'RAW+JPEG (PEF, meilleur)',
        'RAW+JPEG (PEF, Good)' => 'RAW+JPEG (PEF, bon)',
        'RAW+Large/Fine' => 'RAW+grande/fine',
        'RAW+Large/Normal' => 'RAW+grande/normale',
        'RAW+Medium/Fine' => 'RAW+moyenne/fine',
        'RAW+Medium/Normal' => 'RAW+moyenne/normale',
        'RAW+Small/Fine' => 'RAW+petite/fine',
        'RAW+Small/Normal' => 'RAW+petite/normale',
      },
    },
   'RawColorAdj' => {
      PrintConv => {
        'Shot Settings' => 'Paramétrage prise de vue',
      },
    },
   'RawData' => 'Données RAW',
   'RawDataOffset' => 'Décalage données RAW',
   'RawDataUniqueID' => 'ID unique de données brutes',
   'RawDevArtFilter' => {
      PrintConv => {
        'Cross Process' => 'Traitement croisé',
        'Cross Process II' => 'Traitement croisé II',
        'Partial Color' => 'Couleur partielle',
        'Partial Color II' => 'Couleur partielle II',
        'Partial Color III' => 'Couleur partielle III',
        'Soft Focus' => 'Flou artistique',
        'Soft Focus 2' => 'Flou artistique 2',
      },
    },
   'RawDevAutoGradation' => {
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'RawDevColorSpace' => {
      Description => 'Espace couleur du développeur RAW',
      PrintConv => {
        'Adobe RGB' => 'Adobe RVB',
        'Pro Photo RGB' => 'Photo Pro Adobe RVB',
        'sRGB' => 'sRVB',
      },
    },
   'RawDevContrastValue' => 'Valeur de contraste du développeur RAW',
   'RawDevEditStatus' => {
      Description => 'État d\'édition du développeur RAW',
      PrintConv => {
        'Edited (Landscape)' => 'Édité (Paysage)',
        'Edited (Portrait)' => 'Édité (Portrait)',
      },
    },
   'RawDevEngine' => {
      Description => 'Moteur du développeur RAW',
      PrintConv => {
        'Advanced High Function' => 'Haute fonctionnalité avancée',
        'Advanced High Speed' => 'Grande vitesse avancée',
        'High Function' => 'Haute fonctionnalité',
        'High Speed' => 'Grande vitesse',
      },
    },
   'RawDevExposureBiasValue' => 'Valeur du biais d\'exposition du développeur RAW',
   'RawDevGrayPoint' => 'Point gris du développeur RAW',
   'RawDevMemoryColorEmphasis' => 'Accentuation des couleurs en mémoire du développeur RAW',
   'RawDevNoiseReduction' => {
      Description => 'Réduction du bruit du développeur RAW',
      PrintConv => {
        '(none)' => '(aucun)',
        'Noise Filter' => 'Filtre anti-bruit',
        'Noise Filter (ISO Boost)' => 'Filtre anti-bruit (Boost ISO)',
        'Noise Reduction' => 'Réduction du bruit',
      },
    },
   'RawDevPMPictureTone' => {
      PrintConv => {
        'Blue' => 'Bleu',
        'Green' => 'Vert',
        'Neutral' => 'Neutre',
        'Purple' => 'Violet',
        'Sepia' => 'Sépia',
      },
    },
   'RawDevPM_BWFilter' => {
      PrintConv => {
        'Green' => 'Vert',
        'Neutral' => 'Neutre',
        'Red' => 'Rouge',
        'Yellow' => 'Jaune',
      },
    },
   'RawDevPictureMode' => {
      PrintConv => {
        'Natural' => 'Naturel',
        'Sepia' => 'Sépia',
        'Vivid' => 'Éclatant',
      },
    },
   'RawDevSaturationEmphasis' => 'Accentuation de la saturation du développeur RAW',
   'RawDevSettings' => {
      Description => 'Réglages du développeur RAW',
      PrintConv => {
        '(none)' => '(aucun)',
        'Color Space' => 'Espace couleur',
        'Contrast' => 'Contraste',
        'High Function' => 'Haute fonction',
        'Noise Reduction' => 'Réduction du bruit',
        'Sharpness' => 'Netteté',
        'WB Color Temp' => 'Balance des blancs Température de couleur',
        'WB Gray Point' => 'Balance des blancs Point gris',
      },
    },
   'RawDevSharpnessValue' => 'Valeur de netteté du développeur RAW',
   'RawDevVersion' => 'Version du développeur RAW',
   'RawDevWBFineAdjustment' => 'Réglage fin de la Balance des blancs du développeur RAW',
   'RawDevWhiteBalance' => {
      PrintConv => {
        'Color Temperature' => 'Température de couleur',
        'Gray Point' => 'Point gris',
      },
    },
   'RawDevWhiteBalanceValue' => 'Valeur de la balance des blancs du développeur RAW',
   'RawDevelopmentProcess' => 'Processus de développement RAW',
   'RawExposureBias' => 'Biais d\'exposition',
   'RawFile' => 'Fichier RAW',
   'RawFileName' => 'Nom du fichier RAW',
   'RawFormat' => 'Format RAW',
   'RawImageCenter' => 'Centre de l\'image RAW',
   'RawImageCropTopLeft' => 'Image RAW recadrée en haut à gauche',
   'RawImageCroppedSize' => 'Image RAW taille recadrée',
   'RawImageDigest' => 'Résumé de l\'image RAW',
   'RawImageFullHeight' => 'Hauteur totale de l\'image RAW',
   'RawImageFullSize' => 'Taille totale de l\'image RAW',
   'RawImageFullWidth' => 'Largeur total de l\'image RAW',
   'RawImageHeight' => 'Hauteur de l\'image RAW',
   'RawImageMode' => 'Mode de l\'image RAW',
   'RawImageSegmentation' => 'Segmentation de l\'image RAW',
   'RawImageSize' => 'Taille de l\'image RAW',
   'RawImageWidth' => 'Largeur de l\'image brute',
   'RawInfoVersion' => 'Information version RAW',
   'RawJpgQuality' => {
      Description => 'Qualité Jpeg RAW',
      PrintConv => {
        'CRAW' => 'cRAW',
        'Economy' => 'Économie',
        'Light (RAW)' => 'Légère (RAW)',
        'Normal' => 'Normale',
        'Superfine' => 'Super fine',
        'n/a' => 'Non applicable',
      },
    },
   'RawJpgSize' => {
      Description => 'Taille Jpeg RAW',
      PrintConv => {
        '1280x720 Movie' => 'Vidéo 1280x720',
        '1920x1080 Movie' => 'Vidéo 1920 x1080',
        '4096x2160 Movie' => 'Vidéo 4096x2160',
        '640x480 Movie' => 'Vidéo 640x480',
        'Large' => 'Grande',
        'Medium' => 'Moyenne',
        'Medium 1' => 'Moyenne 1',
        'Medium 2' => 'Moyenne 2',
        'Medium 3' => 'Moyenne 3',
        'Medium Movie' => 'Vidéo moyenne',
        'Medium Widescreen' => 'Écran large moyen',
        'Postcard' => 'Carte postale',
        'Small' => 'Petite',
        'Small 1' => 'Petite 1',
        'Small 2' => 'Petite 2',
        'Small 3' => 'Petite 3',
        'Small Movie' => 'Petite vidéo',
        'Widescreen' => 'Écran large',
        'n/a' => 'Non applicable',
      },
    },
   'RecommendedExposureIndex' => 'Index d\'exposition recommandé',
   'RecordID' => 'Identifiant de l\'enregistrement',
   'RecordMode' => {
      Description => 'Mode d\'enregistrement',
      PrintConv => {
        'Aperture Priority' => 'Priorité à l\'ouverture',
        'Best Shot' => 'Meilleure prise de vue',
        'Manual' => 'Manuel',
        'Movie' => 'Vidéo',
        'Movie (19)' => 'Vidéo (19)',
        'Program AE' => 'Programme d\'exposition automatique',
        'Shutter Priority' => 'Priorité à l\'obturateur',
        'YouTube Movie' => 'Vidéo YouTube',
      },
    },
   'RecordingMode' => {
      PrintConv => {
        'Landscape' => 'Paysage',
        'Manual' => 'Manuelle',
        'Night Scene' => 'Nocturne',
      },
    },
   'RedAdjust' => 'Ajustement du rouge',
   'RedBalance' => 'Balance rouge',
   'RedEyeCorrection' => {
      Description => 'Correction des yeux rouges',
      PrintConv => {
        'Automatic' => 'Automatique',
        'Click on Eyes' => 'Clic sur les yeux',
        'Off' => 'Désactivé',
      },
    },
   'RedEyeReduction' => {
      Description => 'Réduction yeux rouges',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'RedMatrixColumn' => 'Colonne de la matrice rouge',
   'RedTRC' => 'Courbe de reproduction des tonalités rouges',
   'ReductionMatrix1' => 'Matrice de réduction 1',
   'ReductionMatrix2' => 'Matrice de réduction 2',
   'ReductionMatrix3' => 'Matrice de réduction 3',
   'ReferenceBlackWhite' => 'Paire de valeurs de référence noir et blanc',
   'ReferenceDate' => 'Date de référence',
   'ReferenceNumber' => 'Numéro de référence',
   'ReferenceService' => 'Service de référence',
   'RegionAppliedToDimensions' => 'Dimensions appliquées à la zone',
   'RegionAppliedToDimensionsH' => 'Dimensions appliquées en hauteur à la zone',
   'RegionAppliedToDimensionsUnit' => 'Unité des valeurs appliquées aux dimensions',
   'RegionAppliedToDimensionsW' => 'Dimensions appliquées en largeur à la zone',
   'RegionArea' => 'Zone',
   'RegionAreaD' => 'Profondeur de la zone',
   'RegionAreaH' => 'Hauteur de la zone',
   'RegionAreaUnit' => 'Unité des valeurs de la zone',
   'RegionAreaW' => 'Largeur de la zone',
   'RegionAreaX' => 'Position horizontale de la zone',
   'RegionAreaY' => 'Position verticale de la zone',
   'RegionBarCodeValue' => 'Valeur du code-barres de la zone',
   'RegionCode' => 'Code de la zone',
   'RegionConstraints' => 'Contraintes de la zone',
   'RegionDataType' => 'Type de données de la zone',
   'RegionDescription' => 'Description de la zone',
   'RegionExtensions' => 'Extensions de la zone',
   'RegionFlags' => 'Indicateurs de la zone',
   'RegionFocusUsage' => {
      Description => 'Utilisation de la zone',
      PrintConv => {
        'Evaluated, Not Used' => 'Évaluée, non utilisée',
        'Evaluated, Used' => 'Évaluée, utilisée',
        'Not Evaluated, Not Used' => 'Non évaluée, non utilisée',
      },
    },
   'RegionName' => 'Nom de la zone',
   'RegionPersonDisplayName' => 'Nom de la personne dans la zone',
   'RegionRectangle' => 'Région - Rectangle',
   'RegionType' => {
      Description => 'Type de région',
      PrintConv => {
        'BarCode' => 'Code-barres',
        'Face' => 'Visage',
        'Pet' => 'Animal de compagnie',
      },
    },
   'RelatedImageFileFormat' => 'Format du fichier image associé',
   'RelatedImageHeight' => 'Hauteur de l\'image asspciée',
   'RelatedImageWidth' => 'Largeur de l\'image associée',
   'RelatedSoundFile' => 'Fichier audio associé',
   'RelatedTo' => 'Associé à',
   'RelativeTime' => 'Temps relatif',
   'RelativeTimeUnits' => 'Unités du temps relatif',
   'RelativeTimestamp' => 'Horodatage relatif',
   'RelativeTimestampScale' => 'Echelle de l\'horodatage relatif',
   'RelativeTimestampValue' => 'Valeur de l\'horodatage relatif',
   'ReleaseButtonToUseDial' => {
      PrintConv => {
        'No' => 'Non',
        'Yes' => 'Oui',
      },
    },
   'ReleaseDate' => 'Date de version',
   'ReleaseMode' => {
      Description => 'Mode de déclenchement',
      PrintConv => {
        'AE Bracketing' => 'Bracketing AE',
        'Continuous' => 'Continu',
        'Continuous High Speed' => 'Haute vitesse continue',
        'Continuous Low Speed' => 'Vitesse lente continue',
        'Contrast Bracketing' => 'Bracketing de contraste',
        'DRO Bracketing' => 'Bracketing DRO',
        'Exposure Bracketing' => 'Bracketing d\'exposition',
        'High Speed Burst' => 'Rafale haute vitesse',
        'Mirror-Up' => 'Miroir en haut',
        'Quiet' => 'Silencieux',
        'Single Frame' => 'Vue par vue',
        'Timer' => 'Minuterie',
        'WB Bracketing' => 'Bracketing de balance des blancs',
        'White Balance Bracketing' => 'Bracketing de balance des blancs',
        'n/a' => 'Non applicable',
      },
    },
   'ReleaseMode2' => {
      Description => 'Mode de déclenchement 2',
      PrintConv => {
        'Continuous' => 'Continu',
        'Continuous - 3D Image' => 'Continu - Image 3D',
        'Continuous - 3D Sweep Panorama' => 'Continu - Panorama 3D par balayage',
        'Continuous - Anti-Motion Blur, Hand-held Twilight' => 'Continu - Flou anti-mouvement, crépuscule à main levée',
        'Continuous - Background defocus' => 'Continu - Défocalisation de l\'arrière-plan',
        'Continuous - Burst' => 'Continu - Rafale',
        'Continuous - Burst 2' => 'Continu - Rafale 2',
        'Continuous - Exposure Bracketing' => 'Continu - Bracketing d\'exposition',
        'Continuous - HDR' => 'Continu - HDR',
        'Continuous - High Resolution Sweep Panorama' => 'Continu - Panorama par balayage haute résolution',
        'Continuous - High Sensitivity' => 'Continu - Haute Sensibilité',
        'Continuous - Multi Frame NR' => 'Continu - RB multi-photos',
        'Continuous - Speed/Advance Priority' => 'Continu - Priorité vitesse/avance',
        'Continuous - Sweep Panorama' => 'Continu - Panorama par balayage',
        'Continuous - Tele-zoom Advance Priority' => 'Continu - Télé-zoom Priorité à l\'avance',
        'Continuous Low' => 'Continu - Bas',
        'DRO or White Balance Bracketing' => 'Bracketing DRO ou balance des blancs',
        'Single Frame - Capture During Movie' => 'Image unique - Capture pendant la vidéo',
        'Single Frame - Movie Capture' => 'Image unique - Capture vidéo',
        'Single-frame - Exposure Bracketing' => 'Image unique - Bracketing d\'exposition',
        'Smile Shutter' => 'Obturateur sur sourire',
      },
    },
   'ReleaseMode3' => {
      Description => 'Mode de déclenchement 3',
      PrintConv => {
        'Continuous' => 'Continu',
        'Continuous - Burst' => 'Continu - Rafale',
        'Continuous - Speed/Advance Priority' => 'Continu - Priorité vitesse/avance',
        'Normal - Self-timer' => 'Normal - Retardateur',
        'Single Burst Shooting' => 'Prise de vue en rafale',
      },
    },
   'ReleaseTime' => 'Heure de version',
   'RenderingIntent' => {
      Description => 'Intention de rendu',
      PrintConv => {
        'ICC-Absolute Colorimetric' => 'Colorimétrique absolu',
        'Media-Relative Colorimetric' => 'Colorimétrique relatif',
        'Perceptual' => 'Perceptif',
      },
    },
   'ResampleParamsQuality' => {
      PrintConv => {
        'Low' => 'Bas',
      },
    },
   'Resaved' => {
      PrintConv => {
        'No' => 'Non',
        'Yes' => 'Oui',
      },
    },
   'Resolution' => 'Résolution de l\'image',
   'ResolutionMode' => 'Mode de résolution',
   'ResolutionUnit' => {
      Description => 'Unité de résolutions',
      PrintConv => {
        'None' => 'Aucune',
        'cm' => 'Pixels/cm',
        'inches' => 'pouce',
      },
    },
   'ResourceForkSize' => 'Taille du champ de ressources',
   'RetouchHistory' => {
      Description => 'Historique retouche',
      PrintConv => {
        'Cross Process (blue)' => 'Traitement croisé (bleu)',
        'Cross Process (green)' => 'Traitement croisé (vert)',
        'Cross Process (red)' => 'Traitement croisé (rouge)',
        'Cross Process (yellow)' => 'Traitement croisé (jaune)',
        'Cross Screen' => 'Écran croisé',
        'High Key' => 'Tons clairs',
        'Low Key' => 'Tons sombres',
        'None' => 'Aucune',
        'Sepia' => 'Sépia',
      },
    },
   'RevisionNumber' => 'Numéro de révision',
   'Rights' => 'Droits',
   'Roll' => 'Roulis',
   'RollAngle' => 'Angle de roulis',
   'Rotation' => {
      PrintConv => {
        '0' => '0°',
        '180' => '180°',
        '270' => '270°',
        '90' => '90°',
        'Horizontal' => 'Horizontale',
        'Horizontal (normal)' => 'Horizontale (normale)',
        'Rotate 180' => 'Rotation de 180°',
        'Rotate 270 CW' => 'Rotation antihoraire de 270°',
        'Rotate 90 CW' => 'Rotation antihoraire de 90°',
      },
    },
   'RowInterleaveFactor' => 'Facteur d\'entrelacement des lignes',
   'RowsPerStrip' => 'Nombre de rangées par bande',
   'RunTimeEpoch' => 'Temps de fonctionnement Epoch',
   'RunTimeFlags' => {
      Description => 'Indicateurs de temps de fonctionnement',
      PrintConv => {
        'Has been rounded' => 'A été arrondi',
        'Indefinite' => 'Indéfini',
        'Negative infinity' => 'Infinité négative',
        'Positive infinity' => 'Infinité positive',
        'Valid' => 'Valide',
      },
    },
   'RunTimeScale' => 'Échelle du temps de fonctionnement',
   'RunTimeSincePowerUp' => 'Temps de fonctionnement depuis la mise sous tension',
   'RunTimeValue' => 'Valeur du temps de fonctionnement',
   'SMaxSampleValue' => 'Valeur maxi d\'échantillon S',
   'SMinSampleValue' => 'Valeur mini d\'échantillon S',
   'SPIFFVersion' => 'Version SPIFF',
   'SR2SubIFDKey' => 'Clé du SubIFD SR2',
   'SR2SubIFDLength' => 'Longueur du SubIFD SR2',
   'SR2SubIFDOffset' => 'Décalage du SubIFD SR2',
   'SRAWQuality' => {
      Description => 'Qualité sRAW',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'SRActive' => {
      Description => 'Réduction de bougé active',
      PrintConv => {
        'No' => 'Non',
        'Yes' => 'Oui',
      },
    },
   'SRFocalLength' => 'Focale de réduction de bougé',
   'SRHalfPressTime' => 'Temps entre mesure et déclenchement',
   'SRResult' => {
      Description => 'Stabilisation',
      PrintConv => {
        'Not stabilized' => 'Non stabilisé',
      },
    },
   'SRawType' => 'Type sRAW',
   'SVGVersion' => 'Version SVG',
   'SafetyShift' => {
      Description => 'Décalage de sécurité',
      PrintConv => {
        'Disable' => 'Désactivé',
        'Enable (ISO speed)' => 'Activé (sensibilité ISO)',
        'Enable (Tv/Av)' => 'Activé (Tv/Av)',
      },
    },
   'SafetyShiftInAvOrTv' => {
      Description => 'Décalage de sécurité Av ou Tv',
      PrintConv => {
        'Disable' => 'Désactivé',
        'Enable' => 'Activé',
      },
    },
   'SampleFormat' => {
      Description => 'Format d\'échantillon',
      PrintConv => {
        'Complex int' => 'Entier complexe',
        'Float' => 'Réel à virgule flottante',
        'Signed' => 'Entier signé',
        'Undefined' => 'Non défini',
        'Unsigned' => 'Entier non signé',
      },
    },
   'SampleStructure' => {
      Description => 'Structure d\'échantillonnage',
      PrintConv => {
        'CompressionDependent' => 'Définie dans le processus de compression',
        'Orthogonal4-2-2Sampling' => 'Orthogonale, avec les fréquences d\'échantillonnage dans le rapport 4:2:2:(4)',
        'OrthogonalConstangSampling' => 'Orthogonale, avec les mêmes fréquences d\'échantillonnage relatives sur chaque composante',
      },
    },
   'SamplesPerPixel' => 'Nombre de composantes',
   'Saturation' => {
      PrintConv => {
        '+1 (medium high)' => '+1 (moyennement élevée)',
        '+2 (high)' => '+2 (élevée)',
        '+3 (very high)' => '+3 (très élevée)',
        '+4 (highest)' => '+4 (la plus élevée)',
        '+4 (maximum)' => '+4 (maximale)',
        '-1 (medium low)' => '-1 (moyennement faible)',
        '-2 (low)' => '-2 (faible)',
        '-3 (very low)' => '-3 (très faible)',
        '-4 (lowest)' => '-4 (la plus faible)',
        '-4 (minimum)' => '-4 (minimale)',
        '0 (normal)' => '0 (normale)',
        'Acros Green Filter' => 'Filtre vert Acros',
        'Acros Red Filter' => 'Filtre rouge Acros',
        'Acros Yellow Filter' => 'Filtre jaune Acros',
        'B&W' => 'N&B',
        'B&W Green Filter' => 'Filtre vert N&B',
        'B&W Red Filter' => 'Filtre rouge N&B',
        'B&W Sepia' => 'Filtre sépia N&B',
        'B&W Yellow Filter' => 'Filtre jaune N&B',
        'Black & White' => 'Noir et blanc',
        'Film Simulation' => 'Simulation de film',
        'High' => 'Forte',
        'Low' => 'Faible',
        'Medium High' => 'Moyennement élevée',
        'Medium Low' => 'Moyennement élevée',
        'Natural' => 'Naturel',
        'None' => 'Aucune',
        'None (B&W)' => 'Aucune (N&B)',
        'Normal' => 'Normale',
        'Toning Effect' => 'Effet tonique',
        'Vintage B&W' => 'Vintage N&B',
        'Vivid' => 'Éclatant',
      },
    },
   'SaturationAdj' => 'Réglage de la saturation',
   'SaturationAdjustmentAqua' => 'Réglage de la saturation Aqua',
   'SaturationAdjustmentBlue' => 'Réglage de la saturation Bleue',
   'SaturationAdjustmentGreen' => 'Réglage de la saturation Verte',
   'SaturationAdjustmentMagenta' => 'Réglage de la saturation Magenta',
   'SaturationAdjustmentOrange' => 'Réglage de la saturation Orange',
   'SaturationAdjustmentPurple' => 'Réglage de la saturation Violette',
   'SaturationAdjustmentRed' => 'Réglage de la saturation Rouge',
   'SaturationAdjustmentYellow' => 'Réglage de la saturation Jaune',
   'SaturationAuto' => {
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'SaturationFaithful' => {
      Description => 'Saturation Fidèle',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'SaturationLandscape' => {
      Description => 'Saturation Paysage',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'SaturationMonochrome' => {
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'SaturationNeutral' => {
      Description => 'Saturation Neutre',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'SaturationPortrait' => {
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'SaturationStandard' => {
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'SaturationUserDef1' => {
      Description => 'Saturation définie par l\\utilisateur 1',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'SaturationUserDef2' => {
      Description => 'Saturation définie par l\\utilisateur 2',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'SaturationUserDef3' => {
      Description => 'Saturation définie par l\\utilisateur 3',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'ScaleFactor' => 'Facteur d\'échelle',
   'ScaleFactor35efl' => 'Facteur d\'échelle équivalent à un objectif 35 mm',
   'ScanImageEnhancer' => {
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'ScanLength' => 'Longueur de numérisation',
   'ScanMode' => 'Mode de numérisation',
   'ScanModeEnumeration' => {
      Description => 'Mode de numérisation',
      PrintConv => {
        'Other' => 'Autre',
      },
    },
   'ScanOptions' => 'Options de numérisation',
   'ScanSoftware' => 'Logiciel de numérisation',
   'ScanSoftwareRevisionDate' => 'Date de révision du logiciel de numérisation',
   'ScanVelocity' => 'Vitesse de numérisation',
   'ScannerFirmwareDate' => 'Date du micrologiciel du scanner',
   'ScannerFirmwareVersion' => 'Version du micrologiciel du scanner',
   'ScannerMake' => 'Fabricant du scanner',
   'ScannerModel' => 'Modèle du scanner',
   'ScannerProductID' => 'ID produit du scanner',
   'ScannerSerialNumber' => 'Numéro de série du scanner',
   'ScanningDirection' => {
      Description => 'Direction de la numérisation',
      PrintConv => {
        'Bottom-Top, L-R' => 'De bas en haut, de gauche à droite',
        'Bottom-Top, R-L' => 'De bas en haut, de droite à gauche',
        'L-R, Bottom-Top' => 'De gauche à droite, de bas en haut',
        'L-R, Top-Bottom' => 'De gauche à droite, de haut en bas',
        'R-L, Bottom-Top' => 'De droite à gauche, de bas en haut',
        'R-L, Top-Bottom' => 'De droite à gauche, de haut en bas',
        'Top-Bottom, L-R' => 'De haut en bas, de gauche à droite',
        'Top-Bottom, R-L' => 'De haut en bas, de droite à gauche',
      },
    },
   'Scene' => 'Scène',
   'SceneAssist' => 'Assistant Scene',
   'SceneCaptureType' => {
      Description => 'Type de capture de scène',
      PrintConv => {
        'Landscape' => 'Paysage',
        'Night' => 'Nocturne',
        'Other' => 'Autre',
      },
    },
   'SceneDetect' => 'Scène détectée',
   'SceneDetectData' => 'Données de scène détectée',
   'SceneMode' => {
      Description => 'Mode Scène',
      PrintConv => {
        '2 in 1' => '2 en 1',
        '3D Sweep Panorama' => 'Panorama par balayage 3D',
        'Aerial Photo' => 'Photo aérienne',
        'Anti Motion Blur' => 'Anti-flou de mouvement',
        'Aperture Priority' => 'Priorité à l\'ouverture',
        'Appetizing Food' => 'Alimentation appétissante',
        'Artistic Nightscape' => 'Paysage nocturne artistique',
        'Auction' => 'Vente aux enchères',
        'Auto' => 'Auto.',
        'Auto PICT' => 'Image auto',
        'Available Light' => 'Lumière disponible',
        'Baby' => 'Bébé',
        'Back Light' => 'Contre-jour',
        'Backlight HDR' => 'Contre-jour HDR',
        'Backlight Silhouette' => 'Silhouette à contre-jour',
        'Backlit Softness' => 'Douceur du contre-jour',
        'Beach' => 'Plage',
        'Beach & Snow' => 'Mer & Montagne ',
        'Beauty Skin' => 'Beauté de la peau',
        'Behind Glass' => 'Derrière la vitre',
        'Bird Watching' => 'Observation des oiseaux',
        'Blue Sky' => 'Ciel bleu',
        'Bright Blue Sky' => 'Ciel bleu lumineux',
        'Bulb' => 'Ampoule',
        'Candle' => 'Bougie',
        'Candlelight' => 'Lumière de bougie',
        'Children' => 'Enfants',
        'Clear Night Portrait' => 'Portrait par nuit claire',
        'Clear Nightscape' => 'Paysage par nuit claire',
        'Clear Portrait' => 'Portrait clair',
        'Clear Sports Shot' => 'Prise de vue sportive claire',
        'Clear in Backlight' => 'Clair en contre-jour',
        'Clipboard' => 'Presse-papiers',
        'Color Effects' => 'Effets de couleurs',
        'Cont. Priority AE' => 'AE priorité continue',
        'Cool Night Sky' => 'Ciel nocturne calme',
        'Creative Control' => 'Contrôle créatif',
        'Cute Dessert' => 'Dessert sympa',
        'Digital Filter' => 'Filtre numérique',
        'Digital Image Stabilization' => 'Stabilisation numérique de l\'image',
        'Distinct Scenery' => 'Paysages distincts',
        'Economy' => 'Économie',
        'Face Portrait' => 'Portrait de visage',
        'Film Grain' => 'Grain de pellicule',
        'Fireworks' => 'Feux d\'artifice',
        'Flash Burst' => 'Éclat de flash',
        'Food' => 'Alimentation',
        'Forest' => 'Forêt',
        'Freeze Animal Motion' => 'Mouvements d\'animaux figés',
        'Glass Through' => 'Verre translucide',
        'Glistening Water' => 'Eau scintillante',
        'Glittering Illuminations' => 'Illuminations scintillantes',
        'Hand-held Starlight' => 'Lumière stellaire à main levée',
        'Handheld Night Shot' => 'Photo nocturne à main levée',
        'High ISO' => 'Haute sensibilité ISO',
        'High Key' => 'Tons clairs',
        'High Sensitivity' => 'Haute Sensibilité',
        'High Speed Continuous Shooting' => 'Prise de vue en continu à haute vitesse',
        'Indoor' => 'Intérieur',
        'Intelligent Auto' => 'Auto Intelligent',
        'Intelligent Auto Plus' => 'Auto Intelligent Plus',
        'Intelligent ISO' => 'ISO intelligente',
        'Kids' => 'Enfants',
        'Landscape' => 'Paysage',
        'Landscape+Portrait' => 'Paysage+Portrait',
        'Light Trails' => 'Sentiers lumineux',
        'Low Key' => 'Tons sombres',
        'Magic Filter' => 'Filtre magique',
        'Manual' => 'Manuelle',
        'Movie' => 'Vidéo',
        'Movie Preview' => 'Prévisualisation vidéo',
        'Multi Focus Shot' => 'Prise de vue multi-focus',
        'Multiple Exposure' => 'Exposition multiple',
        'Museum' => 'Musée',
        'My Color' => 'Ma couleur',
        'My Mode' => 'Mon mode',
        'Nature Macro' => 'Macro Nature',
        'Night Landscape' => 'Paysage nocturne',
        'Night Portrait' => 'Portrait nocturne',
        'Night Scene' => 'Scène nocturne',
        'Night Scene HDR' => 'Scène HDR nocturne',
        'Night Scene Portrait' => 'Portrait de scène nocturne',
        'Night Scenery' => 'Scène nocturne',
        'Night Snap' => 'Instantané nocturne',
        'Night View/Portrait' => 'Vue/Portrait nocturne',
        'Night+Portrait' => 'Nocturne+Portrait',
        'No Flash' => 'Pas de flash',
        'Normal' => 'Normale',
        'Off' => 'Désactivée',
        'Panning' => 'Panoramique',
        'Panorama Assist' => 'Assistance panoramique',
        'Panorama Left-right' => 'Panorama gauche-droite',
        'Panorama Right-left' => 'Panorama droite-gauche',
        'Party' => 'Fête',
        'Peripheral Defocus' => 'Défocalisation périphérique',
        'Pet' => 'Animal de compagnie',
        'Photo Frame' => 'Cadre photo',
        'Pin Hole' => 'Trou d\'épingle',
        'Program' => 'Programme',
        'Quick Shutter' => 'Obturateur rapide',
        'Relaxing Tone' => 'Tonalité relaxante',
        'Romantic Sunset Glow' => 'Lueur romantique au coucher du soleil',
        'Scenery' => 'Paysage',
        'Self Portrait' => 'Autoportrait',
        'Self Portrait+Self Timer' => 'Autoportrait+Auto-Temporisation',
        'Self Protrait+Timer' => 'Autoportrait+Temporisateur',
        'Shoot & Select' => 'Photographier et sélectionner',
        'Shoot & Select1' => 'Photographier et sélectionner 1',
        'Shoot & Select2' => 'Photographier et sélectionner 2',
        'Shooting Guide' => 'Guide de prise de vue',
        'Shutter Priority' => 'Priorité à l\'obturateur',
        'Silent' => 'Silencieux',
        'Silky Skin' => 'Peau soyeuse',
        'Slow Shutter' => 'Obturateur à vitesse lente',
        'Smart Scene' => 'Scène intelligente',
        'Smile Shot' => 'Prise de vue sur sourire',
        'Snow' => 'Neige',
        'Soft Background Shot' => 'Prise de vue en arrière-plan doux',
        'Soft Image of a Flower' => 'Douce image d\'une fleur',
        'Soft Skin' => 'Peau douce',
        'Stage Lighting' => 'Éclairage de scène',
        'Starry Night' => 'Nuit étoilée',
        'Sunset' => 'Coucher de soleil',
        'Super Macro' => 'Super macro',
        'Superior Auto' => 'Auto Supérieur',
        'Surf & Snow' => 'Surf et neige',
        'Sweep Panorama' => 'Panorama par balayage',
        'Sweet Child\'s Face' => 'Doux visage d\'enfant',
        'Text' => 'Texte',
        'Transform' => 'Transformer',
        'Underwater' => 'Subaquatique',
        'Underwater Macro' => 'Macro subaquatique',
        'Underwater Snapshot' => 'Prise de vue Subaquatique',
        'Underwater Wide1' => 'Plan large sous-marin 1',
        'Underwater Wide2' => 'Plan large sous-marin 2',
        'Vivid' => 'Éclatant',
        'Vivid Sunset Glow' => 'Coucher de soleil éclatant',
        'Warm Glowing Nightscape' => 'Paysage nocturne chaud et lumineux',
        'n/a' => 'Non applicable',
      },
    },
   'SceneModeUsed' => {
      Description => 'Mode scène utilisé',
      PrintConv => {
        'Aperture Priority' => 'Priorité à l\'ouverture',
        'Back Light' => 'Rétro-éclairage',
        'Beach' => 'Plage',
        'Candlelight' => 'Chandelle',
        'Children' => 'Enfants',
        'Fireworks' => 'Feux d\'artifice',
        'High ISO' => 'Haute sensibilité ISO',
        'Landscape' => 'Paysage',
        'Manual' => 'Manuel',
        'Museum' => 'Musé',
        'Night Landscape' => 'Paysage nocturne',
        'Night Portrait' => 'Portrait nocturne',
        'Program' => 'Programme',
        'Shutter Priority' => 'Priorité à l\'obturateur',
        'Snow' => 'Neige',
        'Sunset' => 'Coucher de soleil',
        'Text' => 'Texte',
      },
    },
   'SceneNumber' => 'Numéro de scène',
   'SceneRecognition' => {
      Description => 'Reconnaissance de scène',
      PrintConv => {
        'Backlit Portrait' => 'Portrait rétro-éclairé',
        'Landscape Image' => 'Image de paysage',
        'Night Portrait' => 'Portrait nocturne',
        'Night Scene' => 'Scène nocturne',
        'Portrait Image' => 'Image de portrait',
        'Unrecognized' => 'Non reconnue',
      },
    },
   'SceneSelect' => {
      Description => 'Sélection de la scène',
      PrintConv => {
        'Lamp' => 'Lampe',
        'Night' => 'Nocturne',
        'Off' => 'Désactivée',
        'User 1' => 'Utilisateur 1',
        'User 2' => 'Utilisateur 2',
      },
    },
   'SceneType' => {
      Description => 'Type de scène',
      PrintConv => {
        'Digital Scene Generation' => 'Génération d\'une scène numérique',
        'Directly photographed' => 'Photographié directement',
        'Original Scene' => 'Scène originale',
        'Second Generation Scene' => 'Scène de deuxième génération',
      },
    },
   'SecurityClassification' => {
      Description => 'Classification de sécurité',
      PrintConv => {
        'Confidential' => 'Confidentiel',
        'Restricted' => 'Restreint',
        'Top Secret' => 'Trés secret',
        'Unclassified' => 'Non classifié',
      },
    },
   'SelectableAFPoint' => {
      Description => 'Collimateurs AF sélectionnables',
      PrintConv => {
        '11 points' => '11 collimateurs',
        '19 points' => '19 collimateurs',
        '45 points' => '45 collimateurs',
        'Inner 9 points' => '9 collimateurs centraux',
        'Outer 9 points' => '9 collimateurs périphériques',
      },
    },
   'SelfTimer' => {
      Description => 'Minuteur',
      PrintConv => {
        '10 s / 3 pictures' => '10 s / 3 images',
        '10 s after shutter pressed' => '10 s après avoir pressé l\'obturateur',
        '2 s after shutter pressed' => '2 s après avoir pressé l\'obturateur',
        '3 photos after 10 s' => '3 photos après 10 s',
        'Off' => 'Désactivé',
        'Off (0)' => 'Désactivé (0)',
        'On' => 'Activé',
        'Self-timer 10 s' => 'Minuteur 10 s',
        'Self-timer 2 s' => 'Minuteur 2 s',
        'Self-timer 5 or 10 s' => 'Minuteur 5 ou 10 s',
      },
    },
   'SelfTimer2' => 'Minuteur automatique (2)',
   'SelfTimerMode' => 'Mode du minuteur',
   'SelfTimerShotCount' => 'Compteur de prise de vue du minuteur automatique',
   'SelfTimerShotInterval' => 'Intervalle du minuteur automatique',
   'SelfTimerTime' => 'Durée du minuteur automatique',
   'SensingMethod' => {
      Description => 'Méthode de capture',
      PrintConv => {
        'Color sequential area' => 'Capteur couleur séquentiel',
        'Color sequential linear' => 'Capteur couleur séquentiel linéaire',
        'Monochrome area' => 'Capteur monochrome',
        'Monochrome linear' => 'Capteur linéaire monochrome',
        'Not defined' => 'Non définie',
        'One-chip color area' => 'Capteur mono-puce couleur',
        'Three-chip color area' => 'Capteur tri-puces couleur',
        'Trilinear' => 'Capteur tri-linéaire',
        'Two-chip color area' => 'Capteur bi-puces couleur',
      },
    },
   'Sensitivity' => 'Sensibilité',
   'SensitivityAdjust' => 'Réglage de sensibilité',
   'SensitivityCalibrated' => 'Sensibilité calibrée',
   'SensitivitySteps' => {
      Description => 'Pas de sensibilité',
      PrintConv => {
        '1 EV Steps' => 'Pas de 1 IL',
        'As EV Steps' => 'Comme pas IL',
      },
    },
   'SensitivityType' => {
      Description => 'Type de sensibilité',
      PrintConv => {
        'ISO Speed' => 'Vitesse ISO',
        'Recommended Exposure Index' => 'Index d\'exposition recommandé',
        'Recommended Exposure Index and ISO Speed' => 'Index d\'exposition et vitesse ISO recommandés',
        'Standard Output Sensitivity' => 'Sensibilité de sortie standard',
        'Standard Output Sensitivity and ISO Speed' => 'Sensibilité de sortie standard et vitesse ISO',
        'Standard Output Sensitivity and Recommended Exposure Index' => 'Sensibilité de sortie standard et index d\'exposition recommandé',
        'Standard Output Sensitivity, Recommended Exposure Index and ISO Speed' => 'Sensibilité de sortie standard, index d\'exposition et vitesse ISO recommandés',
        'Unknown' => 'Inconnu',
      },
    },
   'Sensor' => 'Capteur',
   'SensorArea' => 'Zone du capteur',
   'SensorAreas' => 'Zones du capteur',
   'SensorBitDepth' => 'Profondeur de bit du capteur',
   'SensorBlueLevel' => 'Niveau bleu du capteur',
   'SensorBottomBorder' => 'Bordure inférieure du capteur',
   'SensorCalibration' => 'Calibrage du capteur',
   'SensorCalibration_0x0404' => 'Calibrage du capteur_0x0404',
   'SensorCalibration_0x0405' => 'Calibrage du capteur_0x0405',
   'SensorCalibration_0x0406' => 'Calibrage du capteur_0x0406',
   'SensorCalibration_0x0408' => 'Calibrage du capteur_0x0408',
   'SensorCalibration_0x040f' => 'Calibrage du capteur_0x040f',
   'SensorCalibration_0x0413' => 'Calibrage du capteur_0x0413',
   'SensorCalibration_0x0414' => 'Calibrage du capteur_0x0414',
   'SensorCalibration_0x0418' => 'Calibrage du capteur_0x0418',
   'SensorCalibration_0x041c' => 'Calibrage du capteur_0x041c',
   'SensorCalibration_0x041e' => 'Calibrage du capteur_0x041e',
   'SensorCleaning' => {
      Description => 'Nettoyage du capteur',
      PrintConv => {
        'Disable' => 'Désactivé',
        'Enable' => 'Activé',
      },
    },
   'SensorData' => 'Données du capteur',
   'SensorFieldOfViewName' => {
      PrintConv => {
        'Wide' => 'Large',
      },
    },
   'SensorFullHeight' => 'Hauteur totale du capteur',
   'SensorFullWidth' => 'Largeur totale du capteur',
   'SensorHeight' => 'Hauteur du capteur',
   'SensorID' => 'Identifiant du capteur',
   'SensorImageHeight' => 'Hauteur de l\'image du capteur',
   'SensorImageWidth' => 'Largeur de l\'image du capteur',
   'SensorLeftBorder' => 'Bordure gauche du capteur',
   'SensorLeftMargin' => 'Marge gauche du capteur',
   'SensorMode' => 'Mode du capteur',
   'SensorName' => 'Nom du capteur',
   'SensorPixelSize' => 'Taille des pixels du capteur',
   'SensorRedLevel' => 'Niveau rouge du capteur',
   'SensorRightBorder' => 'Bordure droit du capteur',
   'SensorRollAngle' => 'Angle de roulis du capteur',
   'SensorSerialNumber' => 'Numéro de sérue du capteur',
   'SensorShield' => {
      Description => 'Protection du capteur',
      PrintConv => {
        'Closes' => 'Se ferme',
        'Stays Open' => 'Reste ouvert',
      },
    },
   'SensorSize' => 'Taille du capteur',
   'SensorTemperature' => 'Capteur de température',
   'SensorTemperature2' => 'Capteur de température 2',
   'SensorTopBorder' => 'Bordure supérieure du capteur',
   'SensorTopMargin' => 'Marge supérieure du capteur',
   'SensorType' => 'Type de capteur',
   'SensorTypeCode' => 'Code du type de senseur',
   'SensorWidth' => 'Largeur du capteur',
   'SequenceFileNumber' => 'Numéro du fichier de la séquence',
   'SequenceImageNumber' => 'Numéro de l\'image de la séquence',
   'SequenceLength' => {
      Description => 'Longueur de la séquence',
      PrintConv => {
        '1 file' => '1 fichier',
        '1 shot' => '1 prise de vue',
        '10 files' => '10 fichiers',
        '10 shots' => '10 prises de vue',
        '12 shots' => '12 prises de vue',
        '16 shots' => '16 prises de vue',
        '2 files' => '2 fichiers',
        '2 shots' => '2 prises de vue',
        '3 files' => '3 fichiers',
        '3 shots' => '3 prises de vue',
        '4 shots' => '4 prises de vue',
        '5 files' => '5 fichiers',
        '5 shots' => '5 prises de vue',
        '7 files' => '7 fichiers',
        '7 shots' => '7 prises de vue',
        '9 files' => '9 fichiers',
        '9 shots' => '9 prises de vue',
        'Continuous' => 'Continue',
        'Continuous - Sweep Panorama' => 'Continue - Balayage du panorama',
        'Continuous - iSweep Panorama' => 'Continue - iBalayage du panorama',
      },
    },
   'SequenceNumber' => {
      Description => 'Numéro de Séquence',
      PrintConv => {
        'Single' => 'Unique',
        'n/a' => 'Non applicable',
      },
    },
   'SequentialShot' => {
      PrintConv => {
        'None' => 'Aucune',
      },
    },
   'SerialNumber' => 'Numéro de série',
   'SerialNumberFormat' => 'Format du numéro de série ',
   'ServiceIdentifier' => 'Identificateur de service',
   'SetButtonCrossKeysFunc' => {
      Description => 'Réglage touche SET/joypad',
      PrintConv => {
        'Cross keys: AF point select' => 'Joypad:Sélec. collim. AF',
        'Normal' => 'Normale',
        'Set: Flash Exposure Comp' => 'SET:Cor expo flash',
        'Set: Parameter' => 'SET:Changer de paramètres',
        'Set: Picture Style' => 'SET:Style d’image',
        'Set: Playback' => 'SET:Lecture',
        'Set: Quality' => 'SET:Qualité',
      },
    },
   'SetButtonWhenShooting' => {
      Description => 'Touche SET au déclenchement',
      PrintConv => {
        'Change parameters' => 'Changer de paramètres',
        'Default (no function)' => 'Normal (désactivée)',
        'Disabled' => 'Désactivée',
        'Flash exposure compensation' => 'Correction expo flash',
        'ISO speed' => 'Sensibilité ISO',
        'Image playback' => 'Lecture de l\'image',
        'Image quality' => 'Changer de qualité',
        'Image size' => 'Taille de l\'image',
        'LCD monitor On/Off' => 'Écran LCD On/Off',
        'Menu display' => 'Affichage du menu',
        'Normal (disabled)' => 'Normal (désactivée)',
        'Picture style' => 'Style de l\'image',
        'Quick control screen' => 'Écran de contrôle rapide',
        'Record func. + media/folder' => 'Fonction enregistrement + média/dossier',
        'Record movie (Live View)' => 'Enr. vidéo (visée écran)',
        'White balance' => 'Balance des blancs',
      },
    },
   'SetFunctionWhenShooting' => {
      Description => 'Régler la fonction lors de la prise de vue',
      PrintConv => {
        'Change Parameters' => 'Changer de paramètres',
        'Change Picture Style' => 'Changer de style de l\'image',
        'Change quality' => 'Changer de qualité',
        'Default (no function)' => 'Par défaut (aucune fonction)',
        'Image replay' => 'Reprise de l\'image',
        'Menu display' => 'Affichage du menu',
      },
    },
   'ShadingCompensation' => {
      Description => 'Compensation de l\'ombrage',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'ShadingCompensation2' => {
      Description => 'Compensation de l\'ombrage 2',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'Shadow' => 'Ombre',
   'ShadowScale' => 'Echelle d\'ombre',
   'ShadowTone' => {
      Description => 'Tonalité d\'ombrage',
      PrintConv => {
        '+1 (medium hard)' => '+1 (moyennement dure)',
        '+2 (hard)' => '+2 (dure)',
        '+3 (very hard)' => '+3 (très dure)',
        '+4 (hardest)' => '+4 (la plus dure)',
        '-1 (medium soft)' => '-1 (moyennement douce)',
        '-2 (soft)' => '-2 (douce)',
        '0 (normal)' => '0 (normale)',
      },
    },
   'Shadows' => 'Ombres',
   'ShakeReduction' => {
      Description => 'Réduction des tremblements',
      PrintConv => {
        'Off' => 'Désactivée',
        'Off (4)' => 'Désactivée (4)',
        'Off (AA simulation off)' => 'Désactivée (simulation AA désactivée)',
        'Off (AA simulation type 1)' => 'Désactivée (simulation AA type 1)',
        'Off (AA simulation type 1) (8)' => 'Désactivée (simulation AA type 1) (8)',
        'Off (AA simulation type 2)' => 'Désactivée (simulation AA type 2)',
        'Off (AA simulation type 2) (16)' => 'Désactivée (simulation AA type 2) (16)',
        'On' => 'Activée',
        'On (135)' => 'Activée (135)',
        'On (15)' => 'Activée (15)',
        'On (7)' => 'Activée (7)',
        'On (AA simulation off)' => 'Activée (simulation AA désactivée)',
        'On (AA simulation type 1)' => 'Activée (simulation AA type 1)',
        'On (AA simulation type 2)' => 'Activée (simulation AA type 2)',
        'On (Video)' => 'Activée (Vidéo)',
        'On (mode 1)' => 'Activée (mode 1)',
        'On (mode 2)' => 'Activée (mode 2)',
        'On but Disabled' => 'Activée mais Désactivée',
      },
    },
   'ShakeReductionInfo' => 'Stabilisation',
   'Sharpness' => {
      Description => 'Netteté',
      PrintConv => {
        '+1 (medium hard)' => '+1 (moyennement dure)',
        '+2 (hard)' => '+2 (dure)',
        '+3 (very hard)' => '+3 (très dure)',
        '+4 (hardest)' => '+4 (la plus dure)',
        '+4 (maximum)' => '+4 (maximale)',
        '-1 (medium soft)' => '-1 (moyennement dure)',
        '-2 (soft)' => '-2 (douce)',
        '-3 (very soft)' => '-3 (très douce)',
        '-4 (minimum)' => '-4 (minimale)',
        '-4 (softest)' => '-4 (la plus douce)',
        '0 (normal)' => '0 (normale)',
        'Film Simulation' => 'Simulation de film',
        'Hard' => 'Dure',
        'Normal' => 'Normale',
        'Sharp' => 'Dure',
        'Soft' => 'Douce',
        'n/a' => 'Non applicable',
      },
    },
   'SharpnessAuto' => {
      Description => 'Netteté Auto',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'SharpnessFactor' => 'Facteur de netteté',
   'SharpnessFaithful' => {
      Description => 'Netteté Fidèle',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'SharpnessFrequency' => {
      Description => 'Fréquence de netteté',
      PrintConv => {
        'High' => 'Haute',
        'Highest' => 'La plus haute',
        'Low' => 'Basse',
        'Lowest' => 'La plus basse',
        'n/a' => 'Non applicable',
      },
    },
   'SharpnessLandscape' => {
      Description => 'Netteté Payasage',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'SharpnessMonochrome' => {
      Description => 'Netteté Monochrome',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'SharpnessNeutral' => {
      Description => 'Netteté Neutre',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'SharpnessPortrait' => {
      Description => 'Netteté Portrait',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'SharpnessRange' => 'Plage de netteté',
   'SharpnessSetting' => 'Réglage de netteté',
   'SharpnessStandard' => {
      Description => 'Netteté Standard',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'SharpnessUserDef1' => {
      Description => 'Netteté définie par l\'utlisateur 1',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'SharpnessUserDef2' => {
      Description => 'Netteté définie par l\'utlisateur 2',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'SharpnessUserDef3' => {
      Description => 'Netteté définie par l\'utlisateur 3',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'ShootingMode' => {
      Description => 'Mode de prise de vue',
      PrintConv => {
        'Aerial Photo' => 'Photo aérienne',
        'Aperture Priority' => 'Priorité à l\'ouverture',
        'Appetizing Food' => 'Alimentation appétissante',
        'Artistic Nightscape' => 'Scène artistique nocturne',
        'Baby' => 'Bébé',
        'Backlit Softness' => 'Douceur du contre-jour',
        'Beach' => 'Plage',
        'Bright Blue Sky' => 'Ciel bleu lumineux',
        'Candlelight' => 'Éclairage de Bougie',
        'Clear Night Portrait' => 'Portrait par nuit claire',
        'Clear Nightscape' => 'Scène par nuit claire',
        'Clear Portrait' => 'Portrait clair',
        'Clear Sports Shot' => 'Prise de vue sportive claire',
        'Clear in Backlight' => 'Clair en contre-jour',
        'Clipboard' => 'Presse-papiers',
        'Color Effects' => 'Effets de couleurs',
        'Cool Night Sky' => 'Ciel nocturne calme',
        'Creative Control' => 'Contrôle créatif',
        'Cute Dessert' => 'Dessert sympa',
        'Digital Filter' => 'Filtre numérique',
        'Distinct Scenery' => 'Paysage distinct',
        'Economy' => 'Économie',
        'Film Grain' => 'Grain de pellicule',
        'Fireworks' => 'Feu d\'artifice',
        'Flash Burst' => 'Éclat de flash',
        'Food' => 'Nourriture',
        'Freeze Animal Motion' => 'Mouvements d\'animaux figés',
        'Glass Through' => 'Verre translucide',
        'Glistening Water' => 'Eau scintillante',
        'Glittering Illuminations' => 'Illuminations scintillantes',
        'Handheld Night Shot' => 'Photo nocturne à main levée',
        'High Sensitivity' => 'Haute sensibilité',
        'High Speed Continuous Shooting' => 'Déclenchement continu à grande vitesse',
        'Intelligent Auto' => 'Mode Auto intelligent',
        'Intelligent Auto Plus' => 'Mode Auto Plus intelligent',
        'Intelligent ISO' => 'ISO Intelligent',
        'Manual' => 'Manuel',
        'Movie' => 'Vidéo',
        'Movie Preview' => 'Prévisualisation vidéo',
        'Multi-aspect' => 'MISMulti-aspectSING',
        'My Color' => 'Ma couleur',
        'Night Portrait' => 'Portrait nocturne',
        'Night Scenery' => 'Scène nocturne',
        'Normal' => 'Normale',
        'Panning' => 'Panoramique',
        'Panorama Assist' => 'Assistant Panorama',
        'Party' => 'Fête',
        'Peripheral Defocus' => 'Défocalisation périphérique',
        'Pet' => 'Animal domestique',
        'Photo Frame' => 'Cadre photo',
        'Pin Hole' => 'Trou d\'épingle',
        'Program' => 'Programme',
        'Relaxing Tone' => 'Tonalité relaxante',
        'Romantic Sunset Glow' => 'Lueur romantique au coucher du soleil',
        'Scenery' => 'Paysage',
        'Self Portrait' => 'Autoportrait',
        'Shutter Priority' => 'Priorité à l\'obturateur',
        'Silky Skin' => 'Peau soyeuse',
        'Snow' => 'Neige',
        'Soft Image of a Flower' => 'Douce image d\'une fleur',
        'Soft Skin' => 'Peau douce',
        'Starry Night' => 'Nuit étoilée',
        'Sunset' => 'Coucher de soleil',
        'Sweet Child\'s Face' => 'Doux visage d\'enfant',
        'Transform' => 'Transformer',
        'Underwater' => 'Subaquatique',
        'Vivid Sunset Glow' => 'Coucher de soleil éclatant',
        'Warm Glowing Nightscape' => 'Paysage nocturne chaud et lumineux',
      },
    },
   'ShootingModeSetting' => {
      Description => 'Paramétrage du mode de prise de vue',
      PrintConv => {
        'Continuous' => 'Continu',
        'Delayed Remote' => 'Télécommande retardée',
        'Quick-response Remote' => 'Télécommande à réponse rapide',
        'Self-timer' => 'Retardateur',
        'Single Frame' => 'Vue par vue',
      },
    },
   'ShortDocumentID' => 'ID court de document',
   'ShortOwnerName' => 'Nom abrégé du propriétaire',
   'ShortReleaseTimeLag' => {
      Description => 'Inertie au déclenchement réduite',
      PrintConv => {
        'Disable' => 'Désactivé',
        'Enable' => 'Activé',
      },
    },
   'ShotInfoVersion' => 'Version des Infos prise de vue',
   'ShotName' => 'Nom de la prise de vue',
   'ShotNumber' => 'Numéro de la prise de vue',
   'ShotNumberSincePowerUp' => 'Numéro de la prise de vue depuis la mise sous tension',
   'ShotNumberSincePowerUp2' => 'Numéro de la prise de vue depuis la mise sous tension 2',
   'Shutter' => {
      Description => 'Obturateur',
      PrintConv => {
        'Silent / Electronic (0 0 0)' => 'Silencieux / électronique (0 0 0)',
      },
    },
   'Shutter-AELock' => {
      Description => 'Déclencheur/Touche verr. AE',
      PrintConv => {
        'AE lock/AF' => 'Verrouillage AE/autofocus',
        'AE/AF, No AE lock' => 'AE/AF, pas de verrou. AE',
        'AF/AE lock' => 'Autofocus/verrouillage AE',
        'AF/AF lock' => 'Autofocus/verrouillage AF',
        'AF/AF lock, No AE lock' => 'AF/verr.AF, pas de verr.AE',
      },
    },
   'ShutterAELButton' => {
      Description => 'Déclencheur/Touche verr. AE',
      PrintConv => {
        'AE lock/AF' => 'Verrouillage AE/Autofocus',
        'AE/AF, No AE lock' => 'AE/AF, pas de verrou. AE',
        'AF/AE lock stop' => 'Autofocus/Verrouillage AE',
        'AF/AF lock, No AE lock' => 'AF/verr.AF, pas de verr.AE',
      },
    },
   'ShutterButtonAFOnButton' => {
      Description => 'Déclencheur/Touche AF',
      PrintConv => {
        'AE lock/Metering + AF start' => 'Mémo expo/lct. mesure+AF',
        'Metering + AF start' => 'Mesure + lancement AF',
        'Metering + AF start/AF stop' => 'Mesure + lancement/arrêt AF',
        'Metering + AF start/disable' => 'Lct. mesure+AF/désactivée',
        'Metering start/Meter + AF start' => 'Lct. mesure/lct. mesure+AF',
      },
    },
   'ShutterCount' => 'Décompte des déclenchements',
   'ShutterCount2' => 'Décompte des déclenchements 2',
   'ShutterCount3' => 'Décompte des déclenchements 3',
   'ShutterCurtainHack' => {
      Description => 'Hack de rideaux à volets',
      PrintConv => {
        '1st-curtain sync' => 'Synchro du 1er rideau',
        '2nd-curtain sync' => 'Synchro du 2e rideau',
      },
    },
   'ShutterCurtainSync' => {
      Description => 'Synchronisation du rideau',
      PrintConv => {
        '1st-curtain sync' => 'Synchronisation premier rideau',
        '2nd-curtain sync' => 'Synchronisation deuxième rideau',
      },
    },
   'ShutterMode' => {
      PrintConv => {
        'Aperture Priority' => 'Priorité à l\'ouverture',
      },
    },
   'ShutterReleaseButtonAE-L' => {
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'ShutterReleaseNoCFCard' => {
      Description => 'Déclench. obtur. sans carte',
      PrintConv => {
        'No' => 'Non',
        'Yes' => 'Oui',
      },
    },
   'ShutterSpeed' => 'Temps de pose',
   'ShutterSpeedDisplayed' => 'Vitesse d\'obturation affichée',
   'ShutterSpeedRange' => {
      Description => 'Régler gamme de vitesses',
      PrintConv => {
        'Disable' => 'Désactivé',
        'Enable' => 'Activée',
      },
    },
   'ShutterSpeedValue' => 'Vitesse d\'obturation',
   'SidecarForExtension' => 'Extension',
   'SimilarityIndex' => 'Indice de similarité',
   'SlaveFlashMeteringSegments' => 'Segments de mesure flash esclave',
   'SlideShow' => {
      PrintConv => {
        'No' => 'Non',
        'Yes' => 'Oui',
      },
    },
   'SlowShutter' => {
      Description => 'Vitesse d\'obturation lente',
      PrintConv => {
        'Night Scene' => 'Nocturne',
        'None' => 'Aucune',
        'Off' => 'Désactivé',
        'On' => 'Activé',
        'n/a' => 'Non applicable',
      },
    },
   'SlowSync' => {
      Description => 'Synchronisation lente',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'SmartAlbumColor' => {
      Description => 'Couleur de l\'album intelligent',
      PrintConv => {
        'Black' => 'Noir',
        'Blue' => 'Bleu',
        'Green' => 'Vert',
        'Red' => 'Rouge',
        'Various' => 'Divers',
        'White' => 'Blanc',
        'Yellow' => 'Jaune',
        'n/a' => 'Non applicable',
      },
    },
   'SoftSkinEffect' => {
      Description => 'Effet peau douce',
      PrintConv => {
        'High' => 'Élevé',
        'Low' => 'Faible',
        'Mid' => 'Moyen',
        'Off' => 'Désactivé',
        'n/a' => 'Non applicable',
      },
    },
   'Software' => 'Logiciel',
   'SonyCropSize' => 'Taille du recadrage Sony',
   'SonyCropTopLeft' => 'Recadrage en haut à gauche Sony',
   'SonyDateTime' => 'Date et heure Sony',
   'SonyDateTime2' => 'Date et heure 2 Sony',
   'SonyExposureTime' => 'Temps d\'exposition Sony',
   'SonyExposureTime2' => 'Temps d\'exposition 2 Sony',
   'SonyFNumber' => 'Diaphragme ƒ Sony',
   'SonyFNumber2' => 'Diaphragme ƒ 2 Sony',
   'SonyISO' => 'ISO Sony',
   'SonyImageHeight' => 'Hauteur de l\'image Sony',
   'SonyImageHeightMax' => 'Hauteur maximale de l\'image Sony',
   'SonyImageWidth' => 'Largeur d\'image Sony',
   'SonyImageWidthMax' => 'Largeur d\'image maximale Sony',
   'SonyMaxAperture' => 'Ouverture maximale Sony',
   'SonyMaxApertureValue' => 'Valeur d\'ouverture maximale Sony',
   'SonyMinAperture' => 'Valeur d\'ouverture minimale Sony',
   'SonyModelID' => {
      Description => 'Identifiant du modèle Sony',
      PrintConv => {
        'DSLR-A850 (APS-C mode)' => 'DSLR-A850 (mode APS-C)',
        'DSLR-A900 (APS-C mode)' => 'DSLR-A900 (mode APS-C)',
      },
    },
   'SonyRawFileType' => {
      Description => 'Type de fichier RAW Sony',
      PrintConv => {
        'Sony Compressed RAW' => 'RAW Sony compressé',
        'Sony Lossless Compressed RAW' => 'RAW Sony compressé sans perte',
        'Sony Lossless Compressed RAW 2' => 'RAW 2 Sony compressé sans perte',
        'Sony Uncompressed 12-bit RAW' => 'RAW Sony 12 bits non compressé',
        'Sony Uncompressed 14-bit RAW' => 'RAW Sony 14 bits non compressé',
      },
    },
   'SonyToneCurve' => 'Courbe de tonalité Sony',
   'SourceDirectoryIndex' => 'Index du répertoire source',
   'SpatialFrequencyResponse' => 'Réponse spatiale en fréquence',
   'SpecialEffectsOpticalFilter' => {
      PrintConv => {
        'None' => 'Aucune',
      },
    },
   'SpecialMode' => 'Mode spécial',
   'SpectralSensitivity' => 'Sensibilité spectrale',
   'Speed' => {
      Description => 'Vitesse',
      PrintConv => {
        'Fast' => 'Rapide',
        'Hardcore' => 'Matérielle',
        'Medium' => 'Moyenne',
        'Slow' => 'Lent',
      },
    },
   'SpotMeterLinkToAFPoint' => {
      Description => 'Mesure spot liée au viseur AF',
      PrintConv => {
        'Disable (use center AF point)' => 'Désactivée (utilise le centre du viseur AF)',
        'Enable (use active AF point)' => 'Activée (utilise le viseur AF actif)',
      },
    },
   'SpotMeteringMode' => {
      Description => 'Mode de mesurage Spot',
      PrintConv => {
        'AF Point' => 'Point de mise au point',
        'Center' => 'Centre',
      },
    },
   'StackedImage' => {
      PrintConv => {
        'HDR1' => 'HDR 1',
        'HDR2' => 'HDR 2',
      },
    },
   'State' => 'État / Région',
   'StopsAboveBaseISO' => 'Arrêt au-dessus de l\'ISO de référence',
   'StraightenAngle' => 'Angle de redressement',
   'StreamType' => {
      PrintConv => {
        'Text' => 'Texte',
      },
    },
   'StripByteCounts' => 'Nombre d\'octets dans la bande',
   'StripOffsets' => 'Décalage des bandes',
   'Sub-location' => 'Lieu',
   'SubSecCreateDate' => 'Date de création des données numériques',
   'SubSecDateTimeOriginal' => 'Date de création des données originales',
   'SubSecModifyDate' => 'Date de modification du fichier',
   'SubSecTime' => 'Fractions de seconde de l\'heure de création',
   'SubSecTimeDigitized' => 'Fractions de seconde de l\'heure de création des données numériques',
   'SubSecTimeOriginal' => 'Fractions de seconde de l\'heure de création des données originales',
   'SubSelector' => {
      PrintConv => {
        'HDR Overlay' => 'Recouvrement HDR',
      },
    },
   'SubTileBlockSize' => 'Taille de bloc de sous-tuile',
   'SubfileType' => {
      Description => 'Type du nouveau sous-fichier',
      PrintConv => {
        'Alternate reduced-resolution image' => 'Image alternative à résolution réduite',
        'Color IW44' => 'Couleurs IW44',
        'Depth map' => 'Carte de profondeur',
        'Depth map of reduced-resolution image' => 'Carte de profondeur de l\'image en résolution réduite',
        'Enhanced image data' => 'Données d\'image améliorées',
        'Full-resolution image' => 'Image en pleine résolution',
        'Grayscale IW44' => 'Niveaux de gris IW44',
        'Multi-page document' => 'Document multipage',
        'Reduced-resolution image' => 'Image en résolution réduite',
        'Semantic Mask' => 'Masque sémantique',
        'Shared component' => 'Composant partagé',
        'Single page of multi-page image' => 'Une page d\'une image multipage',
        'Single page of multi-page reduced-resolution image' => 'Une page d\'une image multipage en résolution réduite',
        'Single-page image' => 'Image sur une seule page',
        'Thumbnail image' => 'Image miniature',
        'Transparency mask' => 'Masque de transparence',
        'Transparency mask of multi-page image' => 'Masque de transparence de l\'image multipage',
        'Transparency mask of reduced-resolution image' => 'Masque de transparence de l\'image en résolution réduite',
        'Transparency mask of reduced-resolution multi-page image' => 'Masque de transparence de l\'image multipage à résolution réduite',
        'invalid' => 'invalide',
      },
    },
   'SubimageColor' => {
      PrintConv => {
        'RGB' => 'RVB',
      },
    },
   'Subject' => 'Sujet',
   'SubjectArea' => 'Zone du sujet',
   'SubjectCode' => 'Code sujet',
   'SubjectDetection' => {
      PrintConv => {
        'People' => 'Personnes',
      },
    },
   'SubjectDistance' => 'Distance du sujet',
   'SubjectDistanceRange' => {
      Description => 'Plage de distance du sujet',
      PrintConv => {
        'Close' => 'Proche',
        'Distant' => 'Lointaine',
        'Unknown' => 'Inconnue',
      },
    },
   'SubjectLocation' => 'Localisation du sujet',
   'SubjectMotion' => {
      Description => 'Mouvements du sujet',
      PrintConv => {
        'Erratic' => 'Erratiques',
        'Middle' => 'Moyens',
        'Steady' => 'Stables',
      },
    },
   'SubjectProgram' => {
      Description => 'Programme du sujet',
      PrintConv => {
        'Night portrait' => 'Portrait nocturne',
        'None' => 'Aucun',
        'Sports action' => 'Action sportive',
        'Sunset' => 'Coucher de soleil',
        'Text' => 'Texte',
      },
    },
   'SubjectReference' => 'Code de sujet',
   'Subsystem' => {
      Description => 'Sous-système',
      PrintConv => {
        'EFI ROM' => 'ROM EFI',
        'EFI application' => 'Application EFI',
        'EFI boot service' => 'Service de démarrage EFI',
        'EFI runtime driver' => 'Pilote d\'exécution EFI',
        'Native' => 'Natif',
        'OS/2 command line' => 'Ligne de commande OS/2',
        'POSIX command line' => 'Ligne de commande POSIX',
        'Unknown' => 'Inconnu',
        'Windows CE GUI' => 'Interface graphique Windows CE',
        'Windows GUI' => 'Interface graphique Windows',
        'Windows command line' => 'Ligne de commande Windows',
      },
    },
   'SuperMacro' => {
      PrintConv => {
        'Off' => 'Désactivé',
      },
    },
   'SuperimposedDisplay' => {
      Description => 'Affichage superposé',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'SupplementalCategories' => 'Catégorie d\'appoint',
   'SupplementalType' => {
      Description => 'Type de supplément',
      PrintConv => {
        'Main Image' => 'Non établi',
        'Rasterized Caption' => 'Titre rastérisé',
        'Reduced Resolution Image' => 'Image de résolution réduite',
      },
    },
   'SvISOSetting' => 'Réglage ISO Sv',
   'SweepPanoramaDirection' => {
      Description => 'Direction du balayage panoramique',
      PrintConv => {
        'Bottom to Top' => 'De bas en haut',
        'Down' => 'Vers le bas',
        'Left' => 'Vers la gauche',
        'Left to Right' => 'De gauche à droite',
        'Off' => 'Désactivé',
        'Right' => 'Vers la droite',
        'Right to Left' => 'De droite à gauche',
        'Top to Bottom' => 'De haut en bas',
        'Up' => 'Vers le haut',
      },
    },
   'SweepPanoramaFieldOfView' => 'Champ de vision panoramique par balayage',
   'SweepPanoramaSize' => {
      Description => 'Taille du panorama balayé',
      PrintConv => {
        'Wide' => 'Large',
      },
    },
   'SwitchToRegisteredAFPoint' => {
      Description => 'Activer collimateur enregistré',
      PrintConv => {
        'Assist' => 'Touche d\'assistance',
        'Assist + AF' => 'Touche d\'assistance + touche AF',
        'Disable' => 'Désactivé',
        'Enable' => 'Activé',
        'Only while pressing assist' => 'Seulement en appuyant touche d\'assistance',
      },
    },
   'System' => 'Système',
   'T4Options' => 'Bits de remplissage ajoutés',
   'T6Options' => 'Options T6',
   'TIFF-EPStandardID' => 'Identification standard TIFF-EP',
   'TTL_DA_ADown' => 'Segment de mesure flash esclave 6',
   'TTL_DA_AUp' => 'Segment de mesure flash esclave 5',
   'TTL_DA_BDown' => 'Segment de mesure flash esclave 8',
   'TTL_DA_BUp' => 'Segment de mesure flash esclave 7',
   'Tagged' => {
      PrintConv => {
        'No' => 'Non',
        'Yes' => 'Oui',
      },
    },
   'TargetAperture' => 'Ouverture de la cible',
   'TargetCompressionRatio' => 'Taux de compression cible',
   'TargetExposureTime' => 'Temps d\'exposition de la cible',
   'TargetImageType' => {
      Description => 'Type d\'image cible',
      PrintConv => {
        'Real-world Subject' => 'Sujet du monde réel',
        'Written Document' => 'Document écrit',
      },
    },
   'TargetPrinter' => 'Imprimante cible',
   'Technology' => {
      Description => 'Technologie',
      PrintConv => {
        'Active Matrix Display' => 'Afficheur à matrice active',
        'Cathode Ray Tube Display' => 'Afficheur à tube cathodique',
        'Digital Camera' => 'Appareil photo numérique',
        'Digital Cinema Projector' => 'Projecteur cinéma numérique',
        'Digital Motion Picture Camera' => 'Caméra numérique de cinéma',
        'Dye Sublimation Printer' => 'Imprimante à sublimation thermique',
        'Electrophotographic Printer' => 'Imprimante électrophotographique',
        'Electrostatic Printer' => 'Imprimante électrostatique',
        'Film Scanner' => 'Scanner de film',
        'Flexography' => 'Flexographie',
        'Ink Jet Printer' => 'Imprimante à jet d\'encre',
        'Motion Picture Film Recorder' => 'Enregistreur de film cinématographique',
        'Motion Picture Film Scanner' => 'Scanneur de films cinématographiques',
        'Offset Lithography' => 'Lithographie offset',
        'Passive Matrix Display' => 'Afficheur à matrice passive',
        'Photo CD' => 'CD photo',
        'Photo Image Setter' => 'Cadre photo',
        'Photographic Paper Printer' => 'Imprimante à papier photo',
        'Projection Television' => 'Télévision par projection',
        'Reflective Scanner' => 'Scanner réflectif',
        'Silkscreen' => 'Sérigraphie',
        'Thermal Wax Printer' => 'Imprimante thermique à cire',
        'Video Camera' => 'Caméra vidéo',
        'Video Monitor' => 'Moniteur vidéo',
      },
    },
   'Teleconverter' => {
      PrintConv => {
        'None' => 'Aucune',
      },
    },
   'TemporalIDNested' => {
      Description => 'ID temporel imbriqué',
      PrintConv => {
        'No' => 'Non',
        'Yes' => 'Oui',
      },
    },
   'Text' => 'Texte',
   'TextColor' => 'Couleur du texte',
   'TextComments' => 'Commentaires du texte ',
   'TextEncoding' => {
      Description => 'Encodage du texte',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'TextFace' => {
      Description => 'Style du texte',
      PrintConv => {
        'Bold' => 'Gras',
        'Condense' => 'Condensé',
        'Extend' => 'Étendu',
        'Italic' => 'Italique',
        'Outline' => 'Contour',
        'Plain' => 'Normal',
        'Shadow' => 'Ombré',
        'Underline' => 'Souligné',
      },
    },
   'TextStamp' => {
      Description => 'Tampon du texte',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'TextString' => 'Chaîne de caractères du texte',
   'TextToSpeech' => {
      PrintConv => {
        'Disabled' => 'Désactivé',
        'Enabled' => 'Activé',
      },
    },
   'Three-DTrackingWatchArea' => {
      PrintConv => {
        'Wide' => 'Large',
      },
    },
   'Thresholding' => 'Seuil',
   'ThumbnailFileName' => 'Nom du fichier de la vignette',
   'ThumbnailHeight' => 'Hauteur de la vignette',
   'ThumbnailImage' => 'Vignette',
   'ThumbnailImageSize' => 'Taille des miniatures',
   'ThumbnailImageValidArea' => 'Zone de validité de l\'image miniature',
   'ThumbnailLength' => 'Longueur de la vignette',
   'ThumbnailOffset' => 'Décalage de la vignette',
   'ThumbnailTIFF' => 'Vignette TIFF',
   'ThumbnailURL' => 'URL de la vignette',
   'ThumbnailWidth' => 'Hauteur de la vignette',
   'TiffMeteringImage' => 'Image de mesure TIFF',
   'TiffMeteringImageHeight' => 'Hauteur de l\'image de mesure TIFF',
   'TiffMeteringImageWidth' => 'Largeur de l\'image de mesure TIFF',
   'TileByteCounts' => 'Nombre d\'octets d\'élément',
   'TileDepth' => 'Profondeur d\'élément',
   'TileLength' => 'Longueur de l\'élément',
   'TileOffsets' => 'Décalages de l\'élément',
   'TileWidth' => 'Largeur de l\'élément',
   'Time' => 'Heure',
   'TimeCreated' => 'Heure de création',
   'TimeLapseShotNumber' => 'Nombre de prises de vue en temps réel',
   'TimeScaleParamsQuality' => {
      PrintConv => {
        'Low' => 'Bas',
      },
    },
   'TimeSent' => 'Heure d\'envoi',
   'TimeSincePowerOn' => 'Temps écoulé depuis la mise sous tension',
   'TimeStamp' => 'Horodateur',
   'TimeStamp1' => 'Horodateur 1',
   'TimeZone' => {
      Description => 'Fuseau horaire',
      PrintConv => {
        '+00:00 (London)' => '+00:00 (Londre)',
        '+02:00 (Athens, Helsinki)' => '+02:00 (Athènes, Helsinki)',
        '+03:00 (Moscow, Nairobi)' => '+03:00 (Moscou, Nairobi)',
        '+03:30 (Tehran)' => '+03:30 (Téhéran)',
        '+04:30 (Kabul)' => '+04:30 (Kaboul)',
        '+05:45 (Kathmandu)' => '+05:45 (Katmandou)',
        '+08:00 (Beijing, Honk Kong, Sinapore)' => '+08:00 (Pékin, Honk Kong, Singapour)',
        '-01:00 (Azores)' => '-01:00 (Les Açores)',
      },
    },
   'TimeZone2' => 'Fuseau horaire 2',
   'TimeZoneCity' => {
      Description => 'Zone horaire de la ville',
      PrintConv => {
        '(not set)' => '(non défini)',
        'Adelaide' => 'Adélaïde',
        'Azores' => 'Les Açores',
        'Cairo' => 'Le Caire',
        'Chatham Islands' => 'Iles Chatham',
        'Dubai' => 'Dubaï',
        'Kabul' => 'Kaboul',
        'Kathmandu' => 'Katmandou',
        'London' => 'Londre',
        'Moscow' => 'Moscou',
        'Newfoundland' => 'Terre-Neuve',
        'Solomon Islands' => 'Iles Salomon',
        'Tehran' => 'Téhéran',
        'Yangon' => 'Rangoun',
        'n/a' => 'Non applicable',
      },
    },
   'TimeZoneCode' => 'Code du fuseau horaire',
   'TimeZoneInfo' => 'Info du fuseau horaire',
   'TimeZoneOffset' => 'Décalage du fuseau horaire',
   'TimeZoneURL' => 'URL du fuseau horaire',
   'TimerFunctionButton' => {
      PrintConv => {
        'Active D-Lighting' => 'D-Lighting actif',
      },
    },
   'TimerLength' => {
      Description => 'Durée du retardateur',
      PrintConv => {
        'Disable' => 'Désactivé',
        'Enable' => 'Activée',
      },
    },
   'TimerRecording' => {
      Description => 'Enregistrement par minuterie',
      PrintConv => {
        'Focus Bracketing' => 'Bracketing de mise au point',
        'Off' => 'Désactivé',
        'Stop-motion Animation' => 'Animation en stop motion',
        'Time Lapse' => 'Lapse de temps',
      },
    },
   'Title' => 'Titre',
   'ToneComp' => 'Correction de tonalité',
   'ToneCurve' => {
      Description => 'Courbe de ton',
      PrintConv => {
        'Manual' => 'Manuelle',
      },
    },
   'ToneCurveActive' => {
      PrintConv => {
        'No' => 'Non',
        'Yes' => 'Oui',
      },
    },
   'ToneCurveProperty' => {
      PrintConv => {
        'Shot Settings' => 'Paramétrage prise de vue',
      },
    },
   'ToneCurveX' => 'Courbe de tonalité X',
   'ToneCurveY' => 'Courbe de tonalité Y',
   'ToneCurves' => 'Courbes de tonalité',
   'ToneLevel' => {
      Description => 'Niveau de tonalité',
      PrintConv => {
        'Highlights' => 'Lumineux',
        'Midtones' => 'Moyen',
        'Shadows' => 'Ombres',
      },
    },
   'ToningEffect' => {
      Description => 'Effet tonifiant',
      PrintConv => {
        'B&W' => 'N&B',
        'Blue' => 'Bleu',
        'Blue-green' => 'Bleu-vert',
        'Color' => 'Coleur',
        'Green' => 'Vert',
        'None' => 'Aucun',
        'Off' => 'Désactivé',
        'Purple' => 'Violet',
        'Purple-blue' => 'Violet-bleu',
        'Red' => 'Rouge',
        'Red-purple' => 'Rouge-violet',
        'Sepia' => 'Sépia',
        'Yellow' => 'Jaune',
        'n/a' => 'Non applicable',
      },
    },
   'ToningEffectAuto' => {
      Description => 'Effet de tonalité Auto',
      PrintConv => {
        'Blue' => 'Bleu',
        'Green' => 'Vert',
        'None' => 'Aucun',
        'Purple' => 'Violet',
        'Sepia' => 'Sépia',
        'n/a' => 'Non applicable',
      },
    },
   'ToningEffectFaithful' => {
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'ToningEffectLandscape' => {
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'ToningEffectMonochrome' => {
      Description => 'Effet de tonalité monochrome ',
      PrintConv => {
        'Blue' => 'Bleu',
        'Green' => 'Vert',
        'None' => 'Aucun',
        'Purple' => 'Violet',
        'Sepia' => 'Sépia',
        'n/a' => 'Non applicable',
      },
    },
   'ToningEffectNeutral' => {
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'ToningEffectPortrait' => {
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'ToningEffectStandard' => {
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'ToningEffectUserDef1' => {
      Description => 'Effet de tonalité personnalisé 1',
      PrintConv => {
        'Blue' => 'Bleu',
        'Green' => 'Vert',
        'None' => 'Aucun',
        'Purple' => 'Violet',
        'Sepia' => 'Sépia',
        'n/a' => 'Non applicable',
      },
    },
   'ToningEffectUserDef2' => {
      Description => 'Effet de tonalité personnalisé 2',
      PrintConv => {
        'Blue' => 'Bleu',
        'Green' => 'Vert',
        'None' => 'Aucun',
        'Purple' => 'Violet',
        'Sepia' => 'Sépia',
        'n/a' => 'Non applicable',
      },
    },
   'ToningEffectUserDef3' => {
      Description => 'Effet de tonalité personnalisé 3',
      PrintConv => {
        'Blue' => 'Bleu',
        'Green' => 'Vert',
        'None' => 'Aucun',
        'Purple' => 'Violet',
        'Sepia' => 'Sépia',
        'n/a' => 'Non applicable',
      },
    },
   'ToningSaturation' => 'Tonalité de la saturation',
   'TouchAE' => {
      Description => 'AE Tactile',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'TransferFunction' => 'Fonction de transfert',
   'TransferRange' => 'Intervalle de transfert',
   'Transformation' => {
      PrintConv => {
        'Horizontal (normal)' => 'Horizontale (normale)',
        'Mirror horizontal' => 'Mise en miroir horizontal',
        'Mirror horizontal and rotate 270 CW' => 'Mise en miroir horizontal et rotation antihoraire de 270°',
        'Mirror horizontal and rotate 90 CW' => 'Mise en miroir horizontal et rotation antihoraire de 90°',
        'Mirror vertical' => 'Mise en miroir vertical)',
        'Rotate 180' => 'Rotation de 180°',
        'Rotate 270 CW' => 'Rotation antihoraire de 270°',
        'Rotate 90 CW' => 'Rotation antihoraire de 90°',
      },
    },
   'TransmissionReference' => 'Référence transmission',
   'TransparencyIndicator' => 'Indicateur de transparence',
   'TrapIndicator' => 'Indicateur de piège',
   'Trapped' => {
      Description => 'Piégé',
      PrintConv => {
        'False' => 'Faux',
        'True' => 'Vrai',
        'Unknown' => 'Inconnu',
      },
    },
   'TravelDay' => 'Date du Voyage',
   'TvExposureTimeSetting' => 'Réglage de temps de pose Tv',
   'URL' => 'URL ',
   'USMLensElectronicMF' => {
      Description => 'MF électronique à objectif USM',
      PrintConv => {
        'Always turned off' => 'Toujours débrayé',
        'Disable after one-shot AF' => 'Désactivée après One-Shot AF',
        'Disable in AF mode' => 'Désactivée en mode AF',
        'Enable after one-shot AF' => 'Activée après AF One-Shot',
        'Turns off after one-shot AF' => 'Débrayé après One-Shot AF',
        'Turns on after one-shot AF' => 'Activé après One-Shot AF',
      },
    },
   'Uncompressed' => {
      Description => 'Non.comprimé',
      PrintConv => {
        'No' => 'Non',
        'Yes' => 'Oui',
      },
    },
   'UncompressedQuickTime' => 'QuickTime non compressé',
   'UncompressedSize' => 'Taille non compressée',
   'UncompressedTextLength' => 'Longueur du texte non compressé',
   'UniqueCameraModel' => 'Nom unique de modèle d\'appareil',
   'UniqueDocumentID' => 'ID unique de document',
   'UniqueFileIdentifier' => 'ID unique du fichier',
   'UniqueID' => 'ID unique',
   'UniqueObjectName' => 'Nom Unique d\'Objet',
   'Unknown' => 'Inconnu',
   'Unsharp1Color' => {
      PrintConv => {
        'Blue' => 'Bleu',
        'Green' => 'Vert',
        'RGB' => 'RVB',
        'Red' => 'Rouge',
        'Yellow' => 'Jaune',
      },
    },
   'Unsharp2Color' => {
      PrintConv => {
        'Blue' => 'Bleu',
        'Green' => 'Vert',
        'RGB' => 'RVB',
        'Red' => 'Rouge',
        'Yellow' => 'Jaune',
      },
    },
   'Unsharp3Color' => {
      PrintConv => {
        'Blue' => 'Bleu',
        'Green' => 'Vert',
        'RGB' => 'RVB',
        'Red' => 'Rouge',
        'Yellow' => 'Jaune',
      },
    },
   'Unsharp4Color' => {
      PrintConv => {
        'Blue' => 'Bleu',
        'Green' => 'Vert',
        'RGB' => 'RVB',
        'Red' => 'Rouge',
        'Yellow' => 'Jaune',
      },
    },
   'UnsharpCount' => 'Compteur d\'unsharp',
   'UnsharpMask' => {
      Description => 'Masque d\'unsharp',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'Urgency' => {
      Description => 'Urgence',
      PrintConv => {
        '0 (reserved)' => '0 (réservé pour utilisation future)',
        '1 (most urgent)' => '1 (très urgent)',
        '5 (normal urgency)' => '5 (normalement urgent)',
        '8 (least urgent)' => '8 (moins urgent)',
        '9 (user-defined priority)' => '9 (réservé pour utilisation future)',
      },
    },
   'UsableMeteringModes' => {
      Description => 'Modes de mesure utilisables',
      PrintConv => {
        'Disable' => 'Désactivés',
        'Enable' => 'Activées',
      },
    },
   'UsableShootingModes' => {
      Description => 'Modes de prises de vue utilisables',
      PrintConv => {
        'Disable' => 'Désactivés',
        'Enable' => 'Activés',
      },
    },
   'Usage' => 'Utilisation',
   'UsageRightsMessage' => 'Message relatif aux droits d\'utilisation',
   'UsageTerms' => 'Conditions d\'Utilisation',
   'UseDialWithoutHold' => {
      Description => 'Utilisation de la fonction d\'appel sans attente',
      PrintConv => {
        'Off' => 'Désactivée',
        'On' => 'Activée',
      },
    },
   'UserCollection' => 'Collection utilisateur',
   'UserComment' => 'Commentaire de l\'utilisateur',
   'UserCustom1' => 'Personnalisé par l\'utilisateur 1',
   'UserCustom2' => 'Personnalisé par l\'utilisateur 2',
   'UserData' => 'Données utilisateur',
   'UserData01' => 'Données utilisateur 01',
   'UserData02' => 'Données utilisateur 02',
   'UserData03' => 'Données utilisateur 03',
   'UserData04' => 'Données utilisateur 04',
   'UserData05' => 'Données utilisateur 05',
   'UserData06' => 'Données utilisateur 06',
   'UserData07' => 'Données utilisateur 07',
   'UserData08' => 'Données utilisateur 08',
   'UserData09' => 'Données utilisateur 09',
   'UserData10' => 'Données utilisateur 10',
   'UserData11' => 'Données utilisateur 11',
   'UserData12' => 'Données utilisateur 12',
   'UserData13' => 'Données utilisateur 13',
   'UserData14' => 'Données utilisateur 14',
   'UserData15' => 'Données utilisateur 15',
   'UserData16' => 'Données utilisateur 16',
   'UserData17' => 'Données utilisateur 17',
   'UserData18' => 'Données utilisateur 18',
   'UserData19' => 'Données utilisateur 19',
   'UserData20' => 'Données utilisateur 20',
   'UserData21' => 'Données utilisateur 21',
   'UserData22' => 'Données utilisateur 22',
   'UserData23' => 'Données utilisateur 23',
   'UserDataMode' => 'Mode des données utilisateur',
   'UserDef1PictureStyle' => {
      Description => 'Style d’image définit par l’utilisateur 1',
      PrintConv => {
        'Faithful' => 'Fidèle',
        'Landscape' => 'Paysage',
        'Neutral' => 'Neutre',
      },
    },
   'UserDef2PictureStyle' => {
      Description => 'Style d’image définit par l’utilisateur 2',
      PrintConv => {
        'Faithful' => 'Fidèle',
        'Landscape' => 'Paysage',
        'Neutral' => 'Neutre',
      },
    },
   'UserDef3PictureStyle' => {
      Description => 'Style d’image définit par l’utilisateur 3',
      PrintConv => {
        'Faithful' => 'Fidèle',
        'Landscape' => 'Paysage',
        'Neutral' => 'Neutre',
      },
    },
   'VRDOffset' => 'Décalage VRD',
   'VRDVersion' => 'Version VRD',
   'VRInfo' => 'Information stabilisateur',
   'VRInfoVersion' => 'Info Version VR',
   'VRMode' => {
      Description => 'Mode VR',
      PrintConv => {
        'Active' => 'Actif',
        'Off' => 'Désactivé',
        'On (1)' => 'Activé (1)',
      },
    },
   'VR_0x66' => {
      PrintConv => {
        'Off' => 'Désactivé',
      },
    },
   'ValidAFPoints' => 'Points de mise au point automatique valides',
   'ValidBits' => 'Bits valides',
   'VariProgram' => 'Variprogramme',
   'VariableLowPassFilter' => {
      Description => 'Filtre passe-bas variable',
      PrintConv => {
        'High' => 'Haut',
        'Off' => 'Désactivé',
        'n/a' => 'Non applicable',
      },
    },
   'VerticalAFOnButton' => {
      PrintConv => {
        'HDR Overlay' => 'Recouvrement HDR',
      },
    },
   'VerticalFuncButton' => {
      PrintConv => {
        'HDR Overlay' => 'Recouvrement HDR',
      },
    },
   'VerticalFuncButtonPlaybackMode' => {
      PrintConv => {
        'HDR Overlay' => 'Recouvrement HDR',
      },
    },
   'VerticalFuncButtonPlusDials' => {
      PrintConv => {
        'Active D-Lighting' => 'D-Lighting actif',
      },
    },
   'VerticalFuncPlusDials' => {
      PrintConv => {
        'Active D-Lighting' => 'D-Lighting actif',
      },
    },
   'VerticalMovieAFOnButton' => {
      PrintConv => {
        'HDR Overlay' => 'Recouvrement HDR',
      },
    },
   'VerticalMovieFuncButton' => {
      PrintConv => {
        'HDR Overlay' => 'Recouvrement HDR',
      },
    },
   'VerticalMultiSelector' => {
      PrintConv => {
        'HDR Overlay' => 'Recouvrement HDR',
      },
    },
   'VibrationReduction' => {
      Description => 'Reduction des vibrations',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
        'n/a' => 'Non applicable',
      },
    },
   'VideoCardGamma' => 'Gamma de la carte vidéo',
   'VideoFrameRate' => {
      Description => 'Fréquence d\'images vidéo',
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'VideoOrientation' => {
      Description => 'Orientation vidéo',
      PrintConv => {
        'Horizontal (normal)' => 'Horizontale (normale)',
        'Mirror horizontal' => 'Mise en miroir horizontal',
        'Mirror horizontal and rotate 270 CW' => 'Mise en miroir horizontal et retournée de 270° dans le sens antihoraire',
        'Mirror horizontal and rotate 90 CW' => 'Mise en miroir horizontal et retournée de 90° dans le sens antihoraire',
        'Mirror vertical' => 'Mise en miroir vertical',
        'Rotate 180' => 'Retournée de 180°',
        'Rotate 270 CW' => 'Retournée de 270° dans le sens antihoraire',
        'Rotate 90 CW' => 'Retournée de 90° dans le sens antihoraire',
      },
    },
   'ViewInfoDuringExposure' => {
      Description => 'Infos viseur pendant exposition',
      PrintConv => {
        'Disable' => 'Désactivé',
        'Enable' => 'Activé',
      },
    },
   'ViewfinderWarning' => {
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'ViewingCondDesc' => 'Description des conditions de visionnage',
   'ViewingCondIlluminant' => 'Illuminant des conditions de visionnage',
   'ViewingCondIlluminantType' => 'Type d\'illuminant des conditions de visionnage',
   'ViewingCondSurround' => 'Environnement des conditions de visionnage',
   'ViewingMode2' => {
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'VignetteControl' => {
      Description => 'Controle du vignettage',
      PrintConv => {
        'High' => 'Haut',
        'Low' => 'Bas',
        'Normal' => 'Normale',
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'VignetteControlIntensity' => 'Intensité du contrôle des vignettes',
   'VignetteCorrectionAlreadyApplied' => 'Correction de vignette déjà appliquée',
   'VignetteMidpoint' => 'Point central de la vignette',
   'Vignetting' => {
      Description => 'Vignettage',
      PrintConv => {
        'High' => 'Haut',
        'Low' => 'Bas',
        'Medium' => 'Moyen',
        'Off' => 'Désactivé',
      },
    },
   'VignettingCorrParams' => 'Paramètres de correction du vignettage',
   'VignettingCorrVersion' => 'Version de la correction du vignettage',
   'VignettingCorrection' => {
      Description => 'Correction du vignettage',
      PrintConv => {
        'No correction params available' => 'Aucun paramètre de correction disponible',
        'Off' => 'Désactivée',
        'n/a' => 'Non applicable',
      },
    },
   'VignettingParams' => 'Paramètres de vignettage',
   'VignettingSetting' => 'Réglages du vignettage',
   'VintageStrength' => 'Force du vintage',
   'VisualColor' => {
      Description => 'Couleur visuelle',
      PrintConv => {
        'Color' => 'Couleur',
      },
    },
   'VoiceMemo' => {
      Description => 'Mémo vocal',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'WBAdjBlueBalance' => 'Balance des blancs Réglage de la balance bleue',
   'WBAdjColorTemp' => 'Balance des blancs Réglage de la température de la couleur',
   'WBAdjLighting' => {
      Description => 'Balance des blancs Réglage de la lumière',
      PrintConv => {
        'Daylight (cloudy)' => 'Lumière du jour (2)',
        'Daylight (direct sunlight)' => 'Lumière du jour (0)',
        'Daylight (shade)' => 'Lumière du jour (1)',
        'Flash (FL-G1 filter)' => 'Flash (Filtre FL-G1)',
        'Flash (FL-G2 filter)' => 'Flash (Filtre FL-G2)',
        'Flash (TN-A1 filter)' => 'Flash (Filtre FL-TN-A1)',
        'Flash (TN-A2 filter)' => 'Flash (Filtre FL-TN-A2)',
        'High Color Rendering Fluorescent (3700K)' => 'Fluorescent à haut rendu des couleurs (3700K)',
        'High Color Rendering Fluorescent (5000K)' => 'Fluorescent à haut rendu des couleurs (5000K)',
        'High Color Rendering Fluorescent (cool white)' => 'Fluorescent à haut rendu des couleurs (blanc froid)',
        'High Color Rendering Fluorescent (daylight)' => 'Fluorescent à haut rendu des couleurs (lumière du jour)',
        'High Color Rendering Fluorescent (warm white)' => 'Fluorescent à haut rendu des couleurs (blanc chaud)',
        'None' => 'Aucune',
        'Sodium Vapor Lamps' => 'Lampes à vapeur de sodium',
        'Standard Fluorescent (3700K)' => 'Fluorescent standard (3700K)',
        'Standard Fluorescent (5000K)' => 'Fluorescent standard (5000K)',
        'Standard Fluorescent (cool white)' => 'Fluorescent standard (blanc froid)',
        'Standard Fluorescent (daylight)' => 'Fluorescent standard (lumière du jour)',
        'Standard Fluorescent (high temperature mercury vapor)' => 'Fluorescent standard (vapeur de mercure à haute température)',
        'Standard Fluorescent (warm white)' => 'Fluorescent standard (blanc chaud)',
      },
    },
   'WBAdjMode' => {
      Description => 'Mode de réglage de la balance des blancs ',
      PrintConv => {
        'Calculate Automatically' => 'Calculer automatiquement',
        'Recorded Value' => 'Valeur enregistrée',
        'Underwater' => 'Sous l\'eau',
        'Use Gray Point' => 'Utiliser le point gris',
        'Use Temperature' => 'Utiliser la température',
      },
    },
   'WBAdjRGGBLevels' => 'Balance des blancs Réglage des niveaux RVVB',
   'WBAdjRedBalance' => 'Balance des blancs Réglage de la balance rouge',
   'WBAdjTemperature' => 'Balance des blancs Réglage de la température',
   'WBBlueLevel' => 'Niveau Bleu Balance des Blancs',
   'WBBracketMode' => {
      Description => 'Mode Bracketing de la balance des blancs',
      PrintConv => {
        'Off' => 'Désactivé',
        'On (shift AB)' => 'Activé (changement AB)',
        'On (shift GM)' => 'Activé (changement GM)',
      },
    },
   'WBBracketValueAB' => 'Balance des blancs Valeur de bracketing AB',
   'WBBracketValueGM' => 'Balance des blancs Valeur de bracketing GM',
   'WBFineTuneActive' => {
      PrintConv => {
        'No' => 'Non',
        'Yes' => 'Oui',
      },
    },
   'WBGreenLevel' => 'Niveau Vert Balance des Blancs',
   'WBMediaImageSizeSetting' => {
      Description => 'Réglage de balance des blancs + taille d\'image',
      PrintConv => {
        'LCD monitor' => 'Écran LCD',
        'Rear LCD panel' => 'Panneau LCD arrière',
      },
    },
   'WBRedLevel' => 'Niveau Rouge Balance des Blancs',
   'WBShiftAB' => 'Décalage Balance des Blancs AB',
   'WBShiftAB_GM' => 'Décalage Balance des Blancs AB_GM',
   'WBShiftAB_GM_Precise' => 'Décalage Balance des Blancs AB_Précis',
   'WBShiftCreativeControl' => 'Changement du Contrôle créatif de la balance des blancs',
   'WBShiftGM' => 'Décalage Balance Blancs vert-magenta',
   'WBShiftIntelligentAuto' => 'Décalage Balance des Blancs Auto intelligent',
   'WBType1' => {
      Description => 'Type de balance des blancs 1',
      PrintConv => {
        'Cloudy' => 'Nuageux',
        'Cool White Fluorescent' => 'Blanc froid fluorescent',
        'Day White Fluorescent' => 'Blanc fluorescent de jour',
        'Daylight' => 'Lumière du jour',
        'Daylight Fluorescent' => 'Lumière du jour fluorescent',
        'Fine Weather' => 'Beau temps',
        'ISO Studio Tungsten' => 'ISO Studio Tungstène',
        'Other' => 'Autre',
        'Shade' => 'Ombre',
        'Standard Light A' => 'Lumière standard A',
        'Standard Light B' => 'Lumière standard B',
        'Standard Light C' => 'Lumière standard C',
        'Tungsten (Incandescent)' => 'Tungstène (Incandescent)',
        'Unknown' => 'Inconnu',
        'Warm White Fluorescent' => 'Blanc chaud fluorescent',
        'White Fluorescent' => 'Blanc fluorescent',
      },
    },
   'WB_GBRGLevels' => 'Balance des Blancs des niveaux VBRV',
   'WB_GLevel' => 'Niveau balance des blancs G',
   'WB_GLevel3000K' => 'Niveau balance des blancs G 3000K',
   'WB_GLevel3300K' => 'Niveau balance des blancs G 3300K',
   'WB_GLevel3600K' => 'Niveau balance des blancs G 3600K',
   'WB_GLevel3900K' => 'Niveau balance des blancs G 3900K',
   'WB_GLevel4000K' => 'Niveau balance des blancs G 4000K',
   'WB_GLevel4300K' => 'Niveau balance des blancs G 4300K',
   'WB_GLevel4500K' => 'Niveau balance des blancs G 4500K',
   'WB_GLevel4800K' => 'Niveau balance des blancs G 4800K',
   'WB_GLevel5300K' => 'Niveau balance des blancs G 5300K',
   'WB_GLevel6000K' => 'Niveau balance des blancs G 6000K',
   'WB_GLevel6600K' => 'Niveau balance des blancs G 6600K',
   'WB_GLevel7500K' => 'Niveau balance des blancs G 7500K',
   'WB_GRBGLevels' => 'Niveau balance des Blancs des niveaux VRBV',
   'WB_GRBLevels' => 'Balance des Blancs des niveaux VRB',
   'WB_GRBLevelsAuto' => 'Balance des Blancs Auto des niveaux VRB',
   'WB_GRBLevelsStandard' => 'Balance des Blancs Standard des niveaux VRB',
   'WB_GRGBLevels' => 'Balance des Blancs des niveaux VRVB',
   'WB_GRGBLevelsAuto' => 'Balance des Blancs Auto des niveaux VRVB',
   'WB_RBGGLevels' => 'Niveau balance des blancs RBVV',
   'WB_RBLevels' => 'Niveaux balance des blancs RB',
   'WB_RBLevels1' => 'Niveaux balance des blancs RB 1',
   'WB_RBLevels2' => 'Niveaux balance des blancs RB 2',
   'WB_RBLevels3' => 'Niveaux balance des blancs RB 3',
   'WB_RBLevels3000K' => 'Niveaux balance des blancs RB 3000K',
   'WB_RBLevels3300K' => 'Niveaux balance des blancs RB 3300K',
   'WB_RBLevels3500K' => 'Niveaux balance des blancs RB 3500K',
   'WB_RBLevels3600K' => 'Niveaux balance des blancs RB 3600K',
   'WB_RBLevels3900K' => 'Niveaux balance des blancs RB 3800K',
   'WB_RBLevels4' => 'Niveaux balance des blancs RB 4',
   'WB_RBLevels4000K' => 'Niveaux balance des blancs RB 4000K',
   'WB_RBLevels4300K' => 'Niveaux balance des blancs RB 4300K',
   'WB_RBLevels4500K' => 'Niveaux balance des blancs RB 4500K',
   'WB_RBLevels4800K' => 'Niveaux balance des blancs RB 4800K',
   'WB_RBLevels5' => 'Niveaux balance des blancs RB 5',
   'WB_RBLevels5300K' => 'Niveaux balance des blancs RB 5300K',
   'WB_RBLevels6' => 'Niveaux balance des blancs RB 6',
   'WB_RBLevels6000K' => 'Niveaux balance des blancs RB 6000K',
   'WB_RBLevels6500K' => 'Niveaux balance des blancs RB 6500K',
   'WB_RBLevels6600K' => 'Niveaux balance des blancs RB 6600K',
   'WB_RBLevels7' => 'Niveaux balance des blancs RB 7',
   'WB_RBLevels7500K' => 'Niveaux balance des blancs RB 7500K',
   'WB_RBLevelsAuto' => 'Niveaux balance des blancs RB Automatique',
   'WB_RBLevelsCWB1' => 'Niveaux balance des blancs RB CWB 1',
   'WB_RBLevelsCWB2' => 'Niveaux balance des blancs RB CWB 2',
   'WB_RBLevelsCWB3' => 'Niveaux balance des blancs RB CWB 3',
   'WB_RBLevelsCWB4' => 'Niveaux balance des blancs RB CWB 4',
   'WB_RBLevelsCloudy' => 'Niveaux balance des blancs RB nuageux',
   'WB_RBLevelsShade' => 'Balance des Blancs des niveaux RB ombre',
   'WB_RBLevelsTungsten' => 'Balance des Blancs des niveaux RB tungstène',
   'WB_RGBGLevels' => 'Balance des Blancs des niveaux RVBV',
   'WB_RGBLevels' => 'Balance des Blancs des niveaux RVB',
   'WB_RGBLevels1' => 'Balance des Blancs des niveaux RVB 1',
   'WB_RGBLevels2' => 'Balance des Blancs des niveaux RVB 2',
   'WB_RGBLevels2500K' => 'Balance des Blancs des niveaux RVB 2500K',
   'WB_RGBLevels3' => 'Balance des Blancs des niveaux RVB 3',
   'WB_RGBLevels3200K' => 'Balance des Blancs des niveaux RVB 3200K',
   'WB_RGBLevels4' => 'Balance des Blancs des niveaux RVB 4',
   'WB_RGBLevels4500K' => 'Balance des Blancs des niveaux RVB 4500K',
   'WB_RGBLevels5' => 'Balance des Blancs des niveaux RVB 5',
   'WB_RGBLevels6' => 'Balance des Blancs des niveaux RVB 6',
   'WB_RGBLevels6000K' => 'Balance des Blancs des niveaux RVB 6000K',
   'WB_RGBLevels7' => 'Balance des Blancs des niveaux RVB 7',
   'WB_RGBLevels8500K' => 'Balance des Blancs des niveaux RVB 8500K',
   'WB_RGBLevelsAsShot' => 'Balance des blancs des niveaux RVB tels que capturés',
   'WB_RGBLevelsCloudy' => 'Balance des Blancs des niveaux RVB nuageux',
   'WB_RGBLevelsDaylight' => 'Balance des Blancs des niveaux RVB Lumière du jour',
   'WB_RGBLevelsFlash' => 'Balance des Blancs des niveaux RVB flash',
   'WB_RGBLevelsFluorescent' => 'Balance des Blancs des niveaux RVB fluorescent',
   'WB_RGBLevelsFluorescentM1' => 'Balance des Blancs des niveaux RVB Fluorescent M1',
   'WB_RGBLevelsFluorescentP1' => 'Balance des Blancs des niveaux RVB Fluorescent P1',
   'WB_RGBLevelsFluorescentP2' => 'Balance des Blancs des niveaux RVB Fluorescent P2',
   'WB_RGBLevelsShade' => 'Balance des Blancs des niveaux RVB ombre',
   'WB_RGBLevelsTungsten' => 'Balance des Blancs des niveaux RVB tungstène',
   'WB_RGGBLevels' => 'Balance des blancs des niveaux RVVB',
   'WB_RGGBLevelsAsShot' => 'Balance des blancs des niveaux RVVB tel que capturé',
   'WB_RGGBLevelsAuto' => 'Balance des blancs des niveaux RVVB automatiques',
   'WB_RGGBLevelsBlack' => 'Balance des Blancs des niveaux RVVB noirs',
   'WB_RGGBLevelsCloudy' => 'Balance des Blancs des niveaux RVVB nuageux',
   'WB_RGGBLevelsCustom' => 'Balance des Blancs des niveaux RVVB personnalisés',
   'WB_RGGBLevelsCustom1' => 'Balance des Blancs des niveaux RVVB personnalisés 1',
   'WB_RGGBLevelsCustom2' => 'Balance des Blancs des niveaux RVVB personnalisés 2',
   'WB_RGGBLevelsDaylight' => 'Balance des Blancs des niveaux RVVB lumière du jour',
   'WB_RGGBLevelsFlash' => 'Balance des Blancs des niveaux RVVB flash',
   'WB_RGGBLevelsFluorescent' => 'Balance des Blancs des niveaux RVVB fluorescent',
   'WB_RGGBLevelsFluorescentD' => 'Balance des Blancs des niveaux RVVB fluorescent',
   'WB_RGGBLevelsFluorescentN' => 'Balance des Blancs des niveaux RVVB fluo N',
   'WB_RGGBLevelsFluorescentW' => 'Balance des Blancs des niveaux RVVB fluo W',
   'WB_RGGBLevelsKelvin' => 'Balance des Blancs des niveaux RVVB Kelvin',
   'WB_RGGBLevelsMeasured' => 'Balance des Blancs des niveaux RVVB mesurés',
   'WB_RGGBLevelsPC1' => 'Balance des Blancs des niveaux RVVB PC 1',
   'WB_RGGBLevelsPC2' => 'Balance des Blancs des niveaux RVVB PC 2',
   'WB_RGGBLevelsPC3' => 'Balance des Blancs des niveaux RVVB PC 3',
   'WB_RGGBLevelsShade' => 'Balance des Blancs des niveaux RVVB ombre',
   'WB_RGGBLevelsTungsten' => 'Balance des Blancs des niveaux RVVB tungstène',
   'WCSProfiles' => 'Profil Windows Color System',
   'Warning' => 'Attention',
   'Watched' => {
      Description => 'Regardé',
      PrintConv => {
        'No' => 'Non',
        'Yes' => 'Oui',
      },
    },
   'WaterDepth' => 'Profondeur de l\'eau',
   'WatercolorFilter' => {
      Description => 'Filtre aquatique',
      PrintConv => {
        'Off' => 'Désactivé',
      },
    },
   'Watermark' => 'Filigrane',
   'WatermarkType' => 'Type de filigrane',
   'WatermarkURL' => 'URL du filigrane',
   'WebP_Flags' => {
      Description => 'Indicateurs WebP',
      PrintConv => {
        'EXIF' => 'Exif',
        'ICC Profile' => 'Profil ICC',
      },
    },
   'WebStatement' => 'Déclaration sur internet',
   'Webpage' => 'Page internet',
   'WhiteBalance' => {
      Description => 'Balance des blancs',
      PrintConv => {
        'Auto' => 'Equilibrage automatique des blancs',
        'Black & White' => 'Monochrome',
        'Cloudy' => 'Temps nuageux',
        'Color Temperature/Color Filter' => 'Temp. Couleur / Filtre couleur',
        'Cool White Fluorescent' => 'Fluorescente type soft',
        'Custom' => 'Personnalisée',
        'Custom 1' => 'Personnalisée 1',
        'Custom 2' => 'Personnalisée 2',
        'Custom 3' => 'Personnalisée 3',
        'Custom 4' => 'Personnalisée 4',
        'Day White Fluorescent' => 'Fluorescente type blanc',
        'Daylight' => 'Lumière du jour',
        'Daylight Fluorescent' => 'Fluorescente type jour',
        'Fluorescent' => 'Fluorescente',
        'Manual' => 'Manuelle',
        'Manual Temperature (Kelvin)' => 'Température de couleur (Kelvin)',
        'Shade' => 'Ombre',
        'Tungsten' => 'Tungstène (lumière incandescente)',
        'Unknown' => 'Inconnu',
        'User-Selected' => 'Sélectionnée par l\'utilisateur',
        'Warm White Fluorescent' => 'Fluorescent blanc chaud',
        'White Fluorescent' => 'Fluorescent blanc',
      },
    },
   'WhiteBalance0' => 'Balance des blancs 0',
   'WhiteBalance1' => 'Balance des blancs 1',
   'WhiteBalance2' => {
      Description => 'Balance des blancs 2',
      PrintConv => {
        '3000K (Tungsten light)' => '3000K (Lumière au tungstène)',
        '3600K (Tungsten light-like)' => '3600K (Similaire à la lumière du tungstène)',
        '4000K (Cool white fluorescent)' => '4000K (Fluorescent blanc froid)',
        '4500K (Neutral white fluorescent)' => '4500K (Fluorescent blanc neutre)',
        '5300K (Fine Weather)' => '5300K (Beau temps)',
        '6000K (Cloudy)' => '6000K (Nuageux)',
        '6600K (Daylight fluorescent)' => '6600K (Lumière du jour fluorescente)',
        '7500K (Fine Weather with Shade)' => '7500K (Beau temps avec de l\'ombre)',
        'Auto (Keep Warm Color Off)' => 'Auto (Maintien de la couleur chaude désactivé)',
        'Auto Setup' => 'Configuration automatique',
        'Custom WB 1' => 'Balance des blancs personnalisée 1',
        'Custom WB 2' => 'Balance des blancs personnalisée 2',
        'Custom WB 3' => 'Balance des blancs personnalisée 3',
        'Custom WB 4' => 'Balance des blancs personnalisée 4',
        'One Touch WB 1' => 'Balance des blancs par simple pression 1',
        'One Touch WB 2' => 'Balance des blancs par simple pression 2',
        'One Touch WB 3' => 'Balance des blancs par simple pression 3',
        'One Touch WB 4' => 'Balance des blancs par simple pression 3',
        'Underwater' => 'Sous-marine',
        'White Fluorescent' => 'Fluorescent blanc',
      },
    },
   'WhiteBalanceAdj' => {
      Description => 'Ajustement de la balance des blancs',
      PrintConv => {
        'Cloudy' => 'Temps nuageux',
        'Daylight' => 'Lumière du jour',
        'Fluorescent' => 'Fluorescente',
        'Manual (Click)' => 'Manuel (Clic)',
        'Off' => 'Désactivé',
        'On' => 'Activé',
        'Shade' => 'Ombre',
        'Shot Settings' => 'Paramétrage prise de vue',
        'Tungsten' => 'Tungstène (lumière incandescente)',
      },
    },
   'WhiteBalanceAutoAdjustment' => {
      Description => 'Ajustement automatique de la balance des blancs',
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'WhiteBalanceBias' => 'Décalage de Balance des blancs',
   'WhiteBalanceBlue' => 'Balance des blancs Bleue',
   'WhiteBalanceBracket' => 'Bracket de la balance des blancs',
   'WhiteBalanceBracketing' => {
      Description => 'Bracketing de la balance des blancs',
      PrintConv => {
        'High' => 'Haut',
        'Low' => 'Bas',
        'Off' => 'Désactivé',
      },
    },
   'WhiteBalanceButtonPlaybackMode' => {
      PrintConv => {
        'HDR Overlay' => 'Recouvrement HDR',
      },
    },
   'WhiteBalanceDetected' => {
      PrintConv => {
        'n/a' => 'Non applicable',
      },
    },
   'WhiteBalanceFineTune' => 'Balance des blancs - Réglage fin',
   'WhiteBalanceMode' => {
      Description => 'Mode de balance des blancs',
      PrintConv => {
        'Auto (Cloudy)' => 'Auto (nuageux)',
        'Auto (Day White Fluorescent)' => 'Auto (fluo jour)',
        'Auto (Daylight Fluorescent)' => 'Auto (fluo lum. jour)',
        'Auto (Daylight)' => 'Auto (lumière du jour)',
        'Auto (Flash)' => 'Auto (flash)',
        'Auto (Shade)' => 'Auto (ombre)',
        'Auto (Tungsten)' => 'Auto (tungstène)',
        'Auto (White Fluorescent)' => 'Auto (fluo blanc)',
        'Unknown' => 'Inconnu',
        'User-Selected' => 'Sélectionnée par l\'utilisateur',
      },
    },
   'WhiteBalanceRGB' => 'Balance des blancs RVB',
   'WhiteBalanceRed' => 'Balance des blancs Rouge',
   'WhiteBalanceSet' => {
      Description => 'Réglage de balance des blancs',
      PrintConv => {
        'Cloudy' => 'Temps nuageux',
        'Day White Fluorescent' => 'Fluorescent blanc jour',
        'Daylight' => 'Lumière du jour',
        'Daylight Fluorescent' => 'Fluorescente type jour',
        'Manual' => 'Manuelle',
        'Set Color Temperature 1' => 'Température de couleur définie 1',
        'Set Color Temperature 2' => 'Température de couleur définie 2',
        'Set Color Temperature 3' => 'Température de couleur définie 3',
        'Shade' => 'Ombre',
        'Tungsten' => 'Tungstène (lumière incandescente)',
        'White Fluorescent' => 'Fluorescent blanc',
        'n/a' => 'Non applicable',
      },
    },
   'WhiteBalanceTemperature' => 'Température de la balance des blancs',
   'WhiteLevel' => 'Niveau blanc',
   'WhitePoint' => 'Chromaticité du point blanc',
   'WhiteSampleBits' => 'Bits d\'échantillons blancs',
   'WhiteSampleHeight' => 'Hauteur de l\'échantillon blanc',
   'WhiteSampleLeftBorder' => 'Bordure gauche de l\'échantillon blanc',
   'WhiteSampleTopBorder' => 'Bordure supérieure de l\'échantillon blanc',
   'WhiteSampleWidth' => 'Largeur de l\'échantillon blanc',
   'Wide' => 'Large',
   'WideRange' => {
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'WorkColorSpace' => {
      PrintConv => {
        'Adobe RGB' => 'Adobe RVB',
        'Apple RGB' => 'Apple RVB',
      },
    },
   'WorldTime' => 'Fuseau horaire',
   'WorldTimeLocation' => {
      Description => 'Référentiel de l\'heure mondiale',
      PrintConv => {
        'Home' => 'Domicile',
        'Hometown' => 'Ville d\'origine',
      },
    },
   'Writer-Editor' => 'Auteur de la légende / description',
   'X3FillLight' => 'Lumière de remplissage X3F',
   'XClipPathUnits' => 'Unités de la valeur horizontale du chemin d\'accès de l\'extrait',
   'XMP' => 'Métadonnées XMP',
   'XMPToolkit' => 'Boîte à outils XMP',
   'XPAuthor' => 'Auteur',
   'XPComment' => 'Commentaire',
   'XPKeywords' => 'Mots-clés',
   'XPSubject' => 'Sujet',
   'XPTitle' => 'Titre',
   'XPosition' => 'Position horizontale',
   'XResolution' => 'Résolution horizontale de l\'image',
   'XTransLayout' => 'Mise en page X-Trans',
   'YCbCrCoefficients' => 'Coefficients de la matrice de transformation de l\'espace colorimétrique',
   'YCbCrPositioning' => {
      Description => 'Positionnement YCbCr',
      PrintConv => {
        'Centered' => 'Centré',
        'Co-sited' => 'Côte à côte',
      },
    },
   'YCbCrSubSampling' => 'Sous-échantillonnage YCbCr',
   'YClipPathUnits' => 'Unités de la valeur verticale du chemin d\'accès de l\'extrait',
   'YPosition' => 'Position verticale',
   'YResolution' => 'Résolution verticale de l\'image',
   'Year' => 'Année',
   'ZebraPatternToneRange' => {
      Description => 'Gamme de tons du motif hachuré',
      PrintConv => {
        'Highlights' => 'Lumineuse',
        'Midtones' => 'Moyenne',
        'Off' => 'Désactivée',
      },
    },
   'ZoneMatching' => {
      Description => 'Ajustage de la zone',
      PrintConv => {
        'High Key' => 'Tons clairs',
        'ISO Setting Used' => 'Désactivée',
        'Low Key' => 'Tons sombres',
      },
    },
   'ZoneMatchingOn' => {
      PrintConv => {
        'Off' => 'Désactivé',
        'On' => 'Activé',
      },
    },
   'ZoomCenter' => 'Centre du zoom',
   'ZoomFactor' => 'Facetur de zoom ',
   'ZoomPosition' => 'Position zoom ',
   'ZoomSourceWidth' => 'Largeur de la source de zoom',
   'ZoomStepCount' => 'Nombre de pas du zoom',
   'ZoomTargetWidth' => 'Largeur de la cible du zoom',
);

1;  # end

__END__

=head1 NAME

Image::ExifTool::Lang::fr.pm - ExifTool French language translations

=head1 DESCRIPTION

This file is used by Image::ExifTool to generate localized tag descriptions
and values.

=head1 AUTHOR

Copyright 2003-2025, Phil Harvey (philharvey66 at gmail.com)

This library is free software; you can redistribute it and/or modify it
under the same terms as Perl itself.

=head1 ACKNOWLEDGEMENTS

Thanks to Jens Duttke, Bernard Guillotin, Jean Glasser, Jean Piquemal, Harry
Nizard, Alphonse Philippe and Philippe Bonnaure (GraphicConverter) for
providing this translation.

=head1 SEE ALSO

L<Image::ExifTool(3pm)|Image::ExifTool>,
L<Image::ExifTool::TagInfoXML(3pm)|Image::ExifTool::TagInfoXML>

=cut
