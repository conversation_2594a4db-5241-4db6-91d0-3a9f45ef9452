#------------------------------------------------------------------------------
# File:         DOSCyrillic.pm
#
# Description:  cp866 to Unicode
#
# Revisions:    2020/03/23 - P<PERSON> created
#
# References:   1) https://www.unicode.org/Public/MAPPINGS/VENDORS/MICSFT/PC/CP866.TXT
#
# Notes:        The table omits 1-byte characters with the same values as Unicode
#------------------------------------------------------------------------------
use strict;

%Image::ExifTool::Charset::DOSCyrillic = (
  0x80 => 0x0410, 0x81 => 0x0411, 0x82 => 0x0412, 0x83 => 0x0413,
  0x84 => 0x0414, 0x85 => 0x0415, 0x86 => 0x0416, 0x87 => 0x0417,
  0x88 => 0x0418, 0x89 => 0x0419, 0x8a => 0x041a, 0x8b => 0x041b,
  0x8c => 0x041c, 0x8d => 0x041d, 0x8e => 0x041e, 0x8f => 0x041f,
  0x90 => 0x0420, 0x91 => 0x0421, 0x92 => 0x0422, 0x93 => 0x0423,
  0x94 => 0x0424, 0x95 => 0x0425, 0x96 => 0x0426, 0x97 => 0x0427,
  0x98 => 0x0428, 0x99 => 0x0429, 0x9a => 0x042a, 0x9b => 0x042b,
  0x9c => 0x042c, 0x9d => 0x042d, 0x9e => 0x042e, 0x9f => 0x042f,
  0xa0 => 0x0430, 0xa1 => 0x0431, 0xa2 => 0x0432, 0xa3 => 0x0433,
  0xa4 => 0x0434, 0xa5 => 0x0435, 0xa6 => 0x0436, 0xa7 => 0x0437,
  0xa8 => 0x0438, 0xa9 => 0x0439, 0xaa => 0x043a, 0xab => 0x043b,
  0xac => 0x043c, 0xad => 0x043d, 0xae => 0x043e, 0xaf => 0x043f,
  0xb0 => 0x2591, 0xb1 => 0x2592, 0xb2 => 0x2593, 0xb3 => 0x2502,
  0xb4 => 0x2524, 0xb5 => 0x2561, 0xb6 => 0x2562, 0xb7 => 0x2556,
  0xb8 => 0x2555, 0xb9 => 0x2563, 0xba => 0x2551, 0xbb => 0x2557,
  0xbc => 0x255d, 0xbd => 0x255c, 0xbe => 0x255b, 0xbf => 0x2510,
  0xc0 => 0x2514, 0xc1 => 0x2534, 0xc2 => 0x252c, 0xc3 => 0x251c,
  0xc4 => 0x2500, 0xc5 => 0x253c, 0xc6 => 0x255e, 0xc7 => 0x255f,
  0xc8 => 0x255a, 0xc9 => 0x2554, 0xca => 0x2569, 0xcb => 0x2566,
  0xcc => 0x2560, 0xcd => 0x2550, 0xce => 0x256c, 0xcf => 0x2567,
  0xd0 => 0x2568, 0xd1 => 0x2564, 0xd2 => 0x2565, 0xd3 => 0x2559,
  0xd4 => 0x2558, 0xd5 => 0x2552, 0xd6 => 0x2553, 0xd7 => 0x256b,
  0xd8 => 0x256a, 0xd9 => 0x2518, 0xda => 0x250c, 0xdb => 0x2588,
  0xdc => 0x2584, 0xdd => 0x258c, 0xde => 0x2590, 0xdf => 0x2580,
  0xe0 => 0x0440, 0xe1 => 0x0441, 0xe2 => 0x0442, 0xe3 => 0x0443,
  0xe4 => 0x0444, 0xe5 => 0x0445, 0xe6 => 0x0446, 0xe7 => 0x0447,
  0xe8 => 0x0448, 0xe9 => 0x0449, 0xea => 0x044a, 0xeb => 0x044b,
  0xec => 0x044c, 0xed => 0x044d, 0xee => 0x044e, 0xef => 0x044f,
  0xf0 => 0x0401, 0xf1 => 0x0451, 0xf2 => 0x0404, 0xf3 => 0x0454,
  0xf4 => 0x0407, 0xf5 => 0x0457, 0xf6 => 0x040e, 0xf7 => 0x045e,
  0xf8 => 0x00b0, 0xf9 => 0x2219, 0xfa => 0x00b7, 0xfb => 0x221a,
  0xfc => 0x2116, 0xfd => 0x00a4, 0xfe => 0x25a0, 0xff => 0x00a0,
);

1; # end
