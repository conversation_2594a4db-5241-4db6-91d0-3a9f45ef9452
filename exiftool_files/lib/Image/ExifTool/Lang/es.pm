#------------------------------------------------------------------------------
# File:         es.pm
#
# Description:  ExifTool Spanish language translations
#
# Notes:        This file generated automatically by Image::ExifTool::TagInfoXML
#------------------------------------------------------------------------------

package Image::ExifTool::Lang::es;

use strict;
use vars qw($VERSION);

$VERSION = '1.16';

%Image::ExifTool::Lang::es::Translate = (
   'AEAperture' => 'Aperture AE',
   'AELock' => 'Bloqueo AE',
   'AELockButton' => {
      Description => 'Botón Bloqueo AE',
      PrintConv => {
        'None' => 'Ninguno',
      },
    },
   'AELockButtonPlusDials' => {
      PrintConv => {
        'None' => 'Ninguno',
      },
    },
   'AEMaxAperture2' => 'Apertura máxima AE 2',
   'AEMinAperture' => 'Apertura mínima AE',
   'AEProgramMode' => {
      PrintConv => {
        'Landscape' => 'Paisaje',
        'Portrait' => 'Retrato',
        'Standard' => 'Estándar',
      },
    },
   'AESetting' => {
      PrintConv => {
        'AE Lock' => 'Bloqueo AE',
        'Exposure Compensation' => 'Compensación Exposición',
      },
    },
   'AFAperture' => 'Apertura AF',
   'AFAreaHeight' => 'AF Alto Área',
   'AFAreaHeights' => 'AF Alto Área',
   'AFAreaIllumination' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'AFAreaMode' => {
      Description => 'AF Modo Área',
      PrintConv => {
        'Face Detect AF' => 'Detección Caras AF',
        'Multi-point AF or AI AF' => 'Multipunto AF o AI AF',
        'Off (Manual Focus)' => 'Desactivado (Enfoque Manual)',
        'Single-point AF' => 'Punto único AF)',
        'Zone AF' => 'Zona AF',
      },
    },
   'AFAreaWidth' => 'AF Ancho Área',
   'AFAreaWidths' => 'AF Ancho Área',
   'AFAssist' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'AFImageHeight' => 'AF Alto Imágen',
   'AFImageWidth' => 'AF Ancho Imágen',
   'AFMode' => 'Modo AF',
   'AFPoint' => {
      Description => 'Punto AF',
      PrintConv => {
        'Center' => 'Centro',
        'Face Detect' => 'Detección Caras',
        'Left' => 'Izquierda',
        'None' => 'Ninguno',
        'None (MF)' => 'Ninguno (MF)',
        'Right' => 'Derecha',
      },
    },
   'AFPointActivationArea' => {
      Description => 'Area de Activación Punto AF',
      PrintConv => {
        'Standard' => 'Estándar',
      },
    },
   'AFPointAreaExpansion' => {
      Description => 'Area Expansion Punto AF',
      PrintConv => {
        'Disable' => 'Desactivado',
      },
    },
   'AFPointAutoSelection' => 'Autoselección Punto AF',
   'AFPointBrightness' => {
      Description => 'Brillo Punto AF',
      PrintConv => {
        'Brighter' => 'Brillante',
        'High' => 'Alto',
        'Low' => 'Bajo',
      },
    },
   'AFPointDisplayDuringFocus' => {
      Description => 'Mostrar Punto AF durante el enfoque',
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'AFPointIllumination' => {
      Description => 'Iluminación de Punto AF',
      PrintConv => {
        'Brighter' => 'Brillante',
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'AFPointMode' => 'Modo Punto AF',
   'AFPointPosition' => 'Posición Punto AF',
   'AFPointRegistration' => 'Registro de Puntos AF',
   'AFPointSelected' => {
      Description => 'Punto AF Seleccionado',
      PrintConv => {
        'None' => 'Ninguno',
      },
    },
   'AFPointSelected2' => 'Punto AF Seleccionado 2 ',
   'AFPointSelection' => 'Selección de Punto AF',
   'AFPointSelectionMethod' => 'Método Selección Punto AF',
   'AFPoints' => 'Punto AF',
   'AFPointsInFocus' => {
      Description => 'Puntos AF en foco',
      PrintConv => {
        'All' => 'Todo',
        'None' => 'Ninguno',
      },
    },
   'AFPointsInFocus1D' => 'Puntos AF en foco',
   'AFPointsInFocus5D' => {
      Description => 'Puntos AF en foco 5D',
      PrintConv => {
        'Bottom' => 'Abajo',
        'Center' => 'Centro',
        'Left' => 'Izquierda',
        'Lower-left' => 'Inferior izquierda',
        'Lower-right' => 'Inferior derecha',
        'Right' => 'Derecha',
        'Top' => 'Arriba',
        'Upper-left' => 'Superior izquierda',
        'Upper-right' => 'Superior derecha',
      },
    },
   'AFPointsSelected' => 'Puntos AF seleccionados',
   'AFPointsUnknown1' => {
      PrintConv => {
        'All' => 'Todo',
      },
    },
   'AFPointsUsed' => 'Puntos AF utilizados',
   'AIServoTrackingSensitivity' => {
      PrintConv => {
        'Fast' => 'Rápido',
        'Standard' => 'Estándar',
      },
    },
   'APEVersion' => 'Versión APE',
   'ARMIdentifier' => 'Identificador ARM',
   'ARMVersion' => 'Versión ARM',
   'AToB0' => 'A a B0',
   'AToB1' => 'A a B1',
   'AToB2' => 'A a B2',
   'ActionAdvised' => {
      Description => 'Acción Aconsejada',
      PrintConv => {
        'Object Append' => 'Añadir Objeto',
        'Object Kill' => 'Destruir Objecto',
        'Object Reference' => 'Referencia Objecto',
        'Object Replace' => 'Reemplazar Objecto',
        'Ojbect Append' => 'Añadir Objeto',
      },
    },
   'ActiveArea' => 'Área Activa',
   'ActiveD-Lighting' => {
      PrintConv => {
        'High' => 'Alto',
        'Low' => 'Bajo',
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'ActiveD-LightingMode' => {
      PrintConv => {
        'High' => 'Alto',
        'Off' => 'Desactivado',
      },
    },
   'AddAspectRatioInfo' => {
      PrintConv => {
        'Off' => 'Desactivado',
      },
    },
   'AddOriginalDecisionData' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'AdjustmentMode' => 'Modo Ajuste',
   'AdvancedRaw' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'AdvancedSceneMode' => {
      PrintConv => {
        'Monochrome' => 'Monocromo',
        'Soft' => 'Suave',
      },
    },
   'Album' => 'Álbum',
   'AlphaByteCount' => 'Número Byte Alfa',
   'AlphaDataDiscard' => {
      Description => 'Datos Alfa Descartados',
      PrintConv => {
        'Flexbits Discarded' => 'FlexBits Descartado',
        'Full Resolution' => 'Resolución Total',
        'HighPass Frequency Data Discarded' => 'Datos Frecuencia High-Pass Descartados',
        'Highpass and LowPass Frequency Data Discarded' => 'Dato Frecuencia High-Pass y Low-Pass Descartados',
      },
    },
   'AlphaOffset' => 'Offset Alfa',
   'AmbienceSelection' => {
      PrintConv => {
        'Brighter' => 'Brillante',
        'Cool' => 'Frío',
        'Darker' => 'Oscuro',
        'Intense' => 'Intenso',
        'Monochrome' => 'Monocromo',
        'Soft' => 'Suave',
        'Standard' => 'Estándar',
        'Vivid' => 'Vívido',
        'Warm' => 'Cálido',
      },
    },
   'AnalogBalance' => 'Balance Analógico',
   'Annotation' => 'Anotación',
   'Annotations' => 'Anotaciones',
   'Anti-Blur' => {
      PrintConv => {
        'Off' => 'Desactivado',
      },
    },
   'AntiAliasStrength' => 'Potencia Relativa del Filtro Antialiasing',
   'Aperture' => 'Apertura',
   'ApertureRange' => 'Rango Apertura',
   'ApertureSetting' => 'Ajustes Apertura',
   'ApertureValue' => 'Apertura',
   'ApplicationRecordVersion' => 'Versión Registro Aplicación',
   'ArtMode' => {
      PrintConv => {
        'Monochrome' => 'Monocromo',
        'Panorama' => 'Panoramica',
      },
    },
   'Artist' => 'Autor',
   'AsShotICCProfile' => 'Perfil ICC Captura',
   'AsShotNeutral' => 'Captura Neutral',
   'AsShotPreProfileMatrix' => 'Matriz Pre Perfil Captura',
   'AsShotProfileName' => 'Nombre Perfil Captura',
   'AsShotWhiteXY' => 'Captura Blanco X-Y',
   'Audio' => {
      PrintConv => {
        'Yes' => 'Si',
      },
    },
   'AudioChannelType' => {
      PrintConv => {
        'Other' => 'Otro',
      },
    },
   'AudioChannels' => 'Canales Audio',
   'AudioCodecID' => {
      PrintConv => {
        'Unknown -' => 'Desconocido -',
      },
    },
   'AudioDuration' => 'Duración Audio',
   'AudioOutcue' => 'Cola Audio',
   'AudioSampleType' => {
      PrintConv => {
        'Other' => 'Otro',
      },
    },
   'AudioSamplingRate' => 'Ratio Muestreo Audio',
   'AudioSamplingResolution' => 'Resolución Muestreo Audio',
   'AudioType' => {
      Description => 'Tipo Audio',
      PrintConv => {
        'Mono Actuality' => 'Actualidad (audio mono (1 canal))',
        'Mono Music' => 'Música transmitida por si misma (audio mono (1 canal))',
        'Mono Question and Answer Session' => 'Sesión pregunta y respuesta (audio mono (1 canal))',
        'Mono Raw Sound' => 'Sonido bruto (audio mono (1 canal))',
        'Mono Response to a Question' => 'Respuesta a una pregunta (audio mono (1 canal))',
        'Mono Scener' => 'Escena (audio mono (1 canal))',
        'Mono Voicer' => 'Voz (audio mono (1 canal))',
        'Mono Wrap' => 'Envolvente (audio mono (1 canal))',
        'Stereo Actuality' => 'Actualidad (audio estéreo (2 canales))',
        'Stereo Music' => 'Música transmitida por si misma (audio estéreo (2 canales))',
        'Stereo Question and Answer Session' => 'Sesión pregunta y respuesta (audio estéreo (2 canales))',
        'Stereo Raw Sound' => 'Sonido bruto (audio estéreo (2 canales))',
        'Stereo Response to a Question' => 'Respuesta a una pregunta (audio estéreo (2 canales))',
        'Stereo Scener' => 'Escena (audio estéreo (2 canales))',
        'Stereo Voicer' => 'Voz (audio estéreo (2 canales))',
        'Stereo Wrap' => 'Envolvente (audio estéreo (2 canales))',
        'Text Only' => 'Solo texto (sin dato de objeto)',
      },
    },
   'Author' => 'Autor',
   'AuthorsPosition' => 'Posición del Autor',
   'AutoAperture' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'AutoBracket' => 'Auto-horquillado',
   'AutoExposureBracketing' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'AutoFP' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'AutoLightingOptimizer' => {
      PrintConv => {
        'Low' => 'Bajo',
        'Off' => 'Desactivado',
        'Standard' => 'Estándar',
        'Strong' => 'Fuerte',
      },
    },
   'AutoLightingOptimizerOn' => {
      PrintConv => {
        'Yes' => 'Si',
      },
    },
   'AutoRedEye' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'AutoRotate' => {
      PrintConv => {
        'None' => 'Ninguno',
        'Rotate 180' => 'Girado 180°',
        'Rotate 270 CW' => 'Girado 270° sentido reloj',
        'Rotate 90 CW' => 'Girado 90° sentido reloj',
      },
    },
   'AverageLevel' => 'Nivel Medio',
   'BToA0' => 'B a A0',
   'BToA1' => 'B a A1',
   'BToA2' => 'B a A2',
   'BackgroundColorIndicator' => 'Indicador Color Fondo',
   'BackgroundColorValue' => 'Valor Color Fondo',
   'BackgroundTiling' => {
      PrintConv => {
        'Yes' => 'Si',
      },
    },
   'BadFaxLines' => 'Líneas Fax Malas',
   'BannerImageType' => {
      PrintConv => {
        'None' => 'Ninguno',
      },
    },
   'BaselineExposure' => 'Exposición Base',
   'BaselineNoise' => 'Ruido Base',
   'BaselineSharpness' => 'Nitidez Base',
   'BatteryLevel' => 'Nivel Batería',
   'BatteryState' => {
      PrintConv => {
        'Low' => 'Bajo',
      },
    },
   'BayerGreenSplit' => 'Mosaico Bayer Verde',
   'Beep' => {
      PrintConv => {
        'High' => 'Alto',
        'Low' => 'Bajo',
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'BeepPitch' => {
      PrintConv => {
        'High' => 'Alto',
        'Low' => 'Bajo',
      },
    },
   'BestQualityScale' => 'Escala Mayor Calidad',
   'BestShotMode' => {
      PrintConv => {
        'Beach' => 'Playa',
        'Fireworks' => 'Fuegos Artificiales',
        'Food' => 'Comida',
        'Monochrome' => 'Monocromo',
        'Portrait' => 'Retrato',
        'Snow' => 'Nieve',
        'Underwater' => 'Subacuatica',
      },
    },
   'BitsPerComponent' => 'Bits Por Componente',
   'BitsPerExtendedRunLength' => 'Bits Por "Run Length" Extendido',
   'BitsPerRunLength' => 'Bits Por "Run Length"',
   'BitsPerSample' => 'Número de Bits Por Muestra',
   'BlackLevel' => 'Nivel Negro',
   'BlackLevelDeltaH' => 'Nivel Negro Delta H',
   'BlackLevelDeltaV' => 'Nivel Negro Delta V',
   'BlackLevelRepeatDim' => 'Dimensión Repetición Nivel Negro',
   'BleachBypassToning' => {
      PrintConv => {
        'Green' => 'Verde',
        'Orange' => 'Naranja',
        'Red' => 'Rojo',
        'Yellow' => 'Amarillo',
      },
    },
   'BlocksPerFrame' => 'Bloques por Imagen',
   'BlueBalance' => 'Balance de azules',
   'BlueMatrixColumn' => 'Columna Matriz Azul',
   'BlueTRC' => 'Curva Reproducción Tono Azul',
   'BlurControl' => {
      PrintConv => {
        'High' => 'Alto',
        'Low' => 'Bajo',
      },
    },
   'BlurWarning' => {
      PrintConv => {
        'None' => 'Ninguno',
      },
    },
   'BodyFirmwareVersion' => 'Versión Firmware del cuerpo de la cámara',
   'BracketMode' => {
      PrintConv => {
        'Off' => 'Desactivado',
      },
    },
   'Brightness' => 'Brillo',
   'BrightnessValue' => 'Luminosidad',
   'By-line' => 'Creador',
   'By-lineTitle' => 'Puesto del Creador',
   'CFALayout' => {
      Description => 'Distribución CFA',
      PrintConv => {
        'Even columns offset down 1/2 row' => 'Distribución escalonada A: columnas pares son movidas hacia abajo 1/2 fila',
        'Even columns offset up 1/2 row' => 'Distribución escalonada B: columnas pares son movidas hacia arriba 1/2 fila',
        'Even rows offset left 1/2 column' => 'Distribución escalonada D: filas pares son movidas a la izquierda 1/2 columna',
        'Even rows offset right 1/2 column' => 'Distribución escalonada C: filas pares son movidas a la derecha 1/2 columna',
        'Rectangular' => 'Distribución Rectangular (o cuadrada)',
      },
    },
   'CFAPattern' => 'Patrón CFA',
   'CFAPattern2' => 'Patrón CFA 2',
   'CFAPlaneColor' => 'Color Plano CFA',
   'CFARepeatPatternDim' => 'Dimensión Patrón Repetición CFA',
   'CMMFlags' => 'Banderas CMM',
   'CMYKEquivalent' => 'CMYK Equivalente',
   'CPUType' => {
      PrintConv => {
        'None' => 'Ninguno',
      },
    },
   'CalibrationDateTime' => 'Fecha y Hora Calibración',
   'CalibrationIlluminant1' => {
      Description => 'Calibración Iluminación 1',
      PrintConv => {
        'Cloudy' => 'Tiempo Nublado',
        'Cool White Fluorescent' => 'Fluorescente blanco cálido (W 3800 - 4500K)',
        'Day White Fluorescent' => 'Fluorescente blanco día (N 4600 - 5500K)',
        'Daylight' => 'Luz del día',
        'Daylight Fluorescent' => 'Fluorescente luz de día (D 5700 - 7100K)',
        'Fine Weather' => 'Buen tiempo',
        'Fluorescent' => 'Fluorescente',
        'ISO Studio Tungsten' => 'Tungsteno estudio ISO',
        'Other' => 'Otras Fuentes Luz',
        'Shade' => 'Sombrío',
        'Standard Light A' => 'Luz Estándar A',
        'Standard Light B' => 'Luz Estándar B',
        'Standard Light C' => 'Luz Estándar C',
        'Tungsten (Incandescent)' => 'Tungsteno (luz incandescente)',
        'Unknown' => 'Desconocido',
        'Warm White Fluorescent' => 'Fluorescente blanco cálido (L 2600 - 3250K)',
        'White Fluorescent' => 'Fluorescente blanco (WW 3250 - 3800K)',
      },
    },
   'CalibrationIlluminant2' => {
      Description => 'Calibración Iluminación 2',
      PrintConv => {
        'Cloudy' => 'Tiempo Nublado',
        'Cool White Fluorescent' => 'Fluorescente blanco cálido (W 3800 - 4500K)',
        'Day White Fluorescent' => 'Fluorescente blanco día (N 4600 - 5500K)',
        'Daylight' => 'Luz del día',
        'Daylight Fluorescent' => 'Fluorescente luz de día (D 5700 - 7100K)',
        'Fine Weather' => 'Buen tiempo',
        'Fluorescent' => 'Fluorescente',
        'ISO Studio Tungsten' => 'Tungsteno estudio ISO',
        'Other' => 'Otras Fuentes Luz',
        'Shade' => 'Sombrío',
        'Standard Light A' => 'Luz Estándar A',
        'Standard Light B' => 'Luz Estándar B',
        'Standard Light C' => 'Luz Estándar C',
        'Tungsten (Incandescent)' => 'Tungsteno (luz incandescente)',
        'Unknown' => 'Desconocido',
        'Warm White Fluorescent' => 'Fluorescente blanco cálido (L 2600 - 3250K)',
        'White Fluorescent' => 'Fluorescente blanco (WW 3250 - 3800K)',
      },
    },
   'CameraCalibration1' => 'Calibración Cámara 1',
   'CameraCalibration2' => 'Calibración Cámara 2',
   'CameraCalibrationSig' => 'Firma Calibración Cámara',
   'CameraISO' => 'Camara-ISO',
   'CameraOrientation' => {
      Description => 'Orientación Cámara',
      PrintConv => {
        'Rotate 270 CW' => 'Girado 270° sentido reloj',
        'Rotate 90 CW' => 'Girado 90° sentido reloj',
      },
    },
   'CameraSerialNumber' => 'Número Serie Cámara',
   'CameraTemperature' => 'Temperatura Cámara',
   'CameraType' => 'Tipo Cámara',
   'CameraType2' => 'Tipo Cámara',
   'CanonFileLength' => 'Tamaño Archivo',
   'CanonFlashMode' => {
      PrintConv => {
        'Auto' => 'Automático',
        'External flash' => 'Flash Externo',
        'Off' => 'Desactivado',
        'On' => 'Activado',
        'Red-eye reduction' => 'Réducción ojos rojos',
        'Red-eye reduction (Auto)' => 'Réducción ojos rojos (Automático)',
        'Red-eye reduction (On)' => 'Réducción ojos rojos (Activado)',
      },
    },
   'CanonImageSize' => {
      PrintConv => {
        '1280x720 Movie' => 'Película 1280x720',
        '1920x1080 Movie' => 'Película 1920x1080',
        '640x480 Movie' => 'Película 640x480',
        'Large' => 'Ancho',
        'Medium' => 'Medio',
        'Medium 1' => 'Medio 1',
        'Medium 2' => 'Medio 2',
        'Medium 3' => 'Medio 3',
        'Small' => 'Pequeño',
        'Small 1' => 'Pequeño 1',
        'Small 2' => 'Pequeño 2',
        'Small 3' => 'Pequeño 3',
        'Small Movie' => 'Película Pequeña',
      },
    },
   'Caption-Abstract' => 'Título/Descripción',
   'CaptionWriter' => 'Autor del Pie de Foto',
   'Categories' => 'Categorías',
   'Category' => 'Categoría',
   'CellLength' => 'Alto Celda',
   'CellWidth' => 'Ancho Celda',
   'Certificate' => 'Certificado',
   'Channels' => 'Canales',
   'CharTarget' => 'Objetivo Caracter',
   'CharacterSet' => 'Conjunto de Caracteres',
   'ChromaBlurRadius' => 'Radio Mezcla Croma',
   'ChromaticAdaptation' => 'Adaptación Cromática',
   'Chromaticity' => 'Cromaticidad',
   'ChrominanceNR_TIFF_JPEG' => {
      PrintConv => {
        'High' => 'Alto',
        'Low' => 'Bajo',
        'Off' => 'Desactivado',
      },
    },
   'ChrominanceNoiseReduction' => {
      PrintConv => {
        'High' => 'Alto',
        'Low' => 'Bajo',
        'Off' => 'Desactivado',
      },
    },
   'City' => 'Ciudad',
   'ClassifyState' => 'Clasificar Estado',
   'CleanFaxData' => 'Datos Fax Claro',
   'ClipPath' => 'Camino Fragmento',
   'CodedCharacterSet' => 'Juego Caracteres Codificado',
   'ColorAberrationControl' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'ColorAdjustment' => 'Ajuste Color',
   'ColorAdjustmentMode' => {
      Description => 'Modo Ajuste Color',
      PrintConv => {
        'Off' => 'Apagado',
        'On' => 'Encendido',
      },
    },
   'ColorBalanceAdj' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'ColorBooster' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'ColorCalibrationMatrix' => 'Tabla Matriz Calibración Color',
   'ColorCharacterization' => 'Caracterización Color',
   'ColorComponents' => 'Componentes de Color',
   'ColorEffect' => {
      PrintConv => {
        'Cool' => 'Frío',
        'Warm' => 'Cálido',
      },
    },
   'ColorFilter' => {
      Description => 'Filtro de Color',
      PrintConv => {
        'Green' => 'Verde',
        'Red' => 'Rojo',
        'Yellow' => 'Amarillo',
      },
    },
   'ColorMap' => 'Mapa Color',
   'ColorMatrix' => 'Matriz de Color',
   'ColorMatrix1' => 'Matriz Color 1',
   'ColorMatrix2' => 'Matriz Color 2',
   'ColorMatrixA' => 'Matriz de Color A',
   'ColorMatrixAdobeRGB' => 'Matriz de Color Adobe RGB',
   'ColorMatrixB' => 'Matriz de Color B',
   'ColorMatrixNumber' => 'Número de Matriz de Color',
   'ColorMatrixSRGB' => 'Matriz de Color SRGB',
   'ColorMode' => {
      Description => 'Modo de Color',
      PrintConv => {
        'Autumn Leaves' => 'Hojas de otoño',
        'B&W' => 'ByN',
        'Clear' => 'Claro',
        'Deep' => 'Profundo',
        'Evening' => 'Tarde',
        'Landscape' => 'Paisaje',
        'Light' => 'Luz',
        'Neutral' => 'Neutro',
        'Night View' => 'Vista nocturna',
        'Night View/Portrait' => 'Retrato noct.',
        'Portrait' => 'Retrato',
        'Standard' => 'Estándar',
        'Sunset' => 'Puesta sol',
        'Vivid' => 'Vívido',
      },
    },
   'ColorMoireReduction' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'ColorMoireReductionMode' => {
      PrintConv => {
        'High' => 'Alto',
        'Low' => 'Bajo',
        'Off' => 'Desactivado',
      },
    },
   'ColorPalette' => 'Paleta Color',
   'ColorRepresentation' => {
      Description => 'Representación Color',
      PrintConv => {
        '3 Components, Frame Sequential in Multiple Objects' => 'Tres componentes, Marco secuencial en múltiples objectos',
        '3 Components, Frame Sequential in One Object' => 'Tres componentes, Marco secuencial en un único objeto',
        '3 Components, Line Sequential' => 'Tres componentes, Línea secuencial',
        '3 Components, Pixel Sequential' => 'Tres componentes, Pixel secuencial',
        '3 Components, Single Frame' => 'Tres componentes, Marco simple',
        '3 Components, Special Interleaving' => 'Tres componentes, Entrelazado especial',
        '4 Components, Frame Sequential in Multiple Objects' => 'Cuatro componentes, Marco secuencial en múltiples objectos',
        '4 Components, Frame Sequential in One Object' => 'Cuatro componentes, Marco secuencial en un único objeto',
        '4 Components, Line Sequential' => 'Cuatro componentes, Línea secuencial',
        '4 Components, Pixel Sequential' => 'Cuatro componentes, Pixel secuencial',
        '4 Components, Single Frame' => 'Cuatro componentes, Marco simple',
        '4 Components, Special Interleaving' => 'Cuatro componentes, Entrelazado especial',
        'Monochrome, Single Frame' => 'Monocromo, Marco simple',
        'No Image, Single Frame' => 'Sin imagen, Marco simple',
      },
    },
   'ColorResponseUnit' => 'Unidad Respuesta Color',
   'ColorSequence' => 'Representación de Color',
   'ColorSpace' => {
      Description => 'Espacio Color',
      PrintConv => {
        'ICC Profile' => 'Perfil ICC',
        'Monochrome' => 'Monocromo',
        'Uncalibrated' => 'Sin calibrar',
        'Wide Gamut RGB' => 'Gamut RVB Grande',
      },
    },
   'ColorSpaceData' => 'Espacio Color Datos',
   'ColorTable' => 'Tabla Color',
   'ColorTempAuto' => 'Temperatura Color Automática',
   'ColorTempCloudy' => 'Temperatura Color Nublado',
   'ColorTempCustom' => 'Temperatura Color Personalizada',
   'ColorTempCustom1' => 'Temperatura Color Personalizada 1',
   'ColorTempCustom2' => 'Temperatura Color Personalizada 2',
   'ColorTempDaylight' => 'Temperatura Color Luz de Día',
   'ColorTempFlash' => 'Temperatura Color Flash',
   'ColorTempFluorescent' => 'Temperatura Color Fluorescente',
   'ColorTempKelvin' => 'Temperatura Color Kelvin',
   'ColorTempMeasured' => 'Temperatura Color Medida',
   'ColorTempShade' => 'Temperatura Color Sombrío',
   'ColorTempTungsten' => 'Temperatura Color Tungsteno',
   'ColorTempUnknown' => 'Temperatura de Color Desconocida',
   'ColorTempUnknown10' => 'Temperatura de Color Desconocida 10',
   'ColorTempUnknown11' => 'Temperatura de Color Desconocida 11',
   'ColorTempUnknown12' => 'Temperatura de Color Desconocida 12',
   'ColorTempUnknown13' => 'Temperatura de Color Desconocida 13',
   'ColorTempUnknown14' => 'Temperatura de Color Desconocida 14',
   'ColorTempUnknown15' => 'Temperatura de Color Desconocida 15',
   'ColorTempUnknown16' => 'Temperatura de Color Desconocida 16',
   'ColorTempUnknown17' => 'Temperatura de Color Desconocida 17',
   'ColorTempUnknown18' => 'Temperatura de Color Desconocida 18',
   'ColorTempUnknown19' => 'Temperatura de Color Desconocida 19',
   'ColorTempUnknown2' => 'Temperatura de Color Desconocida 2',
   'ColorTempUnknown20' => 'Temperatura de Color Desconocida 20',
   'ColorTempUnknown3' => 'Temperatura de Color Desconocida 3',
   'ColorTempUnknown4' => 'Temperatura de Color Desconocida 4',
   'ColorTempUnknown5' => 'Temperatura de Color Desconocida 5',
   'ColorTempUnknown6' => 'Temperatura de Color Desconocida 6',
   'ColorTempUnknown7' => 'Temperatura de Color Desconocida 7',
   'ColorTempUnknown8' => 'Temperatura de Color Desconocida 8',
   'ColorTempUnknown9' => 'Temperatura de Color Desconocida 9',
   'ColorTemperature' => 'Temperatura de Color',
   'ColorTone' => {
      Description => 'Tono de Color',
      PrintConv => {
        'Normal' => 'Estándar',
      },
    },
   'ColorantOrder' => 'Orden Colorante',
   'ColorantTable' => 'Tabla Colorante',
   'ColorimetricReference' => 'Referencia Colorimétrica',
   'CommandDialsChangeMainSub' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'CommandDialsMenuAndPlayback' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'CommandDialsReverseRotation' => {
      PrintConv => {
        'Yes' => 'Si',
      },
    },
   'CommanderGroupAMode' => {
      PrintConv => {
        'Off' => 'Desactivado',
      },
    },
   'CommanderGroupBMode' => {
      PrintConv => {
        'Off' => 'Desactivado',
      },
    },
   'CommanderInternalFlash' => {
      PrintConv => {
        'Off' => 'Desactivado',
      },
    },
   'Comment' => 'Comentario',
   'Compatibility' => 'Compatibilidad',
   'Compilation' => {
      PrintConv => {
        'Yes' => 'Si',
      },
    },
   'ComponentsConfiguration' => 'Configuración de Componentes',
   'Composer' => 'Compositor',
   'CompressedBitsPerPixel' => 'Modo Compresión Imagen',
   'CompressedSize' => 'Tamaño Comprimido',
   'Compression' => {
      Description => 'Compresión',
      PrintConv => {
        'JPEG' => 'Compresión JPEG',
        'JPEG (old-style)' => 'JPEG (estilo antiguo)',
        'Kodak DCR Compressed' => 'Compresión Kodak DCR',
        'Kodak KDC Compressed' => 'Compresión Kodak KDC',
        'Next' => 'Codificación NeXT 2-bit',
        'Nikon NEF Compressed' => 'Compresión Nikon NEF',
        'None' => 'Ninguno',
        'Pentax PEF Compressed' => 'Compresión Pentax PEF',
        'SGILog' => 'Codificación Log Luminancia SGI 32-bit',
        'SGILog24' => 'Codificación Log Luminancia SGI 24-bit',
        'Sony ARW Compressed' => 'Compresión Sony ARW',
        'Thunderscan' => 'Codificación ThunderScan 4-bit',
        'Uncompressed' => 'Sin comprimir',
      },
    },
   'CompressionFactor' => 'Factor de compresión',
   'CompressionLevel' => 'Nivel Compresión',
   'CompressionType' => {
      Description => 'Tipo Compresión',
      PrintConv => {
        'None' => 'Ninguno',
      },
    },
   'CompressorName' => 'Nombre Compresor',
   'Conductor' => 'Director',
   'Conductors' => 'Directores',
   'ConnectionSpaceIlluminant' => 'Iluminación Espacio Conexión',
   'ConsecutiveBadFaxLines' => 'Líneas Fax Malas Consecutivas',
   'Contact' => 'Contacto',
   'ContentLocationCode' => 'Código Localización Contenido',
   'ContentLocationName' => 'Nombre Localización Contenido',
   'ContinuousBracketing' => {
      PrintConv => {
        'High' => 'Alto',
        'Low' => 'Bajo',
      },
    },
   'ContinuousDrive' => {
      PrintConv => {
        'Continuous' => 'Continuo',
        'Continuous, High' => 'Continuo, Alto',
        'Continuous, Low' => 'Continuo, Bajo',
        'Continuous, Speed Priority' => 'Continuo, Prioridad Velocidad',
        'Movie' => 'Película',
        'Single' => 'Simple',
      },
    },
   'Contrast' => {
      Description => 'Contraste',
      PrintConv => {
        'High' => 'Alto',
        'Low' => 'Bajo',
        'Normal' => 'Estándar',
      },
    },
   'ContrastMode' => {
      PrintConv => {
        'High' => 'Alto',
        'Low' => 'Bajo',
      },
    },
   'ContrastSetting' => 'Ajustes de Contraste',
   'Copyright' => 'Copyright Perfil',
   'CopyrightNotice' => 'Aviso Copyright',
   'Country' => 'País',
   'Country-PrimaryLocationCode' => 'Código País ISO',
   'Country-PrimaryLocationName' => 'País',
   'CountryCode' => 'Código País',
   'CreateDate' => 'Fecha y Hora de Datos Digital',
   'CreationDate' => 'Fecha Creación',
   'CreativeStyle' => {
      PrintConv => {
        'Autumn Leaves' => 'Hojas de otoño',
        'B&W' => 'ByN',
        'Clear' => 'Claro',
        'Deep' => 'Profundo',
        'Landscape' => 'Paisaje',
        'Light' => 'Luz',
        'Neutral' => 'Neutro',
        'Night View/Portrait' => 'Retrato noct.',
        'Portrait' => 'Retrato',
        'Standard' => 'Estándar',
        'Sunset' => 'Puesta sol',
        'Vivid' => 'Vívido',
      },
    },
   'CreativeStyleSetting' => {
      PrintConv => {
        'Landscape' => 'Paisaje',
        'Portrait' => 'Retrato',
        'Standard' => 'Estándar',
        'Vivid' => 'Vívido',
      },
    },
   'Creator' => 'Creador',
   'CreatorAddress' => 'Creador - Dirección',
   'CreatorCity' => 'Creador - Ciudad',
   'CreatorContactInfo' => 'Contacto Creador',
   'CreatorCountry' => 'Creador - País',
   'CreatorPostalCode' => 'Creador - Código Postal',
   'CreatorRegion' => 'Creador - Estado/Provincia',
   'CreatorWorkEmail' => 'Creador - Email(s)',
   'CreatorWorkTelephone' => 'Creador - Teléfono(s)',
   'CreatorWorkURL' => 'Creador - Website(s)',
   'Credit' => 'Proveedor',
   'CropActive' => {
      PrintConv => {
        'Yes' => 'Si',
      },
    },
   'CropHeight' => 'Recorte Altura',
   'CropLeft' => 'Recorte Izquierda',
   'CropTop' => 'Recorte Arriba',
   'CropWidth' => 'Recorte Anchura',
   'CroppedImageHeight' => 'Alto Imágen Recortada',
   'CroppedImageLeft' => 'Izquierda Imágen Recortada',
   'CroppedImageTop' => 'Superior Imágen Recortada',
   'CroppedImageWidth' => 'Ancho Imágen Recortada',
   'CurrentICCProfile' => 'Perfil ICC Actual',
   'CurrentPreProfileMatrix' => 'Matriz Pre Perfil Actual',
   'Curves' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'CustomRendered' => {
      Description => 'Proceso Imagen Personalizado',
      PrintConv => {
        'Custom' => 'Proceso personalizado',
        'Normal' => 'Proceso normal',
      },
    },
   'CustomSaturation' => 'Saturación personalizada',
   'D-LightingHQ' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'D-LightingHQSelected' => {
      PrintConv => {
        'Yes' => 'Si',
      },
    },
   'D-LightingHS' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'DNGBackwardVersion' => 'Versión Antigua DNG',
   'DNGLensInfo' => 'Distancia Focal Mínima',
   'DNGVersion' => 'Versión DNG',
   'DOF' => 'Profundidad de campo',
   'Data' => 'Datos',
   'DataCompressionMethod' => 'Proveedor/Propietario Algoritmo Compresión Datos',
   'DataImprint' => {
      PrintConv => {
        'None' => 'Ninguno',
      },
    },
   'DataPackets' => ' Paquetes de Datos',
   'DataType' => 'Tipo Datos',
   'Date' => 'Fecha',
   'DateCreated' => 'Fecha Creación',
   'DateSent' => 'Fecha Envío',
   'DateStampMode' => {
      PrintConv => {
        'Off' => 'Desactivado',
      },
    },
   'DateTimeDigitized' => 'Fecha y Hora Digital',
   'DateTimeOriginal' => 'Fecha y Hora de Datos Original',
   'DaylightSavings' => {
      PrintConv => {
        'Yes' => 'Si',
      },
    },
   'DefaultBlackRender' => {
      PrintConv => {
        'None' => 'Ninguno',
      },
    },
   'DefaultCropOrigin' => 'Origen Corte Defecto',
   'DefaultCropSize' => 'Tamaño Corte Defecto',
   'DefaultScale' => 'Escala por Defecto',
   'DerivedFromMaskMarkers' => {
      PrintConv => {
        'All' => 'Todo',
        'None' => 'Ninguno',
      },
    },
   'Description' => 'Descripción',
   'Destination' => 'Destino',
   'DestinationDST' => {
      PrintConv => {
        'Yes' => 'Si',
      },
    },
   'DeviceAttributes' => 'Atributos Dispositivo',
   'DeviceManufacturer' => 'Fabricante Dispositivo',
   'DeviceMfgDesc' => 'Descripción Fabricante Dispositivo',
   'DeviceModel' => 'Modelo Dispositivo',
   'DeviceModelDesc' => 'Descripción Modelo Dispositivo',
   'DeviceSettingDescription' => 'Descripción Ajustes Dispositivo',
   'DigitalCreationDate' => 'Fecha Creación Digital',
   'DigitalCreationTime' => 'Hora Creación Digital',
   'DigitalFilter01' => {
      PrintConv => {
        'Monochrome' => 'Monocromo',
      },
    },
   'DigitalFilter02' => {
      PrintConv => {
        'Monochrome' => 'Monocromo',
      },
    },
   'DigitalFilter03' => {
      PrintConv => {
        'Monochrome' => 'Monocromo',
      },
    },
   'DigitalFilter04' => {
      PrintConv => {
        'Monochrome' => 'Monocromo',
      },
    },
   'DigitalFilter05' => {
      PrintConv => {
        'Monochrome' => 'Monocromo',
      },
    },
   'DigitalFilter06' => {
      PrintConv => {
        'Monochrome' => 'Monocromo',
      },
    },
   'DigitalFilter07' => {
      PrintConv => {
        'Monochrome' => 'Monocromo',
      },
    },
   'DigitalFilter08' => {
      PrintConv => {
        'Monochrome' => 'Monocromo',
      },
    },
   'DigitalFilter09' => {
      PrintConv => {
        'Monochrome' => 'Monocromo',
      },
    },
   'DigitalFilter10' => {
      PrintConv => {
        'Monochrome' => 'Monocromo',
      },
    },
   'DigitalFilter11' => {
      PrintConv => {
        'Monochrome' => 'Monocromo',
      },
    },
   'DigitalFilter12' => {
      PrintConv => {
        'Monochrome' => 'Monocromo',
      },
    },
   'DigitalFilter13' => {
      PrintConv => {
        'Monochrome' => 'Monocromo',
      },
    },
   'DigitalFilter14' => {
      PrintConv => {
        'Monochrome' => 'Monocromo',
      },
    },
   'DigitalFilter15' => {
      PrintConv => {
        'Monochrome' => 'Monocromo',
      },
    },
   'DigitalFilter16' => {
      PrintConv => {
        'Monochrome' => 'Monocromo',
      },
    },
   'DigitalFilter17' => {
      PrintConv => {
        'Monochrome' => 'Monocromo',
      },
    },
   'DigitalFilter18' => {
      PrintConv => {
        'Monochrome' => 'Monocromo',
      },
    },
   'DigitalFilter19' => {
      PrintConv => {
        'Monochrome' => 'Monocromo',
      },
    },
   'DigitalFilter20' => {
      PrintConv => {
        'Monochrome' => 'Monocromo',
      },
    },
   'DigitalSignature' => 'Firma Digital',
   'DigitalZoom' => {
      Description => 'Zoom Digital',
      PrintConv => {
        'None' => 'Ninguno',
        'Off' => 'Desactivado',
      },
    },
   'DigitalZoomOn' => {
      Description => 'Zoom Digital Encendido',
      PrintConv => {
        'Off' => 'Apagado',
        'On' => 'Encendido',
      },
    },
   'DigitalZoomRatio' => 'Ratio Zoom Digital',
   'Directory' => 'Ubicación del Fichero',
   'DistortionControl' => {
      PrintConv => {
        'Off' => 'Desactivado',
      },
    },
   'DistortionCorrection' => {
      Description => 'Corrección Distorsión',
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'DistortionCorrection2' => {
      Description => 'Corrección Distorsión 2',
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'DistortionCorrectionOn' => 'Corrección Distorsión Activada',
   'DocSecurity' => {
      PrintConv => {
        'None' => 'Ninguno',
      },
    },
   'DocumentHistory' => 'Historial del Documento',
   'DocumentName' => 'Nombre Documento',
   'DocumentNotes' => 'Notas del Documento',
   'DotRange' => 'Intervalo Puntos',
   'DriveMode' => {
      Description => 'Modo Entrada',
      PrintConv => {
        'Off' => 'Desactivado',
      },
    },
   'Duration' => 'Duración',
   'DynamicRange' => {
      PrintConv => {
        'Standard' => 'Estándar',
      },
    },
   'DynamicRangeOptimizer' => {
      Description => 'Optim.gama diná',
      PrintConv => {
        'Advanced Auto' => 'Avanzado Autom',
        'Advanced Lv1' => 'Avanzado Nvl.1',
        'Advanced Lv2' => 'Avanzado Nvl.2',
        'Advanced Lv3' => 'Avanzado Nvl.3',
        'Advanced Lv4' => 'Avanzado Nvl.4',
        'Advanced Lv5' => 'Avanzado Nvl.5',
        'Off' => 'Desactivado',
        'Standard' => 'Estándar',
      },
    },
   'DynamicRangeOptimizerBracket' => {
      PrintConv => {
        'Low' => 'Bajo',
      },
    },
   'DynamicRangeOptimizerMode' => {
      PrintConv => {
        'Standard' => 'Estándar',
      },
    },
   'DynamicRangeOptimizerSetting' => {
      PrintConv => {
        'Standard' => 'Estándar',
      },
    },
   'EasyExposureCompensation' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'EasyMode' => {
      PrintConv => {
        'Beach' => 'Playa',
        'Black & White' => 'Blanco y Negro',
        'Digital Macro' => 'Macro digital',
        'Easy' => 'Fácil',
        'Fireworks' => 'Fuegos Artificiales',
        'Fisheye Effect' => 'Efecto Ojo de Pez',
        'Flash Off' => 'Flash Desactivado',
        'Foliage' => 'Follaje',
        'Gray Scale' => 'Escala de Grises',
        'Indoor' => 'Interior',
        'Kids & Pets' => 'Niños y Mascotas',
        'Landscape' => 'Paisaje',
        'Monochrome' => 'Monocromo',
        'Neutral' => 'Neutro',
        'Night' => 'Nocturno',
        'Night Scene' => 'Escena Nocturna',
        'Night Snapshot' => 'Fotografía Nocturna',
        'Nostalgic' => 'Nostalgico',
        'Portrait' => 'Retrato',
        'Smile' => 'Sonrisa',
        'Snow' => 'Nieve',
        'Sports' => 'Deportes',
        'Sunset' => 'Puesta de sol',
        'Surface' => 'Superficie',
        'Underwater' => 'Subacuatica',
        'Vivid' => 'Vívido',
      },
    },
   'EdgeNoiseReduction' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'EditStatus' => 'Estado Edición',
   'EditorialUpdate' => {
      Description => 'Actualización Editorial',
      PrintConv => {
        'Additional language' => 'Idioma Adicional',
      },
    },
   'EffectiveMaxAperture' => 'Aperture Máxima Efectiva',
   'Emphasis' => {
      PrintConv => {
        'None' => 'Ninguno',
      },
    },
   'EncodedBy' => 'Codificado por',
   'EncodingProcess' => 'Proceso de codificación',
   'EncodingSettings' => 'Ajustes de Codificación',
   'EncodingTime' => 'Hora de codificación',
   'EndPoints' => 'Puntos Finales',
   'EnhanceDarkTones' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'Enhancement' => {
      PrintConv => {
        'Green' => 'Verde',
        'Red' => 'Rojo',
        'Underwater' => 'Subacuatica',
      },
    },
   'EnvelopeNumber' => 'Número Sobre',
   'EnvelopePriority' => {
      Description => 'Prioridad Sobre',
      PrintConv => {
        '0 (reserved)' => '0 (reservada para uso futuro)',
        '1 (most urgent)' => '1 (más urgente)',
        '5 (normal urgency)' => '5 (urgencia normal)',
        '8 (least urgent)' => '8 (menos urgente)',
        '9 (user-defined priority)' => '9 (prioridad definida por el usuario)',
      },
    },
   'EnvelopeRecordVersion' => 'Versión Registro Sobre',
   'EquipmentVersion' => 'Versión Equipo',
   'ErrorCorrection' => 'Correción Error',
   'ErrorCorrectionType' => 'Tipo Corrección Error',
   'ExcursionTolerance' => {
      Description => 'Tolerancia Excursión',
      PrintConv => {
        'Allowed' => 'Puede ocurrir',
        'Not Allowed' => 'No Permitido (defecto)',
      },
    },
   'ExifCameraInfo' => 'Información Cámara Exif',
   'ExifImageHeight' => 'Alto Imagen',
   'ExifImageWidth' => 'Ancho Imagen',
   'ExifOffset' => 'Puntero Exif IFD',
   'ExifToolVersion' => 'Versión ExifTool',
   'ExifVersion' => 'Versión Exif',
   'ExpandFilm' => 'Película Expandida',
   'ExpandFilterLens' => 'Filtro Objetivo Expandida',
   'ExpandFlashLamp' => 'Lampara Flash Expandida',
   'ExpandLens' => 'Objetivo Expandido',
   'ExpandScanner' => 'Escaner Expandido',
   'ExpandSoftware' => 'Software Expandido',
   'ExpirationDate' => 'Fecha Expiración',
   'ExpirationTime' => 'Hora Expiración',
   'ExposureCompensation' => 'Compensación Exposición',
   'ExposureDelayMode' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'ExposureIndex' => 'Índice Exposición',
   'ExposureMode' => {
      Description => 'Modo Exposición',
      PrintConv => {
        'Aperture-priority AE' => 'Prioridad Aberture AE',
        'Auto' => 'Exposición automática',
        'Auto bracket' => 'Auto-horquillado',
        'Beach' => 'Playa',
        'Fireworks' => 'Fuegos Artificiales',
        'Food' => 'Comida',
        'Landscape' => 'Paisaje',
        'Manual' => 'Exposición manual',
        'Panorama' => 'Panoramica',
        'Portrait' => 'Retrato',
        'Program AE' => 'Programa AE',
        'Shutter speed priority AE' => 'Prioridad velocidad obturador AE',
        'Snow' => 'Nieve',
        'Underwater' => 'Subacuatica',
      },
    },
   'ExposureProgram' => {
      Description => 'Programa Exposición',
      PrintConv => {
        'Action (High speed)' => 'Programa acción (orientado a velocidad de obturación rápida)',
        'Aperture-priority AE' => 'Prioridad Apertura',
        'Creative (Slow speed)' => 'Programa creativo (orientado a profundidad de campo)',
        'Landscape' => 'Modo paisaje (para fotos de paisaje con el fondo en enfoque)',
        'Manual' => 'Exposición manual',
        'Not Defined' => 'No definido',
        'Portrait' => 'Modo retrato (para fotos de cerca con el fondo fuera de enfoque)',
        'Program AE' => 'Programa normal',
        'Shutter speed priority AE' => 'Prioridad obturador',
      },
    },
   'ExposureTime' => 'Tiempo de Exposición',
   'ExposureTime2' => 'Tiempo de Exposición 2',
   'Extender' => {
      PrintConv => {
        'None' => 'Ninguno',
      },
    },
   'ExternalFlash' => {
      Description => 'Flash Externo',
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'ExternalFlashBounce' => {
      PrintConv => {
        'Yes' => 'Si',
      },
    },
   'ExternalFlashMode' => {
      PrintConv => {
        'Off' => 'Desactivado',
      },
    },
   'ExternalFlashZoom' => 'Zoom Flash Externo',
   'ExtraSamples' => 'Muestra Extra',
   'FNumber' => 'Número F',
   'FOV' => 'Angulo de Visión',
   'FaceDetectArea' => 'Area detección caras',
   'FaceDetectFrameSize' => 'Tamaño Area detección caras',
   'FaceOrientation' => {
      PrintConv => {
        'Horizontal (normal)' => '0° (arriba/izquierda)',
        'Rotate 180' => 'Girado 180°',
        'Rotate 270 CW' => 'Girado 270° sentido reloj',
        'Rotate 90 CW' => 'Girado 90° sentido reloj',
      },
    },
   'FacesDetected' => 'Caras Detectadas',
   'FastSeek' => {
      PrintConv => {
        'Yes' => 'Si',
      },
    },
   'FaxProfile' => {
      PrintConv => {
        'Unknown' => 'Desconocido',
      },
    },
   'FaxRecvParams' => 'Parámetros Recepción Fax',
   'FaxRecvTime' => 'Hora Recepción Fax',
   'FaxSubAddress' => 'Subdirección Fax',
   'FileAccessDate' => 'Fecha y Hora de Acceso',
   'FileCreateDate' => 'Fecha y Hora de Creación',
   'FileFormat' => 'Formato Archivo',
   'FileLength' => 'Tamaño Archivo',
   'FileModifyDate' => 'Fecha Actualización',
   'FileName' => 'Nombre Archivo',
   'FileNumberMemory' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'FileNumberSequence' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'FileOwner' => 'Propietario del Archivo',
   'FilePermissions' => 'Permisos',
   'FileSize' => 'Tamaño Archivo',
   'FileSource' => {
      Description => 'Fuente Archivo',
      PrintConv => {
        'Digital Camera' => 'Cámara Digital',
        'Film Scanner' => 'Escaner Película',
        'Reflection Print Scanner' => 'Escaner de Reflexión',
      },
    },
   'FileType' => 'Tipo Archivo',
   'FileVersion' => 'Versión Formato Archivo',
   'Filename' => 'Nombre archivo',
   'FillOrder' => 'Orden Rellenado',
   'FilterEffect' => {
      PrintConv => {
        'Green' => 'Verde',
        'None' => 'Ninguno',
        'Off' => 'Desactivado',
        'Orange' => 'Naranja',
        'Red' => 'Rojo',
        'Yellow' => 'Amarillo',
      },
    },
   'FilterEffectMonochrome' => {
      PrintConv => {
        'Green' => 'Verde',
        'None' => 'Ninguno',
        'Orange' => 'Naranja',
        'Red' => 'Rojo',
        'Yellow' => 'Amarillo',
      },
    },
   'FilterEffectUnknown' => {
      PrintConv => {
        'Green' => 'Verde',
        'None' => 'Ninguno',
        'Orange' => 'Naranja',
        'Red' => 'Rojo',
        'Yellow' => 'Amarillo',
      },
    },
   'FilterEffectUserDef1' => {
      PrintConv => {
        'Green' => 'Verde',
        'None' => 'Ninguno',
        'Orange' => 'Naranja',
        'Red' => 'Rojo',
        'Yellow' => 'Amarillo',
      },
    },
   'FilterEffectUserDef2' => {
      PrintConv => {
        'Green' => 'Verde',
        'None' => 'Ninguno',
        'Orange' => 'Naranja',
        'Red' => 'Rojo',
        'Yellow' => 'Amarillo',
      },
    },
   'FilterEffectUserDef3' => {
      PrintConv => {
        'Green' => 'Verde',
        'None' => 'Ninguno',
        'Orange' => 'Naranja',
        'Red' => 'Rojo',
        'Yellow' => 'Amarillo',
      },
    },
   'FinderDisplayDuringExposure' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'FirmwareVersion' => 'Versión Firmware',
   'FixtureIdentifier' => 'Identificador Marca',
   'Flags' => {
      PrintConv => {
        'Comment' => 'Comentario',
        'FileName' => 'Nombre de Archivo',
        'Text' => 'Texto',
      },
    },
   'Flash' => {
      PrintConv => {
        'Auto, Did not fire' => 'Flash no disparado, modo automático',
        'Auto, Did not fire, Red-eye reduction' => 'Auto, Flash no disparado, modo reducción ojos rojos',
        'Auto, Fired' => 'Flash disparado, modo automático',
        'Auto, Fired, Red-eye reduction' => 'Flash disparado, modo automático, modo reducción ojos rojos',
        'Auto, Fired, Red-eye reduction, Return detected' => 'Flash disparado, modo automático, retorno luz detectado, modo reducción ojos rojos',
        'Auto, Fired, Red-eye reduction, Return not detected' => 'Flash disparado, modo automático, retorno luz no detectado, modo reducción ojos rojos',
        'Auto, Fired, Return detected' => 'Flash disparado, modo automático, retorno luz detectado',
        'Auto, Fired, Return not detected' => 'Flash disparado, modo automático, retorno luz no detectado',
        'Did not fire' => 'No se ha disparado el flash',
        'Fired' => 'Flash disparado',
        'Fired, Red-eye reduction' => 'Flash disparado, modo reducción ojos rojos',
        'Fired, Red-eye reduction, Return detected' => 'Flash disparado, modo reducción ojos rojos, retorno luz detectado',
        'Fired, Red-eye reduction, Return not detected' => 'Flash disparado, modo reducción ojos rojos, retorno luz no detectado',
        'Fired, Return detected' => 'Luz devuelta en captador detectada',
        'Fired, Return not detected' => 'Luz devuelta en captador no detectada',
        'No Flash' => 'Flash no disparado',
        'No flash function' => 'Sin función flash',
        'Off' => 'Desactivado',
        'Off, Did not fire' => 'Flash no disparado, modo flash forzado',
        'Off, Did not fire, Return not detected' => 'Apagado, flash no disparado, retorno luz no detectado',
        'Off, No flash function' => 'Apagado, sin función flash',
        'Off, Red-eye reduction' => 'Apagado, modo reducción ojos rojos',
        'On' => 'Activado',
        'On, Did not fire' => 'Encendido, flash no disparado',
        'On, Fired' => 'Flash disparado, modo flash forzardo',
        'On, Red-eye reduction' => 'Flash disparado, modo flash forzado, modo reducción ojos rojos',
        'On, Red-eye reduction, Return detected' => 'Flash disparado, modo flash forzado, modo reducción ojos rojos, retorno luz detectado',
        'On, Red-eye reduction, Return not detected' => 'Flash disparado, modo flash forzado, modo reducción ojos rojos, retorno luz no detectado',
        'On, Return detected' => 'Flash disparado, modo flash forzado, retorno luz detectado',
        'On, Return not detected' => 'Flash disparado, modo flash forzado, retorno luz no detectado',
      },
    },
   'FlashColorFilter' => {
      PrintConv => {
        'None' => 'Ninguno',
        'Red' => 'Rojo',
        'Yellow' => 'Amarillo',
      },
    },
   'FlashCommanderMode' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'FlashControlMode' => {
      Description => 'Modo Control Flash',
      PrintConv => {
        'Off' => 'Desactivado',
        'Repeating Flash' => 'Flash Estroboscopico',
      },
    },
   'FlashDevice' => {
      PrintConv => {
        'None' => 'Ninguno',
      },
    },
   'FlashEnergy' => 'Energía Flash',
   'FlashExposureComp' => 'Compensación Exposición Flash',
   'FlashExposureLock' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'FlashFired' => {
      PrintConv => {
        'Yes' => 'Si',
      },
    },
   'FlashFocalLength' => 'Longitud Flash Flash',
   'FlashGroupAControlMode' => {
      PrintConv => {
        'Off' => 'Desactivado',
      },
    },
   'FlashGroupBControlMode' => {
      PrintConv => {
        'Off' => 'Desactivado',
      },
    },
   'FlashGroupCControlMode' => {
      PrintConv => {
        'Off' => 'Desactivado',
      },
    },
   'FlashIntensity' => {
      Description => 'Intensidad Flash',
      PrintConv => {
        'High' => 'Alto',
        'Low' => 'Bajo',
      },
    },
   'FlashLevel' => {
      PrintConv => {
        'Low' => 'Bajo',
      },
    },
   'FlashMeteringMode' => {
      PrintConv => {
        'External Auto' => 'Externo Automatico',
        'External Manual' => 'Externo Manual',
        'Off' => 'Desactivado',
      },
    },
   'FlashMode' => {
      Description => 'Modo Flash',
      PrintConv => {
        'Auto' => 'Automático',
        'Disabled' => 'Desactivado',
        'Force' => 'Forzado',
        'Off' => 'Apagado',
        'On' => 'Encendido',
        'Red eye' => 'Ojos rojos',
      },
    },
   'FlashModel' => {
      Description => 'Modelo Flash',
      PrintConv => {
        'None' => 'Ninguno',
      },
    },
   'FlashOutput' => 'Flash Salida',
   'FlashRemoteControl' => 'Control Remote Flash',
   'FlashSerialNumber' => 'Número Serie Flash',
   'FlashSource' => {
      PrintConv => {
        'None' => 'Ninguno',
      },
    },
   'FlashStatus' => {
      PrintConv => {
        'Off' => 'Desactivado',
      },
    },
   'FlashType' => {
      Description => 'Tipo Flash',
      PrintConv => {
        'None' => 'Ninguno',
      },
    },
   'FlashWarning' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'FlashpixVersion' => 'Versión Flashpix Soportado',
   'FlickerReduce' => {
      Description => 'Reducir Parpadeo',
      PrintConv => {
        'Off' => 'Apagado',
        'On' => 'Encendido',
      },
    },
   'FlipHorizontal' => {
      PrintConv => {
        'Yes' => 'Si',
      },
    },
   'FocalLength' => 'Distancia Focal Objetivo',
   'FocalLength35efl' => 'Longitud Focal (Conversión a 35 mm)',
   'FocalLengthIn35mmFormat' => 'Distancia Focal en Película de 35 mm',
   'FocalPlaneResolutionUnit' => {
      Description => 'Unidad Resolución Plano Focal',
      PrintConv => {
        'None' => 'Ninguno',
        'inches' => 'Pulgada',
        'um' => 'µm (Micrometro)',
      },
    },
   'FocalPlaneXResolution' => 'Resolución X Plano Focal',
   'FocalPlaneYResolution' => 'Resolución Y Plano Focal',
   'FocusContinuous' => {
      PrintConv => {
        'Continuous' => 'Continuo',
        'Single' => 'Sencillo',
      },
    },
   'FocusMode' => {
      Description => 'Modo Enfoque',
      PrintConv => {
        'Continuous' => 'Continuo',
        'Manual Focus (3)' => 'Enfoque Manual (6)',
        'Manual Focus (6)' => 'Enfoque Manual (6)',
        'Single' => 'Simple',
      },
    },
   'FocusRange' => {
      Description => 'Rango de Enfoque',
      PrintConv => {
        'Close' => 'Próximo',
        'Infinity' => 'Infinito',
        'Not Known' => 'Desconocido',
        'Very Close' => 'Muy Próximo',
      },
    },
   'FocusSetting' => 'Ajuste Enfoque',
   'FocusTrackingLockOn' => {
      PrintConv => {
        'Off' => 'Desactivado',
      },
    },
   'ForwardMatrix1' => 'Matriz Avance 1',
   'ForwardMatrix2' => 'Matriz Avance 2',
   'FrameRate' => 'Velocidad del Fotograma',
   'FrameSize' => 'Tamaño del Fotograma',
   'FreeByteCounts' => 'Número Bytes Libres',
   'FreeOffsets' => 'Offsets Libres',
   'FuncButton' => {
      PrintConv => {
        'None' => 'Ninguno',
      },
    },
   'FuncButtonPlusDials' => {
      PrintConv => {
        'None' => 'Ninguno',
      },
    },
   'GEModel' => 'Modelo',
   'GPSAltitude' => 'Altitud',
   'GPSAltitudeRef' => {
      Description => 'Referencia Altitud',
      PrintConv => {
        'Above Sea Level' => 'Nivel del Mar',
        'Below Sea Level' => 'Referencia Nivel del Mar (valor negativo)',
      },
    },
   'GPSAreaInformation' => 'Nombre de Zona GPS',
   'GPSDOP' => 'Precisión Medición',
   'GPSDateStamp' => 'Fecha GPS',
   'GPSDateTime' => 'Fecha y Hora GPS',
   'GPSDestBearing' => 'Orientación de Destino',
   'GPSDestBearingRef' => {
      Description => 'Referencia para Orientación de Destino',
      PrintConv => {
        'Magnetic North' => 'Dirección magnética',
        'True North' => 'Dirección real',
      },
    },
   'GPSDestDistance' => 'Distancia a Destino',
   'GPSDestDistanceRef' => {
      Description => 'Referencia para Distancia a Destino',
      PrintConv => {
        'Kilometers' => 'Kilómetros',
        'Miles' => 'Millas',
        'Nautical Miles' => 'Nudos',
      },
    },
   'GPSDestLatitude' => 'Latitud de Destino',
   'GPSDestLatitudeRef' => {
      Description => 'Referencia para Latitud de Destino',
      PrintConv => {
        'North' => 'Latitud norte',
        'South' => 'Latitud sur',
      },
    },
   'GPSDestLongitude' => 'Longitud de Destino',
   'GPSDestLongitudeRef' => {
      Description => 'Referencia para Longitud de Destino',
      PrintConv => {
        'East' => 'Longitud este',
        'West' => 'Longitud oeste',
      },
    },
   'GPSDifferential' => {
      Description => 'Corrección Diferencial GPS',
      PrintConv => {
        'Differential Corrected' => 'Corrección diferencial aplicada',
        'No Correction' => 'Medición sin corrección diferencial',
      },
    },
   'GPSImgDirection' => 'Dirección de Imagen',
   'GPSImgDirectionRef' => {
      Description => 'Referencia para Dirección de Imagen',
      PrintConv => {
        'Magnetic North' => 'Dirección magnética',
        'True North' => 'Dirección real',
      },
    },
   'GPSInfo' => 'Puntero IFD de Información GPS',
   'GPSLatitude' => 'Latitud',
   'GPSLatitudeRef' => {
      Description => 'Latitud Norte o Sur',
      PrintConv => {
        'North' => 'Latitud norte',
        'South' => 'Latitud sur',
      },
    },
   'GPSLongitude' => 'Longitud',
   'GPSLongitudeRef' => {
      Description => 'Longitud Este u Oeste',
      PrintConv => {
        'East' => 'Longitud Este',
        'West' => 'Longitud Oeste',
      },
    },
   'GPSMapDatum' => 'Dato Medición Geodésica Usado',
   'GPSMeasureMode' => {
      Description => 'Modo Medición GPS',
      PrintConv => {
        '2-Dimensional Measurement' => 'Medición bidimensional',
        '3-Dimensional Measurement' => 'Medición tridimensional',
      },
    },
   'GPSProcessingMethod' => 'Nombre del Método de Procesado GPS',
   'GPSSatellites' => 'Satélites GPS Usados para Medida',
   'GPSSpeed' => 'Velocidad del Receptor GPS',
   'GPSSpeedRef' => {
      Description => 'Unidad Velocidad',
      PrintConv => {
        'km/h' => 'Kilómetros por hora',
        'knots' => 'Nudos',
        'mph' => 'Millas por hora',
      },
    },
   'GPSStatus' => {
      Description => 'Estado Receptor GPS',
      PrintConv => {
        'Measurement Active' => 'Medición Activa',
        'Measurement Void' => 'Medición Vacía',
      },
    },
   'GPSTimeStamp' => 'Hora GPS (reloj atómico)',
   'GPSTrack' => 'Dirección de Movimiento',
   'GPSTrackRef' => {
      Description => 'Referencia de Dirección de Movimiento',
      PrintConv => {
        'Magnetic North' => 'Dirección magnética',
        'True North' => 'Dirección real',
      },
    },
   'GPSVersionID' => 'Versión Etiqueta GPS',
   'GainControl' => {
      Description => 'Control Ganancia',
      PrintConv => {
        'High gain down' => 'Atenuación alta',
        'High gain up' => 'Ganancia alta',
        'Low gain down' => 'Atenuación baja',
        'Low gain up' => 'Ganancia debil',
        'None' => 'Ninguno',
      },
    },
   'GammaCompensatedValue' => 'Valor Compensado Gamma',
   'Gapless' => {
      PrintConv => {
        'Yes' => 'Si',
      },
    },
   'Genre' => {
      Description => 'Género',
      PrintConv => {
        'None' => 'Ninguno',
        'Other' => 'Otro',
      },
    },
   'GenreID' => 'ID Género',
   'GeoTiffAsciiParams' => 'Etiqueta Parámetros Ascii Geo',
   'GeoTiffDirectory' => 'Etiqueta Directorio Clave Geo',
   'GeoTiffDoubleParams' => 'Etiqueta Parámetros Doble Geo',
   'Gradation' => 'Luminosidad',
   'GrayResponseCurve' => 'Curva Respuesta Gris',
   'GrayResponseUnit' => {
      Description => 'Unidad Respuesta Gris',
      PrintConv => {
        '0.0001' => 'Número representa la milésima de una unidad',
        '0.001' => 'Número representa la centésima de una unidad',
        '0.1' => 'Número representa la décima de una unidad',
        '1e-05' => 'Número representa la diezmilésima de una unidad',
        '1e-06' => 'Número representa la cienmilésima de una unidad',
      },
    },
   'GrayTRC' => 'Columna Matriz Gris',
   'GreenMatrixColumn' => 'Columna Matriz Verde',
   'GreenTRC' => 'Curva Reproducción Tono Verde',
   'GridDisplay' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'HCUsage' => 'Uso HC',
   'HDR' => {
      Description => 'Auto HDR',
      PrintConv => {
        'Off' => 'Desactivado',
      },
    },
   'HDRSmoothing' => {
      PrintConv => {
        'Low' => 'Bajo',
        'Off' => 'Desactivado',
      },
    },
   'HalftoneHints' => 'Indicación Medio Tono',
   'HasAttachedImages' => 'Tiene Imagenes Adjuntas',
   'HasAudio' => 'Tiene Audio',
   'HasImage' => 'Tiene Imagen',
   'HasScript' => 'Tiene Script',
   'HasVideo' => 'Tiene Video',
   'Headline' => 'Titular',
   'HeightResolution' => 'Resolución Imagen Vertical',
   'HighISONoiseReduction' => {
      Description => 'Reducción Ruido ISO Alta',
      PrintConv => {
        'High' => 'Alto',
        'Low' => 'Bajo',
        'Off' => 'Desactivado',
        'On' => 'Activado',
        'Standard' => 'Estándar',
        'Strong' => 'Fuerte',
      },
    },
   'HighISONoiseReduction2' => {
      PrintConv => {
        'Low' => 'Bajo',
      },
    },
   'Highlight' => 'Realce',
   'HighlightColorDistortReduct' => {
      PrintConv => {
        'Standard' => 'Estándar',
      },
    },
   'HighlightTonePriority' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'HometownDST' => {
      PrintConv => {
        'Yes' => 'Si',
      },
    },
   'HostComputer' => 'Ordenador Principal',
   'Hue' => {
      Description => 'Tono',
      PrintConv => {
        'None' => 'Ninguno',
      },
    },
   'HyperfocalDistance' => 'Distancia Hiperfocal',
   'ICCProfile' => 'Perfil ICC',
   'ICC_Profile' => 'Perfil Color Entrada ICC',
   'IDCCreativeStyle' => {
      PrintConv => {
        'Landscape' => 'Retrato',
        'Neutral' => 'Neutro',
        'Standard' => 'Estándar',
        'Vivid' => 'Vívido',
      },
    },
   'IPTC-NAA' => 'Metadato IPTC-NAA',
   'IPTCBitsPerSample' => 'Número de Bits por Muestra',
   'IPTCImageHeight' => 'Número de Líneas',
   'IPTCImageRotation' => {
      Description => 'Rotación Imagen',
      PrintConv => {
        '0' => 'Sin rotación',
        '180' => 'Rotación 180 grados',
        '270' => 'Rotación 270 grados',
        '90' => 'Rotación 90 grados',
      },
    },
   'IPTCImageWidth' => 'Pixels por Línea',
   'IPTCPictureNumber' => 'Número Imagen',
   'IPTCPixelHeight' => 'Tamaño Pixel Perpendicular a Dirección Escaneo',
   'IPTCPixelWidth' => 'Tamaño Pixel en Dirección Escaneo',
   'ISO' => 'Ratio Velocidad ISO',
   'ISOAutoMinSpeed' => {
      PrintConv => {
        'Auto Fast' => 'Auto-rápido',
        'Auto Standard' => 'Auto-estándar',
      },
    },
   'ISOExpansion' => {
      PrintConv => {
        'Off' => 'Desactivado',
      },
    },
   'ISOExpansion2' => {
      PrintConv => {
        'Off' => 'Desactivado',
      },
    },
   'ISOSetting' => 'Ajuste ISO',
   'ISOSpeedExpansion' => {
      PrintConv => {
        'Yes' => 'Si',
      },
    },
   'IT8Header' => 'Cabecera IT8',
   'Illumination' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'Image::ExifTool::AIFF::Comment' => 'Comentario AIFF',
   'ImageAuthentication' => {
      PrintConv => {
        'Off' => 'Desactivado',
      },
    },
   'ImageByteCount' => 'Número Byte Imagen',
   'ImageColor' => {
      PrintConv => {
        'Monochrome' => 'Monocromo',
      },
    },
   'ImageColorIndicator' => 'Indicador Color Imagen',
   'ImageColorValue' => 'Valor Color Imagen',
   'ImageDataDiscard' => {
      Description => 'Datos Imagen Descartado',
      PrintConv => {
        'Flexbits Discarded' => 'FlexBits Descartados',
        'Full Resolution' => 'Resolución Total',
        'HighPass Frequency Data Discarded' => 'Datos Frecuencia High-Pass Descartados',
        'Highpass and LowPass Frequency Data Discarded' => 'Dato Frecuencia High-Pass y Low-Pass Descartados',
      },
    },
   'ImageDepth' => 'Ancho Imagen',
   'ImageDescription' => 'Título Imagen',
   'ImageDustOff' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'ImageEditing' => {
      PrintConv => {
        'None' => 'Ninguno',
      },
    },
   'ImageFileFormatAsDelivered' => {
      PrintConv => {
        'Other' => 'Otro',
      },
    },
   'ImageHeight' => 'Alto Imagen',
   'ImageHistory' => 'Historia Imagen',
   'ImageID' => 'ID Imagen',
   'ImageInfo' => 'Info Imagen',
   'ImageLayer' => 'Capa Imagen',
   'ImageLength' => 'Longitud Imagen',
   'ImageNumber' => 'Número Imagen',
   'ImageOffset' => 'Offset Imagen',
   'ImageOrientation' => {
      Description => 'Orientación Imagen',
      PrintConv => {
        'Landscape' => 'Paisaje',
        'Portrait' => 'Retrato',
        'Square' => 'Cuadro',
      },
    },
   'ImageQuality' => {
      PrintConv => {
        'High' => 'Alto',
        'Standard' => 'Estándar',
      },
    },
   'ImageResourceBlocks' => 'Bloques Recursos Imagen',
   'ImageReview' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'ImageRotated' => {
      PrintConv => {
        'Yes' => 'Si',
      },
    },
   'ImageRotation' => {
      PrintConv => {
        'None' => 'Ninguno',
      },
    },
   'ImageSize' => 'Tamaño de la Imagen',
   'ImageSourceData' => 'Datos Fuente Imagen',
   'ImageStabilization' => {
      Description => 'Estabilización Imagen',
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'ImageStabilizationSetting' => 'Ajustes Estabilización Imagen',
   'ImageStyle' => {
      PrintConv => {
        'Landscape' => 'Paisaje',
        'Neutral' => 'Neutro',
        'Portrait' => 'Retrato',
        'Standard' => 'Estándar',
        'Vivid' => 'Vívido',
      },
    },
   'ImageTone' => {
      PrintConv => {
        'Landscape' => 'Paisaje',
        'Monochrome' => 'Monocromo',
        'Portrait' => 'Retrato',
      },
    },
   'ImageType' => {
      Description => 'Tipo Imagen',
      PrintConv => {
        'Other' => 'Otro',
      },
    },
   'ImageUniqueID' => 'ID Único Imagen',
   'ImageWidth' => 'Ancho Imagen',
   'Index' => 'Índice',
   'Indexed' => 'Indizado',
   'IngredientsMaskMarkers' => {
      PrintConv => {
        'All' => 'Todo',
        'None' => 'Ninguno',
      },
    },
   'InitialKey' => 'Clave inicial',
   'InkNames' => 'Nombres Tinta',
   'InkSet' => 'Conjunto Tinta',
   'Instructions' => 'Instrucciones',
   'IntellectualGenre' => 'Género Intelectual',
   'IntelligentContrast' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
        'n/a' => 'No Aplica',
      },
    },
   'IntelligentD-Range' => {
      PrintConv => {
        'High' => 'Alto',
        'Low' => 'Bajo',
        'Standard' => 'Estándar',
      },
    },
   'IntelligentExposure' => {
      PrintConv => {
        'High' => 'Alto',
        'Low' => 'Bajo',
        'Standard' => 'Estándar',
      },
    },
   'IntelligentResolution' => {
      PrintConv => {
        'High' => 'Alto',
        'Low' => 'Bajo',
        'Standard' => 'Estándar',
      },
    },
   'IntensityStereo' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'InterchangeColorSpace' => {
      PrintConv => {
        'CMY (K) Device Dependent' => 'Dispositivo dependiente CMY(K)',
        'RGB Device Dependent' => 'Dispositivo dependiente RGB',
      },
    },
   'IntergraphMatrix' => 'Etiqueta Matriz Intergráfica',
   'Interlace' => 'Entrelazado',
   'InternalFlash' => 'Flash Interno',
   'InternalFlashAE1' => 'Flash Interno AE1',
   'InternalFlashAE1_0' => 'Flash Interno',
   'InternalFlashAE2' => 'Flash Interno AE2',
   'InternalFlashAE2_0' => 'Flash Interno AE2',
   'InternalFlashMode' => {
      Description => 'Modo Flash Interno',
      PrintConv => {
        'Fired' => 'Activado',
      },
    },
   'InternalFlashTable' => 'Tabla Flash Interno',
   'InternalSerialNumber' => 'Número Serie Interno',
   'InteropIndex' => {
      Description => 'Identificación Interoperabilidad',
      PrintConv => {
        'R03 - DCF option file (Adobe RGB)' => 'R03: Archivo opción DCF (Adobe RGB)',
        'R98 - DCF basic file (sRGB)' => 'R98: Archivo básico DCF (sRGB)',
        'THM - DCF thumbnail file' => 'THM: Archivo miniatura DCF',
      },
    },
   'InteropOffset' => 'Etiqueta de Interoperabilidad',
   'InteropVersion' => 'Versión Interoperabilidad',
   'Is_Protected' => 'Está protegido',
   'Is_Trusted' => 'Es de confianza',
   'JFIFVersion' => 'Versión JFIF',
   'JPEGACTables' => 'Tablas AC JPEG',
   'JPEGDCTables' => 'Tablas DC JPEG',
   'JPEGLosslessPredictors' => 'Predictores Sin Perdidas JPEG',
   'JPEGPointTransforms' => 'Tranformadores Puntos JPEG',
   'JPEGProc' => 'Proc JPEG',
   'JPEGQTables' => 'Tablas Q JPEG',
   'JPEGQuality' => {
      Description => 'Calidad',
      PrintConv => {
        'Extra Fine' => 'Extrafina',
        'Fine' => 'Fina',
        'Standard' => 'Calidad estándar',
      },
    },
   'JPEGRestartInterval' => 'Intervalo Reinicio JPEG',
   'JPEGTables' => 'Tablas JPEG',
   'JobID' => 'ID del Trabajo',
   'JobTitle' => 'Cargo',
   'Keyword' => 'Palabras Clave',
   'Keywords' => 'Clave',
   'LCDIllumination' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'LCDIlluminationDuringBulb' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'LCHEditor' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'Language' => 'Idioma',
   'LanguageCode' => {
      PrintConv => {
        'Neutral' => 'Neutro',
      },
    },
   'LanguageIdentifier' => 'Identificador Idioma',
   'LanguageList' => 'Lista de Idiomas',
   'LeafData' => 'Datos Hoja',
   'Lens' => 'Objetivo',
   'LensApertureRange' => 'Intervalo Apertura Objetivo',
   'LensFirmwareVersion' => 'Versión Firmware Objetivo',
   'LensID' => 'ID Objetivo',
   'LensIDNumber' => 'Número ID Objetivo',
   'LensInfo' => 'Información del Objetivo',
   'LensModel' => 'Modelo Objetivo',
   'LensProperties' => 'Propiedades Objetivo',
   'LensSerialNumber' => 'Número Serie Objetivo',
   'LensType' => {
      Description => 'Tipo Objetivo',
      PrintConv => {
        'None' => 'Ninguno',
      },
    },
   'LightSource' => {
      Description => 'Fuente Luz',
      PrintConv => {
        'Cloudy' => 'Tiempo Nublado',
        'Cool White Fluorescent' => 'Fluorescente blanco cálido (W 3800 - 4500K)',
        'Day White Fluorescent' => 'Fluorescente blanco día (N 4600 - 5500K)',
        'Daylight' => 'Luz del día',
        'Daylight Fluorescent' => 'Fluorescente luz de día (D 5700 - 7100K)',
        'Fine Weather' => 'Buen tiempo',
        'Fluorescent' => 'Fluorescente',
        'ISO Studio Tungsten' => 'Tungsteno estudio ISO',
        'Other' => 'Otras Fuentes Luz',
        'Shade' => 'Sombrío',
        'Standard Light A' => 'Luz Estándar A',
        'Standard Light B' => 'Luz Estándar B',
        'Standard Light C' => 'Luz Estándar C',
        'Tungsten (Incandescent)' => 'Tungsteno (luz incandescente)',
        'Unknown' => 'Desconocido',
        'Warm White Fluorescent' => 'Fluorescente blanco cálido (L 2600 - 3250K)',
        'White Fluorescent' => 'Fluorescente blanco (WW 3250 - 3800K)',
      },
    },
   'LightSourceSpecial' => {
      Description => 'Fuente Luz Especial',
      PrintConv => {
        'Off' => 'Apagado',
        'On' => 'Encendido',
      },
    },
   'Lightness' => 'Luminosidad',
   'LinearResponseLimit' => 'Límite Respuesta Lineal',
   'LinearizationTable' => 'Tabla Linearización',
   'Lit' => {
      PrintConv => {
        'Yes' => 'Si',
      },
    },
   'LiveViewShooting' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'LocalizedCameraModel' => 'Modelo Cámara Traducido',
   'Location' => 'Localización',
   'LongExposureNoiseReduction' => {
      Description => 'RR Exp.Larga',
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'LookupTable' => 'Tabla de Consulta',
   'Luminance' => 'Luminancia',
   'LuminanceNoiseReduction' => {
      PrintConv => {
        'High' => 'Alto',
        'Low' => 'Bajo',
        'Off' => 'Desactivado',
      },
    },
   'Lyrics' => 'Letras',
   'Lyrics_Synchronised' => 'Letras Sincronizadas',
   'MSStereo' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'Macro' => {
      PrintConv => {
        'Off' => 'Apagado',
        'On' => 'Encendido',
        'View' => 'Vista',
      },
    },
   'MacroMode' => 'Modo Macro',
   'MainDialExposureComp' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'Make' => 'Marca',
   'MakeAndModel' => 'Marca y Modelo',
   'MakerNote' => 'Dato Privado DNG',
   'MakerNoteOffset' => 'Offset Maker Note',
   'MakerNoteSafety' => {
      Description => 'Seguridad Maker Note',
      PrintConv => {
        'Safe' => 'Seguro',
        'Unsafe' => 'No Seguro',
      },
    },
   'MakerNoteType' => 'Tipo Maker Note',
   'MakerNoteVersion' => 'Versión nota Fabricante',
   'MakerNotes' => 'Notas del Fabricante',
   'ManagedFromMaskMarkers' => {
      PrintConv => {
        'All' => 'Todo',
        'None' => 'Ninguno',
      },
    },
   'ManifestReferenceMaskMarkers' => {
      PrintConv => {
        'All' => 'Todo',
        'None' => 'Ninguno',
      },
    },
   'ManometerPressure' => 'Presión Manometrica',
   'ManometerReading' => 'Lectura Manometrica',
   'ManualFlashOutput' => {
      PrintConv => {
        'Full' => 'Completo',
        'Low' => 'Bajo',
        'Medium' => 'Medio',
        'n/a' => 'No Aplica',
      },
    },
   'ManualFocusDistance' => 'Distancia Enfoque Manual',
   'Marker' => 'Marcador',
   'MaskedAreas' => 'Área Oculta',
   'MasterDocumentID' => 'ID de Documento Maestro',
   'Matteing' => 'Mate',
   'MaxAperture' => 'Máxima Apertura del Objetivo',
   'MaxApertureAtMaxFocal' => 'Apertura máxima a focal máxima',
   'MaxApertureAtMinFocal' => 'Apertura máxima a focal mínima',
   'MaxApertureValue' => 'Apertura Lente Máxima',
   'MaxFaces' => 'Máximo caras',
   'MaxFocalLength' => 'Longitud focal máxima',
   'MaxPacketSize' => 'Tamaño Máximo Paquete',
   'MaxSampleValue' => 'Valor Muestra Max',
   'MaximumDensityRange' => 'Rango Densidad Maxima',
   'Measurement' => 'Observador de Medida',
   'MeasurementBacking' => 'Apoyo de Medida',
   'MeasurementFlare' => 'Llama de Medida',
   'MeasurementGeometry' => {
      Description => 'Geometría de Medida',
      PrintConv => {
        '0/45 or 45/0' => '0/45 o 45/0',
        '0/d or d/0' => '0/d o d/0',
      },
    },
   'MeasurementIlluminant' => 'Iluminación de Medida',
   'MeasurementObserver' => 'Observador de Medida',
   'MediaBlackPoint' => 'Punto Negro Medio',
   'MediaWhitePoint' => 'Punto Blanco Medio',
   'MeteringMode' => {
      Description => 'Modo Medición',
      PrintConv => {
        'Average' => 'Promedio',
        'Center-weighted average' => 'Media ponderada al centro',
        'Multi-segment' => 'Multi-segmento',
        'Multi-spot' => 'Multi-puntual',
        'Other' => 'Otro',
        'Partial' => 'Parcial',
        'Spot' => 'Puntual',
        'Unknown' => 'Desconocido',
      },
    },
   'MinAperture' => 'Apertura mínima',
   'MinFocalLength' => 'Longitud focal mínima',
   'MinPacketSize' => 'Tamaño Mínimo Paquete',
   'MinSampleValue' => 'Valor Muestra Min',
   'MinoltaQuality' => {
      PrintConv => {
        'Standard' => 'Estándar',
      },
    },
   'ModeDialPosition' => {
      PrintConv => {
        'Panorama' => 'Panoramica',
      },
    },
   'Model' => 'Modelo',
   'Model2' => 'Modelo Equipamiento Entrada Imagen (2)',
   'ModelReleaseStatus' => {
      PrintConv => {
        'None' => 'Ninguno',
      },
    },
   'ModelTiePoint' => 'Etiqueta Modelo Punto Lazo',
   'ModelTransform' => 'Etiqueta Modelo Transformación',
   'ModelingFlash' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'ModifiedBy' => 'Modificado por',
   'ModifiedPictureStyle' => {
      PrintConv => {
        'High Saturation' => 'Saturación Alta',
        'Landscape' => 'Paisaje',
        'Low Saturation' => 'Saturación Baja',
        'Monochrome' => 'Monocromo',
        'Neutral' => 'Neutro',
        'None' => 'Ninguno',
        'Portrait' => 'Retrato',
        'Standard' => 'Estándar',
      },
    },
   'ModifiedSharpnessFreq' => {
      PrintConv => {
        'High' => 'Alto',
        'Low' => 'Bajo',
        'Standard' => 'Estándar',
      },
    },
   'ModifiedToneCurve' => {
      PrintConv => {
        'Standard' => 'Estándar',
      },
    },
   'ModifiedWhiteBalance' => {
      PrintConv => {
        'Underwater' => 'Subacuatica',
      },
    },
   'ModifyDate' => 'Fecha y Hora de Cambio del Archivo',
   'MoireFilter' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'MonochromeFilterEffect' => {
      PrintConv => {
        'Green' => 'Verde',
        'None' => 'Ninguno',
        'Orange' => 'Naranja',
        'Red' => 'Rojo',
        'Yellow' => 'Amarillo',
      },
    },
   'MonochromeLinear' => {
      PrintConv => {
        'Yes' => 'Si',
      },
    },
   'MonochromeToning' => {
      PrintConv => {
        'None' => 'Ninguno',
      },
    },
   'MonochromeToningEffect' => {
      PrintConv => {
        'Green' => 'Verde',
        'None' => 'Ninguno',
      },
    },
   'MultiExposureAutoGain' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'MultiExposureMode' => {
      PrintConv => {
        'Off' => 'Desactivado',
      },
    },
   'MultiFrameNoiseReduction' => {
      Description => 'Reduc. ruido varios fotogr.',
      PrintConv => {
        'None' => 'Ninguno',
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'MultipleExposureMode' => 'Modo Exposición Múltiple',
   'MultipleExposureSet' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'Mute' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'MyColorMode' => {
      PrintConv => {
        'Neutral' => 'Neutro',
        'Off' => 'Desactivado',
        'Vivid' => 'Vívido',
      },
    },
   'NDFilter' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'NSC_Description' => 'Descripción NSC',
   'Name' => 'Nombre',
   'NamedColor2' => 'Color Llamado 2',
   'NativeDisplayInfo' => 'Información Pantalla Nativa',
   'NewsPhotoVersion' => 'Versión Registro Foto Noticias',
   'Noise' => 'Ruido',
   'NoiseFilter' => {
      PrintConv => {
        'High' => 'Alto',
        'Low' => 'Bajo',
        'Off' => 'Desactivado',
        'Standard' => 'Estándar',
      },
    },
   'NoiseReduction' => {
      Description => 'Reducción Ruido',
      PrintConv => {
        'Low' => 'Bajo',
        'Off' => 'Desactivado',
        'Standard' => 'Estándar',
      },
    },
   'NoiseReductionApplied' => 'Reducción Ruido Aplicada',
   'NominalMaxAperture' => 'Apertura máxima nominal',
   'NominalMinAperture' => 'Apertura mínima nominal',
   'NumAFPoints' => 'Número de Puntos AF',
   'NumChannels' => 'Número Canales',
   'NumColors' => 'Número de colores',
   'NumImportantColors' => 'Número Colores Importantes',
   'NumIndexEntries' => 'Número de Entradas de Índice',
   'NumSampleFrames' => 'Número de fotogramas',
   'NumberOfFrames' => 'Número de imágenes',
   'NumberofInks' => 'Número de Tintas',
   'OPIProxy' => 'Proxy OPI',
   'ObjectAttributeReference' => 'Género Intelectual',
   'ObjectCycle' => {
      Description => 'Ciclo Objecto',
      PrintConv => {
        'Both Morning and Evening' => 'Ambos',
        'Evening' => 'Tarde',
        'Morning' => 'Mañana',
      },
    },
   'ObjectFileType' => {
      PrintConv => {
        'None' => 'Ninguno',
        'Unknown' => 'Desconocido',
      },
    },
   'ObjectName' => 'Título',
   'ObjectPreviewData' => 'Datos Previos del Objecto',
   'ObjectPreviewFileFormat' => 'Formato Archivo Previo de Objecto',
   'ObjectPreviewFileVersion' => 'Versión Formato Archivo Previo del Objecto',
   'ObjectTypeReference' => 'Referencia Tipo Objeto',
   'OffsetSchema' => 'Offset Esquema',
   'OperatingSystem' => {
      Description => 'Sistema Operativo',
      PrintConv => {
        'unknown' => 'desconocido',
      },
    },
   'OpticalZoomMode' => {
      PrintConv => {
        'Standard' => 'Estándar',
      },
    },
   'OpticalZoomOn' => {
      Description => 'Zoom Óptico Encendido',
      PrintConv => {
        'Off' => 'Apagado',
        'On' => 'Encendido',
      },
    },
   'Opto-ElectricConvFactor' => 'Factor Conversión Optoeléctrico',
   'Orientation' => {
      Description => 'Orientación de Imagen',
      PrintConv => {
        'Horizontal (normal)' => '0° (arriba/izquierda)',
        'Mirror horizontal' => 'Invertir horizontal',
        'Mirror horizontal and rotate 270 CW' => 'Invertir horizontal y rotar 270° sentido reloj',
        'Mirror horizontal and rotate 90 CW' => 'Invertir horizontal y rotar 90° sentido reloj',
        'Mirror vertical' => 'Invertir vertical',
        'Rotate 180' => 'Girado 180°',
        'Rotate 270 CW' => 'Girado 270° sentido reloj',
        'Rotate 90 CW' => 'Girado 90° sentido reloj',
      },
    },
   'OriginPlatform' => {
      PrintConv => {
        'Other' => 'Otro',
      },
    },
   'OriginalAlbumTitle' => 'Título Original Album',
   'OriginalArtist' => 'Artista original',
   'OriginalFileName' => 'Nombre archivo original',
   'OriginalLyricist' => 'Letrista Original',
   'OriginalRawFileData' => 'Dato Archivo Raw Original',
   'OriginalRawFileDigest' => 'Cifrado Archivo Raw Original',
   'OriginalRawFileName' => 'Nombre Archivo Raw Original',
   'OriginalReleaseYear' => 'Año Versión Original',
   'OriginalTransmissionReference' => 'Identificador de Trabajo',
   'OriginatingProgram' => 'Programa Originario',
   'OutputResponse' => 'Respuesta Salida',
   'Owner' => 'Propietario',
   'OwnerID' => 'ID del Propietario',
   'OwnerName' => 'Nombre del Propietario',
   'PF25ColorMatrix' => 'Matriz de Color PF25',
   'PackingMethod' => {
      PrintConv => {
        'Best Compression' => 'Mejor Compresión',
        'Fast' => 'Rápido',
        'Fastest' => 'Mas Rápido',
        'Good Compression' => 'Buena Compresión',
        'Stored' => 'Almacenado',
      },
    },
   'Padding' => 'Margen Inferior',
   'PageName' => 'Nombre Página',
   'PageNumber' => 'Número Página',
   'PanoramaSize3D' => {
      PrintConv => {
        'Standard' => 'Estándar',
      },
    },
   'Period' => 'Período',
   'PhotoEffect' => {
      PrintConv => {
        'B&W' => 'Blanco y Negro',
        'Custom' => 'Personalizado',
        'Neutral' => 'Neutro',
        'Off' => 'Desactivado',
        'Vivid' => 'Vívido',
      },
    },
   'PhotoEffects' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'PhotoEffectsType' => {
      PrintConv => {
        'None' => 'Ninguno',
      },
    },
   'PhotometricInterpretation' => {
      Description => 'Interpretación Fotométrica',
      PrintConv => {
        'BlackIsZero' => 'Negro es cero',
        'Color Filter Array' => 'CFA (Matriz Filtro Color)',
        'Pixar LogL' => 'CIE Log2(L) (Log luminancia)',
        'Pixar LogLuv' => 'CIE Log2(L)(u\',v\') (Log luminancia y crominancia)',
        'RGB Palette' => 'Paleta Color',
        'Transparency Mask' => 'Máscara de transparencia',
        'WhiteIsZero' => 'Blanco es cero',
      },
    },
   'PhotoshopAnnotations' => 'Anotaciones Photoshop',
   'PhotoshopFormat' => {
      PrintConv => {
        'Standard' => 'Estándar',
      },
    },
   'PictInfo' => 'Info Imagen',
   'Picture' => 'Imágen',
   'PictureControl' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'PictureControlActive' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'PictureDescription' => 'Descripción Imágen',
   'PictureFinish' => {
      PrintConv => {
        'Monochrome' => 'Monocromo',
        'Portrait' => 'Retrato',
      },
    },
   'PictureMode' => {
      PrintConv => {
        'Beach' => 'Playa',
        'Fireworks' => 'Fuegos Artificiales',
        'Food' => 'Comida',
        'Green' => 'Verde',
        'Landscape' => 'Paisaje',
        'Panorama' => 'Panoramica',
        'Portrait' => 'Retrato',
        'Red' => 'Rojo',
        'Snow' => 'Nieve',
        'Soft' => 'Suave',
        'Standard' => 'Estándar',
        'Underwater' => 'Subacuatica',
        'Vivid' => 'Vívido',
        'Yellow' => 'Amarillo',
      },
    },
   'PictureModeBWFilter' => {
      PrintConv => {
        'Green' => 'Verde',
        'Neutral' => 'Neutro',
        'Orange' => 'Naranja',
        'Red' => 'Rojo',
        'Yellow' => 'Amarillo',
      },
    },
   'PictureModeEffect' => {
      PrintConv => {
        'High' => 'Alto',
        'Low' => 'Bajo',
        'Standard' => 'Estándar',
      },
    },
   'PictureModeTone' => {
      PrintConv => {
        'Green' => 'Verde',
        'Neutral' => 'Neutro',
      },
    },
   'PictureStyle' => {
      PrintConv => {
        'Faithful' => 'Fiel',
        'High Saturation' => 'Saturación Alta',
        'Landscape' => 'Paisaje',
        'Low Saturation' => 'Saturación Baja',
        'Monochrome' => 'Monocromo',
        'Neutral' => 'Neutro',
        'None' => 'Ninguno',
        'Portrait' => 'Retrato',
        'Standard' => 'Estándar',
      },
    },
   'PictureType' => {
      Description => 'Tipo Imágen',
      PrintConv => {
        '32x32 PNG Icon' => 'Icono PNG 32x32',
        'Artist' => 'Artista',
        'Back Cover' => 'Cubierta Posterior',
        'Composer' => 'Compositor',
        'Conductor' => 'Director',
        'Front Cover' => 'Cubierta Frontal',
        'Illustration' => 'Ilustración',
        'Lyricist' => 'Letrista',
        'Media' => 'Soporte',
        'Other' => 'Otro',
        'Other Icon' => 'Otro Icono',
        'Performance' => 'Interpretación',
        'Recording Session' => 'Sesión Grabación',
      },
    },
   'PictureWizardMode' => {
      PrintConv => {
        'Cool' => 'Frío',
        'Landscape' => 'Paisaje',
        'Portrait' => 'Retrato',
        'Standard' => 'Estándar',
        'Vivid' => 'Vívido',
      },
    },
   'PixelFormat' => 'Formato Pixel',
   'PixelIntensityRange' => 'Intervalo Intensidad Pixel',
   'PixelScale' => 'Etiqueta Escala Pixel Modelo',
   'PlanarConfiguration' => {
      Description => 'Ajuste Datos Imagen',
      PrintConv => {
        'Chunky' => 'Formato \'Chunky\' (Entrelazado)',
        'Planar' => 'Formato \'Planar\'',
      },
    },
   'Predictor' => {
      PrintConv => {
        'Horizontal differencing' => 'Diferenciación Horizontal',
        'None' => 'No se usa esquema de predicción antes de codificar',
      },
    },
   'Preview0' => 'Previa 0',
   'Preview1' => 'Previa 1',
   'Preview2' => 'Previa 2',
   'PreviewApplicationName' => 'Nombre Aplicación Previa',
   'PreviewApplicationVersion' => 'Versión Aplicación Previa',
   'PreviewButton' => {
      PrintConv => {
        'None' => 'Ninguno',
      },
    },
   'PreviewButtonPlusDials' => {
      PrintConv => {
        'None' => 'Ninguno',
      },
    },
   'PreviewColorSpace' => {
      Description => 'Espacio Color Previa',
      PrintConv => {
        'Unknown' => 'Desconocido',
      },
    },
   'PreviewDateTime' => 'Fecha y Hora Previa',
   'PreviewImage' => 'Vista Previa',
   'PreviewImageLength' => 'Longitud Imagen Previa',
   'PreviewImageSize' => 'Tamaño Imagen Previa',
   'PreviewImageStart' => 'Inicio Imagen Previa',
   'PreviewImageValid' => {
      PrintConv => {
        'Yes' => 'Si',
      },
    },
   'PreviewSettingsDigest' => 'Cifrado Configuración Previa',
   'PreviewSettingsName' => 'Nombre Configuración Previa',
   'PrimaryAFPoint' => 'Punto AF primario',
   'PrimaryChromaticities' => 'Cromaticidades de Colores Primarios',
   'PrimaryPlatform' => 'Plataforma Primaria',
   'ProcessingSoftware' => 'Tratamiendo de Software',
   'Producer' => 'Productor',
   'ProductID' => 'ID Producto',
   'ProfileCMMType' => 'Tipo Perfil CMM',
   'ProfileCalibrationSig' => 'Firma Perfil Calibración',
   'ProfileClass' => {
      Description => 'Clase Perfil',
      PrintConv => {
        'Abstract Profile' => 'Perfil Abstracto',
        'ColorSpace Conversion Profile' => 'Perfil Conversión Espacio Color',
        'DeviceLink Profile' => 'Perfil Dispositivo Conexión',
        'Display Device Profile' => 'Perfil Dispositivo Pantalla',
        'Input Device Profile' => 'Perfil Dispositivo Entrada',
        'NamedColor Profile' => 'Perfil Color Nombrado',
        'Nikon Input Device Profile (NON-STANDARD!)' => 'Perfil Nikon ("nkpf")',
        'Output Device Profile' => 'Perfil Dispositivo Salida',
      },
    },
   'ProfileConnectionSpace' => 'Espacio Conexión Perfil',
   'ProfileCopyright' => 'Copyright',
   'ProfileCreator' => 'Creador Perfil',
   'ProfileDateTime' => 'Fecha y Hora Perfil',
   'ProfileDescription' => 'Descripción Perfil',
   'ProfileDescriptionML' => 'Descripción Perfil ML',
   'ProfileEmbedPolicy' => {
      Description => 'Perfil Política Incrustada',
      PrintConv => {
        'Allow Copying' => 'Permitir copia',
        'Embed if Used' => 'Incrustar si se usa',
        'Never Embed' => 'Incrustado nunca',
        'No Restrictions' => 'Sin restricciones',
      },
    },
   'ProfileFileSignature' => 'Firma Archivo Perfil',
   'ProfileHueSatMapData1' => 'Perfil Matiz Sat. Mapa Dato 1',
   'ProfileHueSatMapData2' => 'Perfil Matiz Sat. Mapa Dato 2',
   'ProfileHueSatMapDims' => 'Divisiones Matiz',
   'ProfileID' => 'ID Perfil',
   'ProfileLookTableData' => 'Perfil Datos Tabla Consulta',
   'ProfileLookTableDims' => 'Divisiones Matiz',
   'ProfileName' => 'Nombre Perfil',
   'ProfileSequenceDesc' => 'Descripción Secuencia Perfil',
   'ProfileToneCurve' => 'Perfil Curva Tono',
   'ProfileVersion' => 'Versión Perfil',
   'ProgramMode' => {
      PrintConv => {
        'None' => 'Ninguno',
        'Portrait' => 'Retrato',
      },
    },
   'ProgramVersion' => 'Versión Programa',
   'Projects' => 'Proyectos',
   'PromotionURL' => 'URL Promocional',
   'PropertyReleaseStatus' => {
      PrintConv => {
        'None' => 'Ninguno',
      },
    },
   'Protect' => 'Protección',
   'ProtectionType' => 'Tipo Protección',
   'Provider' => 'Proveedor',
   'ProviderCopyright' => 'Copyright Proveedor',
   'Province-State' => 'Provincia/Estado',
   'Publisher' => 'Editor',
   'Quality' => {
      Description => 'Calidad',
      PrintConv => {
        'Compressed RAW' => 'cRAW',
        'Compressed RAW + JPEG' => 'cRAW+JPEG',
        'Extra Fine' => 'Extrafina',
        'Fine' => 'Fina',
        'High' => 'Alto',
        'Low' => 'Bajo',
        'Normal' => 'Calidad estándar',
        'RAW + JPEG' => 'RAW+JPEG',
        'Standard' => 'Estándar',
        'n/a' => 'no aplica',
      },
    },
   'QuantizationMethod' => {
      Description => 'Método Cuantización',
      PrintConv => {
        'AP Domestic Analogue' => 'AP Doméstico Análogo',
        'Color Space Specific' => 'Espacio Color Específico',
        'Compression Method Specific' => 'Método Compresión Específico',
        'Gamma Compensated' => 'Gamma Compensada',
        'IPTC Ref B' => 'IPTC ref "B"',
        'Linear Density' => 'Densidad lineal',
        'Linear Dot Percent' => 'Porcentaje Punto lineal',
        'Linear Reflectance/Transmittance' => 'Reflectancia/transmitancia lineal',
      },
    },
   'QuickShot' => {
      Description => 'Disparo Rápido',
      PrintConv => {
        'Off' => 'Apagado',
        'On' => 'Encendido',
      },
    },
   'RadioStationName' => 'Nombre Emisora Radio',
   'RadioStationOwner' => 'Propietario Emisora Radio',
   'RasterPadding' => 'Relleno Trama',
   'RasterizedCaption' => 'Título Rasterizado',
   'Rating' => {
      Description => 'Clasificación',
      PrintConv => {
        'none' => 'Ninguno',
      },
    },
   'RatingPercent' => 'Valoración en Porcentaje',
   'RawCustomSaturation' => 'Raw Saturación personalizada',
   'RawDataUniqueID' => 'ID Único Dato Raw',
   'RawDevAutoGradation' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'RawDevNoiseReduction' => {
      PrintConv => {
        'Noise Filter' => 'Filtro ruido',
        'Noise Filter (ISO Boost)' => 'Filtro ruido (ISO Boost)',
        'Noise Reduction' => 'Reducción ruido',
      },
    },
   'RawDevPMPictureTone' => {
      PrintConv => {
        'Green' => 'Verde',
        'Neutral' => 'Neutro',
      },
    },
   'RawDevPM_BWFilter' => {
      PrintConv => {
        'Green' => 'Verde',
        'Neutral' => 'Neutro',
        'Orange' => 'Naranja',
        'Red' => 'Rojo',
        'Yellow' => 'Amarillo',
      },
    },
   'RawDevPictureMode' => {
      PrintConv => {
        'Vivid' => 'Vívido',
      },
    },
   'RawDevSettings' => {
      PrintConv => {
        'Noise Reduction' => 'Reducción ruido',
      },
    },
   'RawImageDigest' => 'Cifrado Imagen RAW',
   'RawJpgSize' => {
      PrintConv => {
        'Medium' => 'Medio',
        'Medium 1' => 'Medio 1 ',
        'Medium 2' => 'Medio 2',
        'Medium 3' => 'Medio 3',
        'Postcard' => 'Tarjeta Postal',
        'Small 1' => 'Pequeño 1',
        'Small 2' => 'Pequeño 21',
        'Small 3' => 'Pequeño 3',
      },
    },
   'RecordMode' => 'Modo de Grabación',
   'RecordShutterRelease' => {
      Description => 'Soltar Obturador Grabación',
      PrintConv => {
        'Press start, press stop' => 'Pulsa para iniciar, pulsa para parar',
        'Record while down' => 'Grabar mientras se pulsa',
      },
    },
   'RecordedTrackNumber' => 'Número Pista grabada',
   'RecordingMode' => {
      PrintConv => {
        'Panorama' => 'Panoramica',
        'Portrait' => 'Retrato',
      },
    },
   'RedEyeCorrection' => {
      PrintConv => {
        'Off' => 'Desactivado',
      },
    },
   'RedEyeReduction' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'RedMatrixColumn' => 'Columna Matriz Rojo',
   'RedTRC' => 'Curva Reproducción Tono Rojo',
   'ReductionMatrix1' => 'Matriz Reducción 1',
   'ReductionMatrix2' => 'Matriz Reducción 2',
   'ReferenceBlackWhite' => 'Par de Valores de Referencia Blanco y Negro',
   'ReferenceDate' => 'Fecha Referencia',
   'ReferenceNumber' => 'Número Referencia',
   'ReferenceService' => 'Servicio Referencia',
   'RelatedImageFileFormat' => 'Formato Archivo Imagen Relacionado',
   'RelatedImageHeight' => 'Alto Imagen Relacionada',
   'RelatedImageWidth' => 'Ancho Imagen Relacionada',
   'RelatedSoundFile' => 'Archivo Audio Relacionado',
   'ReleaseButtonToUseDial' => {
      PrintConv => {
        'Yes' => 'Si',
      },
    },
   'ReleaseDate' => 'Fecha Lanzamiento',
   'ReleaseTime' => 'Hora Lanzamiento',
   'RenderingIntent' => {
      Description => 'Intento Interpretación',
      PrintConv => {
        'ICC-Absolute Colorimetric' => 'Colorimétrica Absoluta',
        'Media-Relative Colorimetric' => 'Colorimétrica Relativa',
        'Saturation' => 'Saturación',
      },
    },
   'RenditionOfMaskMarkers' => {
      PrintConv => {
        'All' => 'Todo',
        'None' => 'Ninguno',
      },
    },
   'ResampleParamsQuality' => {
      PrintConv => {
        'High' => 'Alto',
        'Low' => 'Bajo',
      },
    },
   'Resaved' => {
      Description => 'Regrabado',
      PrintConv => {
        'Yes' => 'Si',
      },
    },
   'Reserved1' => 'Reservado 1',
   'Resolution' => 'Resolución',
   'ResolutionMode' => 'Modo Resolución',
   'ResolutionUnit' => {
      Description => 'Unidad de Resolución de X e Y',
      PrintConv => {
        'None' => 'Ninguno',
        'cm' => 'Píxeles/cm',
        'inches' => 'Pulgada',
      },
    },
   'RetouchHistory' => {
      PrintConv => {
        'None' => 'Ninguno',
      },
    },
   'RevisionNumber' => 'Número Revisión',
   'Rotation' => {
      Description => 'Rotación',
      PrintConv => {
        'Rotate 180' => 'Girado 180°',
        'Rotate 270 CW' => 'Girado 270° sentido reloj',
        'Rotate 90 CW' => 'Girado 90° sentido reloj',
        'Rotated 180' => 'Girado 180°',
        'Rotated 270 CW' => 'Girado 270° sentido reloj',
        'Rotated 90 CW' => 'Girado 90° sentido reloj',
      },
    },
   'RowInterleaveFactor' => 'Factor Interpolar Fila',
   'RowsPerStrip' => 'Número de Filas por Tira',
   'SMaxSampleValue' => 'Valor Muestra Max S',
   'SMinSampleValue' => 'Valor Muestra Min S',
   'SRAWQuality' => {
      PrintConv => {
        'n/a' => 'No Aplica',
      },
    },
   'SRActive' => {
      PrintConv => {
        'Yes' => 'Si',
      },
    },
   'SampleFormat' => 'Formato Muestra',
   'SampleRate' => 'Frecuencia muestreo',
   'SampleSize' => 'Tamaño muestra',
   'SampleStructure' => {
      Description => 'Estructura Muestreo',
      PrintConv => {
        'CompressionDependent' => 'Definido dentro del proceso de compresión',
        'Orthogonal4-2-2Sampling' => 'Ortogonal con las frecuencias de muestreo en el ratio de 4:2:2:(4)',
        'OrthogonalConstangSampling' => 'Ortogonal con la mismas frecuencias de muestreo relativo en cada componente',
      },
    },
   'SamplesPerPixel' => 'Número de Componentes',
   'SanyoQuality' => {
      Description => 'Calidad Sanyo',
      PrintConv => {
        'Fine/High' => 'Fino/Alto',
        'Fine/Low' => 'Fino/Bajo',
        'Fine/Medium' => 'Fino/Medio',
        'Fine/Medium High' => 'Fino/Medio Alto',
        'Fine/Medium Low' => 'Fino/Medio bajo',
        'Fine/Super High' => 'Fino/Super Alto',
        'Fine/Very High' => 'Fino/Muy Alto',
        'Fine/Very Low' => 'Fino/Muy bajo',
        'Normal/High' => 'Normal/Alto',
        'Normal/Low' => 'Normal/Bajo',
        'Normal/Medium' => 'Normal/Medio',
        'Normal/Medium High' => 'Normal/Medio Alto',
        'Normal/Medium Low' => 'Normal/Medio bajo',
        'Normal/Super High' => 'Normal/Super Alto',
        'Normal/Very High' => 'Normal/Muy Alto',
        'Normal/Very Low' => 'Normal/Muy bajo',
        'Super Fine/High' => 'Super Fino/Alto',
        'Super Fine/Low' => 'Super Fino/Bajo',
        'Super Fine/Medium' => 'Super Fino/Medio',
        'Super Fine/Medium High' => 'Super Fino/Medio Alto',
        'Super Fine/Medium Low' => 'Super Fino/Medio Bajo',
        'Super Fine/Super High' => 'Super Fino/Super Alto',
        'Super Fine/Very High' => 'Super Fino/Muy Alto',
        'Super Fine/Very Low' => 'Super Fino/Muy Bajo',
      },
    },
   'SanyoThumbnail' => 'Miniatura Sanyo',
   'Saturation' => {
      Description => 'Saturación',
      PrintConv => {
        'High' => 'Alto',
        'Low' => 'Bajo',
        'None' => 'Ninguno',
        'None (B&W)' => 'Ninguna (N&B)',
        'Normal' => 'Estándar',
        'Vivid' => 'Vívido',
      },
    },
   'ScanImageEnhancer' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'ScanningDirection' => {
      Description => 'Dirección Escaneo',
      PrintConv => {
        'Bottom-Top, L-R' => 'Abajo a arriba, izquierda a derecha',
        'Bottom-Top, R-L' => 'Abajo a arriba, derecha a izquierda',
        'L-R, Bottom-Top' => 'Izquierda a derecha, abajo a arriba',
        'L-R, Top-Bottom' => 'Izquierda a derecha, arriba a abajo',
        'R-L, Bottom-Top' => 'Derecha a izquierda, abajo a arriba',
        'R-L, Top-Bottom' => 'Derecha a izquierda, arriba a abajo',
        'Top-Bottom, L-R' => 'Arriba a abajo, izquierda a derecha',
        'Top-Bottom, R-L' => 'Arriba a abajo, derecha a izquierda',
      },
    },
   'Scene' => 'Escena',
   'SceneCaptureType' => {
      Description => 'Tipo Captura Escena',
      PrintConv => {
        'Landscape' => 'Paisaje',
        'Night' => 'Escena nocturna',
        'Portrait' => 'Retrato',
        'Standard' => 'Estándar',
      },
    },
   'SceneMode' => {
      Description => 'Selección de escena',
      PrintConv => {
        '3D Sweep Panorama' => '3D',
        'Anti Motion Blur' => 'Anti movimiento',
        'Beach' => 'Playa',
        'Cont. Priority AE' => 'AE prioridad cont.',
        'Fireworks' => 'Fuegos Artificiales',
        'Food' => 'Comida',
        'Handheld Night Shot' => 'Toma noct. manual',
        'Indoor' => 'Interior',
        'Landscape' => 'Paisaje',
        'Night Portrait' => 'Retrato noct.',
        'Night Scene' => 'Vista nocturna',
        'Night View/Portrait' => 'Vista/retrato nocturno',
        'Off' => 'Desactivado',
        'Panorama' => 'Panoramica',
        'Portrait' => 'Retrato',
        'Snow' => 'Nieve',
        'Sports' => 'Acción deportiva',
        'Standard' => 'Estándar',
        'Sunset' => 'Puesta sol',
        'Sweep Panorama' => 'Barrido panorámico',
        'Underwater' => 'Subacuatica',
        'Vivid' => 'Vívido',
      },
    },
   'SceneModeUsed' => {
      PrintConv => {
        'Beach' => 'Playa',
        'Fireworks' => 'Fuegos Artificiales',
        'Landscape' => 'Paisaje',
        'Panorama' => 'Panoramica',
        'Portrait' => 'Retrato',
        'Snow' => 'Nieve',
      },
    },
   'SceneSelect' => {
      Description => 'Selección Escena',
      PrintConv => {
        'Lamp' => 'Lámpara',
        'Night' => 'Noche',
        'Off' => 'Apagado',
        'Sport' => 'Deporte',
        'User 1' => 'Usuario 1',
        'User 2' => 'Usuario 2',
      },
    },
   'SceneType' => {
      Description => 'Tipo Escena',
      PrintConv => {
        'Directly photographed' => 'Imagen fotografiada directamente',
      },
    },
   'Security' => {
      PrintConv => {
        'None' => 'Ninguno',
      },
    },
   'SecurityClassification' => {
      Description => 'Clasificación Seguridad',
      PrintConv => {
        'Confidential' => 'Confidencial',
        'Restricted' => 'Restringida',
        'Secret' => 'Secreta',
        'Top Secret' => 'Alto secreto',
        'Unclassified' => 'Sin clasificar',
      },
    },
   'SelectableAFPoint' => 'Punto AF seleccionable',
   'SelfTimer' => {
      Description => 'Temporizador Automático',
      PrintConv => {
        'Off' => 'Apagado',
        'On' => 'Encendido',
      },
    },
   'SelfTimerMode' => 'Modo Automático',
   'SensingMethod' => {
      Description => 'Método Sensor',
      PrintConv => {
        'Color sequential area' => 'Sensor de color secuencial',
        'Color sequential linear' => 'Sensor lineal de color secuencial',
        'Monochrome area' => 'Sensor monocromo',
        'Monochrome linear' => 'Sensor lineal monocromo',
        'Not defined' => 'No definido',
        'One-chip color area' => 'Sensor monochip de color',
        'Three-chip color area' => 'Sensor tres chips de color',
        'Trilinear' => 'Sensor trilineal',
        'Two-chip color area' => 'Sensor bichip de color',
      },
    },
   'SensitivityType' => {
      PrintConv => {
        'Unknown' => 'Desconocido',
      },
    },
   'SequenceShotInterval' => {
      Description => 'Intervalo Disparo Secuencial',
      PrintConv => {
        '10 frames/s' => '10 cuadros/s',
        '15 frames/s' => '15 cuadros/s',
        '20 frames/s' => '20 cuadros/s',
        '5 frames/s' => '5 cuadros/s',
      },
    },
   'SequentialShot' => {
      Description => 'Disparo Secuencial',
      PrintConv => {
        'Adjust Exposure' => 'Ajustar Exposición',
        'Best' => 'Mejor',
        'None' => 'Ninguno',
        'Standard' => 'Estándar',
      },
    },
   'SerialNumber' => 'Número Serie',
   'ServiceIdentifier' => 'Identificador Servicio',
   'ShadingCompensation' => 'Compensación de Sombreado',
   'ShadingCompensation2' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'Shadow' => 'Sombrío',
   'ShadowScale' => 'Escala Sombrío',
   'ShakeReduction' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'Sharpening' => {
      PrintConv => {
        'High' => 'Alto',
        'Low' => 'Bajo',
      },
    },
   'Sharpness' => {
      Description => 'Nitidez',
      PrintConv => {
        'Hard' => 'Fuerte',
        'Normal' => 'Estándar',
        'Sharp' => 'Nitido',
        'Soft' => 'Suave',
      },
    },
   'SharpnessFrequency' => {
      PrintConv => {
        'High' => 'Alto',
        'Highest' => 'Muy Alto',
        'Low' => 'Bajo',
        'Lowest' => 'Mas bajo',
        'Standard' => 'Estándar',
        'n/a' => 'No Aplica',
      },
    },
   'ShootingMode' => {
      Description => 'Modo de Disparo',
      PrintConv => {
        'Beach' => 'Playa',
        'Fireworks' => 'Fuegos Artificiales',
        'Food' => 'Comida',
        'Portrait' => 'Retrato',
        'Snow' => 'Nieve',
        'Underwater' => 'Subacuatica',
      },
    },
   'ShortDocumentID' => 'ID Corta del Documento',
   'Shutter-AELock' => 'Disparador Bloqueo AE',
   'ShutterCount' => 'Contador de disparos',
   'ShutterReleaseButtonAE-L' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'ShutterReleaseNoCFCard' => {
      PrintConv => {
        'Yes' => 'Si',
      },
    },
   'ShutterSpeed' => 'Tiempo de Exposición',
   'ShutterSpeedValue' => 'Velocidad Obturación',
   'Signature_Name' => 'Firma',
   'SimilarityIndex' => 'Índice de Similitudes',
   'SingleFrameBracketing' => {
      PrintConv => {
        'High' => 'Alto',
        'Low' => 'Bajo',
      },
    },
   'Site' => 'Sitio',
   'SlideShow' => {
      PrintConv => {
        'Yes' => 'Si',
      },
    },
   'SlowShutter' => {
      PrintConv => {
        'None' => 'Ninguno',
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'SoftSkinEffect' => {
      PrintConv => {
        'Low' => 'Bajo',
      },
    },
   'Software' => 'Programa Utilizado',
   'SoftwareVersion' => 'Versión Software',
   'SonyImageSize' => {
      PrintConv => {
        'Standard' => 'Estándar',
      },
    },
   'SonyQuality' => {
      PrintConv => {
        'Standard' => 'Estándar',
      },
    },
   'Source' => 'Fuente',
   'SpatialFrequencyResponse' => 'Respuesta Frecuencia Espacial',
   'SpecialEffectsOpticalFilter' => {
      PrintConv => {
        'None' => 'Ninguno',
      },
    },
   'SpecialInstructions' => 'Instrucciones',
   'SpecialMode' => 'Modo Especial',
   'SpectralSensitivity' => 'Sensibilidad Espectral',
   'Speed' => {
      PrintConv => {
        'Fast' => 'Rápido',
      },
    },
   'SpotMeteringMode' => {
      PrintConv => {
        'AF Point' => 'Punto AF',
        'Center' => 'Centro',
      },
    },
   'State' => 'Estado',
   'StreamColor' => {
      PrintConv => {
        'Monochrome' => 'Monocromo',
      },
    },
   'StreamType' => {
      PrintConv => {
        'Binary' => 'Binario',
        'File Transfer' => 'Transferencia Archivo',
      },
    },
   'StripByteCounts' => 'Bytes por Tira Comprimida',
   'StripOffsets' => 'Localización Datos Imagen',
   'Sub-location' => 'Localización',
   'SubSecTime' => 'Subsegundos DateTime',
   'SubSecTimeDigitized' => 'Subsegundos DateTimeDigitized',
   'SubSecTimeOriginal' => 'Subsegundos DateTimeOriginal',
   'SubTileBlockSize' => 'Tamaño Bloque Submosaico',
   'SubfileType' => 'Nuevo Tipo Subarchivo',
   'SubimageColor' => {
      PrintConv => {
        'Monochrome' => 'Monocromo',
      },
    },
   'Subject' => 'Sujeto',
   'SubjectArea' => 'Zona Sujeto',
   'SubjectDistance' => 'Distancia Sujeto',
   'SubjectDistanceRange' => {
      Description => 'Intervalo Distancia Sujeto',
      PrintConv => {
        'Close' => 'Vista cercana',
        'Distant' => 'Vista alejada',
        'Unknown' => 'Desconocido',
      },
    },
   'SubjectLocation' => 'Localización Sujeto',
   'SubjectProgram' => {
      PrintConv => {
        'None' => 'Ninguno',
        'Portrait' => 'Retrato',
      },
    },
   'SubjectReference' => 'Código Sujeto',
   'Subsystem' => {
      PrintConv => {
        'Unknown' => 'Desconocido',
      },
    },
   'Subtitle' => 'Subtitulo',
   'SubtitleDescription' => 'Descripción Subtitulo',
   'SuperimposedDisplay' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'SupplementalCategories' => 'Categoría Suplementaria',
   'SupplementalType' => {
      Description => 'Tipo Suplemento',
      PrintConv => {
        'Main Image' => 'No Definido',
        'Rasterized Caption' => 'Título Rasterizado',
        'Reduced Resolution Image' => 'Imagen resolución reducida',
      },
    },
   'SweepPanoramaSize' => {
      PrintConv => {
        'Standard' => 'Estándar',
      },
    },
   'SwitchToRegisteredAFPoint' => 'Conmutación de Punto AF registrado',
   'SynchronizedLyricsType' => {
      PrintConv => {
        'Other' => 'Otro',
      },
    },
   'T4Options' => 'Opciones T4',
   'T6Options' => 'Opciones T6',
   'TIFF-EPStandardID' => 'ID Estándar TIFF/EP',
   'Tagged' => {
      PrintConv => {
        'Yes' => 'Si',
      },
    },
   'TargetPrinter' => 'Impresora Objetivo',
   'Technology' => {
      Description => 'Tecnología',
      PrintConv => {
        'Active Matrix Display' => 'Pantalla Matriz Activa',
        'Cathode Ray Tube Display' => 'Pantalla Tubo Rayos Catódicos',
        'Digital Camera' => 'Cámara Digital',
        'Dye Sublimation Printer' => 'Impresora Sublimación',
        'Electrophotographic Printer' => 'Impresora Electrofotográfica (Laser)',
        'Electrostatic Printer' => 'Impresora Electrostática',
        'Film Scanner' => 'Escaner Película',
        'Film Writer' => 'Impresora Película',
        'Flexography' => 'Flexografía',
        'Gravure' => 'Grabado',
        'Ink Jet Printer' => 'Impresora Inyección Tinta',
        'Offset Lithography' => 'Litografía Offset',
        'Passive Matrix Display' => 'Pantalla Matriz Pasiva',
        'Photo Image Setter' => 'Marco Foto',
        'Photographic Paper Printer' => 'Impresora Papel Fotográfico',
        'Projection Television' => 'Televisión Proyección',
        'Reflective Scanner' => 'Escaner Reflectivo',
        'Silkscreen' => 'Pantalla Sedosa',
        'Thermal Wax Printer' => 'Impresora Cera Termal',
        'Video Camera' => 'Videocámara',
        'Video Monitor' => 'Monitor Video',
      },
    },
   'Teleconverter' => {
      PrintConv => {
        'None' => 'Ninguno',
      },
    },
   'Text' => 'Texto',
   'TextStamp' => {
      PrintConv => {
        'On' => 'Activado',
      },
    },
   'Thresholding' => 'Umbral',
   'ThumbnailImage' => 'Miniatura',
   'ThumbnailImageSize' => 'Tamaño de la Vista en Miniatura',
   'TileByteCounts' => 'Número Byte Elemento',
   'TileDepth' => 'Ancho Elemento',
   'TileLength' => 'Largo Elemento',
   'TileOffsets' => 'Offsets Elemento',
   'TileWidth' => 'Ancho Elemento',
   'TimeCreated' => 'Hora Creación',
   'TimeScaleParamsQuality' => {
      PrintConv => {
        'High' => 'Alto',
        'Low' => 'Bajo',
      },
    },
   'TimeSent' => 'Hora Envío',
   'TimeSignature' => {
      PrintConv => {
        'other' => 'Otro',
      },
    },
   'TimeStamp' => 'Marca de Tiempo',
   'TimeStamp1' => 'Marca de Tiempo 1',
   'TimeZoneOffset' => 'Offset Zona Horaria',
   'Title' => 'Título',
   'ToneCurve' => {
      PrintConv => {
        'Standard' => 'Estándar',
      },
    },
   'ToneCurveActive' => {
      PrintConv => {
        'Yes' => 'Si',
      },
    },
   'ToningEffect' => {
      PrintConv => {
        'Green' => 'Verde',
        'None' => 'Ninguno',
        'Red' => 'Rojo',
        'Yellow' => 'Amarillo',
      },
    },
   'ToningEffectMonochrome' => {
      PrintConv => {
        'Green' => 'Verde',
        'None' => 'Ninguno',
      },
    },
   'ToningEffectUnknown' => {
      PrintConv => {
        'Green' => 'Verde',
        'None' => 'Ninguno',
      },
    },
   'ToningEffectUserDef1' => {
      PrintConv => {
        'Green' => 'Verde',
        'None' => 'Ninguno',
      },
    },
   'ToningEffectUserDef2' => {
      PrintConv => {
        'Green' => 'Verde',
        'None' => 'Ninguno',
      },
    },
   'ToningEffectUserDef3' => {
      PrintConv => {
        'Green' => 'Verde',
        'None' => 'Ninguno',
      },
    },
   'ToolName' => 'Nombre Herramienta',
   'ToolVersion' => 'Versión Herramienta',
   'TotalFrames' => 'Total Imagenes',
   'Track' => 'Pista',
   'TrackCreateDate' => 'Fecha creación Track',
   'TrackDefault' => 'Pista Defecto',
   'TrackForced' => 'Pista Forzada',
   'TrackHeaderVersion' => 'Versión cabecera Pista',
   'TrackID' => 'ID Pista',
   'TrackName' => 'Nombre Pista',
   'TrackNumber' => 'Número Pista',
   'TrackType' => 'Tipo Pista',
   'TrackUsed' => 'Pista utilizada',
   'Tracks' => 'Pistas',
   'TransferFunction' => 'Función Transferencia',
   'TransferRange' => 'Intervalo Transferencia',
   'Transformation' => {
      Description => 'Transformación',
      PrintConv => {
        'Mirror horizontal' => 'Invertir horizontal',
        'Mirror horizontal and rotate 270 CW' => 'Invertir horizontal y rotar 270° sentido reloj',
        'Mirror horizontal and rotate 90 CW' => 'Invertir horizontal y rotar 90° sentido reloj',
        'Mirror vertical' => 'Invertir vertical',
        'Rotate 180' => 'Girado 180°',
        'Rotate 270 CW' => 'Girado 270° sentido reloj',
        'Rotate 90 CW' => 'Girado 90° sentido reloj',
      },
    },
   'TransmissionReference' => 'Referencia de Transmisión',
   'TransparencyIndicator' => 'Indicador Transparencia',
   'TrapIndicator' => 'Indicador Tampa',
   'Type' => 'Tipo',
   'Uncompressed' => {
      Description => 'Sin Comprimir',
      PrintConv => {
        'Yes' => 'Si',
      },
    },
   'UncompressedSize' => 'Tamaño Descomprimido',
   'UniqueCameraModel' => 'Modelo Cámara Unico',
   'UniqueDocumentID' => 'ID de Documento Única',
   'UniqueFileIdentifier' => 'Identificador Unico Archivo',
   'UniqueObjectName' => 'Nombre Único de Objeto',
   'Unknown' => 'Desconocido',
   'Unsharp1Color' => {
      PrintConv => {
        'Green' => 'Verde',
        'Red' => 'Rojo',
        'Yellow' => 'Amarillo',
      },
    },
   'Unsharp2Color' => {
      PrintConv => {
        'Green' => 'Verde',
        'Red' => 'Rojo',
        'Yellow' => 'Amarillo',
      },
    },
   'Unsharp3Color' => {
      PrintConv => {
        'Green' => 'Verde',
        'Red' => 'Rojo',
        'Yellow' => 'Amarillo',
      },
    },
   'Unsharp4Color' => {
      PrintConv => {
        'Green' => 'Verde',
        'Red' => 'Rojo',
        'Yellow' => 'Amarillo',
      },
    },
   'UnsharpMask' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'Urgency' => {
      Description => 'Urgencia',
      PrintConv => {
        '0 (reserved)' => '0 (reservado para futuro uso)',
        '1 (most urgent)' => '1 (más urgente)',
        '5 (normal urgency)' => '5 (urgencia normal)',
        '8 (least urgent)' => '8 (menos urgente)',
        '9 (user-defined priority)' => '9 (reservado para futuro uso)',
      },
    },
   'UserComment' => 'Comentarios Usuario',
   'UserDef1PictureStyle' => {
      PrintConv => {
        'Landscape' => 'Paisaje',
        'Monochrome' => 'Monocromo',
        'Neutral' => 'Neutro',
        'Portrait' => 'Retrato',
        'Standard' => 'Estándar',
      },
    },
   'UserDef2PictureStyle' => {
      PrintConv => {
        'Landscape' => 'Paisaje',
        'Monochrome' => 'Monocromo',
        'Neutral' => 'Neutro',
        'Portrait' => 'Retrato',
        'Standard' => 'Estándar',
      },
    },
   'UserDef3PictureStyle' => {
      PrintConv => {
        'Landscape' => 'Paisaje',
        'Monochrome' => 'Monocromo',
        'Neutral' => 'Neutro',
        'Portrait' => 'Retrato',
        'Standard' => 'Estándar',
      },
    },
   'VR_0x66' => {
      PrintConv => {
        'Off' => 'Desactivado',
      },
    },
   'ValidAFPoints' => 'Puntos AF validos',
   'Version' => 'Versión PrintIM',
   'VersionYear' => 'Año versión',
   'VibrationReduction' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'VideoAlphaMode' => {
      PrintConv => {
        'None' => 'Ninguno',
      },
    },
   'VideoCardGamma' => 'Tarjeta Video Gamma',
   'VideoCompressor' => 'Video Compresor',
   'VideoFieldOrder' => {
      PrintConv => {
        'Lower' => 'Inferior',
        'Progressive' => 'Progresivo',
        'Upper' => 'Superior',
      },
    },
   'VideoHeight' => 'Altura Video',
   'VideoPixelDepth' => {
      PrintConv => {
        '16-bit integer' => 'Entero 16-bits',
        '24-bit integer' => 'Entero 24-bits',
        '32-bit float' => 'Flotante 32-bits',
        '32-bit integer' => 'Entero 32-bits',
        '8-bit integer' => 'Entero 8-bits',
        'Other' => 'Otro',
      },
    },
   'VideoQuality' => {
      PrintConv => {
        'Low' => 'Bajo',
        'Standard' => 'Estándar',
      },
    },
   'VideoWidth' => 'Ancho Video',
   'ViewCompressionFactor' => 'Ver Factor de compresión',
   'ViewfinderWarning' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'ViewfinderWarnings' => {
      PrintConv => {
        'Monochrome' => 'Monocromo',
      },
    },
   'ViewingCondDesc' => 'Descripción en Condiciones de Visión',
   'ViewingCondIlluminant' => 'Iluminación en Condiciones de Visión',
   'ViewingCondIlluminantType' => 'Tipo Iluminación en Condiciones de Visión',
   'ViewingCondSurround' => 'Entorno en Condiciones de Visión',
   'ViewingConditions' => 'Iluminación en Condiciones de Visión',
   'VignetteControl' => {
      PrintConv => {
        'High' => 'Alto',
        'Low' => 'Bajo',
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'VoiceMemo' => {
      Description => 'Notas Voz',
      PrintConv => {
        'Off' => 'Apagado',
        'On' => 'Encendido',
      },
    },
   'WBAdjLighting' => {
      PrintConv => {
        'None' => 'Ninguno',
      },
    },
   'WBBracketMode' => {
      PrintConv => {
        'Off' => 'Desactivado',
      },
    },
   'WBFineTuneActive' => {
      PrintConv => {
        'Yes' => 'Si',
      },
    },
   'WCSProfiles' => 'Perfil Sistema Color Windows',
   'WhiteBalance' => {
      Description => 'Balance de Blancos',
      PrintConv => {
        'Auto' => 'Automático',
        'Black & White' => 'Monocromo',
        'Cloudy' => 'Nublado',
        'Color Temperature/Color Filter' => 'Temperatura de color / Filtro de color',
        'Cool White Fluorescent' => 'Fluorescente blanco frío',
        'Custom' => 'Personalizado',
        'Custom 1' => 'Personalizado 1',
        'Custom 2' => 'Personalizado 2',
        'Custom 3' => 'Personalizado 3',
        'Custom 4' => 'Personalizado 4',
        'Day White Fluorescent' => 'Fluorescente blanco de día',
        'Daylight' => 'Luz de día',
        'Daylight Fluorescent' => 'Fluorescente luz de día',
        'Fluorescent' => 'Flourescente',
        'Manual' => 'Equilibrio del blanco manual',
        'Manual Temperature (Kelvin)' => 'Temperatura Manual (Kelvin)',
        'Shade' => 'Sombrío',
        'Tungsten' => 'Tungsteno',
        'Underwater' => 'Subacuatica',
        'Unknown' => 'Desconocido',
        'Warm White Fluorescent' => 'Fluorescente blanco cálido',
        'White Fluorescent' => 'Fluorescente blanco',
      },
    },
   'WhiteBalanceAdj' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'WhiteBalanceBracketing' => {
      PrintConv => {
        'High' => 'Alto',
        'Low' => 'Bajo',
      },
    },
   'WhiteLevel' => 'Nivel Blanco',
   'WhitePoint' => 'Cromaticidad Punto Blanco',
   'WideRange' => {
      Description => 'Intervalo Extendido',
      PrintConv => {
        'Off' => 'Apagado',
        'On' => 'Encendido',
      },
    },
   'WidthResolution' => 'Resolución Imagen Horizontal',
   'Writer' => 'Escritor',
   'Writer-Editor' => 'Título/Descripción Escritor',
   'X3FillLight' => 'Luz Relleno X3',
   'XClipPathUnits' => 'Unidades Camino Fragmento X',
   'XMP' => 'Metadatos XMP',
   'XPosition' => 'Posición X',
   'XResolution' => 'Resolución Imagen Horizontal',
   'YCbCrCoefficients' => 'Coeficientes de Matriz de Tranformación de Espacio de Color',
   'YCbCrPositioning' => {
      Description => 'Posicionamiento Y y C',
      PrintConv => {
        'Centered' => 'Centrado',
        'Co-sited' => 'Vecino',
      },
    },
   'YCbCrSubSampling' => 'Ratio Submuestreo de Y a C',
   'YClipPathUnits' => 'Unidades Camino Fragmento Y',
   'YPosition' => 'Posición Y',
   'YResolution' => 'Resolución Imagen Vertical',
   'Year' => 'Año',
   'YearCreated' => 'Año Creación',
   'ZipCompressedSize' => 'Zip Tamaño Comprimido',
   'ZipCompression' => {
      Description => 'Compresión Zip',
      PrintConv => {
        'None' => 'Ninguno',
        'Reduced with compression factor 1' => 'Reducido con factor de compresión 1',
        'Reduced with compression factor 2' => 'Reducido con factor de compresión 2',
        'Reduced with compression factor 3' => 'Reducido con factor de compresión 3',
        'Reduced with compression factor 4' => 'Reducido con factor de compresión 4',
      },
    },
   'ZipUncompressedSize' => 'Zip Tamaño Descomprimido',
   'ZoneMatching' => {
      Description => 'Zone matching',
      PrintConv => {
        'High Key' => 'Alto',
        'ISO Setting Used' => 'Desactivado',
        'Low Key' => 'Bajo',
      },
    },
   'ZoneMatchingMode' => {
      PrintConv => {
        'Standard' => 'Estándar',
      },
    },
   'ZoneMatchingOn' => {
      PrintConv => {
        'Off' => 'Desactivado',
        'On' => 'Activado',
      },
    },
   'ZoomPos' => 'Posición Zoom',
);

1;  # end


__END__

=head1 NAME

Image::ExifTool::Lang::es.pm - ExifTool Spanish language translations

=head1 DESCRIPTION

This file is used by Image::ExifTool to generate localized tag descriptions
and values.

=head1 AUTHOR

Copyright 2003-2025, Phil Harvey (philharvey66 at gmail.com)

This library is free software; you can redistribute it and/or modify it
under the same terms as Perl itself.

=head1 ACKNOWLEDGEMENTS

Thanks to Jens Duttke, Santiago del BrE<iacute>o GonzE<aacute>lez and Emilio
Sancha for providing this translation.

=head1 SEE ALSO

L<Image::ExifTool(3pm)|Image::ExifTool>,
L<Image::ExifTool::TagInfoXML(3pm)|Image::ExifTool::TagInfoXML>

=cut
