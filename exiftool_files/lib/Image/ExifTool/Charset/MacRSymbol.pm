#------------------------------------------------------------------------------
# File:         MacRSymbol.pm
#
# Description:  Mac RSymbol (cp10008) to Unicode
#
# Revisions:    2010/01/20 - P<PERSON> created
#
# References:   1) http://www.haible.de/bruno/charsets/conversion-tables/GB2312.tar.bz2
#
# Notes:        The table omits 1-byte characters with the same values as Unicode
#------------------------------------------------------------------------------
use strict;

%Image::ExifTool::Charset::MacRSymbol = (
  0x81 => 0xf8d8, 0x82 => 0xf8d9, 0x83 => 0xf8da, 0x84 => 0xf8db,
  0x85 => 0xf8dc, 0x86 => 0xf8dd, 0x87 => 0xf8de, 0x88 => 0xf8df,
  0x89 => 0xf8e0, 0x8a => 0xf8e1, 0x8b => 0xf8e2, 0x8c => 0xf8e3,
  0x8d => 0xf8e4, 0x8e => 0xf8e5, 0x8f => 0xf8e6, 0x90 => 0xf8e7,
  0x91 => 0xf8e8, 0x92 => 0xf8e9, 0x93 => 0xf8ea, 0x94 => 0xf8eb,
  0x95 => 0xf8ec, 0x96 => 0xf8ed, 0x97 => 0xf8ee, 0x98 => 0xf8ef,
  0x99 => 0xf8f0, 0x9a => 0xf8f1, 0x9b => 0xf8f2, 0x9c => 0xf8f3,
  0x9d => 0xf8f4, 0x9e => 0xf8f5, 0x9f => 0xf8f6, 0xa0 => 0xf8f7,
  0xa1 => {
    0xa1 => 0x3000, 0xa2 => 0x3001, 0xa3 => 0x3002, 0xa4 => 0x30fb,
    0xa5 => 0x02c9, 0xa6 => 0x02c7, 0xa7 => 0xa8, 0xa8 => 0x3003,
    0xa9 => 0x3005, 0xaa => 0x2015, 0xab => 0xff5e, 0xad => 0x2026,
    0xae => 0x2018, 0xaf => 0x2019, 0xb0 => 0x201c, 0xb1 => 0x201d,
    0xb2 => 0x3014, 0xb3 => 0x3015, 0xb4 => 0x3008, 0xb5 => 0x3009,
    0xb6 => 0x300a, 0xb7 => 0x300b, 0xb8 => 0x300c, 0xb9 => 0x300d,
    0xba => 0x300e, 0xbb => 0x300f, 0xbc => 0x3016, 0xbd => 0x3017,
    0xbe => 0x3010, 0xbf => 0x3011, 0xc0 => 0xb1, 0xc1 => 0xd7, 0xc2 => 0xf7,
    0xc3 => 0x2236, 0xc4 => 0x2227, 0xc5 => 0x2228, 0xc6 => 0x2211,
    0xc7 => 0x220f, 0xc8 => 0x222a, 0xc9 => 0x2229, 0xca => 0x2208,
    0xcb => 0x2237, 0xcc => 0x221a, 0xcd => 0x22a5, 0xce => 0x2225,
    0xcf => 0x2220, 0xd0 => 0x2312, 0xd1 => 0x2299, 0xd2 => 0x222b,
    0xd3 => 0x222e, 0xd4 => 0x2261, 0xd5 => 0x224c, 0xd6 => 0x2248,
    0xd7 => 0x223d, 0xd8 => 0x221d, 0xd9 => 0x2260, 0xda => 0x226e,
    0xdb => 0x226f, 0xdc => 0x2264, 0xdd => 0x2265, 0xde => 0x221e,
    0xdf => 0x2235, 0xe0 => 0x2234, 0xe1 => 0x2642, 0xe2 => 0x2640,
    0xe3 => 0xb0, 0xe4 => 0x2032, 0xe5 => 0x2033, 0xe6 => 0x2103,
    0xe7 => 0xff04, 0xe8 => 0xa4, 0xe9 => 0xffe0, 0xea => 0xffe1,
    0xeb => 0x2030, 0xec => 0xa7, 0xed => 0x2116, 0xee => 0x2606,
    0xef => 0x2605, 0xf0 => 0x25cb, 0xf1 => 0x25cf, 0xf2 => 0x25ce,
    0xf3 => 0x25c7, 0xf4 => 0x25c6, 0xf5 => 0x25a1, 0xf6 => 0x25a0,
    0xf7 => 0x25b3, 0xf8 => 0x25b2, 0xf9 => 0x203b, 0xfa => 0x2192,
    0xfb => 0x2190, 0xfc => 0x2191, 0xfd => 0x2193, 0xfe => 0x3013,
  },
  0xa2 => {
    0xb1 => 0x2488, 0xb2 => 0x2489, 0xb3 => 0x248a, 0xb4 => 0x248b,
    0xb5 => 0x248c, 0xb6 => 0x248d, 0xb7 => 0x248e, 0xb8 => 0x248f,
    0xb9 => 0x2490, 0xba => 0x2491, 0xbb => 0x2492, 0xbc => 0x2493,
    0xbd => 0x2494, 0xbe => 0x2495, 0xbf => 0x2496, 0xc0 => 0x2497,
    0xc1 => 0x2498, 0xc2 => 0x2499, 0xc3 => 0x249a, 0xc4 => 0x249b,
    0xc5 => 0x2474, 0xc6 => 0x2475, 0xc7 => 0x2476, 0xc8 => 0x2477,
    0xc9 => 0x2478, 0xca => 0x2479, 0xcb => 0x247a, 0xcc => 0x247b,
    0xcd => 0x247c, 0xce => 0x247d, 0xcf => 0x247e, 0xd0 => 0x247f,
    0xd1 => 0x2480, 0xd2 => 0x2481, 0xd3 => 0x2482, 0xd4 => 0x2483,
    0xd5 => 0x2484, 0xd6 => 0x2485, 0xd7 => 0x2486, 0xd8 => 0x2487,
    0xd9 => 0x2460, 0xda => 0x2461, 0xdb => 0x2462, 0xdc => 0x2463,
    0xdd => 0x2464, 0xde => 0x2465, 0xdf => 0x2466, 0xe0 => 0x2467,
    0xe1 => 0x2468, 0xe2 => 0x2469, 0xe5 => 0x3220, 0xe6 => 0x3221,
    0xe7 => 0x3222, 0xe8 => 0x3223, 0xe9 => 0x3224, 0xea => 0x3225,
    0xeb => 0x3226, 0xec => 0x3227, 0xed => 0x3228, 0xee => 0x3229,
    0xf1 => 0x2160, 0xf2 => 0x2161, 0xf3 => 0x2162, 0xf4 => 0x2163,
    0xf5 => 0x2164, 0xf6 => 0x2165, 0xf7 => 0x2166, 0xf8 => 0x2167,
    0xf9 => 0x2168, 0xfa => 0x2169, 0xfb => 0x216a, 0xfc => 0x216b,
  },
  0xa3 => {
    0xa1 => 0xff01, 0xa2 => 0xff02, 0xa3 => 0xff03, 0xa4 => 0xffe5,
    0xa5 => 0xff05, 0xa6 => 0xff06, 0xa7 => 0xff07, 0xa8 => 0xff08,
    0xa9 => 0xff09, 0xaa => 0xff0a, 0xab => 0xff0b, 0xac => 0xff0c,
    0xad => 0xff0d, 0xae => 0xff0e, 0xaf => 0xff0f, 0xb0 => 0xff10,
    0xb1 => 0xff11, 0xb2 => 0xff12, 0xb3 => 0xff13, 0xb4 => 0xff14,
    0xb5 => 0xff15, 0xb6 => 0xff16, 0xb7 => 0xff17, 0xb8 => 0xff18,
    0xb9 => 0xff19, 0xba => 0xff1a, 0xbb => 0xff1b, 0xbc => 0xff1c,
    0xbd => 0xff1d, 0xbe => 0xff1e, 0xbf => 0xff1f, 0xc0 => 0xff20,
    0xc1 => 0xff21, 0xc2 => 0xff22, 0xc3 => 0xff23, 0xc4 => 0xff24,
    0xc5 => 0xff25, 0xc6 => 0xff26, 0xc7 => 0xff27, 0xc8 => 0xff28,
    0xc9 => 0xff29, 0xca => 0xff2a, 0xcb => 0xff2b, 0xcc => 0xff2c,
    0xcd => 0xff2d, 0xce => 0xff2e, 0xcf => 0xff2f, 0xd0 => 0xff30,
    0xd1 => 0xff31, 0xd2 => 0xff32, 0xd3 => 0xff33, 0xd4 => 0xff34,
    0xd5 => 0xff35, 0xd6 => 0xff36, 0xd7 => 0xff37, 0xd8 => 0xff38,
    0xd9 => 0xff39, 0xda => 0xff3a, 0xdb => 0xff3b, 0xdc => 0xff3c,
    0xdd => 0xff3d, 0xde => 0xff3e, 0xdf => 0xff3f, 0xe0 => 0xff40,
    0xe1 => 0xff41, 0xe2 => 0xff42, 0xe3 => 0xff43, 0xe4 => 0xff44,
    0xe5 => 0xff45, 0xe6 => 0xff46, 0xe7 => 0xff47, 0xe8 => 0xff48,
    0xe9 => 0xff49, 0xea => 0xff4a, 0xeb => 0xff4b, 0xec => 0xff4c,
    0xed => 0xff4d, 0xee => 0xff4e, 0xef => 0xff4f, 0xf0 => 0xff50,
    0xf1 => 0xff51, 0xf2 => 0xff52, 0xf3 => 0xff53, 0xf4 => 0xff54,
    0xf5 => 0xff55, 0xf6 => 0xff56, 0xf7 => 0xff57, 0xf8 => 0xff58,
    0xf9 => 0xff59, 0xfa => 0xff5a, 0xfb => 0xff5b, 0xfc => 0xff5c,
    0xfd => 0xff5d, 0xfe => 0xffe3,
  },
  0xa4 => {
    0xa1 => 0x3041, 0xa2 => 0x3042, 0xa3 => 0x3043, 0xa4 => 0x3044,
    0xa5 => 0x3045, 0xa6 => 0x3046, 0xa7 => 0x3047, 0xa8 => 0x3048,
    0xa9 => 0x3049, 0xaa => 0x304a, 0xab => 0x304b, 0xac => 0x304c,
    0xad => 0x304d, 0xae => 0x304e, 0xaf => 0x304f, 0xb0 => 0x3050,
    0xb1 => 0x3051, 0xb2 => 0x3052, 0xb3 => 0x3053, 0xb4 => 0x3054,
    0xb5 => 0x3055, 0xb6 => 0x3056, 0xb7 => 0x3057, 0xb8 => 0x3058,
    0xb9 => 0x3059, 0xba => 0x305a, 0xbb => 0x305b, 0xbc => 0x305c,
    0xbd => 0x305d, 0xbe => 0x305e, 0xbf => 0x305f, 0xc0 => 0x3060,
    0xc1 => 0x3061, 0xc2 => 0x3062, 0xc3 => 0x3063, 0xc4 => 0x3064,
    0xc5 => 0x3065, 0xc6 => 0x3066, 0xc7 => 0x3067, 0xc8 => 0x3068,
    0xc9 => 0x3069, 0xca => 0x306a, 0xcb => 0x306b, 0xcc => 0x306c,
    0xcd => 0x306d, 0xce => 0x306e, 0xcf => 0x306f, 0xd0 => 0x3070,
    0xd1 => 0x3071, 0xd2 => 0x3072, 0xd3 => 0x3073, 0xd4 => 0x3074,
    0xd5 => 0x3075, 0xd6 => 0x3076, 0xd7 => 0x3077, 0xd8 => 0x3078,
    0xd9 => 0x3079, 0xda => 0x307a, 0xdb => 0x307b, 0xdc => 0x307c,
    0xdd => 0x307d, 0xde => 0x307e, 0xdf => 0x307f, 0xe0 => 0x3080,
    0xe1 => 0x3081, 0xe2 => 0x3082, 0xe3 => 0x3083, 0xe4 => 0x3084,
    0xe5 => 0x3085, 0xe6 => 0x3086, 0xe7 => 0x3087, 0xe8 => 0x3088,
    0xe9 => 0x3089, 0xea => 0x308a, 0xeb => 0x308b, 0xec => 0x308c,
    0xed => 0x308d, 0xee => 0x308e, 0xef => 0x308f, 0xf0 => 0x3090,
    0xf1 => 0x3091, 0xf2 => 0x3092, 0xf3 => 0x3093,
  },
  0xa5 => {
    0xa1 => 0x30a1, 0xa2 => 0x30a2, 0xa3 => 0x30a3, 0xa4 => 0x30a4,
    0xa5 => 0x30a5, 0xa6 => 0x30a6, 0xa7 => 0x30a7, 0xa8 => 0x30a8,
    0xa9 => 0x30a9, 0xaa => 0x30aa, 0xab => 0x30ab, 0xac => 0x30ac,
    0xad => 0x30ad, 0xae => 0x30ae, 0xaf => 0x30af, 0xb0 => 0x30b0,
    0xb1 => 0x30b1, 0xb2 => 0x30b2, 0xb3 => 0x30b3, 0xb4 => 0x30b4,
    0xb5 => 0x30b5, 0xb6 => 0x30b6, 0xb7 => 0x30b7, 0xb8 => 0x30b8,
    0xb9 => 0x30b9, 0xba => 0x30ba, 0xbb => 0x30bb, 0xbc => 0x30bc,
    0xbd => 0x30bd, 0xbe => 0x30be, 0xbf => 0x30bf, 0xc0 => 0x30c0,
    0xc1 => 0x30c1, 0xc2 => 0x30c2, 0xc3 => 0x30c3, 0xc4 => 0x30c4,
    0xc5 => 0x30c5, 0xc6 => 0x30c6, 0xc7 => 0x30c7, 0xc8 => 0x30c8,
    0xc9 => 0x30c9, 0xca => 0x30ca, 0xcb => 0x30cb, 0xcc => 0x30cc,
    0xcd => 0x30cd, 0xce => 0x30ce, 0xcf => 0x30cf, 0xd0 => 0x30d0,
    0xd1 => 0x30d1, 0xd2 => 0x30d2, 0xd3 => 0x30d3, 0xd4 => 0x30d4,
    0xd5 => 0x30d5, 0xd6 => 0x30d6, 0xd7 => 0x30d7, 0xd8 => 0x30d8,
    0xd9 => 0x30d9, 0xda => 0x30da, 0xdb => 0x30db, 0xdc => 0x30dc,
    0xdd => 0x30dd, 0xde => 0x30de, 0xdf => 0x30df, 0xe0 => 0x30e0,
    0xe1 => 0x30e1, 0xe2 => 0x30e2, 0xe3 => 0x30e3, 0xe4 => 0x30e4,
    0xe5 => 0x30e5, 0xe6 => 0x30e6, 0xe7 => 0x30e7, 0xe8 => 0x30e8,
    0xe9 => 0x30e9, 0xea => 0x30ea, 0xeb => 0x30eb, 0xec => 0x30ec,
    0xed => 0x30ed, 0xee => 0x30ee, 0xef => 0x30ef, 0xf0 => 0x30f0,
    0xf1 => 0x30f1, 0xf2 => 0x30f2, 0xf3 => 0x30f3, 0xf4 => 0x30f4,
    0xf5 => 0x30f5, 0xf6 => 0x30f6,
  },
  0xa6 => {
    0xa1 => 0x0391, 0xa2 => 0x0392, 0xa3 => 0x0393, 0xa4 => 0x0394,
    0xa5 => 0x0395, 0xa6 => 0x0396, 0xa7 => 0x0397, 0xa8 => 0x0398,
    0xa9 => 0x0399, 0xaa => 0x039a, 0xab => 0x039b, 0xac => 0x039c,
    0xad => 0x039d, 0xae => 0x039e, 0xaf => 0x039f, 0xb0 => 0x03a0,
    0xb1 => 0x03a1, 0xb2 => 0x03a3, 0xb3 => 0x03a4, 0xb4 => 0x03a5,
    0xb5 => 0x03a6, 0xb6 => 0x03a7, 0xb7 => 0x03a8, 0xb8 => 0x03a9,
    0xc1 => 0x03b1, 0xc2 => 0x03b2, 0xc3 => 0x03b3, 0xc4 => 0x03b4,
    0xc5 => 0x03b5, 0xc6 => 0x03b6, 0xc7 => 0x03b7, 0xc8 => 0x03b8,
    0xc9 => 0x03b9, 0xca => 0x03ba, 0xcb => 0x03bb, 0xcc => 0x03bc,
    0xcd => 0x03bd, 0xce => 0x03be, 0xcf => 0x03bf, 0xd0 => 0x03c0,
    0xd1 => 0x03c1, 0xd2 => 0x03c3, 0xd3 => 0x03c4, 0xd4 => 0x03c5,
    0xd5 => 0x03c6, 0xd6 => 0x03c7, 0xd7 => 0x03c8, 0xd8 => 0x03c9,
  },
  0xa7 => {
    0xa1 => 0x0410, 0xa2 => 0x0411, 0xa3 => 0x0412, 0xa4 => 0x0413,
    0xa5 => 0x0414, 0xa6 => 0x0415, 0xa7 => 0x0401, 0xa8 => 0x0416,
    0xa9 => 0x0417, 0xaa => 0x0418, 0xab => 0x0419, 0xac => 0x041a,
    0xad => 0x041b, 0xae => 0x041c, 0xaf => 0x041d, 0xb0 => 0x041e,
    0xb1 => 0x041f, 0xb2 => 0x0420, 0xb3 => 0x0421, 0xb4 => 0x0422,
    0xb5 => 0x0423, 0xb6 => 0x0424, 0xb7 => 0x0425, 0xb8 => 0x0426,
    0xb9 => 0x0427, 0xba => 0x0428, 0xbb => 0x0429, 0xbc => 0x042a,
    0xbd => 0x042b, 0xbe => 0x042c, 0xbf => 0x042d, 0xc0 => 0x042e,
    0xc1 => 0x042f, 0xd1 => 0x0430, 0xd2 => 0x0431, 0xd3 => 0x0432,
    0xd4 => 0x0433, 0xd5 => 0x0434, 0xd6 => 0x0435, 0xd7 => 0x0451,
    0xd8 => 0x0436, 0xd9 => 0x0437, 0xda => 0x0438, 0xdb => 0x0439,
    0xdc => 0x043a, 0xdd => 0x043b, 0xde => 0x043c, 0xdf => 0x043d,
    0xe0 => 0x043e, 0xe1 => 0x043f, 0xe2 => 0x0440, 0xe3 => 0x0441,
    0xe4 => 0x0442, 0xe5 => 0x0443, 0xe6 => 0x0444, 0xe7 => 0x0445,
    0xe8 => 0x0446, 0xe9 => 0x0447, 0xea => 0x0448, 0xeb => 0x0449,
    0xec => 0x044a, 0xed => 0x044b, 0xee => 0x044c, 0xef => 0x044d,
    0xf0 => 0x044e, 0xf1 => 0x044f,
  },
  0xa8 => {
    0xa1 => 0x0101, 0xa2 => 0xe1, 0xa3 => 0x01ce, 0xa4 => 0xe0, 0xa5 => 0x0113,
    0xa6 => 0xe9, 0xa7 => 0x011b, 0xa8 => 0xe8, 0xa9 => 0x012b, 0xaa => 0xed,
    0xab => 0x01d0, 0xac => 0xec, 0xad => 0x014d, 0xae => 0xf3, 0xaf => 0x01d2,
    0xb0 => 0xf2, 0xb1 => 0x016b, 0xb2 => 0xfa, 0xb3 => 0x01d4, 0xb4 => 0xf9,
    0xb5 => 0x01d6, 0xb6 => 0x01d8, 0xb7 => 0x01da, 0xb8 => 0x01dc,
    0xb9 => 0xfc, 0xba => 0xea, 0xc5 => 0x3105, 0xc6 => 0x3106, 0xc7 => 0x3107,
    0xc8 => 0x3108, 0xc9 => 0x3109, 0xca => 0x310a, 0xcb => 0x310b,
    0xcc => 0x310c, 0xcd => 0x310d, 0xce => 0x310e, 0xcf => 0x310f,
    0xd0 => 0x3110, 0xd1 => 0x3111, 0xd2 => 0x3112, 0xd3 => 0x3113,
    0xd4 => 0x3114, 0xd5 => 0x3115, 0xd6 => 0x3116, 0xd7 => 0x3117,
    0xd8 => 0x3118, 0xd9 => 0x3119, 0xda => 0x311a, 0xdb => 0x311b,
    0xdc => 0x311c, 0xdd => 0x311d, 0xde => 0x311e, 0xdf => 0x311f,
    0xe0 => 0x3120, 0xe1 => 0x3121, 0xe2 => 0x3122, 0xe3 => 0x3123,
    0xe4 => 0x3124, 0xe5 => 0x3125, 0xe6 => 0x3126, 0xe7 => 0x3127,
    0xe8 => 0x3128, 0xe9 => 0x3129,
  },
  0xa9 => {
    0xa4 => 0x2500, 0xa5 => 0x2501, 0xa6 => 0x2502, 0xa7 => 0x2503,
    0xa8 => 0x2504, 0xa9 => 0x2505, 0xaa => 0x2506, 0xab => 0x2507,
    0xac => 0x2508, 0xad => 0x2509, 0xae => 0x250a, 0xaf => 0x250b,
    0xb0 => 0x250c, 0xb1 => 0x250d, 0xb2 => 0x250e, 0xb3 => 0x250f,
    0xb4 => 0x2510, 0xb5 => 0x2511, 0xb6 => 0x2512, 0xb7 => 0x2513,
    0xb8 => 0x2514, 0xb9 => 0x2515, 0xba => 0x2516, 0xbb => 0x2517,
    0xbc => 0x2518, 0xbd => 0x2519, 0xbe => 0x251a, 0xbf => 0x251b,
    0xc0 => 0x251c, 0xc1 => 0x251d, 0xc2 => 0x251e, 0xc3 => 0x251f,
    0xc4 => 0x2520, 0xc5 => 0x2521, 0xc6 => 0x2522, 0xc7 => 0x2523,
    0xc8 => 0x2524, 0xc9 => 0x2525, 0xca => 0x2526, 0xcb => 0x2527,
    0xcc => 0x2528, 0xcd => 0x2529, 0xce => 0x252a, 0xcf => 0x252b,
    0xd0 => 0x252c, 0xd1 => 0x252d, 0xd2 => 0x252e, 0xd3 => 0x252f,
    0xd4 => 0x2530, 0xd5 => 0x2531, 0xd6 => 0x2532, 0xd7 => 0x2533,
    0xd8 => 0x2534, 0xd9 => 0x2535, 0xda => 0x2536, 0xdb => 0x2537,
    0xdc => 0x2538, 0xdd => 0x2539, 0xde => 0x253a, 0xdf => 0x253b,
    0xe0 => 0x253c, 0xe1 => 0x253d, 0xe2 => 0x253e, 0xe3 => 0x253f,
    0xe4 => 0x2540, 0xe5 => 0x2541, 0xe6 => 0x2542, 0xe7 => 0x2543,
    0xe8 => 0x2544, 0xe9 => 0x2545, 0xea => 0x2546, 0xeb => 0x2547,
    0xec => 0x2548, 0xed => 0x2549, 0xee => 0x254a, 0xef => 0x254b,
  },
  0xb0 => {
    0xa1 => 0x554a, 0xa2 => 0x963f, 0xa3 => 0x57c3, 0xa4 => 0x6328,
    0xa5 => 0x54ce, 0xa6 => 0x5509, 0xa7 => 0x54c0, 0xa8 => 0x7691,
    0xa9 => 0x764c, 0xaa => 0x853c, 0xab => 0x77ee, 0xac => 0x827e,
    0xad => 0x788d, 0xae => 0x7231, 0xaf => 0x9698, 0xb0 => 0x978d,
    0xb1 => 0x6c28, 0xb2 => 0x5b89, 0xb3 => 0x4ffa, 0xb4 => 0x6309,
    0xb5 => 0x6697, 0xb6 => 0x5cb8, 0xb7 => 0x80fa, 0xb8 => 0x6848,
    0xb9 => 0x80ae, 0xba => 0x6602, 0xbb => 0x76ce, 0xbc => 0x51f9,
    0xbd => 0x6556, 0xbe => 0x71ac, 0xbf => 0x7ff1, 0xc0 => 0x8884,
    0xc1 => 0x50b2, 0xc2 => 0x5965, 0xc3 => 0x61ca, 0xc4 => 0x6fb3,
    0xc5 => 0x82ad, 0xc6 => 0x634c, 0xc7 => 0x6252, 0xc8 => 0x53ed,
    0xc9 => 0x5427, 0xca => 0x7b06, 0xcb => 0x516b, 0xcc => 0x75a4,
    0xcd => 0x5df4, 0xce => 0x62d4, 0xcf => 0x8dcb, 0xd0 => 0x9776,
    0xd1 => 0x628a, 0xd2 => 0x8019, 0xd3 => 0x575d, 0xd4 => 0x9738,
    0xd5 => 0x7f62, 0xd6 => 0x7238, 0xd7 => 0x767d, 0xd8 => 0x67cf,
    0xd9 => 0x767e, 0xda => 0x6446, 0xdb => 0x4f70, 0xdc => 0x8d25,
    0xdd => 0x62dc, 0xde => 0x7a17, 0xdf => 0x6591, 0xe0 => 0x73ed,
    0xe1 => 0x642c, 0xe2 => 0x6273, 0xe3 => 0x822c, 0xe4 => 0x9881,
    0xe5 => 0x677f, 0xe6 => 0x7248, 0xe7 => 0x626e, 0xe8 => 0x62cc,
    0xe9 => 0x4f34, 0xea => 0x74e3, 0xeb => 0x534a, 0xec => 0x529e,
    0xed => 0x7eca, 0xee => 0x90a6, 0xef => 0x5e2e, 0xf0 => 0x6886,
    0xf1 => 0x699c, 0xf2 => 0x8180, 0xf3 => 0x7ed1, 0xf4 => 0x68d2,
    0xf5 => 0x78c5, 0xf6 => 0x868c, 0xf7 => 0x9551, 0xf8 => 0x508d,
    0xf9 => 0x8c24, 0xfa => 0x82de, 0xfb => 0x80de, 0xfc => 0x5305,
    0xfd => 0x8912, 0xfe => 0x5265,
  },
  0xb1 => {
    0xa1 => 0x8584, 0xa2 => 0x96f9, 0xa3 => 0x4fdd, 0xa4 => 0x5821,
    0xa5 => 0x9971, 0xa6 => 0x5b9d, 0xa7 => 0x62b1, 0xa8 => 0x62a5,
    0xa9 => 0x66b4, 0xaa => 0x8c79, 0xab => 0x9c8d, 0xac => 0x7206,
    0xad => 0x676f, 0xae => 0x7891, 0xaf => 0x60b2, 0xb0 => 0x5351,
    0xb1 => 0x5317, 0xb2 => 0x8f88, 0xb3 => 0x80cc, 0xb4 => 0x8d1d,
    0xb5 => 0x94a1, 0xb6 => 0x500d, 0xb7 => 0x72c8, 0xb8 => 0x5907,
    0xb9 => 0x60eb, 0xba => 0x7119, 0xbb => 0x88ab, 0xbc => 0x5954,
    0xbd => 0x82ef, 0xbe => 0x672c, 0xbf => 0x7b28, 0xc0 => 0x5d29,
    0xc1 => 0x7ef7, 0xc2 => 0x752d, 0xc3 => 0x6cf5, 0xc4 => 0x8e66,
    0xc5 => 0x8ff8, 0xc6 => 0x903c, 0xc7 => 0x9f3b, 0xc8 => 0x6bd4,
    0xc9 => 0x9119, 0xca => 0x7b14, 0xcb => 0x5f7c, 0xcc => 0x78a7,
    0xcd => 0x84d6, 0xce => 0x853d, 0xcf => 0x6bd5, 0xd0 => 0x6bd9,
    0xd1 => 0x6bd6, 0xd2 => 0x5e01, 0xd3 => 0x5e87, 0xd4 => 0x75f9,
    0xd5 => 0x95ed, 0xd6 => 0x655d, 0xd7 => 0x5f0a, 0xd8 => 0x5fc5,
    0xd9 => 0x8f9f, 0xda => 0x58c1, 0xdb => 0x81c2, 0xdc => 0x907f,
    0xdd => 0x965b, 0xde => 0x97ad, 0xdf => 0x8fb9, 0xe0 => 0x7f16,
    0xe1 => 0x8d2c, 0xe2 => 0x6241, 0xe3 => 0x4fbf, 0xe4 => 0x53d8,
    0xe5 => 0x535e, 0xe6 => 0x8fa8, 0xe7 => 0x8fa9, 0xe8 => 0x8fab,
    0xe9 => 0x904d, 0xea => 0x6807, 0xeb => 0x5f6a, 0xec => 0x8198,
    0xed => 0x8868, 0xee => 0x9cd6, 0xef => 0x618b, 0xf0 => 0x522b,
    0xf1 => 0x762a, 0xf2 => 0x5f6c, 0xf3 => 0x658c, 0xf4 => 0x6fd2,
    0xf5 => 0x6ee8, 0xf6 => 0x5bbe, 0xf7 => 0x6448, 0xf8 => 0x5175,
    0xf9 => 0x51b0, 0xfa => 0x67c4, 0xfb => 0x4e19, 0xfc => 0x79c9,
    0xfd => 0x997c, 0xfe => 0x70b3,
  },
  0xb2 => {
    0xa1 => 0x75c5, 0xa2 => 0x5e76, 0xa3 => 0x73bb, 0xa4 => 0x83e0,
    0xa5 => 0x64ad, 0xa6 => 0x62e8, 0xa7 => 0x94b5, 0xa8 => 0x6ce2,
    0xa9 => 0x535a, 0xaa => 0x52c3, 0xab => 0x640f, 0xac => 0x94c2,
    0xad => 0x7b94, 0xae => 0x4f2f, 0xaf => 0x5e1b, 0xb0 => 0x8236,
    0xb1 => 0x8116, 0xb2 => 0x818a, 0xb3 => 0x6e24, 0xb4 => 0x6cca,
    0xb5 => 0x9a73, 0xb6 => 0x6355, 0xb7 => 0x535c, 0xb8 => 0x54fa,
    0xb9 => 0x8865, 0xba => 0x57e0, 0xbb => 0x4e0d, 0xbc => 0x5e03,
    0xbd => 0x6b65, 0xbe => 0x7c3f, 0xbf => 0x90e8, 0xc0 => 0x6016,
    0xc1 => 0x64e6, 0xc2 => 0x731c, 0xc3 => 0x88c1, 0xc4 => 0x6750,
    0xc5 => 0x624d, 0xc6 => 0x8d22, 0xc7 => 0x776c, 0xc8 => 0x8e29,
    0xc9 => 0x91c7, 0xca => 0x5f69, 0xcb => 0x83dc, 0xcc => 0x8521,
    0xcd => 0x9910, 0xce => 0x53c2, 0xcf => 0x8695, 0xd0 => 0x6b8b,
    0xd1 => 0x60ed, 0xd2 => 0x60e8, 0xd3 => 0x707f, 0xd4 => 0x82cd,
    0xd5 => 0x8231, 0xd6 => 0x4ed3, 0xd7 => 0x6ca7, 0xd8 => 0x85cf,
    0xd9 => 0x64cd, 0xda => 0x7cd9, 0xdb => 0x69fd, 0xdc => 0x66f9,
    0xdd => 0x8349, 0xde => 0x5395, 0xdf => 0x7b56, 0xe0 => 0x4fa7,
    0xe1 => 0x518c, 0xe2 => 0x6d4b, 0xe3 => 0x5c42, 0xe4 => 0x8e6d,
    0xe5 => 0x63d2, 0xe6 => 0x53c9, 0xe7 => 0x832c, 0xe8 => 0x8336,
    0xe9 => 0x67e5, 0xea => 0x78b4, 0xeb => 0x643d, 0xec => 0x5bdf,
    0xed => 0x5c94, 0xee => 0x5dee, 0xef => 0x8be7, 0xf0 => 0x62c6,
    0xf1 => 0x67f4, 0xf2 => 0x8c7a, 0xf3 => 0x6400, 0xf4 => 0x63ba,
    0xf5 => 0x8749, 0xf6 => 0x998b, 0xf7 => 0x8c17, 0xf8 => 0x7f20,
    0xf9 => 0x94f2, 0xfa => 0x4ea7, 0xfb => 0x9610, 0xfc => 0x98a4,
    0xfd => 0x660c, 0xfe => 0x7316,
  },
  0xb3 => {
    0xa1 => 0x573a, 0xa2 => 0x5c1d, 0xa3 => 0x5e38, 0xa4 => 0x957f,
    0xa5 => 0x507f, 0xa6 => 0x80a0, 0xa7 => 0x5382, 0xa8 => 0x655e,
    0xa9 => 0x7545, 0xaa => 0x5531, 0xab => 0x5021, 0xac => 0x8d85,
    0xad => 0x6284, 0xae => 0x949e, 0xaf => 0x671d, 0xb0 => 0x5632,
    0xb1 => 0x6f6e, 0xb2 => 0x5de2, 0xb3 => 0x5435, 0xb4 => 0x7092,
    0xb5 => 0x8f66, 0xb6 => 0x626f, 0xb7 => 0x64a4, 0xb8 => 0x63a3,
    0xb9 => 0x5f7b, 0xba => 0x6f88, 0xbb => 0x90f4, 0xbc => 0x81e3,
    0xbd => 0x8fb0, 0xbe => 0x5c18, 0xbf => 0x6668, 0xc0 => 0x5ff1,
    0xc1 => 0x6c89, 0xc2 => 0x9648, 0xc3 => 0x8d81, 0xc4 => 0x886c,
    0xc5 => 0x6491, 0xc6 => 0x79f0, 0xc7 => 0x57ce, 0xc8 => 0x6a59,
    0xc9 => 0x6210, 0xca => 0x5448, 0xcb => 0x4e58, 0xcc => 0x7a0b,
    0xcd => 0x60e9, 0xce => 0x6f84, 0xcf => 0x8bda, 0xd0 => 0x627f,
    0xd1 => 0x901e, 0xd2 => 0x9a8b, 0xd3 => 0x79e4, 0xd4 => 0x5403,
    0xd5 => 0x75f4, 0xd6 => 0x6301, 0xd7 => 0x5319, 0xd8 => 0x6c60,
    0xd9 => 0x8fdf, 0xda => 0x5f1b, 0xdb => 0x9a70, 0xdc => 0x803b,
    0xdd => 0x9f7f, 0xde => 0x4f88, 0xdf => 0x5c3a, 0xe0 => 0x8d64,
    0xe1 => 0x7fc5, 0xe2 => 0x65a5, 0xe3 => 0x70bd, 0xe4 => 0x5145,
    0xe5 => 0x51b2, 0xe6 => 0x866b, 0xe7 => 0x5d07, 0xe8 => 0x5ba0,
    0xe9 => 0x62bd, 0xea => 0x916c, 0xeb => 0x7574, 0xec => 0x8e0c,
    0xed => 0x7a20, 0xee => 0x6101, 0xef => 0x7b79, 0xf0 => 0x4ec7,
    0xf1 => 0x7ef8, 0xf2 => 0x7785, 0xf3 => 0x4e11, 0xf4 => 0x81ed,
    0xf5 => 0x521d, 0xf6 => 0x51fa, 0xf7 => 0x6a71, 0xf8 => 0x53a8,
    0xf9 => 0x8e87, 0xfa => 0x9504, 0xfb => 0x96cf, 0xfc => 0x6ec1,
    0xfd => 0x9664, 0xfe => 0x695a,
  },
  0xb4 => {
    0xa1 => 0x7840, 0xa2 => 0x50a8, 0xa3 => 0x77d7, 0xa4 => 0x6410,
    0xa5 => 0x89e6, 0xa6 => 0x5904, 0xa7 => 0x63e3, 0xa8 => 0x5ddd,
    0xa9 => 0x7a7f, 0xaa => 0x693d, 0xab => 0x4f20, 0xac => 0x8239,
    0xad => 0x5598, 0xae => 0x4e32, 0xaf => 0x75ae, 0xb0 => 0x7a97,
    0xb1 => 0x5e62, 0xb2 => 0x5e8a, 0xb3 => 0x95ef, 0xb4 => 0x521b,
    0xb5 => 0x5439, 0xb6 => 0x708a, 0xb7 => 0x6376, 0xb8 => 0x9524,
    0xb9 => 0x5782, 0xba => 0x6625, 0xbb => 0x693f, 0xbc => 0x9187,
    0xbd => 0x5507, 0xbe => 0x6df3, 0xbf => 0x7eaf, 0xc0 => 0x8822,
    0xc1 => 0x6233, 0xc2 => 0x7ef0, 0xc3 => 0x75b5, 0xc4 => 0x8328,
    0xc5 => 0x78c1, 0xc6 => 0x96cc, 0xc7 => 0x8f9e, 0xc8 => 0x6148,
    0xc9 => 0x74f7, 0xca => 0x8bcd, 0xcb => 0x6b64, 0xcc => 0x523a,
    0xcd => 0x8d50, 0xce => 0x6b21, 0xcf => 0x806a, 0xd0 => 0x8471,
    0xd1 => 0x56f1, 0xd2 => 0x5306, 0xd3 => 0x4ece, 0xd4 => 0x4e1b,
    0xd5 => 0x51d1, 0xd6 => 0x7c97, 0xd7 => 0x918b, 0xd8 => 0x7c07,
    0xd9 => 0x4fc3, 0xda => 0x8e7f, 0xdb => 0x7be1, 0xdc => 0x7a9c,
    0xdd => 0x6467, 0xde => 0x5d14, 0xdf => 0x50ac, 0xe0 => 0x8106,
    0xe1 => 0x7601, 0xe2 => 0x7cb9, 0xe3 => 0x6dec, 0xe4 => 0x7fe0,
    0xe5 => 0x6751, 0xe6 => 0x5b58, 0xe7 => 0x5bf8, 0xe8 => 0x78cb,
    0xe9 => 0x64ae, 0xea => 0x6413, 0xeb => 0x63aa, 0xec => 0x632b,
    0xed => 0x9519, 0xee => 0x642d, 0xef => 0x8fbe, 0xf0 => 0x7b54,
    0xf1 => 0x7629, 0xf2 => 0x6253, 0xf3 => 0x5927, 0xf4 => 0x5446,
    0xf5 => 0x6b79, 0xf6 => 0x50a3, 0xf7 => 0x6234, 0xf8 => 0x5e26,
    0xf9 => 0x6b86, 0xfa => 0x4ee3, 0xfb => 0x8d37, 0xfc => 0x888b,
    0xfd => 0x5f85, 0xfe => 0x902e,
  },
  0xb5 => {
    0xa1 => 0x6020, 0xa2 => 0x803d, 0xa3 => 0x62c5, 0xa4 => 0x4e39,
    0xa5 => 0x5355, 0xa6 => 0x90f8, 0xa7 => 0x63b8, 0xa8 => 0x80c6,
    0xa9 => 0x65e6, 0xaa => 0x6c2e, 0xab => 0x4f46, 0xac => 0x60ee,
    0xad => 0x6de1, 0xae => 0x8bde, 0xaf => 0x5f39, 0xb0 => 0x86cb,
    0xb1 => 0x5f53, 0xb2 => 0x6321, 0xb3 => 0x515a, 0xb4 => 0x8361,
    0xb5 => 0x6863, 0xb6 => 0x5200, 0xb7 => 0x6363, 0xb8 => 0x8e48,
    0xb9 => 0x5012, 0xba => 0x5c9b, 0xbb => 0x7977, 0xbc => 0x5bfc,
    0xbd => 0x5230, 0xbe => 0x7a3b, 0xbf => 0x60bc, 0xc0 => 0x9053,
    0xc1 => 0x76d7, 0xc2 => 0x5fb7, 0xc3 => 0x5f97, 0xc4 => 0x7684,
    0xc5 => 0x8e6c, 0xc6 => 0x706f, 0xc7 => 0x767b, 0xc8 => 0x7b49,
    0xc9 => 0x77aa, 0xca => 0x51f3, 0xcb => 0x9093, 0xcc => 0x5824,
    0xcd => 0x4f4e, 0xce => 0x6ef4, 0xcf => 0x8fea, 0xd0 => 0x654c,
    0xd1 => 0x7b1b, 0xd2 => 0x72c4, 0xd3 => 0x6da4, 0xd4 => 0x7fdf,
    0xd5 => 0x5ae1, 0xd6 => 0x62b5, 0xd7 => 0x5e95, 0xd8 => 0x5730,
    0xd9 => 0x8482, 0xda => 0x7b2c, 0xdb => 0x5e1d, 0xdc => 0x5f1f,
    0xdd => 0x9012, 0xde => 0x7f14, 0xdf => 0x98a0, 0xe0 => 0x6382,
    0xe1 => 0x6ec7, 0xe2 => 0x7898, 0xe3 => 0x70b9, 0xe4 => 0x5178,
    0xe5 => 0x975b, 0xe6 => 0x57ab, 0xe7 => 0x7535, 0xe8 => 0x4f43,
    0xe9 => 0x7538, 0xea => 0x5e97, 0xeb => 0x60e6, 0xec => 0x5960,
    0xed => 0x6dc0, 0xee => 0x6bbf, 0xef => 0x7889, 0xf0 => 0x53fc,
    0xf1 => 0x96d5, 0xf2 => 0x51cb, 0xf3 => 0x5201, 0xf4 => 0x6389,
    0xf5 => 0x540a, 0xf6 => 0x9493, 0xf7 => 0x8c03, 0xf8 => 0x8dcc,
    0xf9 => 0x7239, 0xfa => 0x789f, 0xfb => 0x8776, 0xfc => 0x8fed,
    0xfd => 0x8c0d, 0xfe => 0x53e0,
  },
  0xb6 => {
    0xa1 => 0x4e01, 0xa2 => 0x76ef, 0xa3 => 0x53ee, 0xa4 => 0x9489,
    0xa5 => 0x9876, 0xa6 => 0x9f0e, 0xa7 => 0x952d, 0xa8 => 0x5b9a,
    0xa9 => 0x8ba2, 0xaa => 0x4e22, 0xab => 0x4e1c, 0xac => 0x51ac,
    0xad => 0x8463, 0xae => 0x61c2, 0xaf => 0x52a8, 0xb0 => 0x680b,
    0xb1 => 0x4f97, 0xb2 => 0x606b, 0xb3 => 0x51bb, 0xb4 => 0x6d1e,
    0xb5 => 0x515c, 0xb6 => 0x6296, 0xb7 => 0x6597, 0xb8 => 0x9661,
    0xb9 => 0x8c46, 0xba => 0x9017, 0xbb => 0x75d8, 0xbc => 0x90fd,
    0xbd => 0x7763, 0xbe => 0x6bd2, 0xbf => 0x728a, 0xc0 => 0x72ec,
    0xc1 => 0x8bfb, 0xc2 => 0x5835, 0xc3 => 0x7779, 0xc4 => 0x8d4c,
    0xc5 => 0x675c, 0xc6 => 0x9540, 0xc7 => 0x809a, 0xc8 => 0x5ea6,
    0xc9 => 0x6e21, 0xca => 0x5992, 0xcb => 0x7aef, 0xcc => 0x77ed,
    0xcd => 0x953b, 0xce => 0x6bb5, 0xcf => 0x65ad, 0xd0 => 0x7f0e,
    0xd1 => 0x5806, 0xd2 => 0x5151, 0xd3 => 0x961f, 0xd4 => 0x5bf9,
    0xd5 => 0x58a9, 0xd6 => 0x5428, 0xd7 => 0x8e72, 0xd8 => 0x6566,
    0xd9 => 0x987f, 0xda => 0x56e4, 0xdb => 0x949d, 0xdc => 0x76fe,
    0xdd => 0x9041, 0xde => 0x6387, 0xdf => 0x54c6, 0xe0 => 0x591a,
    0xe1 => 0x593a, 0xe2 => 0x579b, 0xe3 => 0x8eb2, 0xe4 => 0x6735,
    0xe5 => 0x8dfa, 0xe6 => 0x8235, 0xe7 => 0x5241, 0xe8 => 0x60f0,
    0xe9 => 0x5815, 0xea => 0x86fe, 0xeb => 0x5ce8, 0xec => 0x9e45,
    0xed => 0x4fc4, 0xee => 0x989d, 0xef => 0x8bb9, 0xf0 => 0x5a25,
    0xf1 => 0x6076, 0xf2 => 0x5384, 0xf3 => 0x627c, 0xf4 => 0x904f,
    0xf5 => 0x9102, 0xf6 => 0x997f, 0xf7 => 0x6069, 0xf8 => 0x800c,
    0xf9 => 0x513f, 0xfa => 0x8033, 0xfb => 0x5c14, 0xfc => 0x9975,
    0xfd => 0x6d31, 0xfe => 0x4e8c,
  },
  0xb7 => {
    0xa1 => 0x8d30, 0xa2 => 0x53d1, 0xa3 => 0x7f5a, 0xa4 => 0x7b4f,
    0xa5 => 0x4f10, 0xa6 => 0x4e4f, 0xa7 => 0x9600, 0xa8 => 0x6cd5,
    0xa9 => 0x73d0, 0xaa => 0x85e9, 0xab => 0x5e06, 0xac => 0x756a,
    0xad => 0x7ffb, 0xae => 0x6a0a, 0xaf => 0x77fe, 0xb0 => 0x9492,
    0xb1 => 0x7e41, 0xb2 => 0x51e1, 0xb3 => 0x70e6, 0xb4 => 0x53cd,
    0xb5 => 0x8fd4, 0xb6 => 0x8303, 0xb7 => 0x8d29, 0xb8 => 0x72af,
    0xb9 => 0x996d, 0xba => 0x6cdb, 0xbb => 0x574a, 0xbc => 0x82b3,
    0xbd => 0x65b9, 0xbe => 0x80aa, 0xbf => 0x623f, 0xc0 => 0x9632,
    0xc1 => 0x59a8, 0xc2 => 0x4eff, 0xc3 => 0x8bbf, 0xc4 => 0x7eba,
    0xc5 => 0x653e, 0xc6 => 0x83f2, 0xc7 => 0x975e, 0xc8 => 0x5561,
    0xc9 => 0x98de, 0xca => 0x80a5, 0xcb => 0x532a, 0xcc => 0x8bfd,
    0xcd => 0x5420, 0xce => 0x80ba, 0xcf => 0x5e9f, 0xd0 => 0x6cb8,
    0xd1 => 0x8d39, 0xd2 => 0x82ac, 0xd3 => 0x915a, 0xd4 => 0x5429,
    0xd5 => 0x6c1b, 0xd6 => 0x5206, 0xd7 => 0x7eb7, 0xd8 => 0x575f,
    0xd9 => 0x711a, 0xda => 0x6c7e, 0xdb => 0x7c89, 0xdc => 0x594b,
    0xdd => 0x4efd, 0xde => 0x5fff, 0xdf => 0x6124, 0xe0 => 0x7caa,
    0xe1 => 0x4e30, 0xe2 => 0x5c01, 0xe3 => 0x67ab, 0xe4 => 0x8702,
    0xe5 => 0x5cf0, 0xe6 => 0x950b, 0xe7 => 0x98ce, 0xe8 => 0x75af,
    0xe9 => 0x70fd, 0xea => 0x9022, 0xeb => 0x51af, 0xec => 0x7f1d,
    0xed => 0x8bbd, 0xee => 0x5949, 0xef => 0x51e4, 0xf0 => 0x4f5b,
    0xf1 => 0x5426, 0xf2 => 0x592b, 0xf3 => 0x6577, 0xf4 => 0x80a4,
    0xf5 => 0x5b75, 0xf6 => 0x6276, 0xf7 => 0x62c2, 0xf8 => 0x8f90,
    0xf9 => 0x5e45, 0xfa => 0x6c1f, 0xfb => 0x7b26, 0xfc => 0x4f0f,
    0xfd => 0x4fd8, 0xfe => 0x670d,
  },
  0xb8 => {
    0xa1 => 0x6d6e, 0xa2 => 0x6daa, 0xa3 => 0x798f, 0xa4 => 0x88b1,
    0xa5 => 0x5f17, 0xa6 => 0x752b, 0xa7 => 0x629a, 0xa8 => 0x8f85,
    0xa9 => 0x4fef, 0xaa => 0x91dc, 0xab => 0x65a7, 0xac => 0x812f,
    0xad => 0x8151, 0xae => 0x5e9c, 0xaf => 0x8150, 0xb0 => 0x8d74,
    0xb1 => 0x526f, 0xb2 => 0x8986, 0xb3 => 0x8d4b, 0xb4 => 0x590d,
    0xb5 => 0x5085, 0xb6 => 0x4ed8, 0xb7 => 0x961c, 0xb8 => 0x7236,
    0xb9 => 0x8179, 0xba => 0x8d1f, 0xbb => 0x5bcc, 0xbc => 0x8ba3,
    0xbd => 0x9644, 0xbe => 0x5987, 0xbf => 0x7f1a, 0xc0 => 0x5490,
    0xc1 => 0x5676, 0xc2 => 0x560e, 0xc3 => 0x8be5, 0xc4 => 0x6539,
    0xc5 => 0x6982, 0xc6 => 0x9499, 0xc7 => 0x76d6, 0xc8 => 0x6e89,
    0xc9 => 0x5e72, 0xca => 0x7518, 0xcb => 0x6746, 0xcc => 0x67d1,
    0xcd => 0x7aff, 0xce => 0x809d, 0xcf => 0x8d76, 0xd0 => 0x611f,
    0xd1 => 0x79c6, 0xd2 => 0x6562, 0xd3 => 0x8d63, 0xd4 => 0x5188,
    0xd5 => 0x521a, 0xd6 => 0x94a2, 0xd7 => 0x7f38, 0xd8 => 0x809b,
    0xd9 => 0x7eb2, 0xda => 0x5c97, 0xdb => 0x6e2f, 0xdc => 0x6760,
    0xdd => 0x7bd9, 0xde => 0x768b, 0xdf => 0x9ad8, 0xe0 => 0x818f,
    0xe1 => 0x7f94, 0xe2 => 0x7cd5, 0xe3 => 0x641e, 0xe4 => 0x9550,
    0xe5 => 0x7a3f, 0xe6 => 0x544a, 0xe7 => 0x54e5, 0xe8 => 0x6b4c,
    0xe9 => 0x6401, 0xea => 0x6208, 0xeb => 0x9e3d, 0xec => 0x80f3,
    0xed => 0x7599, 0xee => 0x5272, 0xef => 0x9769, 0xf0 => 0x845b,
    0xf1 => 0x683c, 0xf2 => 0x86e4, 0xf3 => 0x9601, 0xf4 => 0x9694,
    0xf5 => 0x94ec, 0xf6 => 0x4e2a, 0xf7 => 0x5404, 0xf8 => 0x7ed9,
    0xf9 => 0x6839, 0xfa => 0x8ddf, 0xfb => 0x8015, 0xfc => 0x66f4,
    0xfd => 0x5e9a, 0xfe => 0x7fb9,
  },
  0xb9 => {
    0xa1 => 0x57c2, 0xa2 => 0x803f, 0xa3 => 0x6897, 0xa4 => 0x5de5,
    0xa5 => 0x653b, 0xa6 => 0x529f, 0xa7 => 0x606d, 0xa8 => 0x9f9a,
    0xa9 => 0x4f9b, 0xaa => 0x8eac, 0xab => 0x516c, 0xac => 0x5bab,
    0xad => 0x5f13, 0xae => 0x5de9, 0xaf => 0x6c5e, 0xb0 => 0x62f1,
    0xb1 => 0x8d21, 0xb2 => 0x5171, 0xb3 => 0x94a9, 0xb4 => 0x52fe,
    0xb5 => 0x6c9f, 0xb6 => 0x82df, 0xb7 => 0x72d7, 0xb8 => 0x57a2,
    0xb9 => 0x6784, 0xba => 0x8d2d, 0xbb => 0x591f, 0xbc => 0x8f9c,
    0xbd => 0x83c7, 0xbe => 0x5495, 0xbf => 0x7b8d, 0xc0 => 0x4f30,
    0xc1 => 0x6cbd, 0xc2 => 0x5b64, 0xc3 => 0x59d1, 0xc4 => 0x9f13,
    0xc5 => 0x53e4, 0xc6 => 0x86ca, 0xc7 => 0x9aa8, 0xc8 => 0x8c37,
    0xc9 => 0x80a1, 0xca => 0x6545, 0xcb => 0x987e, 0xcc => 0x56fa,
    0xcd => 0x96c7, 0xce => 0x522e, 0xcf => 0x74dc, 0xd0 => 0x5250,
    0xd1 => 0x5be1, 0xd2 => 0x6302, 0xd3 => 0x8902, 0xd4 => 0x4e56,
    0xd5 => 0x62d0, 0xd6 => 0x602a, 0xd7 => 0x68fa, 0xd8 => 0x5173,
    0xd9 => 0x5b98, 0xda => 0x51a0, 0xdb => 0x89c2, 0xdc => 0x7ba1,
    0xdd => 0x9986, 0xde => 0x7f50, 0xdf => 0x60ef, 0xe0 => 0x704c,
    0xe1 => 0x8d2f, 0xe2 => 0x5149, 0xe3 => 0x5e7f, 0xe4 => 0x901b,
    0xe5 => 0x7470, 0xe6 => 0x89c4, 0xe7 => 0x572d, 0xe8 => 0x7845,
    0xe9 => 0x5f52, 0xea => 0x9f9f, 0xeb => 0x95fa, 0xec => 0x8f68,
    0xed => 0x9b3c, 0xee => 0x8be1, 0xef => 0x7678, 0xf0 => 0x6842,
    0xf1 => 0x67dc, 0xf2 => 0x8dea, 0xf3 => 0x8d35, 0xf4 => 0x523d,
    0xf5 => 0x8f8a, 0xf6 => 0x6eda, 0xf7 => 0x68cd, 0xf8 => 0x9505,
    0xf9 => 0x90ed, 0xfa => 0x56fd, 0xfb => 0x679c, 0xfc => 0x88f9,
    0xfd => 0x8fc7, 0xfe => 0x54c8,
  },
  0xba => {
    0xa1 => 0x9ab8, 0xa2 => 0x5b69, 0xa3 => 0x6d77, 0xa4 => 0x6c26,
    0xa5 => 0x4ea5, 0xa6 => 0x5bb3, 0xa7 => 0x9a87, 0xa8 => 0x9163,
    0xa9 => 0x61a8, 0xaa => 0x90af, 0xab => 0x97e9, 0xac => 0x542b,
    0xad => 0x6db5, 0xae => 0x5bd2, 0xaf => 0x51fd, 0xb0 => 0x558a,
    0xb1 => 0x7f55, 0xb2 => 0x7ff0, 0xb3 => 0x64bc, 0xb4 => 0x634d,
    0xb5 => 0x65f1, 0xb6 => 0x61be, 0xb7 => 0x608d, 0xb8 => 0x710a,
    0xb9 => 0x6c57, 0xba => 0x6c49, 0xbb => 0x592f, 0xbc => 0x676d,
    0xbd => 0x822a, 0xbe => 0x58d5, 0xbf => 0x568e, 0xc0 => 0x8c6a,
    0xc1 => 0x6beb, 0xc2 => 0x90dd, 0xc3 => 0x597d, 0xc4 => 0x8017,
    0xc5 => 0x53f7, 0xc6 => 0x6d69, 0xc7 => 0x5475, 0xc8 => 0x559d,
    0xc9 => 0x8377, 0xca => 0x83cf, 0xcb => 0x6838, 0xcc => 0x79be,
    0xcd => 0x548c, 0xce => 0x4f55, 0xcf => 0x5408, 0xd0 => 0x76d2,
    0xd1 => 0x8c89, 0xd2 => 0x9602, 0xd3 => 0x6cb3, 0xd4 => 0x6db8,
    0xd5 => 0x8d6b, 0xd6 => 0x8910, 0xd7 => 0x9e64, 0xd8 => 0x8d3a,
    0xd9 => 0x563f, 0xda => 0x9ed1, 0xdb => 0x75d5, 0xdc => 0x5f88,
    0xdd => 0x72e0, 0xde => 0x6068, 0xdf => 0x54fc, 0xe0 => 0x4ea8,
    0xe1 => 0x6a2a, 0xe2 => 0x8861, 0xe3 => 0x6052, 0xe4 => 0x8f70,
    0xe5 => 0x54c4, 0xe6 => 0x70d8, 0xe7 => 0x8679, 0xe8 => 0x9e3f,
    0xe9 => 0x6d2a, 0xea => 0x5b8f, 0xeb => 0x5f18, 0xec => 0x7ea2,
    0xed => 0x5589, 0xee => 0x4faf, 0xef => 0x7334, 0xf0 => 0x543c,
    0xf1 => 0x539a, 0xf2 => 0x5019, 0xf3 => 0x540e, 0xf4 => 0x547c,
    0xf5 => 0x4e4e, 0xf6 => 0x5ffd, 0xf7 => 0x745a, 0xf8 => 0x58f6,
    0xf9 => 0x846b, 0xfa => 0x80e1, 0xfb => 0x8774, 0xfc => 0x72d0,
    0xfd => 0x7cca, 0xfe => 0x6e56,
  },
  0xbb => {
    0xa1 => 0x5f27, 0xa2 => 0x864e, 0xa3 => 0x552c, 0xa4 => 0x62a4,
    0xa5 => 0x4e92, 0xa6 => 0x6caa, 0xa7 => 0x6237, 0xa8 => 0x82b1,
    0xa9 => 0x54d7, 0xaa => 0x534e, 0xab => 0x733e, 0xac => 0x6ed1,
    0xad => 0x753b, 0xae => 0x5212, 0xaf => 0x5316, 0xb0 => 0x8bdd,
    0xb1 => 0x69d0, 0xb2 => 0x5f8a, 0xb3 => 0x6000, 0xb4 => 0x6dee,
    0xb5 => 0x574f, 0xb6 => 0x6b22, 0xb7 => 0x73af, 0xb8 => 0x6853,
    0xb9 => 0x8fd8, 0xba => 0x7f13, 0xbb => 0x6362, 0xbc => 0x60a3,
    0xbd => 0x5524, 0xbe => 0x75ea, 0xbf => 0x8c62, 0xc0 => 0x7115,
    0xc1 => 0x6da3, 0xc2 => 0x5ba6, 0xc3 => 0x5e7b, 0xc4 => 0x8352,
    0xc5 => 0x614c, 0xc6 => 0x9ec4, 0xc7 => 0x78fa, 0xc8 => 0x8757,
    0xc9 => 0x7c27, 0xca => 0x7687, 0xcb => 0x51f0, 0xcc => 0x60f6,
    0xcd => 0x714c, 0xce => 0x6643, 0xcf => 0x5e4c, 0xd0 => 0x604d,
    0xd1 => 0x8c0e, 0xd2 => 0x7070, 0xd3 => 0x6325, 0xd4 => 0x8f89,
    0xd5 => 0x5fbd, 0xd6 => 0x6062, 0xd7 => 0x86d4, 0xd8 => 0x56de,
    0xd9 => 0x6bc1, 0xda => 0x6094, 0xdb => 0x6167, 0xdc => 0x5349,
    0xdd => 0x60e0, 0xde => 0x6666, 0xdf => 0x8d3f, 0xe0 => 0x79fd,
    0xe1 => 0x4f1a, 0xe2 => 0x70e9, 0xe3 => 0x6c47, 0xe4 => 0x8bb3,
    0xe5 => 0x8bf2, 0xe6 => 0x7ed8, 0xe7 => 0x8364, 0xe8 => 0x660f,
    0xe9 => 0x5a5a, 0xea => 0x9b42, 0xeb => 0x6d51, 0xec => 0x6df7,
    0xed => 0x8c41, 0xee => 0x6d3b, 0xef => 0x4f19, 0xf0 => 0x706b,
    0xf1 => 0x83b7, 0xf2 => 0x6216, 0xf3 => 0x60d1, 0xf4 => 0x970d,
    0xf5 => 0x8d27, 0xf6 => 0x7978, 0xf7 => 0x51fb, 0xf8 => 0x573e,
    0xf9 => 0x57fa, 0xfa => 0x673a, 0xfb => 0x7578, 0xfc => 0x7a3d,
    0xfd => 0x79ef, 0xfe => 0x7b95,
  },
  0xbc => {
    0xa1 => 0x808c, 0xa2 => 0x9965, 0xa3 => 0x8ff9, 0xa4 => 0x6fc0,
    0xa5 => 0x8ba5, 0xa6 => 0x9e21, 0xa7 => 0x59ec, 0xa8 => 0x7ee9,
    0xa9 => 0x7f09, 0xaa => 0x5409, 0xab => 0x6781, 0xac => 0x68d8,
    0xad => 0x8f91, 0xae => 0x7c4d, 0xaf => 0x96c6, 0xb0 => 0x53ca,
    0xb1 => 0x6025, 0xb2 => 0x75be, 0xb3 => 0x6c72, 0xb4 => 0x5373,
    0xb5 => 0x5ac9, 0xb6 => 0x7ea7, 0xb7 => 0x6324, 0xb8 => 0x51e0,
    0xb9 => 0x810a, 0xba => 0x5df1, 0xbb => 0x84df, 0xbc => 0x6280,
    0xbd => 0x5180, 0xbe => 0x5b63, 0xbf => 0x4f0e, 0xc0 => 0x796d,
    0xc1 => 0x5242, 0xc2 => 0x60b8, 0xc3 => 0x6d4e, 0xc4 => 0x5bc4,
    0xc5 => 0x5bc2, 0xc6 => 0x8ba1, 0xc7 => 0x8bb0, 0xc8 => 0x65e2,
    0xc9 => 0x5fcc, 0xca => 0x9645, 0xcb => 0x5993, 0xcc => 0x7ee7,
    0xcd => 0x7eaa, 0xce => 0x5609, 0xcf => 0x67b7, 0xd0 => 0x5939,
    0xd1 => 0x4f73, 0xd2 => 0x5bb6, 0xd3 => 0x52a0, 0xd4 => 0x835a,
    0xd5 => 0x988a, 0xd6 => 0x8d3e, 0xd7 => 0x7532, 0xd8 => 0x94be,
    0xd9 => 0x5047, 0xda => 0x7a3c, 0xdb => 0x4ef7, 0xdc => 0x67b6,
    0xdd => 0x9a7e, 0xde => 0x5ac1, 0xdf => 0x6b7c, 0xe0 => 0x76d1,
    0xe1 => 0x575a, 0xe2 => 0x5c16, 0xe3 => 0x7b3a, 0xe4 => 0x95f4,
    0xe5 => 0x714e, 0xe6 => 0x517c, 0xe7 => 0x80a9, 0xe8 => 0x8270,
    0xe9 => 0x5978, 0xea => 0x7f04, 0xeb => 0x8327, 0xec => 0x68c0,
    0xed => 0x67ec, 0xee => 0x78b1, 0xef => 0x7877, 0xf0 => 0x62e3,
    0xf1 => 0x6361, 0xf2 => 0x7b80, 0xf3 => 0x4fed, 0xf4 => 0x526a,
    0xf5 => 0x51cf, 0xf6 => 0x8350, 0xf7 => 0x69db, 0xf8 => 0x9274,
    0xf9 => 0x8df5, 0xfa => 0x8d31, 0xfb => 0x89c1, 0xfc => 0x952e,
    0xfd => 0x7bad, 0xfe => 0x4ef6,
  },
  0xbd => {
    0xa1 => 0x5065, 0xa2 => 0x8230, 0xa3 => 0x5251, 0xa4 => 0x996f,
    0xa5 => 0x6e10, 0xa6 => 0x6e85, 0xa7 => 0x6da7, 0xa8 => 0x5efa,
    0xa9 => 0x50f5, 0xaa => 0x59dc, 0xab => 0x5c06, 0xac => 0x6d46,
    0xad => 0x6c5f, 0xae => 0x7586, 0xaf => 0x848b, 0xb0 => 0x6868,
    0xb1 => 0x5956, 0xb2 => 0x8bb2, 0xb3 => 0x5320, 0xb4 => 0x9171,
    0xb5 => 0x964d, 0xb6 => 0x8549, 0xb7 => 0x6912, 0xb8 => 0x7901,
    0xb9 => 0x7126, 0xba => 0x80f6, 0xbb => 0x4ea4, 0xbc => 0x90ca,
    0xbd => 0x6d47, 0xbe => 0x9a84, 0xbf => 0x5a07, 0xc0 => 0x56bc,
    0xc1 => 0x6405, 0xc2 => 0x94f0, 0xc3 => 0x77eb, 0xc4 => 0x4fa5,
    0xc5 => 0x811a, 0xc6 => 0x72e1, 0xc7 => 0x89d2, 0xc8 => 0x997a,
    0xc9 => 0x7f34, 0xca => 0x7ede, 0xcb => 0x527f, 0xcc => 0x6559,
    0xcd => 0x9175, 0xce => 0x8f7f, 0xcf => 0x8f83, 0xd0 => 0x53eb,
    0xd1 => 0x7a96, 0xd2 => 0x63ed, 0xd3 => 0x63a5, 0xd4 => 0x7686,
    0xd5 => 0x79f8, 0xd6 => 0x8857, 0xd7 => 0x9636, 0xd8 => 0x622a,
    0xd9 => 0x52ab, 0xda => 0x8282, 0xdb => 0x6854, 0xdc => 0x6770,
    0xdd => 0x6377, 0xde => 0x776b, 0xdf => 0x7aed, 0xe0 => 0x6d01,
    0xe1 => 0x7ed3, 0xe2 => 0x89e3, 0xe3 => 0x59d0, 0xe4 => 0x6212,
    0xe5 => 0x85c9, 0xe6 => 0x82a5, 0xe7 => 0x754c, 0xe8 => 0x501f,
    0xe9 => 0x4ecb, 0xea => 0x75a5, 0xeb => 0x8beb, 0xec => 0x5c4a,
    0xed => 0x5dfe, 0xee => 0x7b4b, 0xef => 0x65a4, 0xf0 => 0x91d1,
    0xf1 => 0x4eca, 0xf2 => 0x6d25, 0xf3 => 0x895f, 0xf4 => 0x7d27,
    0xf5 => 0x9526, 0xf6 => 0x4ec5, 0xf7 => 0x8c28, 0xf8 => 0x8fdb,
    0xf9 => 0x9773, 0xfa => 0x664b, 0xfb => 0x7981, 0xfc => 0x8fd1,
    0xfd => 0x70ec, 0xfe => 0x6d78,
  },
  0xbe => {
    0xa1 => 0x5c3d, 0xa2 => 0x52b2, 0xa3 => 0x8346, 0xa4 => 0x5162,
    0xa5 => 0x830e, 0xa6 => 0x775b, 0xa7 => 0x6676, 0xa8 => 0x9cb8,
    0xa9 => 0x4eac, 0xaa => 0x60ca, 0xab => 0x7cbe, 0xac => 0x7cb3,
    0xad => 0x7ecf, 0xae => 0x4e95, 0xaf => 0x8b66, 0xb0 => 0x666f,
    0xb1 => 0x9888, 0xb2 => 0x9759, 0xb3 => 0x5883, 0xb4 => 0x656c,
    0xb5 => 0x955c, 0xb6 => 0x5f84, 0xb7 => 0x75c9, 0xb8 => 0x9756,
    0xb9 => 0x7adf, 0xba => 0x7ade, 0xbb => 0x51c0, 0xbc => 0x70af,
    0xbd => 0x7a98, 0xbe => 0x63ea, 0xbf => 0x7a76, 0xc0 => 0x7ea0,
    0xc1 => 0x7396, 0xc2 => 0x97ed, 0xc3 => 0x4e45, 0xc4 => 0x7078,
    0xc5 => 0x4e5d, 0xc6 => 0x9152, 0xc7 => 0x53a9, 0xc8 => 0x6551,
    0xc9 => 0x65e7, 0xca => 0x81fc, 0xcb => 0x8205, 0xcc => 0x548e,
    0xcd => 0x5c31, 0xce => 0x759a, 0xcf => 0x97a0, 0xd0 => 0x62d8,
    0xd1 => 0x72d9, 0xd2 => 0x75bd, 0xd3 => 0x5c45, 0xd4 => 0x9a79,
    0xd5 => 0x83ca, 0xd6 => 0x5c40, 0xd7 => 0x5480, 0xd8 => 0x77e9,
    0xd9 => 0x4e3e, 0xda => 0x6cae, 0xdb => 0x805a, 0xdc => 0x62d2,
    0xdd => 0x636e, 0xde => 0x5de8, 0xdf => 0x5177, 0xe0 => 0x8ddd,
    0xe1 => 0x8e1e, 0xe2 => 0x952f, 0xe3 => 0x4ff1, 0xe4 => 0x53e5,
    0xe5 => 0x60e7, 0xe6 => 0x70ac, 0xe7 => 0x5267, 0xe8 => 0x6350,
    0xe9 => 0x9e43, 0xea => 0x5a1f, 0xeb => 0x5026, 0xec => 0x7737,
    0xed => 0x5377, 0xee => 0x7ee2, 0xef => 0x6485, 0xf0 => 0x652b,
    0xf1 => 0x6289, 0xf2 => 0x6398, 0xf3 => 0x5014, 0xf4 => 0x7235,
    0xf5 => 0x89c9, 0xf6 => 0x51b3, 0xf7 => 0x8bc0, 0xf8 => 0x7edd,
    0xf9 => 0x5747, 0xfa => 0x83cc, 0xfb => 0x94a7, 0xfc => 0x519b,
    0xfd => 0x541b, 0xfe => 0x5cfb,
  },
  0xbf => {
    0xa1 => 0x4fca, 0xa2 => 0x7ae3, 0xa3 => 0x6d5a, 0xa4 => 0x90e1,
    0xa5 => 0x9a8f, 0xa6 => 0x5580, 0xa7 => 0x5496, 0xa8 => 0x5361,
    0xa9 => 0x54af, 0xaa => 0x5f00, 0xab => 0x63e9, 0xac => 0x6977,
    0xad => 0x51ef, 0xae => 0x6168, 0xaf => 0x520a, 0xb0 => 0x582a,
    0xb1 => 0x52d8, 0xb2 => 0x574e, 0xb3 => 0x780d, 0xb4 => 0x770b,
    0xb5 => 0x5eb7, 0xb6 => 0x6177, 0xb7 => 0x7ce0, 0xb8 => 0x625b,
    0xb9 => 0x6297, 0xba => 0x4ea2, 0xbb => 0x7095, 0xbc => 0x8003,
    0xbd => 0x62f7, 0xbe => 0x70e4, 0xbf => 0x9760, 0xc0 => 0x5777,
    0xc1 => 0x82db, 0xc2 => 0x67ef, 0xc3 => 0x68f5, 0xc4 => 0x78d5,
    0xc5 => 0x9897, 0xc6 => 0x79d1, 0xc7 => 0x58f3, 0xc8 => 0x54b3,
    0xc9 => 0x53ef, 0xca => 0x6e34, 0xcb => 0x514b, 0xcc => 0x523b,
    0xcd => 0x5ba2, 0xce => 0x8bfe, 0xcf => 0x80af, 0xd0 => 0x5543,
    0xd1 => 0x57a6, 0xd2 => 0x6073, 0xd3 => 0x5751, 0xd4 => 0x542d,
    0xd5 => 0x7a7a, 0xd6 => 0x6050, 0xd7 => 0x5b54, 0xd8 => 0x63a7,
    0xd9 => 0x62a0, 0xda => 0x53e3, 0xdb => 0x6263, 0xdc => 0x5bc7,
    0xdd => 0x67af, 0xde => 0x54ed, 0xdf => 0x7a9f, 0xe0 => 0x82e6,
    0xe1 => 0x9177, 0xe2 => 0x5e93, 0xe3 => 0x88e4, 0xe4 => 0x5938,
    0xe5 => 0x57ae, 0xe6 => 0x630e, 0xe7 => 0x8de8, 0xe8 => 0x80ef,
    0xe9 => 0x5757, 0xea => 0x7b77, 0xeb => 0x4fa9, 0xec => 0x5feb,
    0xed => 0x5bbd, 0xee => 0x6b3e, 0xef => 0x5321, 0xf0 => 0x7b50,
    0xf1 => 0x72c2, 0xf2 => 0x6846, 0xf3 => 0x77ff, 0xf4 => 0x7736,
    0xf5 => 0x65f7, 0xf6 => 0x51b5, 0xf7 => 0x4e8f, 0xf8 => 0x76d4,
    0xf9 => 0x5cbf, 0xfa => 0x7aa5, 0xfb => 0x8475, 0xfc => 0x594e,
    0xfd => 0x9b41, 0xfe => 0x5080,
  },
  0xc0 => {
    0xa1 => 0x9988, 0xa2 => 0x6127, 0xa3 => 0x6e83, 0xa4 => 0x5764,
    0xa5 => 0x6606, 0xa6 => 0x6346, 0xa7 => 0x56f0, 0xa8 => 0x62ec,
    0xa9 => 0x6269, 0xaa => 0x5ed3, 0xab => 0x9614, 0xac => 0x5783,
    0xad => 0x62c9, 0xae => 0x5587, 0xaf => 0x8721, 0xb0 => 0x814a,
    0xb1 => 0x8fa3, 0xb2 => 0x5566, 0xb3 => 0x83b1, 0xb4 => 0x6765,
    0xb5 => 0x8d56, 0xb6 => 0x84dd, 0xb7 => 0x5a6a, 0xb8 => 0x680f,
    0xb9 => 0x62e6, 0xba => 0x7bee, 0xbb => 0x9611, 0xbc => 0x5170,
    0xbd => 0x6f9c, 0xbe => 0x8c30, 0xbf => 0x63fd, 0xc0 => 0x89c8,
    0xc1 => 0x61d2, 0xc2 => 0x7f06, 0xc3 => 0x70c2, 0xc4 => 0x6ee5,
    0xc5 => 0x7405, 0xc6 => 0x6994, 0xc7 => 0x72fc, 0xc8 => 0x5eca,
    0xc9 => 0x90ce, 0xca => 0x6717, 0xcb => 0x6d6a, 0xcc => 0x635e,
    0xcd => 0x52b3, 0xce => 0x7262, 0xcf => 0x8001, 0xd0 => 0x4f6c,
    0xd1 => 0x59e5, 0xd2 => 0x916a, 0xd3 => 0x70d9, 0xd4 => 0x6d9d,
    0xd5 => 0x52d2, 0xd6 => 0x4e50, 0xd7 => 0x96f7, 0xd8 => 0x956d,
    0xd9 => 0x857e, 0xda => 0x78ca, 0xdb => 0x7d2f, 0xdc => 0x5121,
    0xdd => 0x5792, 0xde => 0x64c2, 0xdf => 0x808b, 0xe0 => 0x7c7b,
    0xe1 => 0x6cea, 0xe2 => 0x68f1, 0xe3 => 0x695e, 0xe4 => 0x51b7,
    0xe5 => 0x5398, 0xe6 => 0x68a8, 0xe7 => 0x7281, 0xe8 => 0x9ece,
    0xe9 => 0x7bf1, 0xea => 0x72f8, 0xeb => 0x79bb, 0xec => 0x6f13,
    0xed => 0x7406, 0xee => 0x674e, 0xef => 0x91cc, 0xf0 => 0x9ca4,
    0xf1 => 0x793c, 0xf2 => 0x8389, 0xf3 => 0x8354, 0xf4 => 0x540f,
    0xf5 => 0x6817, 0xf6 => 0x4e3d, 0xf7 => 0x5389, 0xf8 => 0x52b1,
    0xf9 => 0x783e, 0xfa => 0x5386, 0xfb => 0x5229, 0xfc => 0x5088,
    0xfd => 0x4f8b, 0xfe => 0x4fd0,
  },
  0xc1 => {
    0xa1 => 0x75e2, 0xa2 => 0x7acb, 0xa3 => 0x7c92, 0xa4 => 0x6ca5,
    0xa5 => 0x96b6, 0xa6 => 0x529b, 0xa7 => 0x7483, 0xa8 => 0x54e9,
    0xa9 => 0x4fe9, 0xaa => 0x8054, 0xab => 0x83b2, 0xac => 0x8fde,
    0xad => 0x9570, 0xae => 0x5ec9, 0xaf => 0x601c, 0xb0 => 0x6d9f,
    0xb1 => 0x5e18, 0xb2 => 0x655b, 0xb3 => 0x8138, 0xb4 => 0x94fe,
    0xb5 => 0x604b, 0xb6 => 0x70bc, 0xb7 => 0x7ec3, 0xb8 => 0x7cae,
    0xb9 => 0x51c9, 0xba => 0x6881, 0xbb => 0x7cb1, 0xbc => 0x826f,
    0xbd => 0x4e24, 0xbe => 0x8f86, 0xbf => 0x91cf, 0xc0 => 0x667e,
    0xc1 => 0x4eae, 0xc2 => 0x8c05, 0xc3 => 0x64a9, 0xc4 => 0x804a,
    0xc5 => 0x50da, 0xc6 => 0x7597, 0xc7 => 0x71ce, 0xc8 => 0x5be5,
    0xc9 => 0x8fbd, 0xca => 0x6f66, 0xcb => 0x4e86, 0xcc => 0x6482,
    0xcd => 0x9563, 0xce => 0x5ed6, 0xcf => 0x6599, 0xd0 => 0x5217,
    0xd1 => 0x88c2, 0xd2 => 0x70c8, 0xd3 => 0x52a3, 0xd4 => 0x730e,
    0xd5 => 0x7433, 0xd6 => 0x6797, 0xd7 => 0x78f7, 0xd8 => 0x9716,
    0xd9 => 0x4e34, 0xda => 0x90bb, 0xdb => 0x9cde, 0xdc => 0x6dcb,
    0xdd => 0x51db, 0xde => 0x8d41, 0xdf => 0x541d, 0xe0 => 0x62ce,
    0xe1 => 0x73b2, 0xe2 => 0x83f1, 0xe3 => 0x96f6, 0xe4 => 0x9f84,
    0xe5 => 0x94c3, 0xe6 => 0x4f36, 0xe7 => 0x7f9a, 0xe8 => 0x51cc,
    0xe9 => 0x7075, 0xea => 0x9675, 0xeb => 0x5cad, 0xec => 0x9886,
    0xed => 0x53e6, 0xee => 0x4ee4, 0xef => 0x6e9c, 0xf0 => 0x7409,
    0xf1 => 0x69b4, 0xf2 => 0x786b, 0xf3 => 0x998f, 0xf4 => 0x7559,
    0xf5 => 0x5218, 0xf6 => 0x7624, 0xf7 => 0x6d41, 0xf8 => 0x67f3,
    0xf9 => 0x516d, 0xfa => 0x9f99, 0xfb => 0x804b, 0xfc => 0x5499,
    0xfd => 0x7b3c, 0xfe => 0x7abf,
  },
  0xc2 => {
    0xa1 => 0x9686, 0xa2 => 0x5784, 0xa3 => 0x62e2, 0xa4 => 0x9647,
    0xa5 => 0x697c, 0xa6 => 0x5a04, 0xa7 => 0x6402, 0xa8 => 0x7bd3,
    0xa9 => 0x6f0f, 0xaa => 0x964b, 0xab => 0x82a6, 0xac => 0x5362,
    0xad => 0x9885, 0xae => 0x5e90, 0xaf => 0x7089, 0xb0 => 0x63b3,
    0xb1 => 0x5364, 0xb2 => 0x864f, 0xb3 => 0x9c81, 0xb4 => 0x9e93,
    0xb5 => 0x788c, 0xb6 => 0x9732, 0xb7 => 0x8def, 0xb8 => 0x8d42,
    0xb9 => 0x9e7f, 0xba => 0x6f5e, 0xbb => 0x7984, 0xbc => 0x5f55,
    0xbd => 0x9646, 0xbe => 0x622e, 0xbf => 0x9a74, 0xc0 => 0x5415,
    0xc1 => 0x94dd, 0xc2 => 0x4fa3, 0xc3 => 0x65c5, 0xc4 => 0x5c65,
    0xc5 => 0x5c61, 0xc6 => 0x7f15, 0xc7 => 0x8651, 0xc8 => 0x6c2f,
    0xc9 => 0x5f8b, 0xca => 0x7387, 0xcb => 0x6ee4, 0xcc => 0x7eff,
    0xcd => 0x5ce6, 0xce => 0x631b, 0xcf => 0x5b6a, 0xd0 => 0x6ee6,
    0xd1 => 0x5375, 0xd2 => 0x4e71, 0xd3 => 0x63a0, 0xd4 => 0x7565,
    0xd5 => 0x62a1, 0xd6 => 0x8f6e, 0xd7 => 0x4f26, 0xd8 => 0x4ed1,
    0xd9 => 0x6ca6, 0xda => 0x7eb6, 0xdb => 0x8bba, 0xdc => 0x841d,
    0xdd => 0x87ba, 0xde => 0x7f57, 0xdf => 0x903b, 0xe0 => 0x9523,
    0xe1 => 0x7ba9, 0xe2 => 0x9aa1, 0xe3 => 0x88f8, 0xe4 => 0x843d,
    0xe5 => 0x6d1b, 0xe6 => 0x9a86, 0xe7 => 0x7edc, 0xe8 => 0x5988,
    0xe9 => 0x9ebb, 0xea => 0x739b, 0xeb => 0x7801, 0xec => 0x8682,
    0xed => 0x9a6c, 0xee => 0x9a82, 0xef => 0x561b, 0xf0 => 0x5417,
    0xf1 => 0x57cb, 0xf2 => 0x4e70, 0xf3 => 0x9ea6, 0xf4 => 0x5356,
    0xf5 => 0x8fc8, 0xf6 => 0x8109, 0xf7 => 0x7792, 0xf8 => 0x9992,
    0xf9 => 0x86ee, 0xfa => 0x6ee1, 0xfb => 0x8513, 0xfc => 0x66fc,
    0xfd => 0x6162, 0xfe => 0x6f2b,
  },
  0xc3 => {
    0xa1 => 0x8c29, 0xa2 => 0x8292, 0xa3 => 0x832b, 0xa4 => 0x76f2,
    0xa5 => 0x6c13, 0xa6 => 0x5fd9, 0xa7 => 0x83bd, 0xa8 => 0x732b,
    0xa9 => 0x8305, 0xaa => 0x951a, 0xab => 0x6bdb, 0xac => 0x77db,
    0xad => 0x94c6, 0xae => 0x536f, 0xaf => 0x8302, 0xb0 => 0x5192,
    0xb1 => 0x5e3d, 0xb2 => 0x8c8c, 0xb3 => 0x8d38, 0xb4 => 0x4e48,
    0xb5 => 0x73ab, 0xb6 => 0x679a, 0xb7 => 0x6885, 0xb8 => 0x9176,
    0xb9 => 0x9709, 0xba => 0x7164, 0xbb => 0x6ca1, 0xbc => 0x7709,
    0xbd => 0x5a92, 0xbe => 0x9541, 0xbf => 0x6bcf, 0xc0 => 0x7f8e,
    0xc1 => 0x6627, 0xc2 => 0x5bd0, 0xc3 => 0x59b9, 0xc4 => 0x5a9a,
    0xc5 => 0x95e8, 0xc6 => 0x95f7, 0xc7 => 0x4eec, 0xc8 => 0x840c,
    0xc9 => 0x8499, 0xca => 0x6aac, 0xcb => 0x76df, 0xcc => 0x9530,
    0xcd => 0x731b, 0xce => 0x68a6, 0xcf => 0x5b5f, 0xd0 => 0x772f,
    0xd1 => 0x919a, 0xd2 => 0x9761, 0xd3 => 0x7cdc, 0xd4 => 0x8ff7,
    0xd5 => 0x8c1c, 0xd6 => 0x5f25, 0xd7 => 0x7c73, 0xd8 => 0x79d8,
    0xd9 => 0x89c5, 0xda => 0x6ccc, 0xdb => 0x871c, 0xdc => 0x5bc6,
    0xdd => 0x5e42, 0xde => 0x68c9, 0xdf => 0x7720, 0xe0 => 0x7ef5,
    0xe1 => 0x5195, 0xe2 => 0x514d, 0xe3 => 0x52c9, 0xe4 => 0x5a29,
    0xe5 => 0x7f05, 0xe6 => 0x9762, 0xe7 => 0x82d7, 0xe8 => 0x63cf,
    0xe9 => 0x7784, 0xea => 0x85d0, 0xeb => 0x79d2, 0xec => 0x6e3a,
    0xed => 0x5e99, 0xee => 0x5999, 0xef => 0x8511, 0xf0 => 0x706d,
    0xf1 => 0x6c11, 0xf2 => 0x62bf, 0xf3 => 0x76bf, 0xf4 => 0x654f,
    0xf5 => 0x60af, 0xf6 => 0x95fd, 0xf7 => 0x660e, 0xf8 => 0x879f,
    0xf9 => 0x9e23, 0xfa => 0x94ed, 0xfb => 0x540d, 0xfc => 0x547d,
    0xfd => 0x8c2c, 0xfe => 0x6478,
  },
  0xc4 => {
    0xa1 => 0x6479, 0xa2 => 0x8611, 0xa3 => 0x6a21, 0xa4 => 0x819c,
    0xa5 => 0x78e8, 0xa6 => 0x6469, 0xa7 => 0x9b54, 0xa8 => 0x62b9,
    0xa9 => 0x672b, 0xaa => 0x83ab, 0xab => 0x58a8, 0xac => 0x9ed8,
    0xad => 0x6cab, 0xae => 0x6f20, 0xaf => 0x5bde, 0xb0 => 0x964c,
    0xb1 => 0x8c0b, 0xb2 => 0x725f, 0xb3 => 0x67d0, 0xb4 => 0x62c7,
    0xb5 => 0x7261, 0xb6 => 0x4ea9, 0xb7 => 0x59c6, 0xb8 => 0x6bcd,
    0xb9 => 0x5893, 0xba => 0x66ae, 0xbb => 0x5e55, 0xbc => 0x52df,
    0xbd => 0x6155, 0xbe => 0x6728, 0xbf => 0x76ee, 0xc0 => 0x7766,
    0xc1 => 0x7267, 0xc2 => 0x7a46, 0xc3 => 0x62ff, 0xc4 => 0x54ea,
    0xc5 => 0x5450, 0xc6 => 0x94a0, 0xc7 => 0x90a3, 0xc8 => 0x5a1c,
    0xc9 => 0x7eb3, 0xca => 0x6c16, 0xcb => 0x4e43, 0xcc => 0x5976,
    0xcd => 0x8010, 0xce => 0x5948, 0xcf => 0x5357, 0xd0 => 0x7537,
    0xd1 => 0x96be, 0xd2 => 0x56ca, 0xd3 => 0x6320, 0xd4 => 0x8111,
    0xd5 => 0x607c, 0xd6 => 0x95f9, 0xd7 => 0x6dd6, 0xd8 => 0x5462,
    0xd9 => 0x9981, 0xda => 0x5185, 0xdb => 0x5ae9, 0xdc => 0x80fd,
    0xdd => 0x59ae, 0xde => 0x9713, 0xdf => 0x502a, 0xe0 => 0x6ce5,
    0xe1 => 0x5c3c, 0xe2 => 0x62df, 0xe3 => 0x4f60, 0xe4 => 0x533f,
    0xe5 => 0x817b, 0xe6 => 0x9006, 0xe7 => 0x6eba, 0xe8 => 0x852b,
    0xe9 => 0x62c8, 0xea => 0x5e74, 0xeb => 0x78be, 0xec => 0x64b5,
    0xed => 0x637b, 0xee => 0x5ff5, 0xef => 0x5a18, 0xf0 => 0x917f,
    0xf1 => 0x9e1f, 0xf2 => 0x5c3f, 0xf3 => 0x634f, 0xf4 => 0x8042,
    0xf5 => 0x5b7d, 0xf6 => 0x556e, 0xf7 => 0x954a, 0xf8 => 0x954d,
    0xf9 => 0x6d85, 0xfa => 0x60a8, 0xfb => 0x67e0, 0xfc => 0x72de,
    0xfd => 0x51dd, 0xfe => 0x5b81,
  },
  0xc5 => {
    0xa1 => 0x62e7, 0xa2 => 0x6cde, 0xa3 => 0x725b, 0xa4 => 0x626d,
    0xa5 => 0x94ae, 0xa6 => 0x7ebd, 0xa7 => 0x8113, 0xa8 => 0x6d53,
    0xa9 => 0x519c, 0xaa => 0x5f04, 0xab => 0x5974, 0xac => 0x52aa,
    0xad => 0x6012, 0xae => 0x5973, 0xaf => 0x6696, 0xb0 => 0x8650,
    0xb1 => 0x759f, 0xb2 => 0x632a, 0xb3 => 0x61e6, 0xb4 => 0x7cef,
    0xb5 => 0x8bfa, 0xb6 => 0x54e6, 0xb7 => 0x6b27, 0xb8 => 0x9e25,
    0xb9 => 0x6bb4, 0xba => 0x85d5, 0xbb => 0x5455, 0xbc => 0x5076,
    0xbd => 0x6ca4, 0xbe => 0x556a, 0xbf => 0x8db4, 0xc0 => 0x722c,
    0xc1 => 0x5e15, 0xc2 => 0x6015, 0xc3 => 0x7436, 0xc4 => 0x62cd,
    0xc5 => 0x6392, 0xc6 => 0x724c, 0xc7 => 0x5f98, 0xc8 => 0x6e43,
    0xc9 => 0x6d3e, 0xca => 0x6500, 0xcb => 0x6f58, 0xcc => 0x76d8,
    0xcd => 0x78d0, 0xce => 0x76fc, 0xcf => 0x7554, 0xd0 => 0x5224,
    0xd1 => 0x53db, 0xd2 => 0x4e53, 0xd3 => 0x5e9e, 0xd4 => 0x65c1,
    0xd5 => 0x802a, 0xd6 => 0x80d6, 0xd7 => 0x629b, 0xd8 => 0x5486,
    0xd9 => 0x5228, 0xda => 0x70ae, 0xdb => 0x888d, 0xdc => 0x8dd1,
    0xdd => 0x6ce1, 0xde => 0x5478, 0xdf => 0x80da, 0xe0 => 0x57f9,
    0xe1 => 0x88f4, 0xe2 => 0x8d54, 0xe3 => 0x966a, 0xe4 => 0x914d,
    0xe5 => 0x4f69, 0xe6 => 0x6c9b, 0xe7 => 0x55b7, 0xe8 => 0x76c6,
    0xe9 => 0x7830, 0xea => 0x62a8, 0xeb => 0x70f9, 0xec => 0x6f8e,
    0xed => 0x5f6d, 0xee => 0x84ec, 0xef => 0x68da, 0xf0 => 0x787c,
    0xf1 => 0x7bf7, 0xf2 => 0x81a8, 0xf3 => 0x670b, 0xf4 => 0x9e4f,
    0xf5 => 0x6367, 0xf6 => 0x78b0, 0xf7 => 0x576f, 0xf8 => 0x7812,
    0xf9 => 0x9739, 0xfa => 0x6279, 0xfb => 0x62ab, 0xfc => 0x5288,
    0xfd => 0x7435, 0xfe => 0x6bd7,
  },
  0xc6 => {
    0xa1 => 0x5564, 0xa2 => 0x813e, 0xa3 => 0x75b2, 0xa4 => 0x76ae,
    0xa5 => 0x5339, 0xa6 => 0x75de, 0xa7 => 0x50fb, 0xa8 => 0x5c41,
    0xa9 => 0x8b6c, 0xaa => 0x7bc7, 0xab => 0x504f, 0xac => 0x7247,
    0xad => 0x9a97, 0xae => 0x98d8, 0xaf => 0x6f02, 0xb0 => 0x74e2,
    0xb1 => 0x7968, 0xb2 => 0x6487, 0xb3 => 0x77a5, 0xb4 => 0x62fc,
    0xb5 => 0x9891, 0xb6 => 0x8d2b, 0xb7 => 0x54c1, 0xb8 => 0x8058,
    0xb9 => 0x4e52, 0xba => 0x576a, 0xbb => 0x82f9, 0xbc => 0x840d,
    0xbd => 0x5e73, 0xbe => 0x51ed, 0xbf => 0x74f6, 0xc0 => 0x8bc4,
    0xc1 => 0x5c4f, 0xc2 => 0x5761, 0xc3 => 0x6cfc, 0xc4 => 0x9887,
    0xc5 => 0x5a46, 0xc6 => 0x7834, 0xc7 => 0x9b44, 0xc8 => 0x8feb,
    0xc9 => 0x7c95, 0xca => 0x5256, 0xcb => 0x6251, 0xcc => 0x94fa,
    0xcd => 0x4ec6, 0xce => 0x8386, 0xcf => 0x8461, 0xd0 => 0x83e9,
    0xd1 => 0x84b2, 0xd2 => 0x57d4, 0xd3 => 0x6734, 0xd4 => 0x5703,
    0xd5 => 0x666e, 0xd6 => 0x6d66, 0xd7 => 0x8c31, 0xd8 => 0x66dd,
    0xd9 => 0x7011, 0xda => 0x671f, 0xdb => 0x6b3a, 0xdc => 0x6816,
    0xdd => 0x621a, 0xde => 0x59bb, 0xdf => 0x4e03, 0xe0 => 0x51c4,
    0xe1 => 0x6f06, 0xe2 => 0x67d2, 0xe3 => 0x6c8f, 0xe4 => 0x5176,
    0xe5 => 0x68cb, 0xe6 => 0x5947, 0xe7 => 0x6b67, 0xe8 => 0x7566,
    0xe9 => 0x5d0e, 0xea => 0x8110, 0xeb => 0x9f50, 0xec => 0x65d7,
    0xed => 0x7948, 0xee => 0x7941, 0xef => 0x9a91, 0xf0 => 0x8d77,
    0xf1 => 0x5c82, 0xf2 => 0x4e5e, 0xf3 => 0x4f01, 0xf4 => 0x542f,
    0xf5 => 0x5951, 0xf6 => 0x780c, 0xf7 => 0x5668, 0xf8 => 0x6c14,
    0xf9 => 0x8fc4, 0xfa => 0x5f03, 0xfb => 0x6c7d, 0xfc => 0x6ce3,
    0xfd => 0x8bab, 0xfe => 0x6390,
  },
  0xc7 => {
    0xa1 => 0x6070, 0xa2 => 0x6d3d, 0xa3 => 0x7275, 0xa4 => 0x6266,
    0xa5 => 0x948e, 0xa6 => 0x94c5, 0xa7 => 0x5343, 0xa8 => 0x8fc1,
    0xa9 => 0x7b7e, 0xaa => 0x4edf, 0xab => 0x8c26, 0xac => 0x4e7e,
    0xad => 0x9ed4, 0xae => 0x94b1, 0xaf => 0x94b3, 0xb0 => 0x524d,
    0xb1 => 0x6f5c, 0xb2 => 0x9063, 0xb3 => 0x6d45, 0xb4 => 0x8c34,
    0xb5 => 0x5811, 0xb6 => 0x5d4c, 0xb7 => 0x6b20, 0xb8 => 0x6b49,
    0xb9 => 0x67aa, 0xba => 0x545b, 0xbb => 0x8154, 0xbc => 0x7f8c,
    0xbd => 0x5899, 0xbe => 0x8537, 0xbf => 0x5f3a, 0xc0 => 0x62a2,
    0xc1 => 0x6a47, 0xc2 => 0x9539, 0xc3 => 0x6572, 0xc4 => 0x6084,
    0xc5 => 0x6865, 0xc6 => 0x77a7, 0xc7 => 0x4e54, 0xc8 => 0x4fa8,
    0xc9 => 0x5de7, 0xca => 0x9798, 0xcb => 0x64ac, 0xcc => 0x7fd8,
    0xcd => 0x5ced, 0xce => 0x4fcf, 0xcf => 0x7a8d, 0xd0 => 0x5207,
    0xd1 => 0x8304, 0xd2 => 0x4e14, 0xd3 => 0x602f, 0xd4 => 0x7a83,
    0xd5 => 0x94a6, 0xd6 => 0x4fb5, 0xd7 => 0x4eb2, 0xd8 => 0x79e6,
    0xd9 => 0x7434, 0xda => 0x52e4, 0xdb => 0x82b9, 0xdc => 0x64d2,
    0xdd => 0x79bd, 0xde => 0x5bdd, 0xdf => 0x6c81, 0xe0 => 0x9752,
    0xe1 => 0x8f7b, 0xe2 => 0x6c22, 0xe3 => 0x503e, 0xe4 => 0x537f,
    0xe5 => 0x6e05, 0xe6 => 0x64ce, 0xe7 => 0x6674, 0xe8 => 0x6c30,
    0xe9 => 0x60c5, 0xea => 0x9877, 0xeb => 0x8bf7, 0xec => 0x5e86,
    0xed => 0x743c, 0xee => 0x7a77, 0xef => 0x79cb, 0xf0 => 0x4e18,
    0xf1 => 0x90b1, 0xf2 => 0x7403, 0xf3 => 0x6c42, 0xf4 => 0x56da,
    0xf5 => 0x914b, 0xf6 => 0x6cc5, 0xf7 => 0x8d8b, 0xf8 => 0x533a,
    0xf9 => 0x86c6, 0xfa => 0x66f2, 0xfb => 0x8eaf, 0xfc => 0x5c48,
    0xfd => 0x9a71, 0xfe => 0x6e20,
  },
  0xc8 => {
    0xa1 => 0x53d6, 0xa2 => 0x5a36, 0xa3 => 0x9f8b, 0xa4 => 0x8da3,
    0xa5 => 0x53bb, 0xa6 => 0x5708, 0xa7 => 0x98a7, 0xa8 => 0x6743,
    0xa9 => 0x919b, 0xaa => 0x6cc9, 0xab => 0x5168, 0xac => 0x75ca,
    0xad => 0x62f3, 0xae => 0x72ac, 0xaf => 0x5238, 0xb0 => 0x529d,
    0xb1 => 0x7f3a, 0xb2 => 0x7094, 0xb3 => 0x7638, 0xb4 => 0x5374,
    0xb5 => 0x9e4a, 0xb6 => 0x69b7, 0xb7 => 0x786e, 0xb8 => 0x96c0,
    0xb9 => 0x88d9, 0xba => 0x7fa4, 0xbb => 0x7136, 0xbc => 0x71c3,
    0xbd => 0x5189, 0xbe => 0x67d3, 0xbf => 0x74e4, 0xc0 => 0x58e4,
    0xc1 => 0x6518, 0xc2 => 0x56b7, 0xc3 => 0x8ba9, 0xc4 => 0x9976,
    0xc5 => 0x6270, 0xc6 => 0x7ed5, 0xc7 => 0x60f9, 0xc8 => 0x70ed,
    0xc9 => 0x58ec, 0xca => 0x4ec1, 0xcb => 0x4eba, 0xcc => 0x5fcd,
    0xcd => 0x97e7, 0xce => 0x4efb, 0xcf => 0x8ba4, 0xd0 => 0x5203,
    0xd1 => 0x598a, 0xd2 => 0x7eab, 0xd3 => 0x6254, 0xd4 => 0x4ecd,
    0xd5 => 0x65e5, 0xd6 => 0x620e, 0xd7 => 0x8338, 0xd8 => 0x84c9,
    0xd9 => 0x8363, 0xda => 0x878d, 0xdb => 0x7194, 0xdc => 0x6eb6,
    0xdd => 0x5bb9, 0xde => 0x7ed2, 0xdf => 0x5197, 0xe0 => 0x63c9,
    0xe1 => 0x67d4, 0xe2 => 0x8089, 0xe3 => 0x8339, 0xe4 => 0x8815,
    0xe5 => 0x5112, 0xe6 => 0x5b7a, 0xe7 => 0x5982, 0xe8 => 0x8fb1,
    0xe9 => 0x4e73, 0xea => 0x6c5d, 0xeb => 0x5165, 0xec => 0x8925,
    0xed => 0x8f6f, 0xee => 0x962e, 0xef => 0x854a, 0xf0 => 0x745e,
    0xf1 => 0x9510, 0xf2 => 0x95f0, 0xf3 => 0x6da6, 0xf4 => 0x82e5,
    0xf5 => 0x5f31, 0xf6 => 0x6492, 0xf7 => 0x6d12, 0xf8 => 0x8428,
    0xf9 => 0x816e, 0xfa => 0x9cc3, 0xfb => 0x585e, 0xfc => 0x8d5b,
    0xfd => 0x4e09, 0xfe => 0x53c1,
  },
  0xc9 => {
    0xa1 => 0x4f1e, 0xa2 => 0x6563, 0xa3 => 0x6851, 0xa4 => 0x55d3,
    0xa5 => 0x4e27, 0xa6 => 0x6414, 0xa7 => 0x9a9a, 0xa8 => 0x626b,
    0xa9 => 0x5ac2, 0xaa => 0x745f, 0xab => 0x8272, 0xac => 0x6da9,
    0xad => 0x68ee, 0xae => 0x50e7, 0xaf => 0x838e, 0xb0 => 0x7802,
    0xb1 => 0x6740, 0xb2 => 0x5239, 0xb3 => 0x6c99, 0xb4 => 0x7eb1,
    0xb5 => 0x50bb, 0xb6 => 0x5565, 0xb7 => 0x715e, 0xb8 => 0x7b5b,
    0xb9 => 0x6652, 0xba => 0x73ca, 0xbb => 0x82eb, 0xbc => 0x6749,
    0xbd => 0x5c71, 0xbe => 0x5220, 0xbf => 0x717d, 0xc0 => 0x886b,
    0xc1 => 0x95ea, 0xc2 => 0x9655, 0xc3 => 0x64c5, 0xc4 => 0x8d61,
    0xc5 => 0x81b3, 0xc6 => 0x5584, 0xc7 => 0x6c55, 0xc8 => 0x6247,
    0xc9 => 0x7f2e, 0xca => 0x5892, 0xcb => 0x4f24, 0xcc => 0x5546,
    0xcd => 0x8d4f, 0xce => 0x664c, 0xcf => 0x4e0a, 0xd0 => 0x5c1a,
    0xd1 => 0x88f3, 0xd2 => 0x68a2, 0xd3 => 0x634e, 0xd4 => 0x7a0d,
    0xd5 => 0x70e7, 0xd6 => 0x828d, 0xd7 => 0x52fa, 0xd8 => 0x97f6,
    0xd9 => 0x5c11, 0xda => 0x54e8, 0xdb => 0x90b5, 0xdc => 0x7ecd,
    0xdd => 0x5962, 0xde => 0x8d4a, 0xdf => 0x86c7, 0xe0 => 0x820c,
    0xe1 => 0x820d, 0xe2 => 0x8d66, 0xe3 => 0x6444, 0xe4 => 0x5c04,
    0xe5 => 0x6151, 0xe6 => 0x6d89, 0xe7 => 0x793e, 0xe8 => 0x8bbe,
    0xe9 => 0x7837, 0xea => 0x7533, 0xeb => 0x547b, 0xec => 0x4f38,
    0xed => 0x8eab, 0xee => 0x6df1, 0xef => 0x5a20, 0xf0 => 0x7ec5,
    0xf1 => 0x795e, 0xf2 => 0x6c88, 0xf3 => 0x5ba1, 0xf4 => 0x5a76,
    0xf5 => 0x751a, 0xf6 => 0x80be, 0xf7 => 0x614e, 0xf8 => 0x6e17,
    0xf9 => 0x58f0, 0xfa => 0x751f, 0xfb => 0x7525, 0xfc => 0x7272,
    0xfd => 0x5347, 0xfe => 0x7ef3,
  },
  0xca => {
    0xa1 => 0x7701, 0xa2 => 0x76db, 0xa3 => 0x5269, 0xa4 => 0x80dc,
    0xa5 => 0x5723, 0xa6 => 0x5e08, 0xa7 => 0x5931, 0xa8 => 0x72ee,
    0xa9 => 0x65bd, 0xaa => 0x6e7f, 0xab => 0x8bd7, 0xac => 0x5c38,
    0xad => 0x8671, 0xae => 0x5341, 0xaf => 0x77f3, 0xb0 => 0x62fe,
    0xb1 => 0x65f6, 0xb2 => 0x4ec0, 0xb3 => 0x98df, 0xb4 => 0x8680,
    0xb5 => 0x5b9e, 0xb6 => 0x8bc6, 0xb7 => 0x53f2, 0xb8 => 0x77e2,
    0xb9 => 0x4f7f, 0xba => 0x5c4e, 0xbb => 0x9a76, 0xbc => 0x59cb,
    0xbd => 0x5f0f, 0xbe => 0x793a, 0xbf => 0x58eb, 0xc0 => 0x4e16,
    0xc1 => 0x67ff, 0xc2 => 0x4e8b, 0xc3 => 0x62ed, 0xc4 => 0x8a93,
    0xc5 => 0x901d, 0xc6 => 0x52bf, 0xc7 => 0x662f, 0xc8 => 0x55dc,
    0xc9 => 0x566c, 0xca => 0x9002, 0xcb => 0x4ed5, 0xcc => 0x4f8d,
    0xcd => 0x91ca, 0xce => 0x9970, 0xcf => 0x6c0f, 0xd0 => 0x5e02,
    0xd1 => 0x6043, 0xd2 => 0x5ba4, 0xd3 => 0x89c6, 0xd4 => 0x8bd5,
    0xd5 => 0x6536, 0xd6 => 0x624b, 0xd7 => 0x9996, 0xd8 => 0x5b88,
    0xd9 => 0x5bff, 0xda => 0x6388, 0xdb => 0x552e, 0xdc => 0x53d7,
    0xdd => 0x7626, 0xde => 0x517d, 0xdf => 0x852c, 0xe0 => 0x67a2,
    0xe1 => 0x68b3, 0xe2 => 0x6b8a, 0xe3 => 0x6292, 0xe4 => 0x8f93,
    0xe5 => 0x53d4, 0xe6 => 0x8212, 0xe7 => 0x6dd1, 0xe8 => 0x758f,
    0xe9 => 0x4e66, 0xea => 0x8d4e, 0xeb => 0x5b70, 0xec => 0x719f,
    0xed => 0x85af, 0xee => 0x6691, 0xef => 0x66d9, 0xf0 => 0x7f72,
    0xf1 => 0x8700, 0xf2 => 0x9ecd, 0xf3 => 0x9f20, 0xf4 => 0x5c5e,
    0xf5 => 0x672f, 0xf6 => 0x8ff0, 0xf7 => 0x6811, 0xf8 => 0x675f,
    0xf9 => 0x620d, 0xfa => 0x7ad6, 0xfb => 0x5885, 0xfc => 0x5eb6,
    0xfd => 0x6570, 0xfe => 0x6f31,
  },
  0xcb => {
    0xa1 => 0x6055, 0xa2 => 0x5237, 0xa3 => 0x800d, 0xa4 => 0x6454,
    0xa5 => 0x8870, 0xa6 => 0x7529, 0xa7 => 0x5e05, 0xa8 => 0x6813,
    0xa9 => 0x62f4, 0xaa => 0x971c, 0xab => 0x53cc, 0xac => 0x723d,
    0xad => 0x8c01, 0xae => 0x6c34, 0xaf => 0x7761, 0xb0 => 0x7a0e,
    0xb1 => 0x542e, 0xb2 => 0x77ac, 0xb3 => 0x987a, 0xb4 => 0x821c,
    0xb5 => 0x8bf4, 0xb6 => 0x7855, 0xb7 => 0x6714, 0xb8 => 0x70c1,
    0xb9 => 0x65af, 0xba => 0x6495, 0xbb => 0x5636, 0xbc => 0x601d,
    0xbd => 0x79c1, 0xbe => 0x53f8, 0xbf => 0x4e1d, 0xc0 => 0x6b7b,
    0xc1 => 0x8086, 0xc2 => 0x5bfa, 0xc3 => 0x55e3, 0xc4 => 0x56db,
    0xc5 => 0x4f3a, 0xc6 => 0x4f3c, 0xc7 => 0x9972, 0xc8 => 0x5df3,
    0xc9 => 0x677e, 0xca => 0x8038, 0xcb => 0x6002, 0xcc => 0x9882,
    0xcd => 0x9001, 0xce => 0x5b8b, 0xcf => 0x8bbc, 0xd0 => 0x8bf5,
    0xd1 => 0x641c, 0xd2 => 0x8258, 0xd3 => 0x64de, 0xd4 => 0x55fd,
    0xd5 => 0x82cf, 0xd6 => 0x9165, 0xd7 => 0x4fd7, 0xd8 => 0x7d20,
    0xd9 => 0x901f, 0xda => 0x7c9f, 0xdb => 0x50f3, 0xdc => 0x5851,
    0xdd => 0x6eaf, 0xde => 0x5bbf, 0xdf => 0x8bc9, 0xe0 => 0x8083,
    0xe1 => 0x9178, 0xe2 => 0x849c, 0xe3 => 0x7b97, 0xe4 => 0x867d,
    0xe5 => 0x968b, 0xe6 => 0x968f, 0xe7 => 0x7ee5, 0xe8 => 0x9ad3,
    0xe9 => 0x788e, 0xea => 0x5c81, 0xeb => 0x7a57, 0xec => 0x9042,
    0xed => 0x96a7, 0xee => 0x795f, 0xef => 0x5b59, 0xf0 => 0x635f,
    0xf1 => 0x7b0b, 0xf2 => 0x84d1, 0xf3 => 0x68ad, 0xf4 => 0x5506,
    0xf5 => 0x7f29, 0xf6 => 0x7410, 0xf7 => 0x7d22, 0xf8 => 0x9501,
    0xf9 => 0x6240, 0xfa => 0x584c, 0xfb => 0x4ed6, 0xfc => 0x5b83,
    0xfd => 0x5979, 0xfe => 0x5854,
  },
  0xcc => {
    0xa1 => 0x736d, 0xa2 => 0x631e, 0xa3 => 0x8e4b, 0xa4 => 0x8e0f,
    0xa5 => 0x80ce, 0xa6 => 0x82d4, 0xa7 => 0x62ac, 0xa8 => 0x53f0,
    0xa9 => 0x6cf0, 0xaa => 0x915e, 0xab => 0x592a, 0xac => 0x6001,
    0xad => 0x6c70, 0xae => 0x574d, 0xaf => 0x644a, 0xb0 => 0x8d2a,
    0xb1 => 0x762b, 0xb2 => 0x6ee9, 0xb3 => 0x575b, 0xb4 => 0x6a80,
    0xb5 => 0x75f0, 0xb6 => 0x6f6d, 0xb7 => 0x8c2d, 0xb8 => 0x8c08,
    0xb9 => 0x5766, 0xba => 0x6bef, 0xbb => 0x8892, 0xbc => 0x78b3,
    0xbd => 0x63a2, 0xbe => 0x53f9, 0xbf => 0x70ad, 0xc0 => 0x6c64,
    0xc1 => 0x5858, 0xc2 => 0x642a, 0xc3 => 0x5802, 0xc4 => 0x68e0,
    0xc5 => 0x819b, 0xc6 => 0x5510, 0xc7 => 0x7cd6, 0xc8 => 0x5018,
    0xc9 => 0x8eba, 0xca => 0x6dcc, 0xcb => 0x8d9f, 0xcc => 0x70eb,
    0xcd => 0x638f, 0xce => 0x6d9b, 0xcf => 0x6ed4, 0xd0 => 0x7ee6,
    0xd1 => 0x8404, 0xd2 => 0x6843, 0xd3 => 0x9003, 0xd4 => 0x6dd8,
    0xd5 => 0x9676, 0xd6 => 0x8ba8, 0xd7 => 0x5957, 0xd8 => 0x7279,
    0xd9 => 0x85e4, 0xda => 0x817e, 0xdb => 0x75bc, 0xdc => 0x8a8a,
    0xdd => 0x68af, 0xde => 0x5254, 0xdf => 0x8e22, 0xe0 => 0x9511,
    0xe1 => 0x63d0, 0xe2 => 0x9898, 0xe3 => 0x8e44, 0xe4 => 0x557c,
    0xe5 => 0x4f53, 0xe6 => 0x66ff, 0xe7 => 0x568f, 0xe8 => 0x60d5,
    0xe9 => 0x6d95, 0xea => 0x5243, 0xeb => 0x5c49, 0xec => 0x5929,
    0xed => 0x6dfb, 0xee => 0x586b, 0xef => 0x7530, 0xf0 => 0x751c,
    0xf1 => 0x606c, 0xf2 => 0x8214, 0xf3 => 0x8146, 0xf4 => 0x6311,
    0xf5 => 0x6761, 0xf6 => 0x8fe2, 0xf7 => 0x773a, 0xf8 => 0x8df3,
    0xf9 => 0x8d34, 0xfa => 0x94c1, 0xfb => 0x5e16, 0xfc => 0x5385,
    0xfd => 0x542c, 0xfe => 0x70c3,
  },
  0xcd => {
    0xa1 => 0x6c40, 0xa2 => 0x5ef7, 0xa3 => 0x505c, 0xa4 => 0x4ead,
    0xa5 => 0x5ead, 0xa6 => 0x633a, 0xa7 => 0x8247, 0xa8 => 0x901a,
    0xa9 => 0x6850, 0xaa => 0x916e, 0xab => 0x77b3, 0xac => 0x540c,
    0xad => 0x94dc, 0xae => 0x5f64, 0xaf => 0x7ae5, 0xb0 => 0x6876,
    0xb1 => 0x6345, 0xb2 => 0x7b52, 0xb3 => 0x7edf, 0xb4 => 0x75db,
    0xb5 => 0x5077, 0xb6 => 0x6295, 0xb7 => 0x5934, 0xb8 => 0x900f,
    0xb9 => 0x51f8, 0xba => 0x79c3, 0xbb => 0x7a81, 0xbc => 0x56fe,
    0xbd => 0x5f92, 0xbe => 0x9014, 0xbf => 0x6d82, 0xc0 => 0x5c60,
    0xc1 => 0x571f, 0xc2 => 0x5410, 0xc3 => 0x5154, 0xc4 => 0x6e4d,
    0xc5 => 0x56e2, 0xc6 => 0x63a8, 0xc7 => 0x9893, 0xc8 => 0x817f,
    0xc9 => 0x8715, 0xca => 0x892a, 0xcb => 0x9000, 0xcc => 0x541e,
    0xcd => 0x5c6f, 0xce => 0x81c0, 0xcf => 0x62d6, 0xd0 => 0x6258,
    0xd1 => 0x8131, 0xd2 => 0x9e35, 0xd3 => 0x9640, 0xd4 => 0x9a6e,
    0xd5 => 0x9a7c, 0xd6 => 0x692d, 0xd7 => 0x59a5, 0xd8 => 0x62d3,
    0xd9 => 0x553e, 0xda => 0x6316, 0xdb => 0x54c7, 0xdc => 0x86d9,
    0xdd => 0x6d3c, 0xde => 0x5a03, 0xdf => 0x74e6, 0xe0 => 0x889c,
    0xe1 => 0x6b6a, 0xe2 => 0x5916, 0xe3 => 0x8c4c, 0xe4 => 0x5f2f,
    0xe5 => 0x6e7e, 0xe6 => 0x73a9, 0xe7 => 0x987d, 0xe8 => 0x4e38,
    0xe9 => 0x70f7, 0xea => 0x5b8c, 0xeb => 0x7897, 0xec => 0x633d,
    0xed => 0x665a, 0xee => 0x7696, 0xef => 0x60cb, 0xf0 => 0x5b9b,
    0xf1 => 0x5a49, 0xf2 => 0x4e07, 0xf3 => 0x8155, 0xf4 => 0x6c6a,
    0xf5 => 0x738b, 0xf6 => 0x4ea1, 0xf7 => 0x6789, 0xf8 => 0x7f51,
    0xf9 => 0x5f80, 0xfa => 0x65fa, 0xfb => 0x671b, 0xfc => 0x5fd8,
    0xfd => 0x5984, 0xfe => 0x5a01,
  },
  0xce => {
    0xa1 => 0x5dcd, 0xa2 => 0x5fae, 0xa3 => 0x5371, 0xa4 => 0x97e6,
    0xa5 => 0x8fdd, 0xa6 => 0x6845, 0xa7 => 0x56f4, 0xa8 => 0x552f,
    0xa9 => 0x60df, 0xaa => 0x4e3a, 0xab => 0x6f4d, 0xac => 0x7ef4,
    0xad => 0x82c7, 0xae => 0x840e, 0xaf => 0x59d4, 0xb0 => 0x4f1f,
    0xb1 => 0x4f2a, 0xb2 => 0x5c3e, 0xb3 => 0x7eac, 0xb4 => 0x672a,
    0xb5 => 0x851a, 0xb6 => 0x5473, 0xb7 => 0x754f, 0xb8 => 0x80c3,
    0xb9 => 0x5582, 0xba => 0x9b4f, 0xbb => 0x4f4d, 0xbc => 0x6e2d,
    0xbd => 0x8c13, 0xbe => 0x5c09, 0xbf => 0x6170, 0xc0 => 0x536b,
    0xc1 => 0x761f, 0xc2 => 0x6e29, 0xc3 => 0x868a, 0xc4 => 0x6587,
    0xc5 => 0x95fb, 0xc6 => 0x7eb9, 0xc7 => 0x543b, 0xc8 => 0x7a33,
    0xc9 => 0x7d0a, 0xca => 0x95ee, 0xcb => 0x55e1, 0xcc => 0x7fc1,
    0xcd => 0x74ee, 0xce => 0x631d, 0xcf => 0x8717, 0xd0 => 0x6da1,
    0xd1 => 0x7a9d, 0xd2 => 0x6211, 0xd3 => 0x65a1, 0xd4 => 0x5367,
    0xd5 => 0x63e1, 0xd6 => 0x6c83, 0xd7 => 0x5deb, 0xd8 => 0x545c,
    0xd9 => 0x94a8, 0xda => 0x4e4c, 0xdb => 0x6c61, 0xdc => 0x8bec,
    0xdd => 0x5c4b, 0xde => 0x65e0, 0xdf => 0x829c, 0xe0 => 0x68a7,
    0xe1 => 0x543e, 0xe2 => 0x5434, 0xe3 => 0x6bcb, 0xe4 => 0x6b66,
    0xe5 => 0x4e94, 0xe6 => 0x6342, 0xe7 => 0x5348, 0xe8 => 0x821e,
    0xe9 => 0x4f0d, 0xea => 0x4fae, 0xeb => 0x575e, 0xec => 0x620a,
    0xed => 0x96fe, 0xee => 0x6664, 0xef => 0x7269, 0xf0 => 0x52ff,
    0xf1 => 0x52a1, 0xf2 => 0x609f, 0xf3 => 0x8bef, 0xf4 => 0x6614,
    0xf5 => 0x7199, 0xf6 => 0x6790, 0xf7 => 0x897f, 0xf8 => 0x7852,
    0xf9 => 0x77fd, 0xfa => 0x6670, 0xfb => 0x563b, 0xfc => 0x5438,
    0xfd => 0x9521, 0xfe => 0x727a,
  },
  0xcf => {
    0xa1 => 0x7a00, 0xa2 => 0x606f, 0xa3 => 0x5e0c, 0xa4 => 0x6089,
    0xa5 => 0x819d, 0xa6 => 0x5915, 0xa7 => 0x60dc, 0xa8 => 0x7184,
    0xa9 => 0x70ef, 0xaa => 0x6eaa, 0xab => 0x6c50, 0xac => 0x7280,
    0xad => 0x6a84, 0xae => 0x88ad, 0xaf => 0x5e2d, 0xb0 => 0x4e60,
    0xb1 => 0x5ab3, 0xb2 => 0x559c, 0xb3 => 0x94e3, 0xb4 => 0x6d17,
    0xb5 => 0x7cfb, 0xb6 => 0x9699, 0xb7 => 0x620f, 0xb8 => 0x7ec6,
    0xb9 => 0x778e, 0xba => 0x867e, 0xbb => 0x5323, 0xbc => 0x971e,
    0xbd => 0x8f96, 0xbe => 0x6687, 0xbf => 0x5ce1, 0xc0 => 0x4fa0,
    0xc1 => 0x72ed, 0xc2 => 0x4e0b, 0xc3 => 0x53a6, 0xc4 => 0x590f,
    0xc5 => 0x5413, 0xc6 => 0x6380, 0xc7 => 0x9528, 0xc8 => 0x5148,
    0xc9 => 0x4ed9, 0xca => 0x9c9c, 0xcb => 0x7ea4, 0xcc => 0x54b8,
    0xcd => 0x8d24, 0xce => 0x8854, 0xcf => 0x8237, 0xd0 => 0x95f2,
    0xd1 => 0x6d8e, 0xd2 => 0x5f26, 0xd3 => 0x5acc, 0xd4 => 0x663e,
    0xd5 => 0x9669, 0xd6 => 0x73b0, 0xd7 => 0x732e, 0xd8 => 0x53bf,
    0xd9 => 0x817a, 0xda => 0x9985, 0xdb => 0x7fa1, 0xdc => 0x5baa,
    0xdd => 0x9677, 0xde => 0x9650, 0xdf => 0x7ebf, 0xe0 => 0x76f8,
    0xe1 => 0x53a2, 0xe2 => 0x9576, 0xe3 => 0x9999, 0xe4 => 0x7bb1,
    0xe5 => 0x8944, 0xe6 => 0x6e58, 0xe7 => 0x4e61, 0xe8 => 0x7fd4,
    0xe9 => 0x7965, 0xea => 0x8be6, 0xeb => 0x60f3, 0xec => 0x54cd,
    0xed => 0x4eab, 0xee => 0x9879, 0xef => 0x5df7, 0xf0 => 0x6a61,
    0xf1 => 0x50cf, 0xf2 => 0x5411, 0xf3 => 0x8c61, 0xf4 => 0x8427,
    0xf5 => 0x785d, 0xf6 => 0x9704, 0xf7 => 0x524a, 0xf8 => 0x54ee,
    0xf9 => 0x56a3, 0xfa => 0x9500, 0xfb => 0x6d88, 0xfc => 0x5bb5,
    0xfd => 0x6dc6, 0xfe => 0x6653,
  },
  0xd0 => {
    0xa1 => 0x5c0f, 0xa2 => 0x5b5d, 0xa3 => 0x6821, 0xa4 => 0x8096,
    0xa5 => 0x5578, 0xa6 => 0x7b11, 0xa7 => 0x6548, 0xa8 => 0x6954,
    0xa9 => 0x4e9b, 0xaa => 0x6b47, 0xab => 0x874e, 0xac => 0x978b,
    0xad => 0x534f, 0xae => 0x631f, 0xaf => 0x643a, 0xb0 => 0x90aa,
    0xb1 => 0x659c, 0xb2 => 0x80c1, 0xb3 => 0x8c10, 0xb4 => 0x5199,
    0xb5 => 0x68b0, 0xb6 => 0x5378, 0xb7 => 0x87f9, 0xb8 => 0x61c8,
    0xb9 => 0x6cc4, 0xba => 0x6cfb, 0xbb => 0x8c22, 0xbc => 0x5c51,
    0xbd => 0x85aa, 0xbe => 0x82af, 0xbf => 0x950c, 0xc0 => 0x6b23,
    0xc1 => 0x8f9b, 0xc2 => 0x65b0, 0xc3 => 0x5ffb, 0xc4 => 0x5fc3,
    0xc5 => 0x4fe1, 0xc6 => 0x8845, 0xc7 => 0x661f, 0xc8 => 0x8165,
    0xc9 => 0x7329, 0xca => 0x60fa, 0xcb => 0x5174, 0xcc => 0x5211,
    0xcd => 0x578b, 0xce => 0x5f62, 0xcf => 0x90a2, 0xd0 => 0x884c,
    0xd1 => 0x9192, 0xd2 => 0x5e78, 0xd3 => 0x674f, 0xd4 => 0x6027,
    0xd5 => 0x59d3, 0xd6 => 0x5144, 0xd7 => 0x51f6, 0xd8 => 0x80f8,
    0xd9 => 0x5308, 0xda => 0x6c79, 0xdb => 0x96c4, 0xdc => 0x718a,
    0xdd => 0x4f11, 0xde => 0x4fee, 0xdf => 0x7f9e, 0xe0 => 0x673d,
    0xe1 => 0x55c5, 0xe2 => 0x9508, 0xe3 => 0x79c0, 0xe4 => 0x8896,
    0xe5 => 0x7ee3, 0xe6 => 0x589f, 0xe7 => 0x620c, 0xe8 => 0x9700,
    0xe9 => 0x865a, 0xea => 0x5618, 0xeb => 0x987b, 0xec => 0x5f90,
    0xed => 0x8bb8, 0xee => 0x84c4, 0xef => 0x9157, 0xf0 => 0x53d9,
    0xf1 => 0x65ed, 0xf2 => 0x5e8f, 0xf3 => 0x755c, 0xf4 => 0x6064,
    0xf5 => 0x7d6e, 0xf6 => 0x5a7f, 0xf7 => 0x7eea, 0xf8 => 0x7eed,
    0xf9 => 0x8f69, 0xfa => 0x55a7, 0xfb => 0x5ba3, 0xfc => 0x60ac,
    0xfd => 0x65cb, 0xfe => 0x7384,
  },
  0xd1 => {
    0xa1 => 0x9009, 0xa2 => 0x7663, 0xa3 => 0x7729, 0xa4 => 0x7eda,
    0xa5 => 0x9774, 0xa6 => 0x859b, 0xa7 => 0x5b66, 0xa8 => 0x7a74,
    0xa9 => 0x96ea, 0xaa => 0x8840, 0xab => 0x52cb, 0xac => 0x718f,
    0xad => 0x5faa, 0xae => 0x65ec, 0xaf => 0x8be2, 0xb0 => 0x5bfb,
    0xb1 => 0x9a6f, 0xb2 => 0x5de1, 0xb3 => 0x6b89, 0xb4 => 0x6c5b,
    0xb5 => 0x8bad, 0xb6 => 0x8baf, 0xb7 => 0x900a, 0xb8 => 0x8fc5,
    0xb9 => 0x538b, 0xba => 0x62bc, 0xbb => 0x9e26, 0xbc => 0x9e2d,
    0xbd => 0x5440, 0xbe => 0x4e2b, 0xbf => 0x82bd, 0xc0 => 0x7259,
    0xc1 => 0x869c, 0xc2 => 0x5d16, 0xc3 => 0x8859, 0xc4 => 0x6daf,
    0xc5 => 0x96c5, 0xc6 => 0x54d1, 0xc7 => 0x4e9a, 0xc8 => 0x8bb6,
    0xc9 => 0x7109, 0xca => 0x54bd, 0xcb => 0x9609, 0xcc => 0x70df,
    0xcd => 0x6df9, 0xce => 0x76d0, 0xcf => 0x4e25, 0xd0 => 0x7814,
    0xd1 => 0x8712, 0xd2 => 0x5ca9, 0xd3 => 0x5ef6, 0xd4 => 0x8a00,
    0xd5 => 0x989c, 0xd6 => 0x960e, 0xd7 => 0x708e, 0xd8 => 0x6cbf,
    0xd9 => 0x5944, 0xda => 0x63a9, 0xdb => 0x773c, 0xdc => 0x884d,
    0xdd => 0x6f14, 0xde => 0x8273, 0xdf => 0x5830, 0xe0 => 0x71d5,
    0xe1 => 0x538c, 0xe2 => 0x781a, 0xe3 => 0x96c1, 0xe4 => 0x5501,
    0xe5 => 0x5f66, 0xe6 => 0x7130, 0xe7 => 0x5bb4, 0xe8 => 0x8c1a,
    0xe9 => 0x9a8c, 0xea => 0x6b83, 0xeb => 0x592e, 0xec => 0x9e2f,
    0xed => 0x79e7, 0xee => 0x6768, 0xef => 0x626c, 0xf0 => 0x4f6f,
    0xf1 => 0x75a1, 0xf2 => 0x7f8a, 0xf3 => 0x6d0b, 0xf4 => 0x9633,
    0xf5 => 0x6c27, 0xf6 => 0x4ef0, 0xf7 => 0x75d2, 0xf8 => 0x517b,
    0xf9 => 0x6837, 0xfa => 0x6f3e, 0xfb => 0x9080, 0xfc => 0x8170,
    0xfd => 0x5996, 0xfe => 0x7476,
  },
  0xd2 => {
    0xa1 => 0x6447, 0xa2 => 0x5c27, 0xa3 => 0x9065, 0xa4 => 0x7a91,
    0xa5 => 0x8c23, 0xa6 => 0x59da, 0xa7 => 0x54ac, 0xa8 => 0x8200,
    0xa9 => 0x836f, 0xaa => 0x8981, 0xab => 0x8000, 0xac => 0x6930,
    0xad => 0x564e, 0xae => 0x8036, 0xaf => 0x7237, 0xb0 => 0x91ce,
    0xb1 => 0x51b6, 0xb2 => 0x4e5f, 0xb3 => 0x9875, 0xb4 => 0x6396,
    0xb5 => 0x4e1a, 0xb6 => 0x53f6, 0xb7 => 0x66f3, 0xb8 => 0x814b,
    0xb9 => 0x591c, 0xba => 0x6db2, 0xbb => 0x4e00, 0xbc => 0x58f9,
    0xbd => 0x533b, 0xbe => 0x63d6, 0xbf => 0x94f1, 0xc0 => 0x4f9d,
    0xc1 => 0x4f0a, 0xc2 => 0x8863, 0xc3 => 0x9890, 0xc4 => 0x5937,
    0xc5 => 0x9057, 0xc6 => 0x79fb, 0xc7 => 0x4eea, 0xc8 => 0x80f0,
    0xc9 => 0x7591, 0xca => 0x6c82, 0xcb => 0x5b9c, 0xcc => 0x59e8,
    0xcd => 0x5f5d, 0xce => 0x6905, 0xcf => 0x8681, 0xd0 => 0x501a,
    0xd1 => 0x5df2, 0xd2 => 0x4e59, 0xd3 => 0x77e3, 0xd4 => 0x4ee5,
    0xd5 => 0x827a, 0xd6 => 0x6291, 0xd7 => 0x6613, 0xd8 => 0x9091,
    0xd9 => 0x5c79, 0xda => 0x4ebf, 0xdb => 0x5f79, 0xdc => 0x81c6,
    0xdd => 0x9038, 0xde => 0x8084, 0xdf => 0x75ab, 0xe0 => 0x4ea6,
    0xe1 => 0x88d4, 0xe2 => 0x610f, 0xe3 => 0x6bc5, 0xe4 => 0x5fc6,
    0xe5 => 0x4e49, 0xe6 => 0x76ca, 0xe7 => 0x6ea2, 0xe8 => 0x8be3,
    0xe9 => 0x8bae, 0xea => 0x8c0a, 0xeb => 0x8bd1, 0xec => 0x5f02,
    0xed => 0x7ffc, 0xee => 0x7fcc, 0xef => 0x7ece, 0xf0 => 0x8335,
    0xf1 => 0x836b, 0xf2 => 0x56e0, 0xf3 => 0x6bb7, 0xf4 => 0x97f3,
    0xf5 => 0x9634, 0xf6 => 0x59fb, 0xf7 => 0x541f, 0xf8 => 0x94f6,
    0xf9 => 0x6deb, 0xfa => 0x5bc5, 0xfb => 0x996e, 0xfc => 0x5c39,
    0xfd => 0x5f15, 0xfe => 0x9690,
  },
  0xd3 => {
    0xa1 => 0x5370, 0xa2 => 0x82f1, 0xa3 => 0x6a31, 0xa4 => 0x5a74,
    0xa5 => 0x9e70, 0xa6 => 0x5e94, 0xa7 => 0x7f28, 0xa8 => 0x83b9,
    0xa9 => 0x8424, 0xaa => 0x8425, 0xab => 0x8367, 0xac => 0x8747,
    0xad => 0x8fce, 0xae => 0x8d62, 0xaf => 0x76c8, 0xb0 => 0x5f71,
    0xb1 => 0x9896, 0xb2 => 0x786c, 0xb3 => 0x6620, 0xb4 => 0x54df,
    0xb5 => 0x62e5, 0xb6 => 0x4f63, 0xb7 => 0x81c3, 0xb8 => 0x75c8,
    0xb9 => 0x5eb8, 0xba => 0x96cd, 0xbb => 0x8e0a, 0xbc => 0x86f9,
    0xbd => 0x548f, 0xbe => 0x6cf3, 0xbf => 0x6d8c, 0xc0 => 0x6c38,
    0xc1 => 0x607f, 0xc2 => 0x52c7, 0xc3 => 0x7528, 0xc4 => 0x5e7d,
    0xc5 => 0x4f18, 0xc6 => 0x60a0, 0xc7 => 0x5fe7, 0xc8 => 0x5c24,
    0xc9 => 0x7531, 0xca => 0x90ae, 0xcb => 0x94c0, 0xcc => 0x72b9,
    0xcd => 0x6cb9, 0xce => 0x6e38, 0xcf => 0x9149, 0xd0 => 0x6709,
    0xd1 => 0x53cb, 0xd2 => 0x53f3, 0xd3 => 0x4f51, 0xd4 => 0x91c9,
    0xd5 => 0x8bf1, 0xd6 => 0x53c8, 0xd7 => 0x5e7c, 0xd8 => 0x8fc2,
    0xd9 => 0x6de4, 0xda => 0x4e8e, 0xdb => 0x76c2, 0xdc => 0x6986,
    0xdd => 0x865e, 0xde => 0x611a, 0xdf => 0x8206, 0xe0 => 0x4f59,
    0xe1 => 0x4fde, 0xe2 => 0x903e, 0xe3 => 0x9c7c, 0xe4 => 0x6109,
    0xe5 => 0x6e1d, 0xe6 => 0x6e14, 0xe7 => 0x9685, 0xe8 => 0x4e88,
    0xe9 => 0x5a31, 0xea => 0x96e8, 0xeb => 0x4e0e, 0xec => 0x5c7f,
    0xed => 0x79b9, 0xee => 0x5b87, 0xef => 0x8bed, 0xf0 => 0x7fbd,
    0xf1 => 0x7389, 0xf2 => 0x57df, 0xf3 => 0x828b, 0xf4 => 0x90c1,
    0xf5 => 0x5401, 0xf6 => 0x9047, 0xf7 => 0x55bb, 0xf8 => 0x5cea,
    0xf9 => 0x5fa1, 0xfa => 0x6108, 0xfb => 0x6b32, 0xfc => 0x72f1,
    0xfd => 0x80b2, 0xfe => 0x8a89,
  },
  0xd4 => {
    0xa1 => 0x6d74, 0xa2 => 0x5bd3, 0xa3 => 0x88d5, 0xa4 => 0x9884,
    0xa5 => 0x8c6b, 0xa6 => 0x9a6d, 0xa7 => 0x9e33, 0xa8 => 0x6e0a,
    0xa9 => 0x51a4, 0xaa => 0x5143, 0xab => 0x57a3, 0xac => 0x8881,
    0xad => 0x539f, 0xae => 0x63f4, 0xaf => 0x8f95, 0xb0 => 0x56ed,
    0xb1 => 0x5458, 0xb2 => 0x5706, 0xb3 => 0x733f, 0xb4 => 0x6e90,
    0xb5 => 0x7f18, 0xb6 => 0x8fdc, 0xb7 => 0x82d1, 0xb8 => 0x613f,
    0xb9 => 0x6028, 0xba => 0x9662, 0xbb => 0x66f0, 0xbc => 0x7ea6,
    0xbd => 0x8d8a, 0xbe => 0x8dc3, 0xbf => 0x94a5, 0xc0 => 0x5cb3,
    0xc1 => 0x7ca4, 0xc2 => 0x6708, 0xc3 => 0x60a6, 0xc4 => 0x9605,
    0xc5 => 0x8018, 0xc6 => 0x4e91, 0xc7 => 0x90e7, 0xc8 => 0x5300,
    0xc9 => 0x9668, 0xca => 0x5141, 0xcb => 0x8fd0, 0xcc => 0x8574,
    0xcd => 0x915d, 0xce => 0x6655, 0xcf => 0x97f5, 0xd0 => 0x5b55,
    0xd1 => 0x531d, 0xd2 => 0x7838, 0xd3 => 0x6742, 0xd4 => 0x683d,
    0xd5 => 0x54c9, 0xd6 => 0x707e, 0xd7 => 0x5bb0, 0xd8 => 0x8f7d,
    0xd9 => 0x518d, 0xda => 0x5728, 0xdb => 0x54b1, 0xdc => 0x6512,
    0xdd => 0x6682, 0xde => 0x8d5e, 0xdf => 0x8d43, 0xe0 => 0x810f,
    0xe1 => 0x846c, 0xe2 => 0x906d, 0xe3 => 0x7cdf, 0xe4 => 0x51ff,
    0xe5 => 0x85fb, 0xe6 => 0x67a3, 0xe7 => 0x65e9, 0xe8 => 0x6fa1,
    0xe9 => 0x86a4, 0xea => 0x8e81, 0xeb => 0x566a, 0xec => 0x9020,
    0xed => 0x7682, 0xee => 0x7076, 0xef => 0x71e5, 0xf0 => 0x8d23,
    0xf1 => 0x62e9, 0xf2 => 0x5219, 0xf3 => 0x6cfd, 0xf4 => 0x8d3c,
    0xf5 => 0x600e, 0xf6 => 0x589e, 0xf7 => 0x618e, 0xf8 => 0x66fe,
    0xf9 => 0x8d60, 0xfa => 0x624e, 0xfb => 0x55b3, 0xfc => 0x6e23,
    0xfd => 0x672d, 0xfe => 0x8f67,
  },
  0xd5 => {
    0xa1 => 0x94e1, 0xa2 => 0x95f8, 0xa3 => 0x7728, 0xa4 => 0x6805,
    0xa5 => 0x69a8, 0xa6 => 0x548b, 0xa7 => 0x4e4d, 0xa8 => 0x70b8,
    0xa9 => 0x8bc8, 0xaa => 0x6458, 0xab => 0x658b, 0xac => 0x5b85,
    0xad => 0x7a84, 0xae => 0x503a, 0xaf => 0x5be8, 0xb0 => 0x77bb,
    0xb1 => 0x6be1, 0xb2 => 0x8a79, 0xb3 => 0x7c98, 0xb4 => 0x6cbe,
    0xb5 => 0x76cf, 0xb6 => 0x65a9, 0xb7 => 0x8f97, 0xb8 => 0x5d2d,
    0xb9 => 0x5c55, 0xba => 0x8638, 0xbb => 0x6808, 0xbc => 0x5360,
    0xbd => 0x6218, 0xbe => 0x7ad9, 0xbf => 0x6e5b, 0xc0 => 0x7efd,
    0xc1 => 0x6a1f, 0xc2 => 0x7ae0, 0xc3 => 0x5f70, 0xc4 => 0x6f33,
    0xc5 => 0x5f20, 0xc6 => 0x638c, 0xc7 => 0x6da8, 0xc8 => 0x6756,
    0xc9 => 0x4e08, 0xca => 0x5e10, 0xcb => 0x8d26, 0xcc => 0x4ed7,
    0xcd => 0x80c0, 0xce => 0x7634, 0xcf => 0x969c, 0xd0 => 0x62db,
    0xd1 => 0x662d, 0xd2 => 0x627e, 0xd3 => 0x6cbc, 0xd4 => 0x8d75,
    0xd5 => 0x7167, 0xd6 => 0x7f69, 0xd7 => 0x5146, 0xd8 => 0x8087,
    0xd9 => 0x53ec, 0xda => 0x906e, 0xdb => 0x6298, 0xdc => 0x54f2,
    0xdd => 0x86f0, 0xde => 0x8f99, 0xdf => 0x8005, 0xe0 => 0x9517,
    0xe1 => 0x8517, 0xe2 => 0x8fd9, 0xe3 => 0x6d59, 0xe4 => 0x73cd,
    0xe5 => 0x659f, 0xe6 => 0x771f, 0xe7 => 0x7504, 0xe8 => 0x7827,
    0xe9 => 0x81fb, 0xea => 0x8d1e, 0xeb => 0x9488, 0xec => 0x4fa6,
    0xed => 0x6795, 0xee => 0x75b9, 0xef => 0x8bca, 0xf0 => 0x9707,
    0xf1 => 0x632f, 0xf2 => 0x9547, 0xf3 => 0x9635, 0xf4 => 0x84b8,
    0xf5 => 0x6323, 0xf6 => 0x7741, 0xf7 => 0x5f81, 0xf8 => 0x72f0,
    0xf9 => 0x4e89, 0xfa => 0x6014, 0xfb => 0x6574, 0xfc => 0x62ef,
    0xfd => 0x6b63, 0xfe => 0x653f,
  },
  0xd6 => {
    0xa1 => 0x5e27, 0xa2 => 0x75c7, 0xa3 => 0x90d1, 0xa4 => 0x8bc1,
    0xa5 => 0x829d, 0xa6 => 0x679d, 0xa7 => 0x652f, 0xa8 => 0x5431,
    0xa9 => 0x8718, 0xaa => 0x77e5, 0xab => 0x80a2, 0xac => 0x8102,
    0xad => 0x6c41, 0xae => 0x4e4b, 0xaf => 0x7ec7, 0xb0 => 0x804c,
    0xb1 => 0x76f4, 0xb2 => 0x690d, 0xb3 => 0x6b96, 0xb4 => 0x6267,
    0xb5 => 0x503c, 0xb6 => 0x4f84, 0xb7 => 0x5740, 0xb8 => 0x6307,
    0xb9 => 0x6b62, 0xba => 0x8dbe, 0xbb => 0x53ea, 0xbc => 0x65e8,
    0xbd => 0x7eb8, 0xbe => 0x5fd7, 0xbf => 0x631a, 0xc0 => 0x63b7,
    0xc1 => 0x81f3, 0xc2 => 0x81f4, 0xc3 => 0x7f6e, 0xc4 => 0x5e1c,
    0xc5 => 0x5cd9, 0xc6 => 0x5236, 0xc7 => 0x667a, 0xc8 => 0x79e9,
    0xc9 => 0x7a1a, 0xca => 0x8d28, 0xcb => 0x7099, 0xcc => 0x75d4,
    0xcd => 0x6ede, 0xce => 0x6cbb, 0xcf => 0x7a92, 0xd0 => 0x4e2d,
    0xd1 => 0x76c5, 0xd2 => 0x5fe0, 0xd3 => 0x949f, 0xd4 => 0x8877,
    0xd5 => 0x7ec8, 0xd6 => 0x79cd, 0xd7 => 0x80bf, 0xd8 => 0x91cd,
    0xd9 => 0x4ef2, 0xda => 0x4f17, 0xdb => 0x821f, 0xdc => 0x5468,
    0xdd => 0x5dde, 0xde => 0x6d32, 0xdf => 0x8bcc, 0xe0 => 0x7ca5,
    0xe1 => 0x8f74, 0xe2 => 0x8098, 0xe3 => 0x5e1a, 0xe4 => 0x5492,
    0xe5 => 0x76b1, 0xe6 => 0x5b99, 0xe7 => 0x663c, 0xe8 => 0x9aa4,
    0xe9 => 0x73e0, 0xea => 0x682a, 0xeb => 0x86db, 0xec => 0x6731,
    0xed => 0x732a, 0xee => 0x8bf8, 0xef => 0x8bdb, 0xf0 => 0x9010,
    0xf1 => 0x7af9, 0xf2 => 0x70db, 0xf3 => 0x716e, 0xf4 => 0x62c4,
    0xf5 => 0x77a9, 0xf6 => 0x5631, 0xf7 => 0x4e3b, 0xf8 => 0x8457,
    0xf9 => 0x67f1, 0xfa => 0x52a9, 0xfb => 0x86c0, 0xfc => 0x8d2e,
    0xfd => 0x94f8, 0xfe => 0x7b51,
  },
  0xd7 => {
    0xa1 => 0x4f4f, 0xa2 => 0x6ce8, 0xa3 => 0x795d, 0xa4 => 0x9a7b,
    0xa5 => 0x6293, 0xa6 => 0x722a, 0xa7 => 0x62fd, 0xa8 => 0x4e13,
    0xa9 => 0x7816, 0xaa => 0x8f6c, 0xab => 0x64b0, 0xac => 0x8d5a,
    0xad => 0x7bc6, 0xae => 0x6869, 0xaf => 0x5e84, 0xb0 => 0x88c5,
    0xb1 => 0x5986, 0xb2 => 0x649e, 0xb3 => 0x58ee, 0xb4 => 0x72b6,
    0xb5 => 0x690e, 0xb6 => 0x9525, 0xb7 => 0x8ffd, 0xb8 => 0x8d58,
    0xb9 => 0x5760, 0xba => 0x7f00, 0xbb => 0x8c06, 0xbc => 0x51c6,
    0xbd => 0x6349, 0xbe => 0x62d9, 0xbf => 0x5353, 0xc0 => 0x684c,
    0xc1 => 0x7422, 0xc2 => 0x8301, 0xc3 => 0x914c, 0xc4 => 0x5544,
    0xc5 => 0x7740, 0xc6 => 0x707c, 0xc7 => 0x6d4a, 0xc8 => 0x5179,
    0xc9 => 0x54a8, 0xca => 0x8d44, 0xcb => 0x59ff, 0xcc => 0x6ecb,
    0xcd => 0x6dc4, 0xce => 0x5b5c, 0xcf => 0x7d2b, 0xd0 => 0x4ed4,
    0xd1 => 0x7c7d, 0xd2 => 0x6ed3, 0xd3 => 0x5b50, 0xd4 => 0x81ea,
    0xd5 => 0x6e0d, 0xd6 => 0x5b57, 0xd7 => 0x9b03, 0xd8 => 0x68d5,
    0xd9 => 0x8e2a, 0xda => 0x5b97, 0xdb => 0x7efc, 0xdc => 0x603b,
    0xdd => 0x7eb5, 0xde => 0x90b9, 0xdf => 0x8d70, 0xe0 => 0x594f,
    0xe1 => 0x63cd, 0xe2 => 0x79df, 0xe3 => 0x8db3, 0xe4 => 0x5352,
    0xe5 => 0x65cf, 0xe6 => 0x7956, 0xe7 => 0x8bc5, 0xe8 => 0x963b,
    0xe9 => 0x7ec4, 0xea => 0x94bb, 0xeb => 0x7e82, 0xec => 0x5634,
    0xed => 0x9189, 0xee => 0x6700, 0xef => 0x7f6a, 0xf0 => 0x5c0a,
    0xf1 => 0x9075, 0xf2 => 0x6628, 0xf3 => 0x5de6, 0xf4 => 0x4f50,
    0xf5 => 0x67de, 0xf6 => 0x505a, 0xf7 => 0x4f5c, 0xf8 => 0x5750,
    0xf9 => 0x5ea7,
  },
  0xd8 => {
    0xa1 => 0x4e8d, 0xa2 => 0x4e0c, 0xa3 => 0x5140, 0xa4 => 0x4e10,
    0xa5 => 0x5eff, 0xa6 => 0x5345, 0xa7 => 0x4e15, 0xa8 => 0x4e98,
    0xa9 => 0x4e1e, 0xaa => 0x9b32, 0xab => 0x5b6c, 0xac => 0x5669,
    0xad => 0x4e28, 0xae => 0x79ba, 0xaf => 0x4e3f, 0xb0 => 0x5315,
    0xb1 => 0x4e47, 0xb2 => 0x592d, 0xb3 => 0x723b, 0xb4 => 0x536e,
    0xb5 => 0x6c10, 0xb6 => 0x56df, 0xb7 => 0x80e4, 0xb8 => 0x9997,
    0xb9 => 0x6bd3, 0xba => 0x777e, 0xbb => 0x9f17, 0xbc => 0x4e36,
    0xbd => 0x4e9f, 0xbe => 0x9f10, 0xbf => 0x4e5c, 0xc0 => 0x4e69,
    0xc1 => 0x4e93, 0xc2 => 0x8288, 0xc3 => 0x5b5b, 0xc4 => 0x556c,
    0xc5 => 0x560f, 0xc6 => 0x4ec4, 0xc7 => 0x538d, 0xc8 => 0x539d,
    0xc9 => 0x53a3, 0xca => 0x53a5, 0xcb => 0x53ae, 0xcc => 0x9765,
    0xcd => 0x8d5d, 0xce => 0x531a, 0xcf => 0x53f5, 0xd0 => 0x5326,
    0xd1 => 0x532e, 0xd2 => 0x533e, 0xd3 => 0x8d5c, 0xd4 => 0x5366,
    0xd5 => 0x5363, 0xd6 => 0x5202, 0xd7 => 0x5208, 0xd8 => 0x520e,
    0xd9 => 0x522d, 0xda => 0x5233, 0xdb => 0x523f, 0xdc => 0x5240,
    0xdd => 0x524c, 0xde => 0x525e, 0xdf => 0x5261, 0xe0 => 0x525c,
    0xe1 => 0x84af, 0xe2 => 0x527d, 0xe3 => 0x5282, 0xe4 => 0x5281,
    0xe5 => 0x5290, 0xe6 => 0x5293, 0xe7 => 0x5182, 0xe8 => 0x7f54,
    0xe9 => 0x4ebb, 0xea => 0x4ec3, 0xeb => 0x4ec9, 0xec => 0x4ec2,
    0xed => 0x4ee8, 0xee => 0x4ee1, 0xef => 0x4eeb, 0xf0 => 0x4ede,
    0xf1 => 0x4f1b, 0xf2 => 0x4ef3, 0xf3 => 0x4f22, 0xf4 => 0x4f64,
    0xf5 => 0x4ef5, 0xf6 => 0x4f25, 0xf7 => 0x4f27, 0xf8 => 0x4f09,
    0xf9 => 0x4f2b, 0xfa => 0x4f5e, 0xfb => 0x4f67, 0xfc => 0x6538,
    0xfd => 0x4f5a, 0xfe => 0x4f5d,
  },
  0xd9 => {
    0xa1 => 0x4f5f, 0xa2 => 0x4f57, 0xa3 => 0x4f32, 0xa4 => 0x4f3d,
    0xa5 => 0x4f76, 0xa6 => 0x4f74, 0xa7 => 0x4f91, 0xa8 => 0x4f89,
    0xa9 => 0x4f83, 0xaa => 0x4f8f, 0xab => 0x4f7e, 0xac => 0x4f7b,
    0xad => 0x4faa, 0xae => 0x4f7c, 0xaf => 0x4fac, 0xb0 => 0x4f94,
    0xb1 => 0x4fe6, 0xb2 => 0x4fe8, 0xb3 => 0x4fea, 0xb4 => 0x4fc5,
    0xb5 => 0x4fda, 0xb6 => 0x4fe3, 0xb7 => 0x4fdc, 0xb8 => 0x4fd1,
    0xb9 => 0x4fdf, 0xba => 0x4ff8, 0xbb => 0x5029, 0xbc => 0x504c,
    0xbd => 0x4ff3, 0xbe => 0x502c, 0xbf => 0x500f, 0xc0 => 0x502e,
    0xc1 => 0x502d, 0xc2 => 0x4ffe, 0xc3 => 0x501c, 0xc4 => 0x500c,
    0xc5 => 0x5025, 0xc6 => 0x5028, 0xc7 => 0x507e, 0xc8 => 0x5043,
    0xc9 => 0x5055, 0xca => 0x5048, 0xcb => 0x504e, 0xcc => 0x506c,
    0xcd => 0x507b, 0xce => 0x50a5, 0xcf => 0x50a7, 0xd0 => 0x50a9,
    0xd1 => 0x50ba, 0xd2 => 0x50d6, 0xd3 => 0x5106, 0xd4 => 0x50ed,
    0xd5 => 0x50ec, 0xd6 => 0x50e6, 0xd7 => 0x50ee, 0xd8 => 0x5107,
    0xd9 => 0x510b, 0xda => 0x4edd, 0xdb => 0x6c3d, 0xdc => 0x4f58,
    0xdd => 0x4f65, 0xde => 0x4fce, 0xdf => 0x9fa0, 0xe0 => 0x6c46,
    0xe1 => 0x7c74, 0xe2 => 0x516e, 0xe3 => 0x5dfd, 0xe4 => 0x9ec9,
    0xe5 => 0x9998, 0xe6 => 0x5181, 0xe7 => 0x5914, 0xe8 => 0x52f9,
    0xe9 => 0x530d, 0xea => 0x8a07, 0xeb => 0x5310, 0xec => 0x51eb,
    0xed => 0x5919, 0xee => 0x5155, 0xef => 0x4ea0, 0xf0 => 0x5156,
    0xf1 => 0x4eb3, 0xf2 => 0x886e, 0xf3 => 0x88a4, 0xf4 => 0x4eb5,
    0xf5 => 0x8114, 0xf6 => 0x88d2, 0xf7 => 0x7980, 0xf8 => 0x5b34,
    0xf9 => 0x8803, 0xfa => 0x7fb8, 0xfb => 0x51ab, 0xfc => 0x51b1,
    0xfd => 0x51bd, 0xfe => 0x51bc,
  },
  0xda => {
    0xa1 => 0x51c7, 0xa2 => 0x5196, 0xa3 => 0x51a2, 0xa4 => 0x51a5,
    0xa5 => 0x8ba0, 0xa6 => 0x8ba6, 0xa7 => 0x8ba7, 0xa8 => 0x8baa,
    0xa9 => 0x8bb4, 0xaa => 0x8bb5, 0xab => 0x8bb7, 0xac => 0x8bc2,
    0xad => 0x8bc3, 0xae => 0x8bcb, 0xaf => 0x8bcf, 0xb0 => 0x8bce,
    0xb1 => 0x8bd2, 0xb2 => 0x8bd3, 0xb3 => 0x8bd4, 0xb4 => 0x8bd6,
    0xb5 => 0x8bd8, 0xb6 => 0x8bd9, 0xb7 => 0x8bdc, 0xb8 => 0x8bdf,
    0xb9 => 0x8be0, 0xba => 0x8be4, 0xbb => 0x8be8, 0xbc => 0x8be9,
    0xbd => 0x8bee, 0xbe => 0x8bf0, 0xbf => 0x8bf3, 0xc0 => 0x8bf6,
    0xc1 => 0x8bf9, 0xc2 => 0x8bfc, 0xc3 => 0x8bff, 0xc4 => 0x8c00,
    0xc5 => 0x8c02, 0xc6 => 0x8c04, 0xc7 => 0x8c07, 0xc8 => 0x8c0c,
    0xc9 => 0x8c0f, 0xca => 0x8c11, 0xcb => 0x8c12, 0xcc => 0x8c14,
    0xcd => 0x8c15, 0xce => 0x8c16, 0xcf => 0x8c19, 0xd0 => 0x8c1b,
    0xd1 => 0x8c18, 0xd2 => 0x8c1d, 0xd3 => 0x8c1f, 0xd4 => 0x8c20,
    0xd5 => 0x8c21, 0xd6 => 0x8c25, 0xd7 => 0x8c27, 0xd8 => 0x8c2a,
    0xd9 => 0x8c2b, 0xda => 0x8c2e, 0xdb => 0x8c2f, 0xdc => 0x8c32,
    0xdd => 0x8c33, 0xde => 0x8c35, 0xdf => 0x8c36, 0xe0 => 0x5369,
    0xe1 => 0x537a, 0xe2 => 0x961d, 0xe3 => 0x9622, 0xe4 => 0x9621,
    0xe5 => 0x9631, 0xe6 => 0x962a, 0xe7 => 0x963d, 0xe8 => 0x963c,
    0xe9 => 0x9642, 0xea => 0x9649, 0xeb => 0x9654, 0xec => 0x965f,
    0xed => 0x9667, 0xee => 0x966c, 0xef => 0x9672, 0xf0 => 0x9674,
    0xf1 => 0x9688, 0xf2 => 0x968d, 0xf3 => 0x9697, 0xf4 => 0x96b0,
    0xf5 => 0x9097, 0xf6 => 0x909b, 0xf7 => 0x909d, 0xf8 => 0x9099,
    0xf9 => 0x90ac, 0xfa => 0x90a1, 0xfb => 0x90b4, 0xfc => 0x90b3,
    0xfd => 0x90b6, 0xfe => 0x90ba,
  },
  0xdb => {
    0xa1 => 0x90b8, 0xa2 => 0x90b0, 0xa3 => 0x90cf, 0xa4 => 0x90c5,
    0xa5 => 0x90be, 0xa6 => 0x90d0, 0xa7 => 0x90c4, 0xa8 => 0x90c7,
    0xa9 => 0x90d3, 0xaa => 0x90e6, 0xab => 0x90e2, 0xac => 0x90dc,
    0xad => 0x90d7, 0xae => 0x90db, 0xaf => 0x90eb, 0xb0 => 0x90ef,
    0xb1 => 0x90fe, 0xb2 => 0x9104, 0xb3 => 0x9122, 0xb4 => 0x911e,
    0xb5 => 0x9123, 0xb6 => 0x9131, 0xb7 => 0x912f, 0xb8 => 0x9139,
    0xb9 => 0x9143, 0xba => 0x9146, 0xbb => 0x520d, 0xbc => 0x5942,
    0xbd => 0x52a2, 0xbe => 0x52ac, 0xbf => 0x52ad, 0xc0 => 0x52be,
    0xc1 => 0x54ff, 0xc2 => 0x52d0, 0xc3 => 0x52d6, 0xc4 => 0x52f0,
    0xc5 => 0x53df, 0xc6 => 0x71ee, 0xc7 => 0x77cd, 0xc8 => 0x5ef4,
    0xc9 => 0x51f5, 0xca => 0x51fc, 0xcb => 0x9b2f, 0xcc => 0x53b6,
    0xcd => 0x5f01, 0xce => 0x755a, 0xcf => 0x5def, 0xd0 => 0x574c,
    0xd1 => 0x57a9, 0xd2 => 0x57a1, 0xd3 => 0x587e, 0xd4 => 0x58bc,
    0xd5 => 0x58c5, 0xd6 => 0x58d1, 0xd7 => 0x5729, 0xd8 => 0x572c,
    0xd9 => 0x572a, 0xda => 0x5733, 0xdb => 0x5739, 0xdc => 0x572e,
    0xdd => 0x572f, 0xde => 0x575c, 0xdf => 0x573b, 0xe0 => 0x5742,
    0xe1 => 0x5769, 0xe2 => 0x5785, 0xe3 => 0x576b, 0xe4 => 0x5786,
    0xe5 => 0x577c, 0xe6 => 0x577b, 0xe7 => 0x5768, 0xe8 => 0x576d,
    0xe9 => 0x5776, 0xea => 0x5773, 0xeb => 0x57ad, 0xec => 0x57a4,
    0xed => 0x578c, 0xee => 0x57b2, 0xef => 0x57cf, 0xf0 => 0x57a7,
    0xf1 => 0x57b4, 0xf2 => 0x5793, 0xf3 => 0x57a0, 0xf4 => 0x57d5,
    0xf5 => 0x57d8, 0xf6 => 0x57da, 0xf7 => 0x57d9, 0xf8 => 0x57d2,
    0xf9 => 0x57b8, 0xfa => 0x57f4, 0xfb => 0x57ef, 0xfc => 0x57f8,
    0xfd => 0x57e4, 0xfe => 0x57dd,
  },
  0xdc => {
    0xa1 => 0x580b, 0xa2 => 0x580d, 0xa3 => 0x57fd, 0xa4 => 0x57ed,
    0xa5 => 0x5800, 0xa6 => 0x581e, 0xa7 => 0x5819, 0xa8 => 0x5844,
    0xa9 => 0x5820, 0xaa => 0x5865, 0xab => 0x586c, 0xac => 0x5881,
    0xad => 0x5889, 0xae => 0x589a, 0xaf => 0x5880, 0xb0 => 0x99a8,
    0xb1 => 0x9f19, 0xb2 => 0x61ff, 0xb3 => 0x8279, 0xb4 => 0x827d,
    0xb5 => 0x827f, 0xb6 => 0x828f, 0xb7 => 0x828a, 0xb8 => 0x82a8,
    0xb9 => 0x8284, 0xba => 0x828e, 0xbb => 0x8291, 0xbc => 0x8297,
    0xbd => 0x8299, 0xbe => 0x82ab, 0xbf => 0x82b8, 0xc0 => 0x82be,
    0xc1 => 0x82b0, 0xc2 => 0x82c8, 0xc3 => 0x82ca, 0xc4 => 0x82e3,
    0xc5 => 0x8298, 0xc6 => 0x82b7, 0xc7 => 0x82ae, 0xc8 => 0x82cb,
    0xc9 => 0x82cc, 0xca => 0x82c1, 0xcb => 0x82a9, 0xcc => 0x82b4,
    0xcd => 0x82a1, 0xce => 0x82aa, 0xcf => 0x829f, 0xd0 => 0x82c4,
    0xd1 => 0x82ce, 0xd2 => 0x82a4, 0xd3 => 0x82e1, 0xd4 => 0x8309,
    0xd5 => 0x82f7, 0xd6 => 0x82e4, 0xd7 => 0x830f, 0xd8 => 0x8307,
    0xd9 => 0x82dc, 0xda => 0x82f4, 0xdb => 0x82d2, 0xdc => 0x82d8,
    0xdd => 0x830c, 0xde => 0x82fb, 0xdf => 0x82d3, 0xe0 => 0x8311,
    0xe1 => 0x831a, 0xe2 => 0x8306, 0xe3 => 0x8314, 0xe4 => 0x8315,
    0xe5 => 0x82e0, 0xe6 => 0x82d5, 0xe7 => 0x831c, 0xe8 => 0x8351,
    0xe9 => 0x835b, 0xea => 0x835c, 0xeb => 0x8308, 0xec => 0x8392,
    0xed => 0x833c, 0xee => 0x8334, 0xef => 0x8331, 0xf0 => 0x839b,
    0xf1 => 0x835e, 0xf2 => 0x832f, 0xf3 => 0x834f, 0xf4 => 0x8347,
    0xf5 => 0x8343, 0xf6 => 0x835f, 0xf7 => 0x8340, 0xf8 => 0x8317,
    0xf9 => 0x8360, 0xfa => 0x832d, 0xfb => 0x833a, 0xfc => 0x8333,
    0xfd => 0x8366, 0xfe => 0x8365,
  },
  0xdd => {
    0xa1 => 0x8368, 0xa2 => 0x831b, 0xa3 => 0x8369, 0xa4 => 0x836c,
    0xa5 => 0x836a, 0xa6 => 0x836d, 0xa7 => 0x836e, 0xa8 => 0x83b0,
    0xa9 => 0x8378, 0xaa => 0x83b3, 0xab => 0x83b4, 0xac => 0x83a0,
    0xad => 0x83aa, 0xae => 0x8393, 0xaf => 0x839c, 0xb0 => 0x8385,
    0xb1 => 0x837c, 0xb2 => 0x83b6, 0xb3 => 0x83a9, 0xb4 => 0x837d,
    0xb5 => 0x83b8, 0xb6 => 0x837b, 0xb7 => 0x8398, 0xb8 => 0x839e,
    0xb9 => 0x83a8, 0xba => 0x83ba, 0xbb => 0x83bc, 0xbc => 0x83c1,
    0xbd => 0x8401, 0xbe => 0x83e5, 0xbf => 0x83d8, 0xc0 => 0x5807,
    0xc1 => 0x8418, 0xc2 => 0x840b, 0xc3 => 0x83dd, 0xc4 => 0x83fd,
    0xc5 => 0x83d6, 0xc6 => 0x841c, 0xc7 => 0x8438, 0xc8 => 0x8411,
    0xc9 => 0x8406, 0xca => 0x83d4, 0xcb => 0x83df, 0xcc => 0x840f,
    0xcd => 0x8403, 0xce => 0x83f8, 0xcf => 0x83f9, 0xd0 => 0x83ea,
    0xd1 => 0x83c5, 0xd2 => 0x83c0, 0xd3 => 0x8426, 0xd4 => 0x83f0,
    0xd5 => 0x83e1, 0xd6 => 0x845c, 0xd7 => 0x8451, 0xd8 => 0x845a,
    0xd9 => 0x8459, 0xda => 0x8473, 0xdb => 0x8487, 0xdc => 0x8488,
    0xdd => 0x847a, 0xde => 0x8489, 0xdf => 0x8478, 0xe0 => 0x843c,
    0xe1 => 0x8446, 0xe2 => 0x8469, 0xe3 => 0x8476, 0xe4 => 0x848c,
    0xe5 => 0x848e, 0xe6 => 0x8431, 0xe7 => 0x846d, 0xe8 => 0x84c1,
    0xe9 => 0x84cd, 0xea => 0x84d0, 0xeb => 0x84e6, 0xec => 0x84bd,
    0xed => 0x84d3, 0xee => 0x84ca, 0xef => 0x84bf, 0xf0 => 0x84ba,
    0xf1 => 0x84e0, 0xf2 => 0x84a1, 0xf3 => 0x84b9, 0xf4 => 0x84b4,
    0xf5 => 0x8497, 0xf6 => 0x84e5, 0xf7 => 0x84e3, 0xf8 => 0x850c,
    0xf9 => 0x750d, 0xfa => 0x8538, 0xfb => 0x84f0, 0xfc => 0x8539,
    0xfd => 0x851f, 0xfe => 0x853a,
  },
  0xde => {
    0xa1 => 0x8556, 0xa2 => 0x853b, 0xa3 => 0x84ff, 0xa4 => 0x84fc,
    0xa5 => 0x8559, 0xa6 => 0x8548, 0xa7 => 0x8568, 0xa8 => 0x8564,
    0xa9 => 0x855e, 0xaa => 0x857a, 0xab => 0x77a2, 0xac => 0x8543,
    0xad => 0x8572, 0xae => 0x857b, 0xaf => 0x85a4, 0xb0 => 0x85a8,
    0xb1 => 0x8587, 0xb2 => 0x858f, 0xb3 => 0x8579, 0xb4 => 0x85ae,
    0xb5 => 0x859c, 0xb6 => 0x8585, 0xb7 => 0x85b9, 0xb8 => 0x85b7,
    0xb9 => 0x85b0, 0xba => 0x85d3, 0xbb => 0x85c1, 0xbc => 0x85dc,
    0xbd => 0x85ff, 0xbe => 0x8627, 0xbf => 0x8605, 0xc0 => 0x8629,
    0xc1 => 0x8616, 0xc2 => 0x863c, 0xc3 => 0x5efe, 0xc4 => 0x5f08,
    0xc5 => 0x593c, 0xc6 => 0x5941, 0xc7 => 0x8037, 0xc8 => 0x5955,
    0xc9 => 0x595a, 0xca => 0x5958, 0xcb => 0x530f, 0xcc => 0x5c22,
    0xcd => 0x5c25, 0xce => 0x5c2c, 0xcf => 0x5c34, 0xd0 => 0x624c,
    0xd1 => 0x626a, 0xd2 => 0x629f, 0xd3 => 0x62bb, 0xd4 => 0x62ca,
    0xd5 => 0x62da, 0xd6 => 0x62d7, 0xd7 => 0x62ee, 0xd8 => 0x6322,
    0xd9 => 0x62f6, 0xda => 0x6339, 0xdb => 0x634b, 0xdc => 0x6343,
    0xdd => 0x63ad, 0xde => 0x63f6, 0xdf => 0x6371, 0xe0 => 0x637a,
    0xe1 => 0x638e, 0xe2 => 0x63b4, 0xe3 => 0x636d, 0xe4 => 0x63ac,
    0xe5 => 0x638a, 0xe6 => 0x6369, 0xe7 => 0x63ae, 0xe8 => 0x63bc,
    0xe9 => 0x63f2, 0xea => 0x63f8, 0xeb => 0x63e0, 0xec => 0x63ff,
    0xed => 0x63c4, 0xee => 0x63de, 0xef => 0x63ce, 0xf0 => 0x6452,
    0xf1 => 0x63c6, 0xf2 => 0x63be, 0xf3 => 0x6445, 0xf4 => 0x6441,
    0xf5 => 0x640b, 0xf6 => 0x641b, 0xf7 => 0x6420, 0xf8 => 0x640c,
    0xf9 => 0x6426, 0xfa => 0x6421, 0xfb => 0x645e, 0xfc => 0x6484,
    0xfd => 0x646d, 0xfe => 0x6496,
  },
  0xdf => {
    0xa1 => 0x647a, 0xa2 => 0x64b7, 0xa3 => 0x64b8, 0xa4 => 0x6499,
    0xa5 => 0x64ba, 0xa6 => 0x64c0, 0xa7 => 0x64d0, 0xa8 => 0x64d7,
    0xa9 => 0x64e4, 0xaa => 0x64e2, 0xab => 0x6509, 0xac => 0x6525,
    0xad => 0x652e, 0xae => 0x5f0b, 0xaf => 0x5fd2, 0xb0 => 0x7519,
    0xb1 => 0x5f11, 0xb2 => 0x535f, 0xb3 => 0x53f1, 0xb4 => 0x53fd,
    0xb5 => 0x53e9, 0xb6 => 0x53e8, 0xb7 => 0x53fb, 0xb8 => 0x5412,
    0xb9 => 0x5416, 0xba => 0x5406, 0xbb => 0x544b, 0xbc => 0x5452,
    0xbd => 0x5453, 0xbe => 0x5454, 0xbf => 0x5456, 0xc0 => 0x5443,
    0xc1 => 0x5421, 0xc2 => 0x5457, 0xc3 => 0x5459, 0xc4 => 0x5423,
    0xc5 => 0x5432, 0xc6 => 0x5482, 0xc7 => 0x5494, 0xc8 => 0x5477,
    0xc9 => 0x5471, 0xca => 0x5464, 0xcb => 0x549a, 0xcc => 0x549b,
    0xcd => 0x5484, 0xce => 0x5476, 0xcf => 0x5466, 0xd0 => 0x549d,
    0xd1 => 0x54d0, 0xd2 => 0x54ad, 0xd3 => 0x54c2, 0xd4 => 0x54b4,
    0xd5 => 0x54d2, 0xd6 => 0x54a7, 0xd7 => 0x54a6, 0xd8 => 0x54d3,
    0xd9 => 0x54d4, 0xda => 0x5472, 0xdb => 0x54a3, 0xdc => 0x54d5,
    0xdd => 0x54bb, 0xde => 0x54bf, 0xdf => 0x54cc, 0xe0 => 0x54d9,
    0xe1 => 0x54da, 0xe2 => 0x54dc, 0xe3 => 0x54a9, 0xe4 => 0x54aa,
    0xe5 => 0x54a4, 0xe6 => 0x54dd, 0xe7 => 0x54cf, 0xe8 => 0x54de,
    0xe9 => 0x551b, 0xea => 0x54e7, 0xeb => 0x5520, 0xec => 0x54fd,
    0xed => 0x5514, 0xee => 0x54f3, 0xef => 0x5522, 0xf0 => 0x5523,
    0xf1 => 0x550f, 0xf2 => 0x5511, 0xf3 => 0x5527, 0xf4 => 0x552a,
    0xf5 => 0x5567, 0xf6 => 0x558f, 0xf7 => 0x55b5, 0xf8 => 0x5549,
    0xf9 => 0x556d, 0xfa => 0x5541, 0xfb => 0x5555, 0xfc => 0x553f,
    0xfd => 0x5550, 0xfe => 0x553c,
  },
  0xe0 => {
    0xa1 => 0x5537, 0xa2 => 0x5556, 0xa3 => 0x5575, 0xa4 => 0x5576,
    0xa5 => 0x5577, 0xa6 => 0x5533, 0xa7 => 0x5530, 0xa8 => 0x555c,
    0xa9 => 0x558b, 0xaa => 0x55d2, 0xab => 0x5583, 0xac => 0x55b1,
    0xad => 0x55b9, 0xae => 0x5588, 0xaf => 0x5581, 0xb0 => 0x559f,
    0xb1 => 0x557e, 0xb2 => 0x55d6, 0xb3 => 0x5591, 0xb4 => 0x557b,
    0xb5 => 0x55df, 0xb6 => 0x55bd, 0xb7 => 0x55be, 0xb8 => 0x5594,
    0xb9 => 0x5599, 0xba => 0x55ea, 0xbb => 0x55f7, 0xbc => 0x55c9,
    0xbd => 0x561f, 0xbe => 0x55d1, 0xbf => 0x55eb, 0xc0 => 0x55ec,
    0xc1 => 0x55d4, 0xc2 => 0x55e6, 0xc3 => 0x55dd, 0xc4 => 0x55c4,
    0xc5 => 0x55ef, 0xc6 => 0x55e5, 0xc7 => 0x55f2, 0xc8 => 0x55f3,
    0xc9 => 0x55cc, 0xca => 0x55cd, 0xcb => 0x55e8, 0xcc => 0x55f5,
    0xcd => 0x55e4, 0xce => 0x8f94, 0xcf => 0x561e, 0xd0 => 0x5608,
    0xd1 => 0x560c, 0xd2 => 0x5601, 0xd3 => 0x5624, 0xd4 => 0x5623,
    0xd5 => 0x55fe, 0xd6 => 0x5600, 0xd7 => 0x5627, 0xd8 => 0x562d,
    0xd9 => 0x5658, 0xda => 0x5639, 0xdb => 0x5657, 0xdc => 0x562c,
    0xdd => 0x564d, 0xde => 0x5662, 0xdf => 0x5659, 0xe0 => 0x565c,
    0xe1 => 0x564c, 0xe2 => 0x5654, 0xe3 => 0x5686, 0xe4 => 0x5664,
    0xe5 => 0x5671, 0xe6 => 0x566b, 0xe7 => 0x567b, 0xe8 => 0x567c,
    0xe9 => 0x5685, 0xea => 0x5693, 0xeb => 0x56af, 0xec => 0x56d4,
    0xed => 0x56d7, 0xee => 0x56dd, 0xef => 0x56e1, 0xf0 => 0x56f5,
    0xf1 => 0x56eb, 0xf2 => 0x56f9, 0xf3 => 0x56ff, 0xf4 => 0x5704,
    0xf5 => 0x570a, 0xf6 => 0x5709, 0xf7 => 0x571c, 0xf8 => 0x5e0f,
    0xf9 => 0x5e19, 0xfa => 0x5e14, 0xfb => 0x5e11, 0xfc => 0x5e31,
    0xfd => 0x5e3b, 0xfe => 0x5e3c,
  },
  0xe1 => {
    0xa1 => 0x5e37, 0xa2 => 0x5e44, 0xa3 => 0x5e54, 0xa4 => 0x5e5b,
    0xa5 => 0x5e5e, 0xa6 => 0x5e61, 0xa7 => 0x5c8c, 0xa8 => 0x5c7a,
    0xa9 => 0x5c8d, 0xaa => 0x5c90, 0xab => 0x5c96, 0xac => 0x5c88,
    0xad => 0x5c98, 0xae => 0x5c99, 0xaf => 0x5c91, 0xb0 => 0x5c9a,
    0xb1 => 0x5c9c, 0xb2 => 0x5cb5, 0xb3 => 0x5ca2, 0xb4 => 0x5cbd,
    0xb5 => 0x5cac, 0xb6 => 0x5cab, 0xb7 => 0x5cb1, 0xb8 => 0x5ca3,
    0xb9 => 0x5cc1, 0xba => 0x5cb7, 0xbb => 0x5cc4, 0xbc => 0x5cd2,
    0xbd => 0x5ce4, 0xbe => 0x5ccb, 0xbf => 0x5ce5, 0xc0 => 0x5d02,
    0xc1 => 0x5d03, 0xc2 => 0x5d27, 0xc3 => 0x5d26, 0xc4 => 0x5d2e,
    0xc5 => 0x5d24, 0xc6 => 0x5d1e, 0xc7 => 0x5d06, 0xc8 => 0x5d1b,
    0xc9 => 0x5d58, 0xca => 0x5d3e, 0xcb => 0x5d34, 0xcc => 0x5d3d,
    0xcd => 0x5d6c, 0xce => 0x5d5b, 0xcf => 0x5d6f, 0xd0 => 0x5d5d,
    0xd1 => 0x5d6b, 0xd2 => 0x5d4b, 0xd3 => 0x5d4a, 0xd4 => 0x5d69,
    0xd5 => 0x5d74, 0xd6 => 0x5d82, 0xd7 => 0x5d99, 0xd8 => 0x5d9d,
    0xd9 => 0x8c73, 0xda => 0x5db7, 0xdb => 0x5dc5, 0xdc => 0x5f73,
    0xdd => 0x5f77, 0xde => 0x5f82, 0xdf => 0x5f87, 0xe0 => 0x5f89,
    0xe1 => 0x5f8c, 0xe2 => 0x5f95, 0xe3 => 0x5f99, 0xe4 => 0x5f9c,
    0xe5 => 0x5fa8, 0xe6 => 0x5fad, 0xe7 => 0x5fb5, 0xe8 => 0x5fbc,
    0xe9 => 0x8862, 0xea => 0x5f61, 0xeb => 0x72ad, 0xec => 0x72b0,
    0xed => 0x72b4, 0xee => 0x72b7, 0xef => 0x72b8, 0xf0 => 0x72c3,
    0xf1 => 0x72c1, 0xf2 => 0x72ce, 0xf3 => 0x72cd, 0xf4 => 0x72d2,
    0xf5 => 0x72e8, 0xf6 => 0x72ef, 0xf7 => 0x72e9, 0xf8 => 0x72f2,
    0xf9 => 0x72f4, 0xfa => 0x72f7, 0xfb => 0x7301, 0xfc => 0x72f3,
    0xfd => 0x7303, 0xfe => 0x72fa,
  },
  0xe2 => {
    0xa1 => 0x72fb, 0xa2 => 0x7317, 0xa3 => 0x7313, 0xa4 => 0x7321,
    0xa5 => 0x730a, 0xa6 => 0x731e, 0xa7 => 0x731d, 0xa8 => 0x7315,
    0xa9 => 0x7322, 0xaa => 0x7339, 0xab => 0x7325, 0xac => 0x732c,
    0xad => 0x7338, 0xae => 0x7331, 0xaf => 0x7350, 0xb0 => 0x734d,
    0xb1 => 0x7357, 0xb2 => 0x7360, 0xb3 => 0x736c, 0xb4 => 0x736f,
    0xb5 => 0x737e, 0xb6 => 0x821b, 0xb7 => 0x5925, 0xb8 => 0x98e7,
    0xb9 => 0x5924, 0xba => 0x5902, 0xbb => 0x9963, 0xbc => 0x9967,
    0xbd => 0x9968, 0xbe => 0x9969, 0xbf => 0x996a, 0xc0 => 0x996b,
    0xc1 => 0x996c, 0xc2 => 0x9974, 0xc3 => 0x9977, 0xc4 => 0x997d,
    0xc5 => 0x9980, 0xc6 => 0x9984, 0xc7 => 0x9987, 0xc8 => 0x998a,
    0xc9 => 0x998d, 0xca => 0x9990, 0xcb => 0x9991, 0xcc => 0x9993,
    0xcd => 0x9994, 0xce => 0x9995, 0xcf => 0x5e80, 0xd0 => 0x5e91,
    0xd1 => 0x5e8b, 0xd2 => 0x5e96, 0xd3 => 0x5ea5, 0xd4 => 0x5ea0,
    0xd5 => 0x5eb9, 0xd6 => 0x5eb5, 0xd7 => 0x5ebe, 0xd8 => 0x5eb3,
    0xd9 => 0x8d53, 0xda => 0x5ed2, 0xdb => 0x5ed1, 0xdc => 0x5edb,
    0xdd => 0x5ee8, 0xde => 0x5eea, 0xdf => 0x81ba, 0xe0 => 0x5fc4,
    0xe1 => 0x5fc9, 0xe2 => 0x5fd6, 0xe3 => 0x5fcf, 0xe4 => 0x6003,
    0xe5 => 0x5fee, 0xe6 => 0x6004, 0xe7 => 0x5fe1, 0xe8 => 0x5fe4,
    0xe9 => 0x5ffe, 0xea => 0x6005, 0xeb => 0x6006, 0xec => 0x5fea,
    0xed => 0x5fed, 0xee => 0x5ff8, 0xef => 0x6019, 0xf0 => 0x6035,
    0xf1 => 0x6026, 0xf2 => 0x601b, 0xf3 => 0x600f, 0xf4 => 0x600d,
    0xf5 => 0x6029, 0xf6 => 0x602b, 0xf7 => 0x600a, 0xf8 => 0x603f,
    0xf9 => 0x6021, 0xfa => 0x6078, 0xfb => 0x6079, 0xfc => 0x607b,
    0xfd => 0x607a, 0xfe => 0x6042,
  },
  0xe3 => {
    0xa1 => 0x606a, 0xa2 => 0x607d, 0xa3 => 0x6096, 0xa4 => 0x609a,
    0xa5 => 0x60ad, 0xa6 => 0x609d, 0xa7 => 0x6083, 0xa8 => 0x6092,
    0xa9 => 0x608c, 0xaa => 0x609b, 0xab => 0x60ec, 0xac => 0x60bb,
    0xad => 0x60b1, 0xae => 0x60dd, 0xaf => 0x60d8, 0xb0 => 0x60c6,
    0xb1 => 0x60da, 0xb2 => 0x60b4, 0xb3 => 0x6120, 0xb4 => 0x6126,
    0xb5 => 0x6115, 0xb6 => 0x6123, 0xb7 => 0x60f4, 0xb8 => 0x6100,
    0xb9 => 0x610e, 0xba => 0x612b, 0xbb => 0x614a, 0xbc => 0x6175,
    0xbd => 0x61ac, 0xbe => 0x6194, 0xbf => 0x61a7, 0xc0 => 0x61b7,
    0xc1 => 0x61d4, 0xc2 => 0x61f5, 0xc3 => 0x5fdd, 0xc4 => 0x96b3,
    0xc5 => 0x95e9, 0xc6 => 0x95eb, 0xc7 => 0x95f1, 0xc8 => 0x95f3,
    0xc9 => 0x95f5, 0xca => 0x95f6, 0xcb => 0x95fc, 0xcc => 0x95fe,
    0xcd => 0x9603, 0xce => 0x9604, 0xcf => 0x9606, 0xd0 => 0x9608,
    0xd1 => 0x960a, 0xd2 => 0x960b, 0xd3 => 0x960c, 0xd4 => 0x960d,
    0xd5 => 0x960f, 0xd6 => 0x9612, 0xd7 => 0x9615, 0xd8 => 0x9616,
    0xd9 => 0x9617, 0xda => 0x9619, 0xdb => 0x961a, 0xdc => 0x4e2c,
    0xdd => 0x723f, 0xde => 0x6215, 0xdf => 0x6c35, 0xe0 => 0x6c54,
    0xe1 => 0x6c5c, 0xe2 => 0x6c4a, 0xe3 => 0x6ca3, 0xe4 => 0x6c85,
    0xe5 => 0x6c90, 0xe6 => 0x6c94, 0xe7 => 0x6c8c, 0xe8 => 0x6c68,
    0xe9 => 0x6c69, 0xea => 0x6c74, 0xeb => 0x6c76, 0xec => 0x6c86,
    0xed => 0x6ca9, 0xee => 0x6cd0, 0xef => 0x6cd4, 0xf0 => 0x6cad,
    0xf1 => 0x6cf7, 0xf2 => 0x6cf8, 0xf3 => 0x6cf1, 0xf4 => 0x6cd7,
    0xf5 => 0x6cb2, 0xf6 => 0x6ce0, 0xf7 => 0x6cd6, 0xf8 => 0x6cfa,
    0xf9 => 0x6ceb, 0xfa => 0x6cee, 0xfb => 0x6cb1, 0xfc => 0x6cd3,
    0xfd => 0x6cef, 0xfe => 0x6cfe,
  },
  0xe4 => {
    0xa1 => 0x6d39, 0xa2 => 0x6d27, 0xa3 => 0x6d0c, 0xa4 => 0x6d43,
    0xa5 => 0x6d48, 0xa6 => 0x6d07, 0xa7 => 0x6d04, 0xa8 => 0x6d19,
    0xa9 => 0x6d0e, 0xaa => 0x6d2b, 0xab => 0x6d4d, 0xac => 0x6d2e,
    0xad => 0x6d35, 0xae => 0x6d1a, 0xaf => 0x6d4f, 0xb0 => 0x6d52,
    0xb1 => 0x6d54, 0xb2 => 0x6d33, 0xb3 => 0x6d91, 0xb4 => 0x6d6f,
    0xb5 => 0x6d9e, 0xb6 => 0x6da0, 0xb7 => 0x6d5e, 0xb8 => 0x6d93,
    0xb9 => 0x6d94, 0xba => 0x6d5c, 0xbb => 0x6d60, 0xbc => 0x6d7c,
    0xbd => 0x6d63, 0xbe => 0x6e1a, 0xbf => 0x6dc7, 0xc0 => 0x6dc5,
    0xc1 => 0x6dde, 0xc2 => 0x6e0e, 0xc3 => 0x6dbf, 0xc4 => 0x6de0,
    0xc5 => 0x6e11, 0xc6 => 0x6de6, 0xc7 => 0x6ddd, 0xc8 => 0x6dd9,
    0xc9 => 0x6e16, 0xca => 0x6dab, 0xcb => 0x6e0c, 0xcc => 0x6dae,
    0xcd => 0x6e2b, 0xce => 0x6e6e, 0xcf => 0x6e4e, 0xd0 => 0x6e6b,
    0xd1 => 0x6eb2, 0xd2 => 0x6e5f, 0xd3 => 0x6e86, 0xd4 => 0x6e53,
    0xd5 => 0x6e54, 0xd6 => 0x6e32, 0xd7 => 0x6e25, 0xd8 => 0x6e44,
    0xd9 => 0x6edf, 0xda => 0x6eb1, 0xdb => 0x6e98, 0xdc => 0x6ee0,
    0xdd => 0x6f2d, 0xde => 0x6ee2, 0xdf => 0x6ea5, 0xe0 => 0x6ea7,
    0xe1 => 0x6ebd, 0xe2 => 0x6ebb, 0xe3 => 0x6eb7, 0xe4 => 0x6ed7,
    0xe5 => 0x6eb4, 0xe6 => 0x6ecf, 0xe7 => 0x6e8f, 0xe8 => 0x6ec2,
    0xe9 => 0x6e9f, 0xea => 0x6f62, 0xeb => 0x6f46, 0xec => 0x6f47,
    0xed => 0x6f24, 0xee => 0x6f15, 0xef => 0x6ef9, 0xf0 => 0x6f2f,
    0xf1 => 0x6f36, 0xf2 => 0x6f4b, 0xf3 => 0x6f74, 0xf4 => 0x6f2a,
    0xf5 => 0x6f09, 0xf6 => 0x6f29, 0xf7 => 0x6f89, 0xf8 => 0x6f8d,
    0xf9 => 0x6f8c, 0xfa => 0x6f78, 0xfb => 0x6f72, 0xfc => 0x6f7c,
    0xfd => 0x6f7a, 0xfe => 0x6fd1,
  },
  0xe5 => {
    0xa1 => 0x6fc9, 0xa2 => 0x6fa7, 0xa3 => 0x6fb9, 0xa4 => 0x6fb6,
    0xa5 => 0x6fc2, 0xa6 => 0x6fe1, 0xa7 => 0x6fee, 0xa8 => 0x6fde,
    0xa9 => 0x6fe0, 0xaa => 0x6fef, 0xab => 0x701a, 0xac => 0x7023,
    0xad => 0x701b, 0xae => 0x7039, 0xaf => 0x7035, 0xb0 => 0x704f,
    0xb1 => 0x705e, 0xb2 => 0x5b80, 0xb3 => 0x5b84, 0xb4 => 0x5b95,
    0xb5 => 0x5b93, 0xb6 => 0x5ba5, 0xb7 => 0x5bb8, 0xb8 => 0x752f,
    0xb9 => 0x9a9e, 0xba => 0x6434, 0xbb => 0x5be4, 0xbc => 0x5bee,
    0xbd => 0x8930, 0xbe => 0x5bf0, 0xbf => 0x8e47, 0xc0 => 0x8b07,
    0xc1 => 0x8fb6, 0xc2 => 0x8fd3, 0xc3 => 0x8fd5, 0xc4 => 0x8fe5,
    0xc5 => 0x8fee, 0xc6 => 0x8fe4, 0xc7 => 0x8fe9, 0xc8 => 0x8fe6,
    0xc9 => 0x8ff3, 0xca => 0x8fe8, 0xcb => 0x9005, 0xcc => 0x9004,
    0xcd => 0x900b, 0xce => 0x9026, 0xcf => 0x9011, 0xd0 => 0x900d,
    0xd1 => 0x9016, 0xd2 => 0x9021, 0xd3 => 0x9035, 0xd4 => 0x9036,
    0xd5 => 0x902d, 0xd6 => 0x902f, 0xd7 => 0x9044, 0xd8 => 0x9051,
    0xd9 => 0x9052, 0xda => 0x9050, 0xdb => 0x9068, 0xdc => 0x9058,
    0xdd => 0x9062, 0xde => 0x905b, 0xdf => 0x66b9, 0xe0 => 0x9074,
    0xe1 => 0x907d, 0xe2 => 0x9082, 0xe3 => 0x9088, 0xe4 => 0x9083,
    0xe5 => 0x908b, 0xe6 => 0x5f50, 0xe7 => 0x5f57, 0xe8 => 0x5f56,
    0xe9 => 0x5f58, 0xea => 0x5c3b, 0xeb => 0x54ab, 0xec => 0x5c50,
    0xed => 0x5c59, 0xee => 0x5b71, 0xef => 0x5c63, 0xf0 => 0x5c66,
    0xf1 => 0x7fbc, 0xf2 => 0x5f2a, 0xf3 => 0x5f29, 0xf4 => 0x5f2d,
    0xf5 => 0x8274, 0xf6 => 0x5f3c, 0xf7 => 0x9b3b, 0xf8 => 0x5c6e,
    0xf9 => 0x5981, 0xfa => 0x5983, 0xfb => 0x598d, 0xfc => 0x59a9,
    0xfd => 0x59aa, 0xfe => 0x59a3,
  },
  0xe6 => {
    0xa1 => 0x5997, 0xa2 => 0x59ca, 0xa3 => 0x59ab, 0xa4 => 0x599e,
    0xa5 => 0x59a4, 0xa6 => 0x59d2, 0xa7 => 0x59b2, 0xa8 => 0x59af,
    0xa9 => 0x59d7, 0xaa => 0x59be, 0xab => 0x5a05, 0xac => 0x5a06,
    0xad => 0x59dd, 0xae => 0x5a08, 0xaf => 0x59e3, 0xb0 => 0x59d8,
    0xb1 => 0x59f9, 0xb2 => 0x5a0c, 0xb3 => 0x5a09, 0xb4 => 0x5a32,
    0xb5 => 0x5a34, 0xb6 => 0x5a11, 0xb7 => 0x5a23, 0xb8 => 0x5a13,
    0xb9 => 0x5a40, 0xba => 0x5a67, 0xbb => 0x5a4a, 0xbc => 0x5a55,
    0xbd => 0x5a3c, 0xbe => 0x5a62, 0xbf => 0x5a75, 0xc0 => 0x80ec,
    0xc1 => 0x5aaa, 0xc2 => 0x5a9b, 0xc3 => 0x5a77, 0xc4 => 0x5a7a,
    0xc5 => 0x5abe, 0xc6 => 0x5aeb, 0xc7 => 0x5ab2, 0xc8 => 0x5ad2,
    0xc9 => 0x5ad4, 0xca => 0x5ab8, 0xcb => 0x5ae0, 0xcc => 0x5ae3,
    0xcd => 0x5af1, 0xce => 0x5ad6, 0xcf => 0x5ae6, 0xd0 => 0x5ad8,
    0xd1 => 0x5adc, 0xd2 => 0x5b09, 0xd3 => 0x5b17, 0xd4 => 0x5b16,
    0xd5 => 0x5b32, 0xd6 => 0x5b37, 0xd7 => 0x5b40, 0xd8 => 0x5c15,
    0xd9 => 0x5c1c, 0xda => 0x5b5a, 0xdb => 0x5b65, 0xdc => 0x5b73,
    0xdd => 0x5b51, 0xde => 0x5b53, 0xdf => 0x5b62, 0xe0 => 0x9a75,
    0xe1 => 0x9a77, 0xe2 => 0x9a78, 0xe3 => 0x9a7a, 0xe4 => 0x9a7f,
    0xe5 => 0x9a7d, 0xe6 => 0x9a80, 0xe7 => 0x9a81, 0xe8 => 0x9a85,
    0xe9 => 0x9a88, 0xea => 0x9a8a, 0xeb => 0x9a90, 0xec => 0x9a92,
    0xed => 0x9a93, 0xee => 0x9a96, 0xef => 0x9a98, 0xf0 => 0x9a9b,
    0xf1 => 0x9a9c, 0xf2 => 0x9a9d, 0xf3 => 0x9a9f, 0xf4 => 0x9aa0,
    0xf5 => 0x9aa2, 0xf6 => 0x9aa3, 0xf7 => 0x9aa5, 0xf8 => 0x9aa7,
    0xf9 => 0x7e9f, 0xfa => 0x7ea1, 0xfb => 0x7ea3, 0xfc => 0x7ea5,
    0xfd => 0x7ea8, 0xfe => 0x7ea9,
  },
  0xe7 => {
    0xa1 => 0x7ead, 0xa2 => 0x7eb0, 0xa3 => 0x7ebe, 0xa4 => 0x7ec0,
    0xa5 => 0x7ec1, 0xa6 => 0x7ec2, 0xa7 => 0x7ec9, 0xa8 => 0x7ecb,
    0xa9 => 0x7ecc, 0xaa => 0x7ed0, 0xab => 0x7ed4, 0xac => 0x7ed7,
    0xad => 0x7edb, 0xae => 0x7ee0, 0xaf => 0x7ee1, 0xb0 => 0x7ee8,
    0xb1 => 0x7eeb, 0xb2 => 0x7eee, 0xb3 => 0x7eef, 0xb4 => 0x7ef1,
    0xb5 => 0x7ef2, 0xb6 => 0x7f0d, 0xb7 => 0x7ef6, 0xb8 => 0x7efa,
    0xb9 => 0x7efb, 0xba => 0x7efe, 0xbb => 0x7f01, 0xbc => 0x7f02,
    0xbd => 0x7f03, 0xbe => 0x7f07, 0xbf => 0x7f08, 0xc0 => 0x7f0b,
    0xc1 => 0x7f0c, 0xc2 => 0x7f0f, 0xc3 => 0x7f11, 0xc4 => 0x7f12,
    0xc5 => 0x7f17, 0xc6 => 0x7f19, 0xc7 => 0x7f1c, 0xc8 => 0x7f1b,
    0xc9 => 0x7f1f, 0xca => 0x7f21, 0xcb => 0x7f22, 0xcc => 0x7f23,
    0xcd => 0x7f24, 0xce => 0x7f25, 0xcf => 0x7f26, 0xd0 => 0x7f27,
    0xd1 => 0x7f2a, 0xd2 => 0x7f2b, 0xd3 => 0x7f2c, 0xd4 => 0x7f2d,
    0xd5 => 0x7f2f, 0xd6 => 0x7f30, 0xd7 => 0x7f31, 0xd8 => 0x7f32,
    0xd9 => 0x7f33, 0xda => 0x7f35, 0xdb => 0x5e7a, 0xdc => 0x757f,
    0xdd => 0x5ddb, 0xde => 0x753e, 0xdf => 0x9095, 0xe0 => 0x738e,
    0xe1 => 0x7391, 0xe2 => 0x73ae, 0xe3 => 0x73a2, 0xe4 => 0x739f,
    0xe5 => 0x73cf, 0xe6 => 0x73c2, 0xe7 => 0x73d1, 0xe8 => 0x73b7,
    0xe9 => 0x73b3, 0xea => 0x73c0, 0xeb => 0x73c9, 0xec => 0x73c8,
    0xed => 0x73e5, 0xee => 0x73d9, 0xef => 0x987c, 0xf0 => 0x740a,
    0xf1 => 0x73e9, 0xf2 => 0x73e7, 0xf3 => 0x73de, 0xf4 => 0x73ba,
    0xf5 => 0x73f2, 0xf6 => 0x740f, 0xf7 => 0x742a, 0xf8 => 0x745b,
    0xf9 => 0x7426, 0xfa => 0x7425, 0xfb => 0x7428, 0xfc => 0x7430,
    0xfd => 0x742e, 0xfe => 0x742c,
  },
  0xe8 => {
    0xa1 => 0x741b, 0xa2 => 0x741a, 0xa3 => 0x7441, 0xa4 => 0x745c,
    0xa5 => 0x7457, 0xa6 => 0x7455, 0xa7 => 0x7459, 0xa8 => 0x7477,
    0xa9 => 0x746d, 0xaa => 0x747e, 0xab => 0x749c, 0xac => 0x748e,
    0xad => 0x7480, 0xae => 0x7481, 0xaf => 0x7487, 0xb0 => 0x748b,
    0xb1 => 0x749e, 0xb2 => 0x74a8, 0xb3 => 0x74a9, 0xb4 => 0x7490,
    0xb5 => 0x74a7, 0xb6 => 0x74d2, 0xb7 => 0x74ba, 0xb8 => 0x97ea,
    0xb9 => 0x97eb, 0xba => 0x97ec, 0xbb => 0x674c, 0xbc => 0x6753,
    0xbd => 0x675e, 0xbe => 0x6748, 0xbf => 0x6769, 0xc0 => 0x67a5,
    0xc1 => 0x6787, 0xc2 => 0x676a, 0xc3 => 0x6773, 0xc4 => 0x6798,
    0xc5 => 0x67a7, 0xc6 => 0x6775, 0xc7 => 0x67a8, 0xc8 => 0x679e,
    0xc9 => 0x67ad, 0xca => 0x678b, 0xcb => 0x6777, 0xcc => 0x677c,
    0xcd => 0x67f0, 0xce => 0x6809, 0xcf => 0x67d8, 0xd0 => 0x680a,
    0xd1 => 0x67e9, 0xd2 => 0x67b0, 0xd3 => 0x680c, 0xd4 => 0x67d9,
    0xd5 => 0x67b5, 0xd6 => 0x67da, 0xd7 => 0x67b3, 0xd8 => 0x67dd,
    0xd9 => 0x6800, 0xda => 0x67c3, 0xdb => 0x67b8, 0xdc => 0x67e2,
    0xdd => 0x680e, 0xde => 0x67c1, 0xdf => 0x67fd, 0xe0 => 0x6832,
    0xe1 => 0x6833, 0xe2 => 0x6860, 0xe3 => 0x6861, 0xe4 => 0x684e,
    0xe5 => 0x6862, 0xe6 => 0x6844, 0xe7 => 0x6864, 0xe8 => 0x6883,
    0xe9 => 0x681d, 0xea => 0x6855, 0xeb => 0x6866, 0xec => 0x6841,
    0xed => 0x6867, 0xee => 0x6840, 0xef => 0x683e, 0xf0 => 0x684a,
    0xf1 => 0x6849, 0xf2 => 0x6829, 0xf3 => 0x68b5, 0xf4 => 0x688f,
    0xf5 => 0x6874, 0xf6 => 0x6877, 0xf7 => 0x6893, 0xf8 => 0x686b,
    0xf9 => 0x68c2, 0xfa => 0x696e, 0xfb => 0x68fc, 0xfc => 0x691f,
    0xfd => 0x6920, 0xfe => 0x68f9,
  },
  0xe9 => {
    0xa1 => 0x6924, 0xa2 => 0x68f0, 0xa3 => 0x690b, 0xa4 => 0x6901,
    0xa5 => 0x6957, 0xa6 => 0x68e3, 0xa7 => 0x6910, 0xa8 => 0x6971,
    0xa9 => 0x6939, 0xaa => 0x6960, 0xab => 0x6942, 0xac => 0x695d,
    0xad => 0x6984, 0xae => 0x696b, 0xaf => 0x6980, 0xb0 => 0x6998,
    0xb1 => 0x6978, 0xb2 => 0x6934, 0xb3 => 0x69cc, 0xb4 => 0x6987,
    0xb5 => 0x6988, 0xb6 => 0x69ce, 0xb7 => 0x6989, 0xb8 => 0x6966,
    0xb9 => 0x6963, 0xba => 0x6979, 0xbb => 0x699b, 0xbc => 0x69a7,
    0xbd => 0x69bb, 0xbe => 0x69ab, 0xbf => 0x69ad, 0xc0 => 0x69d4,
    0xc1 => 0x69b1, 0xc2 => 0x69c1, 0xc3 => 0x69ca, 0xc4 => 0x69df,
    0xc5 => 0x6995, 0xc6 => 0x69e0, 0xc7 => 0x698d, 0xc8 => 0x69ff,
    0xc9 => 0x6a2f, 0xca => 0x69ed, 0xcb => 0x6a17, 0xcc => 0x6a18,
    0xcd => 0x6a65, 0xce => 0x69f2, 0xcf => 0x6a44, 0xd0 => 0x6a3e,
    0xd1 => 0x6aa0, 0xd2 => 0x6a50, 0xd3 => 0x6a5b, 0xd4 => 0x6a35,
    0xd5 => 0x6a8e, 0xd6 => 0x6a79, 0xd7 => 0x6a3d, 0xd8 => 0x6a28,
    0xd9 => 0x6a58, 0xda => 0x6a7c, 0xdb => 0x6a91, 0xdc => 0x6a90,
    0xdd => 0x6aa9, 0xde => 0x6a97, 0xdf => 0x6aab, 0xe0 => 0x7337,
    0xe1 => 0x7352, 0xe2 => 0x6b81, 0xe3 => 0x6b82, 0xe4 => 0x6b87,
    0xe5 => 0x6b84, 0xe6 => 0x6b92, 0xe7 => 0x6b93, 0xe8 => 0x6b8d,
    0xe9 => 0x6b9a, 0xea => 0x6b9b, 0xeb => 0x6ba1, 0xec => 0x6baa,
    0xed => 0x8f6b, 0xee => 0x8f6d, 0xef => 0x8f71, 0xf0 => 0x8f72,
    0xf1 => 0x8f73, 0xf2 => 0x8f75, 0xf3 => 0x8f76, 0xf4 => 0x8f78,
    0xf5 => 0x8f77, 0xf6 => 0x8f79, 0xf7 => 0x8f7a, 0xf8 => 0x8f7c,
    0xf9 => 0x8f7e, 0xfa => 0x8f81, 0xfb => 0x8f82, 0xfc => 0x8f84,
    0xfd => 0x8f87, 0xfe => 0x8f8b,
  },
  0xea => {
    0xa1 => 0x8f8d, 0xa2 => 0x8f8e, 0xa3 => 0x8f8f, 0xa4 => 0x8f98,
    0xa5 => 0x8f9a, 0xa6 => 0x8ece, 0xa7 => 0x620b, 0xa8 => 0x6217,
    0xa9 => 0x621b, 0xaa => 0x621f, 0xab => 0x6222, 0xac => 0x6221,
    0xad => 0x6225, 0xae => 0x6224, 0xaf => 0x622c, 0xb0 => 0x81e7,
    0xb1 => 0x74ef, 0xb2 => 0x74f4, 0xb3 => 0x74ff, 0xb4 => 0x750f,
    0xb5 => 0x7511, 0xb6 => 0x7513, 0xb7 => 0x6534, 0xb8 => 0x65ee,
    0xb9 => 0x65ef, 0xba => 0x65f0, 0xbb => 0x660a, 0xbc => 0x6619,
    0xbd => 0x6772, 0xbe => 0x6603, 0xbf => 0x6615, 0xc0 => 0x6600,
    0xc1 => 0x7085, 0xc2 => 0x66f7, 0xc3 => 0x661d, 0xc4 => 0x6634,
    0xc5 => 0x6631, 0xc6 => 0x6636, 0xc7 => 0x6635, 0xc8 => 0x8006,
    0xc9 => 0x665f, 0xca => 0x6654, 0xcb => 0x6641, 0xcc => 0x664f,
    0xcd => 0x6656, 0xce => 0x6661, 0xcf => 0x6657, 0xd0 => 0x6677,
    0xd1 => 0x6684, 0xd2 => 0x668c, 0xd3 => 0x66a7, 0xd4 => 0x669d,
    0xd5 => 0x66be, 0xd6 => 0x66db, 0xd7 => 0x66dc, 0xd8 => 0x66e6,
    0xd9 => 0x66e9, 0xda => 0x8d32, 0xdb => 0x8d33, 0xdc => 0x8d36,
    0xdd => 0x8d3b, 0xde => 0x8d3d, 0xdf => 0x8d40, 0xe0 => 0x8d45,
    0xe1 => 0x8d46, 0xe2 => 0x8d48, 0xe3 => 0x8d49, 0xe4 => 0x8d47,
    0xe5 => 0x8d4d, 0xe6 => 0x8d55, 0xe7 => 0x8d59, 0xe8 => 0x89c7,
    0xe9 => 0x89ca, 0xea => 0x89cb, 0xeb => 0x89cc, 0xec => 0x89ce,
    0xed => 0x89cf, 0xee => 0x89d0, 0xef => 0x89d1, 0xf0 => 0x726e,
    0xf1 => 0x729f, 0xf2 => 0x725d, 0xf3 => 0x7266, 0xf4 => 0x726f,
    0xf5 => 0x727e, 0xf6 => 0x727f, 0xf7 => 0x7284, 0xf8 => 0x728b,
    0xf9 => 0x728d, 0xfa => 0x728f, 0xfb => 0x7292, 0xfc => 0x6308,
    0xfd => 0x6332, 0xfe => 0x63b0,
  },
  0xeb => {
    0xa1 => 0x643f, 0xa2 => 0x64d8, 0xa3 => 0x8004, 0xa4 => 0x6bea,
    0xa5 => 0x6bf3, 0xa6 => 0x6bfd, 0xa7 => 0x6bf5, 0xa8 => 0x6bf9,
    0xa9 => 0x6c05, 0xaa => 0x6c07, 0xab => 0x6c06, 0xac => 0x6c0d,
    0xad => 0x6c15, 0xae => 0x6c18, 0xaf => 0x6c19, 0xb0 => 0x6c1a,
    0xb1 => 0x6c21, 0xb2 => 0x6c29, 0xb3 => 0x6c24, 0xb4 => 0x6c2a,
    0xb5 => 0x6c32, 0xb6 => 0x6535, 0xb7 => 0x6555, 0xb8 => 0x656b,
    0xb9 => 0x724d, 0xba => 0x7252, 0xbb => 0x7256, 0xbc => 0x7230,
    0xbd => 0x8662, 0xbe => 0x5216, 0xbf => 0x809f, 0xc0 => 0x809c,
    0xc1 => 0x8093, 0xc2 => 0x80bc, 0xc3 => 0x670a, 0xc4 => 0x80bd,
    0xc5 => 0x80b1, 0xc6 => 0x80ab, 0xc7 => 0x80ad, 0xc8 => 0x80b4,
    0xc9 => 0x80b7, 0xca => 0x80e7, 0xcb => 0x80e8, 0xcc => 0x80e9,
    0xcd => 0x80ea, 0xce => 0x80db, 0xcf => 0x80c2, 0xd0 => 0x80c4,
    0xd1 => 0x80d9, 0xd2 => 0x80cd, 0xd3 => 0x80d7, 0xd4 => 0x6710,
    0xd5 => 0x80dd, 0xd6 => 0x80eb, 0xd7 => 0x80f1, 0xd8 => 0x80f4,
    0xd9 => 0x80ed, 0xda => 0x810d, 0xdb => 0x810e, 0xdc => 0x80f2,
    0xdd => 0x80fc, 0xde => 0x6715, 0xdf => 0x8112, 0xe0 => 0x8c5a,
    0xe1 => 0x8136, 0xe2 => 0x811e, 0xe3 => 0x812c, 0xe4 => 0x8118,
    0xe5 => 0x8132, 0xe6 => 0x8148, 0xe7 => 0x814c, 0xe8 => 0x8153,
    0xe9 => 0x8174, 0xea => 0x8159, 0xeb => 0x815a, 0xec => 0x8171,
    0xed => 0x8160, 0xee => 0x8169, 0xef => 0x817c, 0xf0 => 0x817d,
    0xf1 => 0x816d, 0xf2 => 0x8167, 0xf3 => 0x584d, 0xf4 => 0x5ab5,
    0xf5 => 0x8188, 0xf6 => 0x8182, 0xf7 => 0x8191, 0xf8 => 0x6ed5,
    0xf9 => 0x81a3, 0xfa => 0x81aa, 0xfb => 0x81cc, 0xfc => 0x6726,
    0xfd => 0x81ca, 0xfe => 0x81bb,
  },
  0xec => {
    0xa1 => 0x81c1, 0xa2 => 0x81a6, 0xa3 => 0x6b24, 0xa4 => 0x6b37,
    0xa5 => 0x6b39, 0xa6 => 0x6b43, 0xa7 => 0x6b46, 0xa8 => 0x6b59,
    0xa9 => 0x98d1, 0xaa => 0x98d2, 0xab => 0x98d3, 0xac => 0x98d5,
    0xad => 0x98d9, 0xae => 0x98da, 0xaf => 0x6bb3, 0xb0 => 0x5f40,
    0xb1 => 0x6bc2, 0xb2 => 0x89f3, 0xb3 => 0x6590, 0xb4 => 0x9f51,
    0xb5 => 0x6593, 0xb6 => 0x65bc, 0xb7 => 0x65c6, 0xb8 => 0x65c4,
    0xb9 => 0x65c3, 0xba => 0x65cc, 0xbb => 0x65ce, 0xbc => 0x65d2,
    0xbd => 0x65d6, 0xbe => 0x7080, 0xbf => 0x709c, 0xc0 => 0x7096,
    0xc1 => 0x709d, 0xc2 => 0x70bb, 0xc3 => 0x70c0, 0xc4 => 0x70b7,
    0xc5 => 0x70ab, 0xc6 => 0x70b1, 0xc7 => 0x70e8, 0xc8 => 0x70ca,
    0xc9 => 0x7110, 0xca => 0x7113, 0xcb => 0x7116, 0xcc => 0x712f,
    0xcd => 0x7131, 0xce => 0x7173, 0xcf => 0x715c, 0xd0 => 0x7168,
    0xd1 => 0x7145, 0xd2 => 0x7172, 0xd3 => 0x714a, 0xd4 => 0x7178,
    0xd5 => 0x717a, 0xd6 => 0x7198, 0xd7 => 0x71b3, 0xd8 => 0x71b5,
    0xd9 => 0x71a8, 0xda => 0x71a0, 0xdb => 0x71e0, 0xdc => 0x71d4,
    0xdd => 0x71e7, 0xde => 0x71f9, 0xdf => 0x721d, 0xe0 => 0x7228,
    0xe1 => 0x706c, 0xe2 => 0x7118, 0xe3 => 0x7166, 0xe4 => 0x71b9,
    0xe5 => 0x623e, 0xe6 => 0x623d, 0xe7 => 0x6243, 0xe8 => 0x6248,
    0xe9 => 0x6249, 0xea => 0x793b, 0xeb => 0x7940, 0xec => 0x7946,
    0xed => 0x7949, 0xee => 0x795b, 0xef => 0x795c, 0xf0 => 0x7953,
    0xf1 => 0x795a, 0xf2 => 0x7962, 0xf3 => 0x7957, 0xf4 => 0x7960,
    0xf5 => 0x796f, 0xf6 => 0x7967, 0xf7 => 0x797a, 0xf8 => 0x7985,
    0xf9 => 0x798a, 0xfa => 0x799a, 0xfb => 0x79a7, 0xfc => 0x79b3,
    0xfd => 0x5fd1, 0xfe => 0x5fd0,
  },
  0xed => {
    0xa1 => 0x603c, 0xa2 => 0x605d, 0xa3 => 0x605a, 0xa4 => 0x6067,
    0xa5 => 0x6041, 0xa6 => 0x6059, 0xa7 => 0x6063, 0xa8 => 0x60ab,
    0xa9 => 0x6106, 0xaa => 0x610d, 0xab => 0x615d, 0xac => 0x61a9,
    0xad => 0x619d, 0xae => 0x61cb, 0xaf => 0x61d1, 0xb0 => 0x6206,
    0xb1 => 0x8080, 0xb2 => 0x807f, 0xb3 => 0x6c93, 0xb4 => 0x6cf6,
    0xb5 => 0x6dfc, 0xb6 => 0x77f6, 0xb7 => 0x77f8, 0xb8 => 0x7800,
    0xb9 => 0x7809, 0xba => 0x7817, 0xbb => 0x7818, 0xbc => 0x7811,
    0xbd => 0x65ab, 0xbe => 0x782d, 0xbf => 0x781c, 0xc0 => 0x781d,
    0xc1 => 0x7839, 0xc2 => 0x783a, 0xc3 => 0x783b, 0xc4 => 0x781f,
    0xc5 => 0x783c, 0xc6 => 0x7825, 0xc7 => 0x782c, 0xc8 => 0x7823,
    0xc9 => 0x7829, 0xca => 0x784e, 0xcb => 0x786d, 0xcc => 0x7856,
    0xcd => 0x7857, 0xce => 0x7826, 0xcf => 0x7850, 0xd0 => 0x7847,
    0xd1 => 0x784c, 0xd2 => 0x786a, 0xd3 => 0x789b, 0xd4 => 0x7893,
    0xd5 => 0x789a, 0xd6 => 0x7887, 0xd7 => 0x789c, 0xd8 => 0x78a1,
    0xd9 => 0x78a3, 0xda => 0x78b2, 0xdb => 0x78b9, 0xdc => 0x78a5,
    0xdd => 0x78d4, 0xde => 0x78d9, 0xdf => 0x78c9, 0xe0 => 0x78ec,
    0xe1 => 0x78f2, 0xe2 => 0x7905, 0xe3 => 0x78f4, 0xe4 => 0x7913,
    0xe5 => 0x7924, 0xe6 => 0x791e, 0xe7 => 0x7934, 0xe8 => 0x9f9b,
    0xe9 => 0x9ef9, 0xea => 0x9efb, 0xeb => 0x9efc, 0xec => 0x76f1,
    0xed => 0x7704, 0xee => 0x770d, 0xef => 0x76f9, 0xf0 => 0x7707,
    0xf1 => 0x7708, 0xf2 => 0x771a, 0xf3 => 0x7722, 0xf4 => 0x7719,
    0xf5 => 0x772d, 0xf6 => 0x7726, 0xf7 => 0x7735, 0xf8 => 0x7738,
    0xf9 => 0x7750, 0xfa => 0x7751, 0xfb => 0x7747, 0xfc => 0x7743,
    0xfd => 0x775a, 0xfe => 0x7768,
  },
  0xee => {
    0xa1 => 0x7762, 0xa2 => 0x7765, 0xa3 => 0x777f, 0xa4 => 0x778d,
    0xa5 => 0x777d, 0xa6 => 0x7780, 0xa7 => 0x778c, 0xa8 => 0x7791,
    0xa9 => 0x779f, 0xaa => 0x77a0, 0xab => 0x77b0, 0xac => 0x77b5,
    0xad => 0x77bd, 0xae => 0x753a, 0xaf => 0x7540, 0xb0 => 0x754e,
    0xb1 => 0x754b, 0xb2 => 0x7548, 0xb3 => 0x755b, 0xb4 => 0x7572,
    0xb5 => 0x7579, 0xb6 => 0x7583, 0xb7 => 0x7f58, 0xb8 => 0x7f61,
    0xb9 => 0x7f5f, 0xba => 0x8a48, 0xbb => 0x7f68, 0xbc => 0x7f74,
    0xbd => 0x7f71, 0xbe => 0x7f79, 0xbf => 0x7f81, 0xc0 => 0x7f7e,
    0xc1 => 0x76cd, 0xc2 => 0x76e5, 0xc3 => 0x8832, 0xc4 => 0x9485,
    0xc5 => 0x9486, 0xc6 => 0x9487, 0xc7 => 0x948b, 0xc8 => 0x948a,
    0xc9 => 0x948c, 0xca => 0x948d, 0xcb => 0x948f, 0xcc => 0x9490,
    0xcd => 0x9494, 0xce => 0x9497, 0xcf => 0x9495, 0xd0 => 0x949a,
    0xd1 => 0x949b, 0xd2 => 0x949c, 0xd3 => 0x94a3, 0xd4 => 0x94a4,
    0xd5 => 0x94ab, 0xd6 => 0x94aa, 0xd7 => 0x94ad, 0xd8 => 0x94ac,
    0xd9 => 0x94af, 0xda => 0x94b0, 0xdb => 0x94b2, 0xdc => 0x94b4,
    0xdd => 0x94b6, 0xde => 0x94b7, 0xdf => 0x94b8, 0xe0 => 0x94b9,
    0xe1 => 0x94ba, 0xe2 => 0x94bc, 0xe3 => 0x94bd, 0xe4 => 0x94bf,
    0xe5 => 0x94c4, 0xe6 => 0x94c8, 0xe7 => 0x94c9, 0xe8 => 0x94ca,
    0xe9 => 0x94cb, 0xea => 0x94cc, 0xeb => 0x94cd, 0xec => 0x94ce,
    0xed => 0x94d0, 0xee => 0x94d1, 0xef => 0x94d2, 0xf0 => 0x94d5,
    0xf1 => 0x94d6, 0xf2 => 0x94d7, 0xf3 => 0x94d9, 0xf4 => 0x94d8,
    0xf5 => 0x94db, 0xf6 => 0x94de, 0xf7 => 0x94df, 0xf8 => 0x94e0,
    0xf9 => 0x94e2, 0xfa => 0x94e4, 0xfb => 0x94e5, 0xfc => 0x94e7,
    0xfd => 0x94e8, 0xfe => 0x94ea,
  },
  0xef => {
    0xa1 => 0x94e9, 0xa2 => 0x94eb, 0xa3 => 0x94ee, 0xa4 => 0x94ef,
    0xa5 => 0x94f3, 0xa6 => 0x94f4, 0xa7 => 0x94f5, 0xa8 => 0x94f7,
    0xa9 => 0x94f9, 0xaa => 0x94fc, 0xab => 0x94fd, 0xac => 0x94ff,
    0xad => 0x9503, 0xae => 0x9502, 0xaf => 0x9506, 0xb0 => 0x9507,
    0xb1 => 0x9509, 0xb2 => 0x950a, 0xb3 => 0x950d, 0xb4 => 0x950e,
    0xb5 => 0x950f, 0xb6 => 0x9512, 0xb7 => 0x9513, 0xb8 => 0x9514,
    0xb9 => 0x9515, 0xba => 0x9516, 0xbb => 0x9518, 0xbc => 0x951b,
    0xbd => 0x951d, 0xbe => 0x951e, 0xbf => 0x951f, 0xc0 => 0x9522,
    0xc1 => 0x952a, 0xc2 => 0x952b, 0xc3 => 0x9529, 0xc4 => 0x952c,
    0xc5 => 0x9531, 0xc6 => 0x9532, 0xc7 => 0x9534, 0xc8 => 0x9536,
    0xc9 => 0x9537, 0xca => 0x9538, 0xcb => 0x953c, 0xcc => 0x953e,
    0xcd => 0x953f, 0xce => 0x9542, 0xcf => 0x9535, 0xd0 => 0x9544,
    0xd1 => 0x9545, 0xd2 => 0x9546, 0xd3 => 0x9549, 0xd4 => 0x954c,
    0xd5 => 0x954e, 0xd6 => 0x954f, 0xd7 => 0x9552, 0xd8 => 0x9553,
    0xd9 => 0x9554, 0xda => 0x9556, 0xdb => 0x9557, 0xdc => 0x9558,
    0xdd => 0x9559, 0xde => 0x955b, 0xdf => 0x955e, 0xe0 => 0x955f,
    0xe1 => 0x955d, 0xe2 => 0x9561, 0xe3 => 0x9562, 0xe4 => 0x9564,
    0xe5 => 0x9565, 0xe6 => 0x9566, 0xe7 => 0x9567, 0xe8 => 0x9568,
    0xe9 => 0x9569, 0xea => 0x956a, 0xeb => 0x956b, 0xec => 0x956c,
    0xed => 0x956f, 0xee => 0x9571, 0xef => 0x9572, 0xf0 => 0x9573,
    0xf1 => 0x953a, 0xf2 => 0x77e7, 0xf3 => 0x77ec, 0xf4 => 0x96c9,
    0xf5 => 0x79d5, 0xf6 => 0x79ed, 0xf7 => 0x79e3, 0xf8 => 0x79eb,
    0xf9 => 0x7a06, 0xfa => 0x5d47, 0xfb => 0x7a03, 0xfc => 0x7a02,
    0xfd => 0x7a1e, 0xfe => 0x7a14,
  },
  0xf0 => {
    0xa1 => 0x7a39, 0xa2 => 0x7a37, 0xa3 => 0x7a51, 0xa4 => 0x9ecf,
    0xa5 => 0x99a5, 0xa6 => 0x7a70, 0xa7 => 0x7688, 0xa8 => 0x768e,
    0xa9 => 0x7693, 0xaa => 0x7699, 0xab => 0x76a4, 0xac => 0x74de,
    0xad => 0x74e0, 0xae => 0x752c, 0xaf => 0x9e20, 0xb0 => 0x9e22,
    0xb1 => 0x9e28, 0xb2 => 0x9e29, 0xb3 => 0x9e2a, 0xb4 => 0x9e2b,
    0xb5 => 0x9e2c, 0xb6 => 0x9e32, 0xb7 => 0x9e31, 0xb8 => 0x9e36,
    0xb9 => 0x9e38, 0xba => 0x9e37, 0xbb => 0x9e39, 0xbc => 0x9e3a,
    0xbd => 0x9e3e, 0xbe => 0x9e41, 0xbf => 0x9e42, 0xc0 => 0x9e44,
    0xc1 => 0x9e46, 0xc2 => 0x9e47, 0xc3 => 0x9e48, 0xc4 => 0x9e49,
    0xc5 => 0x9e4b, 0xc6 => 0x9e4c, 0xc7 => 0x9e4e, 0xc8 => 0x9e51,
    0xc9 => 0x9e55, 0xca => 0x9e57, 0xcb => 0x9e5a, 0xcc => 0x9e5b,
    0xcd => 0x9e5c, 0xce => 0x9e5e, 0xcf => 0x9e63, 0xd0 => 0x9e66,
    0xd1 => 0x9e67, 0xd2 => 0x9e68, 0xd3 => 0x9e69, 0xd4 => 0x9e6a,
    0xd5 => 0x9e6b, 0xd6 => 0x9e6c, 0xd7 => 0x9e71, 0xd8 => 0x9e6d,
    0xd9 => 0x9e73, 0xda => 0x7592, 0xdb => 0x7594, 0xdc => 0x7596,
    0xdd => 0x75a0, 0xde => 0x759d, 0xdf => 0x75ac, 0xe0 => 0x75a3,
    0xe1 => 0x75b3, 0xe2 => 0x75b4, 0xe3 => 0x75b8, 0xe4 => 0x75c4,
    0xe5 => 0x75b1, 0xe6 => 0x75b0, 0xe7 => 0x75c3, 0xe8 => 0x75c2,
    0xe9 => 0x75d6, 0xea => 0x75cd, 0xeb => 0x75e3, 0xec => 0x75e8,
    0xed => 0x75e6, 0xee => 0x75e4, 0xef => 0x75eb, 0xf0 => 0x75e7,
    0xf1 => 0x7603, 0xf2 => 0x75f1, 0xf3 => 0x75fc, 0xf4 => 0x75ff,
    0xf5 => 0x7610, 0xf6 => 0x7600, 0xf7 => 0x7605, 0xf8 => 0x760c,
    0xf9 => 0x7617, 0xfa => 0x760a, 0xfb => 0x7625, 0xfc => 0x7618,
    0xfd => 0x7615, 0xfe => 0x7619,
  },
  0xf1 => {
    0xa1 => 0x761b, 0xa2 => 0x763c, 0xa3 => 0x7622, 0xa4 => 0x7620,
    0xa5 => 0x7640, 0xa6 => 0x762d, 0xa7 => 0x7630, 0xa8 => 0x763f,
    0xa9 => 0x7635, 0xaa => 0x7643, 0xab => 0x763e, 0xac => 0x7633,
    0xad => 0x764d, 0xae => 0x765e, 0xaf => 0x7654, 0xb0 => 0x765c,
    0xb1 => 0x7656, 0xb2 => 0x766b, 0xb3 => 0x766f, 0xb4 => 0x7fca,
    0xb5 => 0x7ae6, 0xb6 => 0x7a78, 0xb7 => 0x7a79, 0xb8 => 0x7a80,
    0xb9 => 0x7a86, 0xba => 0x7a88, 0xbb => 0x7a95, 0xbc => 0x7aa6,
    0xbd => 0x7aa0, 0xbe => 0x7aac, 0xbf => 0x7aa8, 0xc0 => 0x7aad,
    0xc1 => 0x7ab3, 0xc2 => 0x8864, 0xc3 => 0x8869, 0xc4 => 0x8872,
    0xc5 => 0x887d, 0xc6 => 0x887f, 0xc7 => 0x8882, 0xc8 => 0x88a2,
    0xc9 => 0x88c6, 0xca => 0x88b7, 0xcb => 0x88bc, 0xcc => 0x88c9,
    0xcd => 0x88e2, 0xce => 0x88ce, 0xcf => 0x88e3, 0xd0 => 0x88e5,
    0xd1 => 0x88f1, 0xd2 => 0x891a, 0xd3 => 0x88fc, 0xd4 => 0x88e8,
    0xd5 => 0x88fe, 0xd6 => 0x88f0, 0xd7 => 0x8921, 0xd8 => 0x8919,
    0xd9 => 0x8913, 0xda => 0x891b, 0xdb => 0x890a, 0xdc => 0x8934,
    0xdd => 0x892b, 0xde => 0x8936, 0xdf => 0x8941, 0xe0 => 0x8966,
    0xe1 => 0x897b, 0xe2 => 0x758b, 0xe3 => 0x80e5, 0xe4 => 0x76b2,
    0xe5 => 0x76b4, 0xe6 => 0x77dc, 0xe7 => 0x8012, 0xe8 => 0x8014,
    0xe9 => 0x8016, 0xea => 0x801c, 0xeb => 0x8020, 0xec => 0x8022,
    0xed => 0x8025, 0xee => 0x8026, 0xef => 0x8027, 0xf0 => 0x8029,
    0xf1 => 0x8028, 0xf2 => 0x8031, 0xf3 => 0x800b, 0xf4 => 0x8035,
    0xf5 => 0x8043, 0xf6 => 0x8046, 0xf7 => 0x804d, 0xf8 => 0x8052,
    0xf9 => 0x8069, 0xfa => 0x8071, 0xfb => 0x8983, 0xfc => 0x9878,
    0xfd => 0x9880, 0xfe => 0x9883,
  },
  0xf2 => {
    0xa1 => 0x9889, 0xa2 => 0x988c, 0xa3 => 0x988d, 0xa4 => 0x988f,
    0xa5 => 0x9894, 0xa6 => 0x989a, 0xa7 => 0x989b, 0xa8 => 0x989e,
    0xa9 => 0x989f, 0xaa => 0x98a1, 0xab => 0x98a2, 0xac => 0x98a5,
    0xad => 0x98a6, 0xae => 0x864d, 0xaf => 0x8654, 0xb0 => 0x866c,
    0xb1 => 0x866e, 0xb2 => 0x867f, 0xb3 => 0x867a, 0xb4 => 0x867c,
    0xb5 => 0x867b, 0xb6 => 0x86a8, 0xb7 => 0x868d, 0xb8 => 0x868b,
    0xb9 => 0x86ac, 0xba => 0x869d, 0xbb => 0x86a7, 0xbc => 0x86a3,
    0xbd => 0x86aa, 0xbe => 0x8693, 0xbf => 0x86a9, 0xc0 => 0x86b6,
    0xc1 => 0x86c4, 0xc2 => 0x86b5, 0xc3 => 0x86ce, 0xc4 => 0x86b0,
    0xc5 => 0x86ba, 0xc6 => 0x86b1, 0xc7 => 0x86af, 0xc8 => 0x86c9,
    0xc9 => 0x86cf, 0xca => 0x86b4, 0xcb => 0x86e9, 0xcc => 0x86f1,
    0xcd => 0x86f2, 0xce => 0x86ed, 0xcf => 0x86f3, 0xd0 => 0x86d0,
    0xd1 => 0x8713, 0xd2 => 0x86de, 0xd3 => 0x86f4, 0xd4 => 0x86df,
    0xd5 => 0x86d8, 0xd6 => 0x86d1, 0xd7 => 0x8703, 0xd8 => 0x8707,
    0xd9 => 0x86f8, 0xda => 0x8708, 0xdb => 0x870a, 0xdc => 0x870d,
    0xdd => 0x8709, 0xde => 0x8723, 0xdf => 0x873b, 0xe0 => 0x871e,
    0xe1 => 0x8725, 0xe2 => 0x872e, 0xe3 => 0x871a, 0xe4 => 0x873e,
    0xe5 => 0x8748, 0xe6 => 0x8734, 0xe7 => 0x8731, 0xe8 => 0x8729,
    0xe9 => 0x8737, 0xea => 0x873f, 0xeb => 0x8782, 0xec => 0x8722,
    0xed => 0x877d, 0xee => 0x877e, 0xef => 0x877b, 0xf0 => 0x8760,
    0xf1 => 0x8770, 0xf2 => 0x874c, 0xf3 => 0x876e, 0xf4 => 0x878b,
    0xf5 => 0x8753, 0xf6 => 0x8763, 0xf7 => 0x877c, 0xf8 => 0x8764,
    0xf9 => 0x8759, 0xfa => 0x8765, 0xfb => 0x8793, 0xfc => 0x87af,
    0xfd => 0x87a8, 0xfe => 0x87d2,
  },
  0xf3 => {
    0xa1 => 0x87c6, 0xa2 => 0x8788, 0xa3 => 0x8785, 0xa4 => 0x87ad,
    0xa5 => 0x8797, 0xa6 => 0x8783, 0xa7 => 0x87ab, 0xa8 => 0x87e5,
    0xa9 => 0x87ac, 0xaa => 0x87b5, 0xab => 0x87b3, 0xac => 0x87cb,
    0xad => 0x87d3, 0xae => 0x87bd, 0xaf => 0x87d1, 0xb0 => 0x87c0,
    0xb1 => 0x87ca, 0xb2 => 0x87db, 0xb3 => 0x87ea, 0xb4 => 0x87e0,
    0xb5 => 0x87ee, 0xb6 => 0x8816, 0xb7 => 0x8813, 0xb8 => 0x87fe,
    0xb9 => 0x880a, 0xba => 0x881b, 0xbb => 0x8821, 0xbc => 0x8839,
    0xbd => 0x883c, 0xbe => 0x7f36, 0xbf => 0x7f42, 0xc0 => 0x7f44,
    0xc1 => 0x7f45, 0xc2 => 0x8210, 0xc3 => 0x7afa, 0xc4 => 0x7afd,
    0xc5 => 0x7b08, 0xc6 => 0x7b03, 0xc7 => 0x7b04, 0xc8 => 0x7b15,
    0xc9 => 0x7b0a, 0xca => 0x7b2b, 0xcb => 0x7b0f, 0xcc => 0x7b47,
    0xcd => 0x7b38, 0xce => 0x7b2a, 0xcf => 0x7b19, 0xd0 => 0x7b2e,
    0xd1 => 0x7b31, 0xd2 => 0x7b20, 0xd3 => 0x7b25, 0xd4 => 0x7b24,
    0xd5 => 0x7b33, 0xd6 => 0x7b3e, 0xd7 => 0x7b1e, 0xd8 => 0x7b58,
    0xd9 => 0x7b5a, 0xda => 0x7b45, 0xdb => 0x7b75, 0xdc => 0x7b4c,
    0xdd => 0x7b5d, 0xde => 0x7b60, 0xdf => 0x7b6e, 0xe0 => 0x7b7b,
    0xe1 => 0x7b62, 0xe2 => 0x7b72, 0xe3 => 0x7b71, 0xe4 => 0x7b90,
    0xe5 => 0x7ba6, 0xe6 => 0x7ba7, 0xe7 => 0x7bb8, 0xe8 => 0x7bac,
    0xe9 => 0x7b9d, 0xea => 0x7ba8, 0xeb => 0x7b85, 0xec => 0x7baa,
    0xed => 0x7b9c, 0xee => 0x7ba2, 0xef => 0x7bab, 0xf0 => 0x7bb4,
    0xf1 => 0x7bd1, 0xf2 => 0x7bc1, 0xf3 => 0x7bcc, 0xf4 => 0x7bdd,
    0xf5 => 0x7bda, 0xf6 => 0x7be5, 0xf7 => 0x7be6, 0xf8 => 0x7bea,
    0xf9 => 0x7c0c, 0xfa => 0x7bfe, 0xfb => 0x7bfc, 0xfc => 0x7c0f,
    0xfd => 0x7c16, 0xfe => 0x7c0b,
  },
  0xf4 => {
    0xa1 => 0x7c1f, 0xa2 => 0x7c2a, 0xa3 => 0x7c26, 0xa4 => 0x7c38,
    0xa5 => 0x7c41, 0xa6 => 0x7c40, 0xa7 => 0x81fe, 0xa8 => 0x8201,
    0xa9 => 0x8202, 0xaa => 0x8204, 0xab => 0x81ec, 0xac => 0x8844,
    0xad => 0x8221, 0xae => 0x8222, 0xaf => 0x8223, 0xb0 => 0x822d,
    0xb1 => 0x822f, 0xb2 => 0x8228, 0xb3 => 0x822b, 0xb4 => 0x8238,
    0xb5 => 0x823b, 0xb6 => 0x8233, 0xb7 => 0x8234, 0xb8 => 0x823e,
    0xb9 => 0x8244, 0xba => 0x8249, 0xbb => 0x824b, 0xbc => 0x824f,
    0xbd => 0x825a, 0xbe => 0x825f, 0xbf => 0x8268, 0xc0 => 0x887e,
    0xc1 => 0x8885, 0xc2 => 0x8888, 0xc3 => 0x88d8, 0xc4 => 0x88df,
    0xc5 => 0x895e, 0xc6 => 0x7f9d, 0xc7 => 0x7f9f, 0xc8 => 0x7fa7,
    0xc9 => 0x7faf, 0xca => 0x7fb0, 0xcb => 0x7fb2, 0xcc => 0x7c7c,
    0xcd => 0x6549, 0xce => 0x7c91, 0xcf => 0x7c9d, 0xd0 => 0x7c9c,
    0xd1 => 0x7c9e, 0xd2 => 0x7ca2, 0xd3 => 0x7cb2, 0xd4 => 0x7cbc,
    0xd5 => 0x7cbd, 0xd6 => 0x7cc1, 0xd7 => 0x7cc7, 0xd8 => 0x7ccc,
    0xd9 => 0x7ccd, 0xda => 0x7cc8, 0xdb => 0x7cc5, 0xdc => 0x7cd7,
    0xdd => 0x7ce8, 0xde => 0x826e, 0xdf => 0x66a8, 0xe0 => 0x7fbf,
    0xe1 => 0x7fce, 0xe2 => 0x7fd5, 0xe3 => 0x7fe5, 0xe4 => 0x7fe1,
    0xe5 => 0x7fe6, 0xe6 => 0x7fe9, 0xe7 => 0x7fee, 0xe8 => 0x7ff3,
    0xe9 => 0x7cf8, 0xea => 0x7d77, 0xeb => 0x7da6, 0xec => 0x7dae,
    0xed => 0x7e47, 0xee => 0x7e9b, 0xef => 0x9eb8, 0xf0 => 0x9eb4,
    0xf1 => 0x8d73, 0xf2 => 0x8d84, 0xf3 => 0x8d94, 0xf4 => 0x8d91,
    0xf5 => 0x8db1, 0xf6 => 0x8d67, 0xf7 => 0x8d6d, 0xf8 => 0x8c47,
    0xf9 => 0x8c49, 0xfa => 0x914a, 0xfb => 0x9150, 0xfc => 0x914e,
    0xfd => 0x914f, 0xfe => 0x9164,
  },
  0xf5 => {
    0xa1 => 0x9162, 0xa2 => 0x9161, 0xa3 => 0x9170, 0xa4 => 0x9169,
    0xa5 => 0x916f, 0xa6 => 0x917d, 0xa7 => 0x917e, 0xa8 => 0x9172,
    0xa9 => 0x9174, 0xaa => 0x9179, 0xab => 0x918c, 0xac => 0x9185,
    0xad => 0x9190, 0xae => 0x918d, 0xaf => 0x9191, 0xb0 => 0x91a2,
    0xb1 => 0x91a3, 0xb2 => 0x91aa, 0xb3 => 0x91ad, 0xb4 => 0x91ae,
    0xb5 => 0x91af, 0xb6 => 0x91b5, 0xb7 => 0x91b4, 0xb8 => 0x91ba,
    0xb9 => 0x8c55, 0xba => 0x9e7e, 0xbb => 0x8db8, 0xbc => 0x8deb,
    0xbd => 0x8e05, 0xbe => 0x8e59, 0xbf => 0x8e69, 0xc0 => 0x8db5,
    0xc1 => 0x8dbf, 0xc2 => 0x8dbc, 0xc3 => 0x8dba, 0xc4 => 0x8dc4,
    0xc5 => 0x8dd6, 0xc6 => 0x8dd7, 0xc7 => 0x8dda, 0xc8 => 0x8dde,
    0xc9 => 0x8dce, 0xca => 0x8dcf, 0xcb => 0x8ddb, 0xcc => 0x8dc6,
    0xcd => 0x8dec, 0xce => 0x8df7, 0xcf => 0x8df8, 0xd0 => 0x8de3,
    0xd1 => 0x8df9, 0xd2 => 0x8dfb, 0xd3 => 0x8de4, 0xd4 => 0x8e09,
    0xd5 => 0x8dfd, 0xd6 => 0x8e14, 0xd7 => 0x8e1d, 0xd8 => 0x8e1f,
    0xd9 => 0x8e2c, 0xda => 0x8e2e, 0xdb => 0x8e23, 0xdc => 0x8e2f,
    0xdd => 0x8e3a, 0xde => 0x8e40, 0xdf => 0x8e39, 0xe0 => 0x8e35,
    0xe1 => 0x8e3d, 0xe2 => 0x8e31, 0xe3 => 0x8e49, 0xe4 => 0x8e41,
    0xe5 => 0x8e42, 0xe6 => 0x8e51, 0xe7 => 0x8e52, 0xe8 => 0x8e4a,
    0xe9 => 0x8e70, 0xea => 0x8e76, 0xeb => 0x8e7c, 0xec => 0x8e6f,
    0xed => 0x8e74, 0xee => 0x8e85, 0xef => 0x8e8f, 0xf0 => 0x8e94,
    0xf1 => 0x8e90, 0xf2 => 0x8e9c, 0xf3 => 0x8e9e, 0xf4 => 0x8c78,
    0xf5 => 0x8c82, 0xf6 => 0x8c8a, 0xf7 => 0x8c85, 0xf8 => 0x8c98,
    0xf9 => 0x8c94, 0xfa => 0x659b, 0xfb => 0x89d6, 0xfc => 0x89de,
    0xfd => 0x89da, 0xfe => 0x89dc,
  },
  0xf6 => {
    0xa1 => 0x89e5, 0xa2 => 0x89eb, 0xa3 => 0x89ef, 0xa4 => 0x8a3e,
    0xa5 => 0x8b26, 0xa6 => 0x9753, 0xa7 => 0x96e9, 0xa8 => 0x96f3,
    0xa9 => 0x96ef, 0xaa => 0x9706, 0xab => 0x9701, 0xac => 0x9708,
    0xad => 0x970f, 0xae => 0x970e, 0xaf => 0x972a, 0xb0 => 0x972d,
    0xb1 => 0x9730, 0xb2 => 0x973e, 0xb3 => 0x9f80, 0xb4 => 0x9f83,
    0xb5 => 0x9f85, 0xb6 => 0x9f86, 0xb7 => 0x9f87, 0xb8 => 0x9f88,
    0xb9 => 0x9f89, 0xba => 0x9f8a, 0xbb => 0x9f8c, 0xbc => 0x9efe,
    0xbd => 0x9f0b, 0xbe => 0x9f0d, 0xbf => 0x96b9, 0xc0 => 0x96bc,
    0xc1 => 0x96bd, 0xc2 => 0x96ce, 0xc3 => 0x96d2, 0xc4 => 0x77bf,
    0xc5 => 0x96e0, 0xc6 => 0x928e, 0xc7 => 0x92ae, 0xc8 => 0x92c8,
    0xc9 => 0x933e, 0xca => 0x936a, 0xcb => 0x93ca, 0xcc => 0x938f,
    0xcd => 0x943e, 0xce => 0x946b, 0xcf => 0x9c7f, 0xd0 => 0x9c82,
    0xd1 => 0x9c85, 0xd2 => 0x9c86, 0xd3 => 0x9c87, 0xd4 => 0x9c88,
    0xd5 => 0x7a23, 0xd6 => 0x9c8b, 0xd7 => 0x9c8e, 0xd8 => 0x9c90,
    0xd9 => 0x9c91, 0xda => 0x9c92, 0xdb => 0x9c94, 0xdc => 0x9c95,
    0xdd => 0x9c9a, 0xde => 0x9c9b, 0xdf => 0x9c9e, 0xe0 => 0x9c9f,
    0xe1 => 0x9ca0, 0xe2 => 0x9ca1, 0xe3 => 0x9ca2, 0xe4 => 0x9ca3,
    0xe5 => 0x9ca5, 0xe6 => 0x9ca6, 0xe7 => 0x9ca7, 0xe8 => 0x9ca8,
    0xe9 => 0x9ca9, 0xea => 0x9cab, 0xeb => 0x9cad, 0xec => 0x9cae,
    0xed => 0x9cb0, 0xee => 0x9cb1, 0xef => 0x9cb2, 0xf0 => 0x9cb3,
    0xf1 => 0x9cb4, 0xf2 => 0x9cb5, 0xf3 => 0x9cb6, 0xf4 => 0x9cb7,
    0xf5 => 0x9cba, 0xf6 => 0x9cbb, 0xf7 => 0x9cbc, 0xf8 => 0x9cbd,
    0xf9 => 0x9cc4, 0xfa => 0x9cc5, 0xfb => 0x9cc6, 0xfc => 0x9cc7,
    0xfd => 0x9cca, 0xfe => 0x9ccb,
  },
  0xf7 => {
    0xa1 => 0x9ccc, 0xa2 => 0x9ccd, 0xa3 => 0x9cce, 0xa4 => 0x9ccf,
    0xa5 => 0x9cd0, 0xa6 => 0x9cd3, 0xa7 => 0x9cd4, 0xa8 => 0x9cd5,
    0xa9 => 0x9cd7, 0xaa => 0x9cd8, 0xab => 0x9cd9, 0xac => 0x9cdc,
    0xad => 0x9cdd, 0xae => 0x9cdf, 0xaf => 0x9ce2, 0xb0 => 0x977c,
    0xb1 => 0x9785, 0xb2 => 0x9791, 0xb3 => 0x9792, 0xb4 => 0x9794,
    0xb5 => 0x97af, 0xb6 => 0x97ab, 0xb7 => 0x97a3, 0xb8 => 0x97b2,
    0xb9 => 0x97b4, 0xba => 0x9ab1, 0xbb => 0x9ab0, 0xbc => 0x9ab7,
    0xbd => 0x9e58, 0xbe => 0x9ab6, 0xbf => 0x9aba, 0xc0 => 0x9abc,
    0xc1 => 0x9ac1, 0xc2 => 0x9ac0, 0xc3 => 0x9ac5, 0xc4 => 0x9ac2,
    0xc5 => 0x9acb, 0xc6 => 0x9acc, 0xc7 => 0x9ad1, 0xc8 => 0x9b45,
    0xc9 => 0x9b43, 0xca => 0x9b47, 0xcb => 0x9b49, 0xcc => 0x9b48,
    0xcd => 0x9b4d, 0xce => 0x9b51, 0xcf => 0x98e8, 0xd0 => 0x990d,
    0xd1 => 0x992e, 0xd2 => 0x9955, 0xd3 => 0x9954, 0xd4 => 0x9adf,
    0xd5 => 0x9ae1, 0xd6 => 0x9ae6, 0xd7 => 0x9aef, 0xd8 => 0x9aeb,
    0xd9 => 0x9afb, 0xda => 0x9aed, 0xdb => 0x9af9, 0xdc => 0x9b08,
    0xdd => 0x9b0f, 0xde => 0x9b13, 0xdf => 0x9b1f, 0xe0 => 0x9b23,
    0xe1 => 0x9ebd, 0xe2 => 0x9ebe, 0xe3 => 0x7e3b, 0xe4 => 0x9e82,
    0xe5 => 0x9e87, 0xe6 => 0x9e88, 0xe7 => 0x9e8b, 0xe8 => 0x9e92,
    0xe9 => 0x93d6, 0xea => 0x9e9d, 0xeb => 0x9e9f, 0xec => 0x9edb,
    0xed => 0x9edc, 0xee => 0x9edd, 0xef => 0x9ee0, 0xf0 => 0x9edf,
    0xf1 => 0x9ee2, 0xf2 => 0x9ee9, 0xf3 => 0x9ee7, 0xf4 => 0x9ee5,
    0xf5 => 0x9eea, 0xf6 => 0x9eef, 0xf7 => 0x9f22, 0xf8 => 0x9f2c,
    0xf9 => 0x9f2f, 0xfa => 0x9f39, 0xfb => 0x9f37, 0xfc => 0x9f3d,
    0xfd => 0x9f3e, 0xfe => 0x9f44,
  },
  0xf8 => 0xf8f8, 0xf9 => 0xf8f9, 0xfa => 0xf8fa, 0xfb => 0xf8fb,
  0xfc => 0xf8fc, 0xfd => 0xf8fd, 0xfe => 0xf8fe, 0xff => 0xf8ff,
);

1; # end
