import javax.swing.*;
import java.awt.*;
import java.awt.event.*;
import java.io.*;
import java.nio.file.*;
import java.util.*;
import java.util.List;
import java.util.concurrent.*;
import java.util.stream.*;

public class MetadataRestorerGUI extends JFrame {
    private JTextField inputField, outputField;
    private JButton browseInputBtn, browseOutputBtn, startBtn, pauseBtn, cancelBtn;
    private JProgressBar progressBar;
    private JLabel statusLabel;

    private volatile boolean paused = false;
    private volatile boolean cancelled = false;

    private static final String[] MEDIA_EXTS = {".jpg", ".jpeg", ".png", ".heic", ".mp4", ".mov", ".avi", ".mkv"};
    private static final String EXIFTOOL = "exiftool.exe"; // assumes exiftool.exe in same folder as program

    public MetadataRestorerGUI() {
        setTitle("Google Takeout Metadata Restorer");
        setSize(600, 250);
        setDefaultCloseOperation(EXIT_ON_CLOSE);
        setLayout(new GridBagLayout());

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.fill = GridBagConstraints.HORIZONTAL;

        inputField = new JTextField();
        outputField = new JTextField();
        browseInputBtn = new JButton("Browse");
        browseOutputBtn = new JButton("Browse");
        startBtn = new JButton("Start");
        pauseBtn = new JButton("Pause");
        cancelBtn = new JButton("Cancel");
        progressBar = new JProgressBar(0, 100);
        statusLabel = new JLabel("Idle...");

        // Input folder
        gbc.gridx = 0; gbc.gridy = 0; gbc.gridwidth = 1;
        add(new JLabel("Takeout Folder:"), gbc);
        gbc.gridx = 1; gbc.gridwidth = 2;
        add(inputField, gbc);
        gbc.gridx = 3; gbc.gridwidth = 1;
        add(browseInputBtn, gbc);

        // Output folder
        gbc.gridx = 0; gbc.gridy = 1; gbc.gridwidth = 1;
        add(new JLabel("Output Folder:"), gbc);
        gbc.gridx = 1; gbc.gridwidth = 2;
        add(outputField, gbc);
        gbc.gridx = 3; gbc.gridwidth = 1;
        add(browseOutputBtn, gbc);

        // Progress
        gbc.gridx = 0; gbc.gridy = 2; gbc.gridwidth = 4;
        add(progressBar, gbc);

        // Status
        gbc.gridy = 3;
        add(statusLabel, gbc);

        // Buttons
        JPanel buttonPanel = new JPanel();
        buttonPanel.add(startBtn);
        buttonPanel.add(pauseBtn);
        buttonPanel.add(cancelBtn);
        gbc.gridy = 4;
        add(buttonPanel, gbc);

        // Action Listeners
        browseInputBtn.addActionListener(e -> chooseFolder(inputField));
        browseOutputBtn.addActionListener(e -> chooseFolder(outputField));
        startBtn.addActionListener(e -> startProcessing());
        pauseBtn.addActionListener(e -> togglePause());
        cancelBtn.addActionListener(e -> cancelled = true);
    }

    private void chooseFolder(JTextField field) {
        JFileChooser chooser = new JFileChooser();
        chooser.setFileSelectionMode(JFileChooser.DIRECTORIES_ONLY);
        if (chooser.showOpenDialog(this) == JFileChooser.APPROVE_OPTION) {
            field.setText(chooser.getSelectedFile().getAbsolutePath());
        }
    }

    private void startProcessing() {
        String inputPath = inputField.getText().trim();
        String outputPath = outputField.getText().trim();

        if (inputPath.isEmpty() || outputPath.isEmpty()) {
            JOptionPane.showMessageDialog(this, "Please select both input and output folders.");
            return;
        }

        System.out.println("DEBUG: Starting processing...");
        System.out.println("DEBUG: Input path: " + inputPath);
        System.out.println("DEBUG: Output path: " + outputPath);

        cancelled = false;
        paused = false;

        new Thread(() -> processAll(inputPath, outputPath)).start();
    }

    private void togglePause() {
        paused = !paused;
        pauseBtn.setText(paused ? "Resume" : "Pause");
    }

    private void processAll(String inputPath, String outputPath) {
        try {
            System.out.println("=== STARTING PROCESSING ===");
            System.out.println("Input path: " + inputPath);
            System.out.println("Output path: " + outputPath);

            Path root = Paths.get(inputPath);
            Path output = Paths.get(outputPath);
            Files.createDirectories(output);

            // Collect all media files
            List<Path> allFiles;
            try (Stream<Path> stream = Files.walk(root)) {
                allFiles = stream.filter(Files::isRegularFile)
                        .filter(p -> Arrays.stream(MEDIA_EXTS).anyMatch(ext -> p.toString().toLowerCase().endsWith(ext)))
                        .collect(Collectors.toList());
            }

            int total = allFiles.size();
            System.out.println("DEBUG: Found " + total + " media files");

            // List first few files for debugging
            for (int i = 0; i < Math.min(5, total); i++) {
                System.out.println("DEBUG: File " + (i+1) + ": " + allFiles.get(i));
            }
            if (total > 5) {
                System.out.println("DEBUG: ... and " + (total-5) + " more files");
            }

            if (total == 0) {
                SwingUtilities.invokeLater(() -> JOptionPane.showMessageDialog(this, "No media files found."));
                return;
            }

            for (int i = 0; i < total; i++) {
                if (cancelled) break;

                // Handle pause
                while (paused && !cancelled) {
                    Thread.sleep(200);
                }

                Path file = allFiles.get(i);
                System.out.println("\nDEBUG: --- Processing file " + (i+1) + "/" + total + " ---");
                System.out.println("DEBUG: Source: " + file.toString());

                Path relPath = root.relativize(file);
                Path outFile = output.resolve(relPath);
                System.out.println("DEBUG: Destination: " + outFile.toString());

                Files.createDirectories(outFile.getParent());
                Files.copy(file, outFile, StandardCopyOption.REPLACE_EXISTING);
                System.out.println("DEBUG: File copied successfully");

                // Find matching JSON
                String baseName = file.getFileName().toString();
                System.out.println("DEBUG: Looking for JSON files matching pattern: " + baseName + "*.json");
                System.out.println("DEBUG: Searching in directory: " + file.getParent().toString());

                boolean foundJson = false;
                int jsonCount = 0;
                try (DirectoryStream<Path> dirStream = Files.newDirectoryStream(file.getParent(), baseName + "*.json")) {
                    for (Path json : dirStream) {
                        String jsonName = json.getFileName().toString();
                        jsonCount++;
                        System.out.println("DEBUG: ✓ Found JSON #" + jsonCount + ": " + jsonName + " for file: " + baseName);
                        runExifTool(json.toAbsolutePath().toString(), outFile.toAbsolutePath().toString());
                        foundJson = true;
                        // Don't break - process all matching JSON files in case there are multiple
                    }
                } catch (Exception e) {
                    System.out.println("DEBUG: Error searching for JSON files: " + e.getMessage());
                }
                if (!foundJson) {
                    System.out.println("DEBUG: ✗ No JSON found for: " + baseName);
                }

                final int currentIndex = i + 1;
                final String fileName = file.getFileName().toString();
                int progress = (int) ((currentIndex / (double) total) * 100);
                SwingUtilities.invokeLater(() -> {
                    progressBar.setValue(progress);
                    statusLabel.setText("Processed " + currentIndex + "/" + total + ": " + fileName);
                });
            }

            System.out.println("\n=== PROCESSING COMPLETE ===");
            System.out.println("Status: " + (cancelled ? "CANCELLED" : "COMPLETED"));

            SwingUtilities.invokeLater(() -> {
                statusLabel.setText(cancelled ? "❌ Cancelled" : "✅ Done!");
                JOptionPane.showMessageDialog(this, cancelled ? "Processing cancelled." : "All files processed successfully.");
            });

        } catch (Exception e) {
            e.printStackTrace();
            SwingUtilities.invokeLater(() -> JOptionPane.showMessageDialog(this, "Error: " + e.getMessage()));
        }
    }

    private void runExifTool(String jsonFile, String mediaFile) {
        try {
            System.out.println("DEBUG: === RUNNING EXIFTOOL ===");

            // First check if ExifTool exists
            File exifToolFile = new File(EXIFTOOL);
            System.out.println("DEBUG: Checking for ExifTool at: " + exifToolFile.getAbsolutePath());
            if (!exifToolFile.exists()) {
                System.err.println("DEBUG: ERROR - ExifTool not found: " + EXIFTOOL);
                return;
            }
            System.out.println("DEBUG: ExifTool found!");

            System.out.println("DEBUG: JSON file: " + jsonFile);
            System.out.println("DEBUG: Media file: " + mediaFile);

            // Check if files exist
            if (!new File(jsonFile).exists()) {
                System.err.println("DEBUG: ERROR - JSON file does not exist: " + jsonFile);
                return;
            }
            if (!new File(mediaFile).exists()) {
                System.err.println("DEBUG: ERROR - Media file does not exist: " + mediaFile);
                return;
            }

            ProcessBuilder pb = new ProcessBuilder(
                    EXIFTOOL,
                    "-json=" + jsonFile,
                    "-overwrite_original",
                    "-v",  // Verbose output for debugging
                    mediaFile
            );
            pb.redirectErrorStream(true);

            System.out.println("DEBUG: Command: " + String.join(" ", pb.command()));
            Process process = pb.start();

            // Read the output for debugging
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    System.out.println("DEBUG: ExifTool output: " + line);
                }
            }

            int exitCode = process.waitFor();
            System.out.println("DEBUG: ExifTool exit code: " + exitCode);
            if (exitCode == 0) {
                System.out.println("DEBUG: ✓ ExifTool succeeded!");
            } else {
                System.out.println("DEBUG: ✗ ExifTool failed with exit code: " + exitCode);
            }

        } catch (Exception e) {
            System.err.println("DEBUG: ExifTool exception: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            MetadataRestorerGUI gui = new MetadataRestorerGUI();
            gui.setVisible(true);
        });
    }
}
