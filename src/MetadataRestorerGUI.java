import javax.swing.*;
import java.awt.*;
import java.awt.event.*;
import java.io.*;
import java.nio.file.*;
import java.util.*;
import java.util.List;
import java.util.concurrent.*;
import java.util.stream.*;

public class MetadataRestorerGUI extends JFrame {
    private JTextField inputField, outputField;
    private JButton browseInputBtn, browseOutputBtn, startBtn, pauseBtn, cancelBtn;
    private JProgressBar progressBar;
    private JLabel statusLabel;

    private volatile boolean paused = false;
    private volatile boolean cancelled = false;

    private static final String[] MEDIA_EXTS = {".jpg", ".jpeg", ".png", ".heic", ".mp4", ".mov", ".avi", ".mkv"};
    private static final String EXIFTOOL = "exiftool.exe"; // assumes exiftool.exe in same folder as program

    public MetadataRestorerGUI() {
        setTitle("Google Takeout Metadata Restorer");
        setSize(600, 250);
        setDefaultCloseOperation(EXIT_ON_CLOSE);
        setLayout(new GridBagLayout());

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.fill = GridBagConstraints.HORIZONTAL;

        inputField = new JTextField();
        outputField = new JTextField();
        browseInputBtn = new JButton("Browse");
        browseOutputBtn = new JButton("Browse");
        startBtn = new JButton("Start");
        pauseBtn = new JButton("Pause");
        cancelBtn = new JButton("Cancel");
        progressBar = new JProgressBar(0, 100);
        statusLabel = new JLabel("Idle...");

        // Input folder
        gbc.gridx = 0; gbc.gridy = 0; gbc.gridwidth = 1;
        add(new JLabel("Takeout Folder:"), gbc);
        gbc.gridx = 1; gbc.gridwidth = 2;
        add(inputField, gbc);
        gbc.gridx = 3; gbc.gridwidth = 1;
        add(browseInputBtn, gbc);

        // Output folder
        gbc.gridx = 0; gbc.gridy = 1; gbc.gridwidth = 1;
        add(new JLabel("Output Folder:"), gbc);
        gbc.gridx = 1; gbc.gridwidth = 2;
        add(outputField, gbc);
        gbc.gridx = 3; gbc.gridwidth = 1;
        add(browseOutputBtn, gbc);

        // Progress
        gbc.gridx = 0; gbc.gridy = 2; gbc.gridwidth = 4;
        add(progressBar, gbc);

        // Status
        gbc.gridy = 3;
        add(statusLabel, gbc);

        // Buttons
        JPanel buttonPanel = new JPanel();
        buttonPanel.add(startBtn);
        buttonPanel.add(pauseBtn);
        buttonPanel.add(cancelBtn);
        gbc.gridy = 4;
        add(buttonPanel, gbc);

        // Action Listeners
        browseInputBtn.addActionListener(e -> chooseFolder(inputField));
        browseOutputBtn.addActionListener(e -> chooseFolder(outputField));
        startBtn.addActionListener(e -> startProcessing());
        pauseBtn.addActionListener(e -> togglePause());
        cancelBtn.addActionListener(e -> cancelled = true);
    }

    private void chooseFolder(JTextField field) {
        JFileChooser chooser = new JFileChooser();
        chooser.setFileSelectionMode(JFileChooser.DIRECTORIES_ONLY);
        if (chooser.showOpenDialog(this) == JFileChooser.APPROVE_OPTION) {
            field.setText(chooser.getSelectedFile().getAbsolutePath());
        }
    }

    private void startProcessing() {
        String inputPath = inputField.getText().trim();
        String outputPath = outputField.getText().trim();

        if (inputPath.isEmpty() || outputPath.isEmpty()) {
            JOptionPane.showMessageDialog(this, "Please select both input and output folders.");
            return;
        }

        cancelled = false;
        paused = false;

        new Thread(() -> processAll(inputPath, outputPath)).start();
    }

    private void togglePause() {
        paused = !paused;
        pauseBtn.setText(paused ? "Resume" : "Pause");
    }

    private void processAll(String inputPath, String outputPath) {
        try {
            Path root = Paths.get(inputPath);
            Path output = Paths.get(outputPath);
            Files.createDirectories(output);

            // Collect all media files
            List<Path> allFiles;
            try (Stream<Path> stream = Files.walk(root)) {
                allFiles = stream.filter(Files::isRegularFile)
                        .filter(p -> Arrays.stream(MEDIA_EXTS).anyMatch(ext -> p.toString().toLowerCase().endsWith(ext)))
                        .collect(Collectors.toList());
            }

            int total = allFiles.size();
            if (total == 0) {
                SwingUtilities.invokeLater(() -> JOptionPane.showMessageDialog(this, "No media files found."));
                return;
            }

            for (int i = 0; i < total; i++) {
                if (cancelled) break;

                // Handle pause
                while (paused && !cancelled) {
                    Thread.sleep(200);
                }

                Path file = allFiles.get(i);
                Path relPath = root.relativize(file);
                Path outFile = output.resolve(relPath);
                Files.createDirectories(outFile.getParent());
                Files.copy(file, outFile, StandardCopyOption.REPLACE_EXISTING);

                // Find matching JSON
                String baseName = file.getFileName().toString();
                try (DirectoryStream<Path> dirStream = Files.newDirectoryStream(file.getParent(), "*.json")) {
                    for (Path json : dirStream) {
                        if (json.getFileName().toString().startsWith(baseName)) {
                            runExifTool(json.toAbsolutePath().toString(), outFile.toAbsolutePath().toString());
                        }
                    }
                }

                final int currentIndex = i + 1;
                final String fileName = file.getFileName().toString();
                int progress = (int) ((currentIndex / (double) total) * 100);
                SwingUtilities.invokeLater(() -> {
                    progressBar.setValue(progress);
                    statusLabel.setText("Processed " + currentIndex + "/" + total + ": " + fileName);
                });
            }

            SwingUtilities.invokeLater(() -> {
                statusLabel.setText(cancelled ? "❌ Cancelled" : "✅ Done!");
                JOptionPane.showMessageDialog(this, cancelled ? "Processing cancelled." : "All files processed successfully.");
            });

        } catch (Exception e) {
            e.printStackTrace();
            SwingUtilities.invokeLater(() -> JOptionPane.showMessageDialog(this, "Error: " + e.getMessage()));
        }
    }

    private void runExifTool(String jsonFile, String mediaFile) {
        try {
            ProcessBuilder pb = new ProcessBuilder(
                    EXIFTOOL,
                    "-j=" + jsonFile,
                    "-overwrite_original",
                    mediaFile
            );
            pb.redirectErrorStream(true);
            Process process = pb.start();
            process.waitFor();
        } catch (Exception e) {
            System.err.println("ExifTool error: " + e.getMessage());
        }
    }

    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            MetadataRestorerGUI gui = new MetadataRestorerGUI();
            gui.setVisible(true);
        });
    }
}
