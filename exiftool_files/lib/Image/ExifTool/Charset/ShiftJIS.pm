#------------------------------------------------------------------------------
# File:         ShiftJIS.pm
#
# Description:  Shift-J<PERSON> to Unicode
#
# Revisions:    2010/01/20 - P<PERSON> created
#               2010/07/30 - P. <PERSON> fixed to use variable-width characters
#
# References:   1) http://unicode.org/Public/MAPPINGS/OBSOLETE/EASTASIA/JIS/SHIFTJIS.TXT
#
# Notes:        The table omits 1-byte characters with the same values as Unicode
#------------------------------------------------------------------------------
use strict;

%Image::ExifTool::Charset::ShiftJIS = (
  0x5c => 0xa5, 0x7e => 0x203e,
  0x81 => {
    0x40 => 0x3000, 0x41 => 0x3001, 0x42 => 0x3002, 0x43 => 0xff0c,
    0x44 => 0xff0e, 0x45 => 0x30fb, 0x46 => 0xff1a, 0x47 => 0xff1b,
    0x48 => 0xff1f, 0x49 => 0xff01, 0x4a => 0x309b, 0x4b => 0x309c,
    0x4c => 0xb4, 0x4d => 0xff40, 0x4e => 0xa8, 0x4f => 0xff3e, 0x50 => 0xffe3,
    0x51 => 0xff3f, 0x52 => 0x30fd, 0x53 => 0x30fe, 0x54 => 0x309d,
    0x55 => 0x309e, 0x56 => 0x3003, 0x57 => 0x4edd, 0x58 => 0x3005,
    0x59 => 0x3006, 0x5a => 0x3007, 0x5b => 0x30fc, 0x5c => 0x2015,
    0x5d => 0x2010, 0x5e => 0xff0f, 0x5f => 0x5c, 0x60 => 0x301c,
    0x61 => 0x2016, 0x62 => 0xff5c, 0x63 => 0x2026, 0x64 => 0x2025,
    0x65 => 0x2018, 0x66 => 0x2019, 0x67 => 0x201c, 0x68 => 0x201d,
    0x69 => 0xff08, 0x6a => 0xff09, 0x6b => 0x3014, 0x6c => 0x3015,
    0x6d => 0xff3b, 0x6e => 0xff3d, 0x6f => 0xff5b, 0x70 => 0xff5d,
    0x71 => 0x3008, 0x72 => 0x3009, 0x73 => 0x300a, 0x74 => 0x300b,
    0x75 => 0x300c, 0x76 => 0x300d, 0x77 => 0x300e, 0x78 => 0x300f,
    0x79 => 0x3010, 0x7a => 0x3011, 0x7b => 0xff0b, 0x7c => 0x2212,
    0x7d => 0xb1, 0x7e => 0xd7, 0x80 => 0xf7, 0x81 => 0xff1d, 0x82 => 0x2260,
    0x83 => 0xff1c, 0x84 => 0xff1e, 0x85 => 0x2266, 0x86 => 0x2267,
    0x87 => 0x221e, 0x88 => 0x2234, 0x89 => 0x2642, 0x8a => 0x2640,
    0x8b => 0xb0, 0x8c => 0x2032, 0x8d => 0x2033, 0x8e => 0x2103,
    0x8f => 0xffe5, 0x90 => 0xff04, 0x91 => 0xa2, 0x92 => 0xa3, 0x93 => 0xff05,
    0x94 => 0xff03, 0x95 => 0xff06, 0x96 => 0xff0a, 0x97 => 0xff20,
    0x98 => 0xa7, 0x99 => 0x2606, 0x9a => 0x2605, 0x9b => 0x25cb,
    0x9c => 0x25cf, 0x9d => 0x25ce, 0x9e => 0x25c7, 0x9f => 0x25c6,
    0xa0 => 0x25a1, 0xa1 => 0x25a0, 0xa2 => 0x25b3, 0xa3 => 0x25b2,
    0xa4 => 0x25bd, 0xa5 => 0x25bc, 0xa6 => 0x203b, 0xa7 => 0x3012,
    0xa8 => 0x2192, 0xa9 => 0x2190, 0xaa => 0x2191, 0xab => 0x2193,
    0xac => 0x3013, 0xb8 => 0x2208, 0xb9 => 0x220b, 0xba => 0x2286,
    0xbb => 0x2287, 0xbc => 0x2282, 0xbd => 0x2283, 0xbe => 0x222a,
    0xbf => 0x2229, 0xc8 => 0x2227, 0xc9 => 0x2228, 0xca => 0xac,
    0xcb => 0x21d2, 0xcc => 0x21d4, 0xcd => 0x2200, 0xce => 0x2203,
    0xda => 0x2220, 0xdb => 0x22a5, 0xdc => 0x2312, 0xdd => 0x2202,
    0xde => 0x2207, 0xdf => 0x2261, 0xe0 => 0x2252, 0xe1 => 0x226a,
    0xe2 => 0x226b, 0xe3 => 0x221a, 0xe4 => 0x223d, 0xe5 => 0x221d,
    0xe6 => 0x2235, 0xe7 => 0x222b, 0xe8 => 0x222c, 0xf0 => 0x212b,
    0xf1 => 0x2030, 0xf2 => 0x266f, 0xf3 => 0x266d, 0xf4 => 0x266a,
    0xf5 => 0x2020, 0xf6 => 0x2021, 0xf7 => 0xb6, 0xfc => 0x25ef,
  },
  0x82 => {
    0x4f => 0xff10, 0x50 => 0xff11, 0x51 => 0xff12, 0x52 => 0xff13,
    0x53 => 0xff14, 0x54 => 0xff15, 0x55 => 0xff16, 0x56 => 0xff17,
    0x57 => 0xff18, 0x58 => 0xff19, 0x60 => 0xff21, 0x61 => 0xff22,
    0x62 => 0xff23, 0x63 => 0xff24, 0x64 => 0xff25, 0x65 => 0xff26,
    0x66 => 0xff27, 0x67 => 0xff28, 0x68 => 0xff29, 0x69 => 0xff2a,
    0x6a => 0xff2b, 0x6b => 0xff2c, 0x6c => 0xff2d, 0x6d => 0xff2e,
    0x6e => 0xff2f, 0x6f => 0xff30, 0x70 => 0xff31, 0x71 => 0xff32,
    0x72 => 0xff33, 0x73 => 0xff34, 0x74 => 0xff35, 0x75 => 0xff36,
    0x76 => 0xff37, 0x77 => 0xff38, 0x78 => 0xff39, 0x79 => 0xff3a,
    0x81 => 0xff41, 0x82 => 0xff42, 0x83 => 0xff43, 0x84 => 0xff44,
    0x85 => 0xff45, 0x86 => 0xff46, 0x87 => 0xff47, 0x88 => 0xff48,
    0x89 => 0xff49, 0x8a => 0xff4a, 0x8b => 0xff4b, 0x8c => 0xff4c,
    0x8d => 0xff4d, 0x8e => 0xff4e, 0x8f => 0xff4f, 0x90 => 0xff50,
    0x91 => 0xff51, 0x92 => 0xff52, 0x93 => 0xff53, 0x94 => 0xff54,
    0x95 => 0xff55, 0x96 => 0xff56, 0x97 => 0xff57, 0x98 => 0xff58,
    0x99 => 0xff59, 0x9a => 0xff5a, 0x9f => 0x3041, 0xa0 => 0x3042,
    0xa1 => 0x3043, 0xa2 => 0x3044, 0xa3 => 0x3045, 0xa4 => 0x3046,
    0xa5 => 0x3047, 0xa6 => 0x3048, 0xa7 => 0x3049, 0xa8 => 0x304a,
    0xa9 => 0x304b, 0xaa => 0x304c, 0xab => 0x304d, 0xac => 0x304e,
    0xad => 0x304f, 0xae => 0x3050, 0xaf => 0x3051, 0xb0 => 0x3052,
    0xb1 => 0x3053, 0xb2 => 0x3054, 0xb3 => 0x3055, 0xb4 => 0x3056,
    0xb5 => 0x3057, 0xb6 => 0x3058, 0xb7 => 0x3059, 0xb8 => 0x305a,
    0xb9 => 0x305b, 0xba => 0x305c, 0xbb => 0x305d, 0xbc => 0x305e,
    0xbd => 0x305f, 0xbe => 0x3060, 0xbf => 0x3061, 0xc0 => 0x3062,
    0xc1 => 0x3063, 0xc2 => 0x3064, 0xc3 => 0x3065, 0xc4 => 0x3066,
    0xc5 => 0x3067, 0xc6 => 0x3068, 0xc7 => 0x3069, 0xc8 => 0x306a,
    0xc9 => 0x306b, 0xca => 0x306c, 0xcb => 0x306d, 0xcc => 0x306e,
    0xcd => 0x306f, 0xce => 0x3070, 0xcf => 0x3071, 0xd0 => 0x3072,
    0xd1 => 0x3073, 0xd2 => 0x3074, 0xd3 => 0x3075, 0xd4 => 0x3076,
    0xd5 => 0x3077, 0xd6 => 0x3078, 0xd7 => 0x3079, 0xd8 => 0x307a,
    0xd9 => 0x307b, 0xda => 0x307c, 0xdb => 0x307d, 0xdc => 0x307e,
    0xdd => 0x307f, 0xde => 0x3080, 0xdf => 0x3081, 0xe0 => 0x3082,
    0xe1 => 0x3083, 0xe2 => 0x3084, 0xe3 => 0x3085, 0xe4 => 0x3086,
    0xe5 => 0x3087, 0xe6 => 0x3088, 0xe7 => 0x3089, 0xe8 => 0x308a,
    0xe9 => 0x308b, 0xea => 0x308c, 0xeb => 0x308d, 0xec => 0x308e,
    0xed => 0x308f, 0xee => 0x3090, 0xef => 0x3091, 0xf0 => 0x3092,
    0xf1 => 0x3093,
  },
  0x83 => {
    0x40 => 0x30a1, 0x41 => 0x30a2, 0x42 => 0x30a3, 0x43 => 0x30a4,
    0x44 => 0x30a5, 0x45 => 0x30a6, 0x46 => 0x30a7, 0x47 => 0x30a8,
    0x48 => 0x30a9, 0x49 => 0x30aa, 0x4a => 0x30ab, 0x4b => 0x30ac,
    0x4c => 0x30ad, 0x4d => 0x30ae, 0x4e => 0x30af, 0x4f => 0x30b0,
    0x50 => 0x30b1, 0x51 => 0x30b2, 0x52 => 0x30b3, 0x53 => 0x30b4,
    0x54 => 0x30b5, 0x55 => 0x30b6, 0x56 => 0x30b7, 0x57 => 0x30b8,
    0x58 => 0x30b9, 0x59 => 0x30ba, 0x5a => 0x30bb, 0x5b => 0x30bc,
    0x5c => 0x30bd, 0x5d => 0x30be, 0x5e => 0x30bf, 0x5f => 0x30c0,
    0x60 => 0x30c1, 0x61 => 0x30c2, 0x62 => 0x30c3, 0x63 => 0x30c4,
    0x64 => 0x30c5, 0x65 => 0x30c6, 0x66 => 0x30c7, 0x67 => 0x30c8,
    0x68 => 0x30c9, 0x69 => 0x30ca, 0x6a => 0x30cb, 0x6b => 0x30cc,
    0x6c => 0x30cd, 0x6d => 0x30ce, 0x6e => 0x30cf, 0x6f => 0x30d0,
    0x70 => 0x30d1, 0x71 => 0x30d2, 0x72 => 0x30d3, 0x73 => 0x30d4,
    0x74 => 0x30d5, 0x75 => 0x30d6, 0x76 => 0x30d7, 0x77 => 0x30d8,
    0x78 => 0x30d9, 0x79 => 0x30da, 0x7a => 0x30db, 0x7b => 0x30dc,
    0x7c => 0x30dd, 0x7d => 0x30de, 0x7e => 0x30df, 0x80 => 0x30e0,
    0x81 => 0x30e1, 0x82 => 0x30e2, 0x83 => 0x30e3, 0x84 => 0x30e4,
    0x85 => 0x30e5, 0x86 => 0x30e6, 0x87 => 0x30e7, 0x88 => 0x30e8,
    0x89 => 0x30e9, 0x8a => 0x30ea, 0x8b => 0x30eb, 0x8c => 0x30ec,
    0x8d => 0x30ed, 0x8e => 0x30ee, 0x8f => 0x30ef, 0x90 => 0x30f0,
    0x91 => 0x30f1, 0x92 => 0x30f2, 0x93 => 0x30f3, 0x94 => 0x30f4,
    0x95 => 0x30f5, 0x96 => 0x30f6, 0x9f => 0x0391, 0xa0 => 0x0392,
    0xa1 => 0x0393, 0xa2 => 0x0394, 0xa3 => 0x0395, 0xa4 => 0x0396,
    0xa5 => 0x0397, 0xa6 => 0x0398, 0xa7 => 0x0399, 0xa8 => 0x039a,
    0xa9 => 0x039b, 0xaa => 0x039c, 0xab => 0x039d, 0xac => 0x039e,
    0xad => 0x039f, 0xae => 0x03a0, 0xaf => 0x03a1, 0xb0 => 0x03a3,
    0xb1 => 0x03a4, 0xb2 => 0x03a5, 0xb3 => 0x03a6, 0xb4 => 0x03a7,
    0xb5 => 0x03a8, 0xb6 => 0x03a9, 0xbf => 0x03b1, 0xc0 => 0x03b2,
    0xc1 => 0x03b3, 0xc2 => 0x03b4, 0xc3 => 0x03b5, 0xc4 => 0x03b6,
    0xc5 => 0x03b7, 0xc6 => 0x03b8, 0xc7 => 0x03b9, 0xc8 => 0x03ba,
    0xc9 => 0x03bb, 0xca => 0x03bc, 0xcb => 0x03bd, 0xcc => 0x03be,
    0xcd => 0x03bf, 0xce => 0x03c0, 0xcf => 0x03c1, 0xd0 => 0x03c3,
    0xd1 => 0x03c4, 0xd2 => 0x03c5, 0xd3 => 0x03c6, 0xd4 => 0x03c7,
    0xd5 => 0x03c8, 0xd6 => 0x03c9,
  },
  0x84 => {
    0x40 => 0x0410, 0x41 => 0x0411, 0x42 => 0x0412, 0x43 => 0x0413,
    0x44 => 0x0414, 0x45 => 0x0415, 0x46 => 0x0401, 0x47 => 0x0416,
    0x48 => 0x0417, 0x49 => 0x0418, 0x4a => 0x0419, 0x4b => 0x041a,
    0x4c => 0x041b, 0x4d => 0x041c, 0x4e => 0x041d, 0x4f => 0x041e,
    0x50 => 0x041f, 0x51 => 0x0420, 0x52 => 0x0421, 0x53 => 0x0422,
    0x54 => 0x0423, 0x55 => 0x0424, 0x56 => 0x0425, 0x57 => 0x0426,
    0x58 => 0x0427, 0x59 => 0x0428, 0x5a => 0x0429, 0x5b => 0x042a,
    0x5c => 0x042b, 0x5d => 0x042c, 0x5e => 0x042d, 0x5f => 0x042e,
    0x60 => 0x042f, 0x70 => 0x0430, 0x71 => 0x0431, 0x72 => 0x0432,
    0x73 => 0x0433, 0x74 => 0x0434, 0x75 => 0x0435, 0x76 => 0x0451,
    0x77 => 0x0436, 0x78 => 0x0437, 0x79 => 0x0438, 0x7a => 0x0439,
    0x7b => 0x043a, 0x7c => 0x043b, 0x7d => 0x043c, 0x7e => 0x043d,
    0x80 => 0x043e, 0x81 => 0x043f, 0x82 => 0x0440, 0x83 => 0x0441,
    0x84 => 0x0442, 0x85 => 0x0443, 0x86 => 0x0444, 0x87 => 0x0445,
    0x88 => 0x0446, 0x89 => 0x0447, 0x8a => 0x0448, 0x8b => 0x0449,
    0x8c => 0x044a, 0x8d => 0x044b, 0x8e => 0x044c, 0x8f => 0x044d,
    0x90 => 0x044e, 0x91 => 0x044f, 0x9f => 0x2500, 0xa0 => 0x2502,
    0xa1 => 0x250c, 0xa2 => 0x2510, 0xa3 => 0x2518, 0xa4 => 0x2514,
    0xa5 => 0x251c, 0xa6 => 0x252c, 0xa7 => 0x2524, 0xa8 => 0x2534,
    0xa9 => 0x253c, 0xaa => 0x2501, 0xab => 0x2503, 0xac => 0x250f,
    0xad => 0x2513, 0xae => 0x251b, 0xaf => 0x2517, 0xb0 => 0x2523,
    0xb1 => 0x2533, 0xb2 => 0x252b, 0xb3 => 0x253b, 0xb4 => 0x254b,
    0xb5 => 0x2520, 0xb6 => 0x252f, 0xb7 => 0x2528, 0xb8 => 0x2537,
    0xb9 => 0x253f, 0xba => 0x251d, 0xbb => 0x2530, 0xbc => 0x2525,
    0xbd => 0x2538, 0xbe => 0x2542,
  },
  0x88 => {
    0x9f => 0x4e9c, 0xa0 => 0x5516, 0xa1 => 0x5a03, 0xa2 => 0x963f,
    0xa3 => 0x54c0, 0xa4 => 0x611b, 0xa5 => 0x6328, 0xa6 => 0x59f6,
    0xa7 => 0x9022, 0xa8 => 0x8475, 0xa9 => 0x831c, 0xaa => 0x7a50,
    0xab => 0x60aa, 0xac => 0x63e1, 0xad => 0x6e25, 0xae => 0x65ed,
    0xaf => 0x8466, 0xb0 => 0x82a6, 0xb1 => 0x9bf5, 0xb2 => 0x6893,
    0xb3 => 0x5727, 0xb4 => 0x65a1, 0xb5 => 0x6271, 0xb6 => 0x5b9b,
    0xb7 => 0x59d0, 0xb8 => 0x867b, 0xb9 => 0x98f4, 0xba => 0x7d62,
    0xbb => 0x7dbe, 0xbc => 0x9b8e, 0xbd => 0x6216, 0xbe => 0x7c9f,
    0xbf => 0x88b7, 0xc0 => 0x5b89, 0xc1 => 0x5eb5, 0xc2 => 0x6309,
    0xc3 => 0x6697, 0xc4 => 0x6848, 0xc5 => 0x95c7, 0xc6 => 0x978d,
    0xc7 => 0x674f, 0xc8 => 0x4ee5, 0xc9 => 0x4f0a, 0xca => 0x4f4d,
    0xcb => 0x4f9d, 0xcc => 0x5049, 0xcd => 0x56f2, 0xce => 0x5937,
    0xcf => 0x59d4, 0xd0 => 0x5a01, 0xd1 => 0x5c09, 0xd2 => 0x60df,
    0xd3 => 0x610f, 0xd4 => 0x6170, 0xd5 => 0x6613, 0xd6 => 0x6905,
    0xd7 => 0x70ba, 0xd8 => 0x754f, 0xd9 => 0x7570, 0xda => 0x79fb,
    0xdb => 0x7dad, 0xdc => 0x7def, 0xdd => 0x80c3, 0xde => 0x840e,
    0xdf => 0x8863, 0xe0 => 0x8b02, 0xe1 => 0x9055, 0xe2 => 0x907a,
    0xe3 => 0x533b, 0xe4 => 0x4e95, 0xe5 => 0x4ea5, 0xe6 => 0x57df,
    0xe7 => 0x80b2, 0xe8 => 0x90c1, 0xe9 => 0x78ef, 0xea => 0x4e00,
    0xeb => 0x58f1, 0xec => 0x6ea2, 0xed => 0x9038, 0xee => 0x7a32,
    0xef => 0x8328, 0xf0 => 0x828b, 0xf1 => 0x9c2f, 0xf2 => 0x5141,
    0xf3 => 0x5370, 0xf4 => 0x54bd, 0xf5 => 0x54e1, 0xf6 => 0x56e0,
    0xf7 => 0x59fb, 0xf8 => 0x5f15, 0xf9 => 0x98f2, 0xfa => 0x6deb,
    0xfb => 0x80e4, 0xfc => 0x852d,
  },
  0x89 => {
    0x40 => 0x9662, 0x41 => 0x9670, 0x42 => 0x96a0, 0x43 => 0x97fb,
    0x44 => 0x540b, 0x45 => 0x53f3, 0x46 => 0x5b87, 0x47 => 0x70cf,
    0x48 => 0x7fbd, 0x49 => 0x8fc2, 0x4a => 0x96e8, 0x4b => 0x536f,
    0x4c => 0x9d5c, 0x4d => 0x7aba, 0x4e => 0x4e11, 0x4f => 0x7893,
    0x50 => 0x81fc, 0x51 => 0x6e26, 0x52 => 0x5618, 0x53 => 0x5504,
    0x54 => 0x6b1d, 0x55 => 0x851a, 0x56 => 0x9c3b, 0x57 => 0x59e5,
    0x58 => 0x53a9, 0x59 => 0x6d66, 0x5a => 0x74dc, 0x5b => 0x958f,
    0x5c => 0x5642, 0x5d => 0x4e91, 0x5e => 0x904b, 0x5f => 0x96f2,
    0x60 => 0x834f, 0x61 => 0x990c, 0x62 => 0x53e1, 0x63 => 0x55b6,
    0x64 => 0x5b30, 0x65 => 0x5f71, 0x66 => 0x6620, 0x67 => 0x66f3,
    0x68 => 0x6804, 0x69 => 0x6c38, 0x6a => 0x6cf3, 0x6b => 0x6d29,
    0x6c => 0x745b, 0x6d => 0x76c8, 0x6e => 0x7a4e, 0x6f => 0x9834,
    0x70 => 0x82f1, 0x71 => 0x885b, 0x72 => 0x8a60, 0x73 => 0x92ed,
    0x74 => 0x6db2, 0x75 => 0x75ab, 0x76 => 0x76ca, 0x77 => 0x99c5,
    0x78 => 0x60a6, 0x79 => 0x8b01, 0x7a => 0x8d8a, 0x7b => 0x95b2,
    0x7c => 0x698e, 0x7d => 0x53ad, 0x7e => 0x5186, 0x80 => 0x5712,
    0x81 => 0x5830, 0x82 => 0x5944, 0x83 => 0x5bb4, 0x84 => 0x5ef6,
    0x85 => 0x6028, 0x86 => 0x63a9, 0x87 => 0x63f4, 0x88 => 0x6cbf,
    0x89 => 0x6f14, 0x8a => 0x708e, 0x8b => 0x7114, 0x8c => 0x7159,
    0x8d => 0x71d5, 0x8e => 0x733f, 0x8f => 0x7e01, 0x90 => 0x8276,
    0x91 => 0x82d1, 0x92 => 0x8597, 0x93 => 0x9060, 0x94 => 0x925b,
    0x95 => 0x9d1b, 0x96 => 0x5869, 0x97 => 0x65bc, 0x98 => 0x6c5a,
    0x99 => 0x7525, 0x9a => 0x51f9, 0x9b => 0x592e, 0x9c => 0x5965,
    0x9d => 0x5f80, 0x9e => 0x5fdc, 0x9f => 0x62bc, 0xa0 => 0x65fa,
    0xa1 => 0x6a2a, 0xa2 => 0x6b27, 0xa3 => 0x6bb4, 0xa4 => 0x738b,
    0xa5 => 0x7fc1, 0xa6 => 0x8956, 0xa7 => 0x9d2c, 0xa8 => 0x9d0e,
    0xa9 => 0x9ec4, 0xaa => 0x5ca1, 0xab => 0x6c96, 0xac => 0x837b,
    0xad => 0x5104, 0xae => 0x5c4b, 0xaf => 0x61b6, 0xb0 => 0x81c6,
    0xb1 => 0x6876, 0xb2 => 0x7261, 0xb3 => 0x4e59, 0xb4 => 0x4ffa,
    0xb5 => 0x5378, 0xb6 => 0x6069, 0xb7 => 0x6e29, 0xb8 => 0x7a4f,
    0xb9 => 0x97f3, 0xba => 0x4e0b, 0xbb => 0x5316, 0xbc => 0x4eee,
    0xbd => 0x4f55, 0xbe => 0x4f3d, 0xbf => 0x4fa1, 0xc0 => 0x4f73,
    0xc1 => 0x52a0, 0xc2 => 0x53ef, 0xc3 => 0x5609, 0xc4 => 0x590f,
    0xc5 => 0x5ac1, 0xc6 => 0x5bb6, 0xc7 => 0x5be1, 0xc8 => 0x79d1,
    0xc9 => 0x6687, 0xca => 0x679c, 0xcb => 0x67b6, 0xcc => 0x6b4c,
    0xcd => 0x6cb3, 0xce => 0x706b, 0xcf => 0x73c2, 0xd0 => 0x798d,
    0xd1 => 0x79be, 0xd2 => 0x7a3c, 0xd3 => 0x7b87, 0xd4 => 0x82b1,
    0xd5 => 0x82db, 0xd6 => 0x8304, 0xd7 => 0x8377, 0xd8 => 0x83ef,
    0xd9 => 0x83d3, 0xda => 0x8766, 0xdb => 0x8ab2, 0xdc => 0x5629,
    0xdd => 0x8ca8, 0xde => 0x8fe6, 0xdf => 0x904e, 0xe0 => 0x971e,
    0xe1 => 0x868a, 0xe2 => 0x4fc4, 0xe3 => 0x5ce8, 0xe4 => 0x6211,
    0xe5 => 0x7259, 0xe6 => 0x753b, 0xe7 => 0x81e5, 0xe8 => 0x82bd,
    0xe9 => 0x86fe, 0xea => 0x8cc0, 0xeb => 0x96c5, 0xec => 0x9913,
    0xed => 0x99d5, 0xee => 0x4ecb, 0xef => 0x4f1a, 0xf0 => 0x89e3,
    0xf1 => 0x56de, 0xf2 => 0x584a, 0xf3 => 0x58ca, 0xf4 => 0x5efb,
    0xf5 => 0x5feb, 0xf6 => 0x602a, 0xf7 => 0x6094, 0xf8 => 0x6062,
    0xf9 => 0x61d0, 0xfa => 0x6212, 0xfb => 0x62d0, 0xfc => 0x6539,
  },
  0x8a => {
    0x40 => 0x9b41, 0x41 => 0x6666, 0x42 => 0x68b0, 0x43 => 0x6d77,
    0x44 => 0x7070, 0x45 => 0x754c, 0x46 => 0x7686, 0x47 => 0x7d75,
    0x48 => 0x82a5, 0x49 => 0x87f9, 0x4a => 0x958b, 0x4b => 0x968e,
    0x4c => 0x8c9d, 0x4d => 0x51f1, 0x4e => 0x52be, 0x4f => 0x5916,
    0x50 => 0x54b3, 0x51 => 0x5bb3, 0x52 => 0x5d16, 0x53 => 0x6168,
    0x54 => 0x6982, 0x55 => 0x6daf, 0x56 => 0x788d, 0x57 => 0x84cb,
    0x58 => 0x8857, 0x59 => 0x8a72, 0x5a => 0x93a7, 0x5b => 0x9ab8,
    0x5c => 0x6d6c, 0x5d => 0x99a8, 0x5e => 0x86d9, 0x5f => 0x57a3,
    0x60 => 0x67ff, 0x61 => 0x86ce, 0x62 => 0x920e, 0x63 => 0x5283,
    0x64 => 0x5687, 0x65 => 0x5404, 0x66 => 0x5ed3, 0x67 => 0x62e1,
    0x68 => 0x64b9, 0x69 => 0x683c, 0x6a => 0x6838, 0x6b => 0x6bbb,
    0x6c => 0x7372, 0x6d => 0x78ba, 0x6e => 0x7a6b, 0x6f => 0x899a,
    0x70 => 0x89d2, 0x71 => 0x8d6b, 0x72 => 0x8f03, 0x73 => 0x90ed,
    0x74 => 0x95a3, 0x75 => 0x9694, 0x76 => 0x9769, 0x77 => 0x5b66,
    0x78 => 0x5cb3, 0x79 => 0x697d, 0x7a => 0x984d, 0x7b => 0x984e,
    0x7c => 0x639b, 0x7d => 0x7b20, 0x7e => 0x6a2b, 0x80 => 0x6a7f,
    0x81 => 0x68b6, 0x82 => 0x9c0d, 0x83 => 0x6f5f, 0x84 => 0x5272,
    0x85 => 0x559d, 0x86 => 0x6070, 0x87 => 0x62ec, 0x88 => 0x6d3b,
    0x89 => 0x6e07, 0x8a => 0x6ed1, 0x8b => 0x845b, 0x8c => 0x8910,
    0x8d => 0x8f44, 0x8e => 0x4e14, 0x8f => 0x9c39, 0x90 => 0x53f6,
    0x91 => 0x691b, 0x92 => 0x6a3a, 0x93 => 0x9784, 0x94 => 0x682a,
    0x95 => 0x515c, 0x96 => 0x7ac3, 0x97 => 0x84b2, 0x98 => 0x91dc,
    0x99 => 0x938c, 0x9a => 0x565b, 0x9b => 0x9d28, 0x9c => 0x6822,
    0x9d => 0x8305, 0x9e => 0x8431, 0x9f => 0x7ca5, 0xa0 => 0x5208,
    0xa1 => 0x82c5, 0xa2 => 0x74e6, 0xa3 => 0x4e7e, 0xa4 => 0x4f83,
    0xa5 => 0x51a0, 0xa6 => 0x5bd2, 0xa7 => 0x520a, 0xa8 => 0x52d8,
    0xa9 => 0x52e7, 0xaa => 0x5dfb, 0xab => 0x559a, 0xac => 0x582a,
    0xad => 0x59e6, 0xae => 0x5b8c, 0xaf => 0x5b98, 0xb0 => 0x5bdb,
    0xb1 => 0x5e72, 0xb2 => 0x5e79, 0xb3 => 0x60a3, 0xb4 => 0x611f,
    0xb5 => 0x6163, 0xb6 => 0x61be, 0xb7 => 0x63db, 0xb8 => 0x6562,
    0xb9 => 0x67d1, 0xba => 0x6853, 0xbb => 0x68fa, 0xbc => 0x6b3e,
    0xbd => 0x6b53, 0xbe => 0x6c57, 0xbf => 0x6f22, 0xc0 => 0x6f97,
    0xc1 => 0x6f45, 0xc2 => 0x74b0, 0xc3 => 0x7518, 0xc4 => 0x76e3,
    0xc5 => 0x770b, 0xc6 => 0x7aff, 0xc7 => 0x7ba1, 0xc8 => 0x7c21,
    0xc9 => 0x7de9, 0xca => 0x7f36, 0xcb => 0x7ff0, 0xcc => 0x809d,
    0xcd => 0x8266, 0xce => 0x839e, 0xcf => 0x89b3, 0xd0 => 0x8acc,
    0xd1 => 0x8cab, 0xd2 => 0x9084, 0xd3 => 0x9451, 0xd4 => 0x9593,
    0xd5 => 0x9591, 0xd6 => 0x95a2, 0xd7 => 0x9665, 0xd8 => 0x97d3,
    0xd9 => 0x9928, 0xda => 0x8218, 0xdb => 0x4e38, 0xdc => 0x542b,
    0xdd => 0x5cb8, 0xde => 0x5dcc, 0xdf => 0x73a9, 0xe0 => 0x764c,
    0xe1 => 0x773c, 0xe2 => 0x5ca9, 0xe3 => 0x7feb, 0xe4 => 0x8d0b,
    0xe5 => 0x96c1, 0xe6 => 0x9811, 0xe7 => 0x9854, 0xe8 => 0x9858,
    0xe9 => 0x4f01, 0xea => 0x4f0e, 0xeb => 0x5371, 0xec => 0x559c,
    0xed => 0x5668, 0xee => 0x57fa, 0xef => 0x5947, 0xf0 => 0x5b09,
    0xf1 => 0x5bc4, 0xf2 => 0x5c90, 0xf3 => 0x5e0c, 0xf4 => 0x5e7e,
    0xf5 => 0x5fcc, 0xf6 => 0x63ee, 0xf7 => 0x673a, 0xf8 => 0x65d7,
    0xf9 => 0x65e2, 0xfa => 0x671f, 0xfb => 0x68cb, 0xfc => 0x68c4,
  },
  0x8b => {
    0x40 => 0x6a5f, 0x41 => 0x5e30, 0x42 => 0x6bc5, 0x43 => 0x6c17,
    0x44 => 0x6c7d, 0x45 => 0x757f, 0x46 => 0x7948, 0x47 => 0x5b63,
    0x48 => 0x7a00, 0x49 => 0x7d00, 0x4a => 0x5fbd, 0x4b => 0x898f,
    0x4c => 0x8a18, 0x4d => 0x8cb4, 0x4e => 0x8d77, 0x4f => 0x8ecc,
    0x50 => 0x8f1d, 0x51 => 0x98e2, 0x52 => 0x9a0e, 0x53 => 0x9b3c,
    0x54 => 0x4e80, 0x55 => 0x507d, 0x56 => 0x5100, 0x57 => 0x5993,
    0x58 => 0x5b9c, 0x59 => 0x622f, 0x5a => 0x6280, 0x5b => 0x64ec,
    0x5c => 0x6b3a, 0x5d => 0x72a0, 0x5e => 0x7591, 0x5f => 0x7947,
    0x60 => 0x7fa9, 0x61 => 0x87fb, 0x62 => 0x8abc, 0x63 => 0x8b70,
    0x64 => 0x63ac, 0x65 => 0x83ca, 0x66 => 0x97a0, 0x67 => 0x5409,
    0x68 => 0x5403, 0x69 => 0x55ab, 0x6a => 0x6854, 0x6b => 0x6a58,
    0x6c => 0x8a70, 0x6d => 0x7827, 0x6e => 0x6775, 0x6f => 0x9ecd,
    0x70 => 0x5374, 0x71 => 0x5ba2, 0x72 => 0x811a, 0x73 => 0x8650,
    0x74 => 0x9006, 0x75 => 0x4e18, 0x76 => 0x4e45, 0x77 => 0x4ec7,
    0x78 => 0x4f11, 0x79 => 0x53ca, 0x7a => 0x5438, 0x7b => 0x5bae,
    0x7c => 0x5f13, 0x7d => 0x6025, 0x7e => 0x6551, 0x80 => 0x673d,
    0x81 => 0x6c42, 0x82 => 0x6c72, 0x83 => 0x6ce3, 0x84 => 0x7078,
    0x85 => 0x7403, 0x86 => 0x7a76, 0x87 => 0x7aae, 0x88 => 0x7b08,
    0x89 => 0x7d1a, 0x8a => 0x7cfe, 0x8b => 0x7d66, 0x8c => 0x65e7,
    0x8d => 0x725b, 0x8e => 0x53bb, 0x8f => 0x5c45, 0x90 => 0x5de8,
    0x91 => 0x62d2, 0x92 => 0x62e0, 0x93 => 0x6319, 0x94 => 0x6e20,
    0x95 => 0x865a, 0x96 => 0x8a31, 0x97 => 0x8ddd, 0x98 => 0x92f8,
    0x99 => 0x6f01, 0x9a => 0x79a6, 0x9b => 0x9b5a, 0x9c => 0x4ea8,
    0x9d => 0x4eab, 0x9e => 0x4eac, 0x9f => 0x4f9b, 0xa0 => 0x4fa0,
    0xa1 => 0x50d1, 0xa2 => 0x5147, 0xa3 => 0x7af6, 0xa4 => 0x5171,
    0xa5 => 0x51f6, 0xa6 => 0x5354, 0xa7 => 0x5321, 0xa8 => 0x537f,
    0xa9 => 0x53eb, 0xaa => 0x55ac, 0xab => 0x5883, 0xac => 0x5ce1,
    0xad => 0x5f37, 0xae => 0x5f4a, 0xaf => 0x602f, 0xb0 => 0x6050,
    0xb1 => 0x606d, 0xb2 => 0x631f, 0xb3 => 0x6559, 0xb4 => 0x6a4b,
    0xb5 => 0x6cc1, 0xb6 => 0x72c2, 0xb7 => 0x72ed, 0xb8 => 0x77ef,
    0xb9 => 0x80f8, 0xba => 0x8105, 0xbb => 0x8208, 0xbc => 0x854e,
    0xbd => 0x90f7, 0xbe => 0x93e1, 0xbf => 0x97ff, 0xc0 => 0x9957,
    0xc1 => 0x9a5a, 0xc2 => 0x4ef0, 0xc3 => 0x51dd, 0xc4 => 0x5c2d,
    0xc5 => 0x6681, 0xc6 => 0x696d, 0xc7 => 0x5c40, 0xc8 => 0x66f2,
    0xc9 => 0x6975, 0xca => 0x7389, 0xcb => 0x6850, 0xcc => 0x7c81,
    0xcd => 0x50c5, 0xce => 0x52e4, 0xcf => 0x5747, 0xd0 => 0x5dfe,
    0xd1 => 0x9326, 0xd2 => 0x65a4, 0xd3 => 0x6b23, 0xd4 => 0x6b3d,
    0xd5 => 0x7434, 0xd6 => 0x7981, 0xd7 => 0x79bd, 0xd8 => 0x7b4b,
    0xd9 => 0x7dca, 0xda => 0x82b9, 0xdb => 0x83cc, 0xdc => 0x887f,
    0xdd => 0x895f, 0xde => 0x8b39, 0xdf => 0x8fd1, 0xe0 => 0x91d1,
    0xe1 => 0x541f, 0xe2 => 0x9280, 0xe3 => 0x4e5d, 0xe4 => 0x5036,
    0xe5 => 0x53e5, 0xe6 => 0x533a, 0xe7 => 0x72d7, 0xe8 => 0x7396,
    0xe9 => 0x77e9, 0xea => 0x82e6, 0xeb => 0x8eaf, 0xec => 0x99c6,
    0xed => 0x99c8, 0xee => 0x99d2, 0xef => 0x5177, 0xf0 => 0x611a,
    0xf1 => 0x865e, 0xf2 => 0x55b0, 0xf3 => 0x7a7a, 0xf4 => 0x5076,
    0xf5 => 0x5bd3, 0xf6 => 0x9047, 0xf7 => 0x9685, 0xf8 => 0x4e32,
    0xf9 => 0x6adb, 0xfa => 0x91e7, 0xfb => 0x5c51, 0xfc => 0x5c48,
  },
  0x8c => {
    0x40 => 0x6398, 0x41 => 0x7a9f, 0x42 => 0x6c93, 0x43 => 0x9774,
    0x44 => 0x8f61, 0x45 => 0x7aaa, 0x46 => 0x718a, 0x47 => 0x9688,
    0x48 => 0x7c82, 0x49 => 0x6817, 0x4a => 0x7e70, 0x4b => 0x6851,
    0x4c => 0x936c, 0x4d => 0x52f2, 0x4e => 0x541b, 0x4f => 0x85ab,
    0x50 => 0x8a13, 0x51 => 0x7fa4, 0x52 => 0x8ecd, 0x53 => 0x90e1,
    0x54 => 0x5366, 0x55 => 0x8888, 0x56 => 0x7941, 0x57 => 0x4fc2,
    0x58 => 0x50be, 0x59 => 0x5211, 0x5a => 0x5144, 0x5b => 0x5553,
    0x5c => 0x572d, 0x5d => 0x73ea, 0x5e => 0x578b, 0x5f => 0x5951,
    0x60 => 0x5f62, 0x61 => 0x5f84, 0x62 => 0x6075, 0x63 => 0x6176,
    0x64 => 0x6167, 0x65 => 0x61a9, 0x66 => 0x63b2, 0x67 => 0x643a,
    0x68 => 0x656c, 0x69 => 0x666f, 0x6a => 0x6842, 0x6b => 0x6e13,
    0x6c => 0x7566, 0x6d => 0x7a3d, 0x6e => 0x7cfb, 0x6f => 0x7d4c,
    0x70 => 0x7d99, 0x71 => 0x7e4b, 0x72 => 0x7f6b, 0x73 => 0x830e,
    0x74 => 0x834a, 0x75 => 0x86cd, 0x76 => 0x8a08, 0x77 => 0x8a63,
    0x78 => 0x8b66, 0x79 => 0x8efd, 0x7a => 0x981a, 0x7b => 0x9d8f,
    0x7c => 0x82b8, 0x7d => 0x8fce, 0x7e => 0x9be8, 0x80 => 0x5287,
    0x81 => 0x621f, 0x82 => 0x6483, 0x83 => 0x6fc0, 0x84 => 0x9699,
    0x85 => 0x6841, 0x86 => 0x5091, 0x87 => 0x6b20, 0x88 => 0x6c7a,
    0x89 => 0x6f54, 0x8a => 0x7a74, 0x8b => 0x7d50, 0x8c => 0x8840,
    0x8d => 0x8a23, 0x8e => 0x6708, 0x8f => 0x4ef6, 0x90 => 0x5039,
    0x91 => 0x5026, 0x92 => 0x5065, 0x93 => 0x517c, 0x94 => 0x5238,
    0x95 => 0x5263, 0x96 => 0x55a7, 0x97 => 0x570f, 0x98 => 0x5805,
    0x99 => 0x5acc, 0x9a => 0x5efa, 0x9b => 0x61b2, 0x9c => 0x61f8,
    0x9d => 0x62f3, 0x9e => 0x6372, 0x9f => 0x691c, 0xa0 => 0x6a29,
    0xa1 => 0x727d, 0xa2 => 0x72ac, 0xa3 => 0x732e, 0xa4 => 0x7814,
    0xa5 => 0x786f, 0xa6 => 0x7d79, 0xa7 => 0x770c, 0xa8 => 0x80a9,
    0xa9 => 0x898b, 0xaa => 0x8b19, 0xab => 0x8ce2, 0xac => 0x8ed2,
    0xad => 0x9063, 0xae => 0x9375, 0xaf => 0x967a, 0xb0 => 0x9855,
    0xb1 => 0x9a13, 0xb2 => 0x9e78, 0xb3 => 0x5143, 0xb4 => 0x539f,
    0xb5 => 0x53b3, 0xb6 => 0x5e7b, 0xb7 => 0x5f26, 0xb8 => 0x6e1b,
    0xb9 => 0x6e90, 0xba => 0x7384, 0xbb => 0x73fe, 0xbc => 0x7d43,
    0xbd => 0x8237, 0xbe => 0x8a00, 0xbf => 0x8afa, 0xc0 => 0x9650,
    0xc1 => 0x4e4e, 0xc2 => 0x500b, 0xc3 => 0x53e4, 0xc4 => 0x547c,
    0xc5 => 0x56fa, 0xc6 => 0x59d1, 0xc7 => 0x5b64, 0xc8 => 0x5df1,
    0xc9 => 0x5eab, 0xca => 0x5f27, 0xcb => 0x6238, 0xcc => 0x6545,
    0xcd => 0x67af, 0xce => 0x6e56, 0xcf => 0x72d0, 0xd0 => 0x7cca,
    0xd1 => 0x88b4, 0xd2 => 0x80a1, 0xd3 => 0x80e1, 0xd4 => 0x83f0,
    0xd5 => 0x864e, 0xd6 => 0x8a87, 0xd7 => 0x8de8, 0xd8 => 0x9237,
    0xd9 => 0x96c7, 0xda => 0x9867, 0xdb => 0x9f13, 0xdc => 0x4e94,
    0xdd => 0x4e92, 0xde => 0x4f0d, 0xdf => 0x5348, 0xe0 => 0x5449,
    0xe1 => 0x543e, 0xe2 => 0x5a2f, 0xe3 => 0x5f8c, 0xe4 => 0x5fa1,
    0xe5 => 0x609f, 0xe6 => 0x68a7, 0xe7 => 0x6a8e, 0xe8 => 0x745a,
    0xe9 => 0x7881, 0xea => 0x8a9e, 0xeb => 0x8aa4, 0xec => 0x8b77,
    0xed => 0x9190, 0xee => 0x4e5e, 0xef => 0x9bc9, 0xf0 => 0x4ea4,
    0xf1 => 0x4f7c, 0xf2 => 0x4faf, 0xf3 => 0x5019, 0xf4 => 0x5016,
    0xf5 => 0x5149, 0xf6 => 0x516c, 0xf7 => 0x529f, 0xf8 => 0x52b9,
    0xf9 => 0x52fe, 0xfa => 0x539a, 0xfb => 0x53e3, 0xfc => 0x5411,
  },
  0x8d => {
    0x40 => 0x540e, 0x41 => 0x5589, 0x42 => 0x5751, 0x43 => 0x57a2,
    0x44 => 0x597d, 0x45 => 0x5b54, 0x46 => 0x5b5d, 0x47 => 0x5b8f,
    0x48 => 0x5de5, 0x49 => 0x5de7, 0x4a => 0x5df7, 0x4b => 0x5e78,
    0x4c => 0x5e83, 0x4d => 0x5e9a, 0x4e => 0x5eb7, 0x4f => 0x5f18,
    0x50 => 0x6052, 0x51 => 0x614c, 0x52 => 0x6297, 0x53 => 0x62d8,
    0x54 => 0x63a7, 0x55 => 0x653b, 0x56 => 0x6602, 0x57 => 0x6643,
    0x58 => 0x66f4, 0x59 => 0x676d, 0x5a => 0x6821, 0x5b => 0x6897,
    0x5c => 0x69cb, 0x5d => 0x6c5f, 0x5e => 0x6d2a, 0x5f => 0x6d69,
    0x60 => 0x6e2f, 0x61 => 0x6e9d, 0x62 => 0x7532, 0x63 => 0x7687,
    0x64 => 0x786c, 0x65 => 0x7a3f, 0x66 => 0x7ce0, 0x67 => 0x7d05,
    0x68 => 0x7d18, 0x69 => 0x7d5e, 0x6a => 0x7db1, 0x6b => 0x8015,
    0x6c => 0x8003, 0x6d => 0x80af, 0x6e => 0x80b1, 0x6f => 0x8154,
    0x70 => 0x818f, 0x71 => 0x822a, 0x72 => 0x8352, 0x73 => 0x884c,
    0x74 => 0x8861, 0x75 => 0x8b1b, 0x76 => 0x8ca2, 0x77 => 0x8cfc,
    0x78 => 0x90ca, 0x79 => 0x9175, 0x7a => 0x9271, 0x7b => 0x783f,
    0x7c => 0x92fc, 0x7d => 0x95a4, 0x7e => 0x964d, 0x80 => 0x9805,
    0x81 => 0x9999, 0x82 => 0x9ad8, 0x83 => 0x9d3b, 0x84 => 0x525b,
    0x85 => 0x52ab, 0x86 => 0x53f7, 0x87 => 0x5408, 0x88 => 0x58d5,
    0x89 => 0x62f7, 0x8a => 0x6fe0, 0x8b => 0x8c6a, 0x8c => 0x8f5f,
    0x8d => 0x9eb9, 0x8e => 0x514b, 0x8f => 0x523b, 0x90 => 0x544a,
    0x91 => 0x56fd, 0x92 => 0x7a40, 0x93 => 0x9177, 0x94 => 0x9d60,
    0x95 => 0x9ed2, 0x96 => 0x7344, 0x97 => 0x6f09, 0x98 => 0x8170,
    0x99 => 0x7511, 0x9a => 0x5ffd, 0x9b => 0x60da, 0x9c => 0x9aa8,
    0x9d => 0x72db, 0x9e => 0x8fbc, 0x9f => 0x6b64, 0xa0 => 0x9803,
    0xa1 => 0x4eca, 0xa2 => 0x56f0, 0xa3 => 0x5764, 0xa4 => 0x58be,
    0xa5 => 0x5a5a, 0xa6 => 0x6068, 0xa7 => 0x61c7, 0xa8 => 0x660f,
    0xa9 => 0x6606, 0xaa => 0x6839, 0xab => 0x68b1, 0xac => 0x6df7,
    0xad => 0x75d5, 0xae => 0x7d3a, 0xaf => 0x826e, 0xb0 => 0x9b42,
    0xb1 => 0x4e9b, 0xb2 => 0x4f50, 0xb3 => 0x53c9, 0xb4 => 0x5506,
    0xb5 => 0x5d6f, 0xb6 => 0x5de6, 0xb7 => 0x5dee, 0xb8 => 0x67fb,
    0xb9 => 0x6c99, 0xba => 0x7473, 0xbb => 0x7802, 0xbc => 0x8a50,
    0xbd => 0x9396, 0xbe => 0x88df, 0xbf => 0x5750, 0xc0 => 0x5ea7,
    0xc1 => 0x632b, 0xc2 => 0x50b5, 0xc3 => 0x50ac, 0xc4 => 0x518d,
    0xc5 => 0x6700, 0xc6 => 0x54c9, 0xc7 => 0x585e, 0xc8 => 0x59bb,
    0xc9 => 0x5bb0, 0xca => 0x5f69, 0xcb => 0x624d, 0xcc => 0x63a1,
    0xcd => 0x683d, 0xce => 0x6b73, 0xcf => 0x6e08, 0xd0 => 0x707d,
    0xd1 => 0x91c7, 0xd2 => 0x7280, 0xd3 => 0x7815, 0xd4 => 0x7826,
    0xd5 => 0x796d, 0xd6 => 0x658e, 0xd7 => 0x7d30, 0xd8 => 0x83dc,
    0xd9 => 0x88c1, 0xda => 0x8f09, 0xdb => 0x969b, 0xdc => 0x5264,
    0xdd => 0x5728, 0xde => 0x6750, 0xdf => 0x7f6a, 0xe0 => 0x8ca1,
    0xe1 => 0x51b4, 0xe2 => 0x5742, 0xe3 => 0x962a, 0xe4 => 0x583a,
    0xe5 => 0x698a, 0xe6 => 0x80b4, 0xe7 => 0x54b2, 0xe8 => 0x5d0e,
    0xe9 => 0x57fc, 0xea => 0x7895, 0xeb => 0x9dfa, 0xec => 0x4f5c,
    0xed => 0x524a, 0xee => 0x548b, 0xef => 0x643e, 0xf0 => 0x6628,
    0xf1 => 0x6714, 0xf2 => 0x67f5, 0xf3 => 0x7a84, 0xf4 => 0x7b56,
    0xf5 => 0x7d22, 0xf6 => 0x932f, 0xf7 => 0x685c, 0xf8 => 0x9bad,
    0xf9 => 0x7b39, 0xfa => 0x5319, 0xfb => 0x518a, 0xfc => 0x5237,
  },
  0x8e => {
    0x40 => 0x5bdf, 0x41 => 0x62f6, 0x42 => 0x64ae, 0x43 => 0x64e6,
    0x44 => 0x672d, 0x45 => 0x6bba, 0x46 => 0x85a9, 0x47 => 0x96d1,
    0x48 => 0x7690, 0x49 => 0x9bd6, 0x4a => 0x634c, 0x4b => 0x9306,
    0x4c => 0x9bab, 0x4d => 0x76bf, 0x4e => 0x6652, 0x4f => 0x4e09,
    0x50 => 0x5098, 0x51 => 0x53c2, 0x52 => 0x5c71, 0x53 => 0x60e8,
    0x54 => 0x6492, 0x55 => 0x6563, 0x56 => 0x685f, 0x57 => 0x71e6,
    0x58 => 0x73ca, 0x59 => 0x7523, 0x5a => 0x7b97, 0x5b => 0x7e82,
    0x5c => 0x8695, 0x5d => 0x8b83, 0x5e => 0x8cdb, 0x5f => 0x9178,
    0x60 => 0x9910, 0x61 => 0x65ac, 0x62 => 0x66ab, 0x63 => 0x6b8b,
    0x64 => 0x4ed5, 0x65 => 0x4ed4, 0x66 => 0x4f3a, 0x67 => 0x4f7f,
    0x68 => 0x523a, 0x69 => 0x53f8, 0x6a => 0x53f2, 0x6b => 0x55e3,
    0x6c => 0x56db, 0x6d => 0x58eb, 0x6e => 0x59cb, 0x6f => 0x59c9,
    0x70 => 0x59ff, 0x71 => 0x5b50, 0x72 => 0x5c4d, 0x73 => 0x5e02,
    0x74 => 0x5e2b, 0x75 => 0x5fd7, 0x76 => 0x601d, 0x77 => 0x6307,
    0x78 => 0x652f, 0x79 => 0x5b5c, 0x7a => 0x65af, 0x7b => 0x65bd,
    0x7c => 0x65e8, 0x7d => 0x679d, 0x7e => 0x6b62, 0x80 => 0x6b7b,
    0x81 => 0x6c0f, 0x82 => 0x7345, 0x83 => 0x7949, 0x84 => 0x79c1,
    0x85 => 0x7cf8, 0x86 => 0x7d19, 0x87 => 0x7d2b, 0x88 => 0x80a2,
    0x89 => 0x8102, 0x8a => 0x81f3, 0x8b => 0x8996, 0x8c => 0x8a5e,
    0x8d => 0x8a69, 0x8e => 0x8a66, 0x8f => 0x8a8c, 0x90 => 0x8aee,
    0x91 => 0x8cc7, 0x92 => 0x8cdc, 0x93 => 0x96cc, 0x94 => 0x98fc,
    0x95 => 0x6b6f, 0x96 => 0x4e8b, 0x97 => 0x4f3c, 0x98 => 0x4f8d,
    0x99 => 0x5150, 0x9a => 0x5b57, 0x9b => 0x5bfa, 0x9c => 0x6148,
    0x9d => 0x6301, 0x9e => 0x6642, 0x9f => 0x6b21, 0xa0 => 0x6ecb,
    0xa1 => 0x6cbb, 0xa2 => 0x723e, 0xa3 => 0x74bd, 0xa4 => 0x75d4,
    0xa5 => 0x78c1, 0xa6 => 0x793a, 0xa7 => 0x800c, 0xa8 => 0x8033,
    0xa9 => 0x81ea, 0xaa => 0x8494, 0xab => 0x8f9e, 0xac => 0x6c50,
    0xad => 0x9e7f, 0xae => 0x5f0f, 0xaf => 0x8b58, 0xb0 => 0x9d2b,
    0xb1 => 0x7afa, 0xb2 => 0x8ef8, 0xb3 => 0x5b8d, 0xb4 => 0x96eb,
    0xb5 => 0x4e03, 0xb6 => 0x53f1, 0xb7 => 0x57f7, 0xb8 => 0x5931,
    0xb9 => 0x5ac9, 0xba => 0x5ba4, 0xbb => 0x6089, 0xbc => 0x6e7f,
    0xbd => 0x6f06, 0xbe => 0x75be, 0xbf => 0x8cea, 0xc0 => 0x5b9f,
    0xc1 => 0x8500, 0xc2 => 0x7be0, 0xc3 => 0x5072, 0xc4 => 0x67f4,
    0xc5 => 0x829d, 0xc6 => 0x5c61, 0xc7 => 0x854a, 0xc8 => 0x7e1e,
    0xc9 => 0x820e, 0xca => 0x5199, 0xcb => 0x5c04, 0xcc => 0x6368,
    0xcd => 0x8d66, 0xce => 0x659c, 0xcf => 0x716e, 0xd0 => 0x793e,
    0xd1 => 0x7d17, 0xd2 => 0x8005, 0xd3 => 0x8b1d, 0xd4 => 0x8eca,
    0xd5 => 0x906e, 0xd6 => 0x86c7, 0xd7 => 0x90aa, 0xd8 => 0x501f,
    0xd9 => 0x52fa, 0xda => 0x5c3a, 0xdb => 0x6753, 0xdc => 0x707c,
    0xdd => 0x7235, 0xde => 0x914c, 0xdf => 0x91c8, 0xe0 => 0x932b,
    0xe1 => 0x82e5, 0xe2 => 0x5bc2, 0xe3 => 0x5f31, 0xe4 => 0x60f9,
    0xe5 => 0x4e3b, 0xe6 => 0x53d6, 0xe7 => 0x5b88, 0xe8 => 0x624b,
    0xe9 => 0x6731, 0xea => 0x6b8a, 0xeb => 0x72e9, 0xec => 0x73e0,
    0xed => 0x7a2e, 0xee => 0x816b, 0xef => 0x8da3, 0xf0 => 0x9152,
    0xf1 => 0x9996, 0xf2 => 0x5112, 0xf3 => 0x53d7, 0xf4 => 0x546a,
    0xf5 => 0x5bff, 0xf6 => 0x6388, 0xf7 => 0x6a39, 0xf8 => 0x7dac,
    0xf9 => 0x9700, 0xfa => 0x56da, 0xfb => 0x53ce, 0xfc => 0x5468,
  },
  0x8f => {
    0x40 => 0x5b97, 0x41 => 0x5c31, 0x42 => 0x5dde, 0x43 => 0x4fee,
    0x44 => 0x6101, 0x45 => 0x62fe, 0x46 => 0x6d32, 0x47 => 0x79c0,
    0x48 => 0x79cb, 0x49 => 0x7d42, 0x4a => 0x7e4d, 0x4b => 0x7fd2,
    0x4c => 0x81ed, 0x4d => 0x821f, 0x4e => 0x8490, 0x4f => 0x8846,
    0x50 => 0x8972, 0x51 => 0x8b90, 0x52 => 0x8e74, 0x53 => 0x8f2f,
    0x54 => 0x9031, 0x55 => 0x914b, 0x56 => 0x916c, 0x57 => 0x96c6,
    0x58 => 0x919c, 0x59 => 0x4ec0, 0x5a => 0x4f4f, 0x5b => 0x5145,
    0x5c => 0x5341, 0x5d => 0x5f93, 0x5e => 0x620e, 0x5f => 0x67d4,
    0x60 => 0x6c41, 0x61 => 0x6e0b, 0x62 => 0x7363, 0x63 => 0x7e26,
    0x64 => 0x91cd, 0x65 => 0x9283, 0x66 => 0x53d4, 0x67 => 0x5919,
    0x68 => 0x5bbf, 0x69 => 0x6dd1, 0x6a => 0x795d, 0x6b => 0x7e2e,
    0x6c => 0x7c9b, 0x6d => 0x587e, 0x6e => 0x719f, 0x6f => 0x51fa,
    0x70 => 0x8853, 0x71 => 0x8ff0, 0x72 => 0x4fca, 0x73 => 0x5cfb,
    0x74 => 0x6625, 0x75 => 0x77ac, 0x76 => 0x7ae3, 0x77 => 0x821c,
    0x78 => 0x99ff, 0x79 => 0x51c6, 0x7a => 0x5faa, 0x7b => 0x65ec,
    0x7c => 0x696f, 0x7d => 0x6b89, 0x7e => 0x6df3, 0x80 => 0x6e96,
    0x81 => 0x6f64, 0x82 => 0x76fe, 0x83 => 0x7d14, 0x84 => 0x5de1,
    0x85 => 0x9075, 0x86 => 0x9187, 0x87 => 0x9806, 0x88 => 0x51e6,
    0x89 => 0x521d, 0x8a => 0x6240, 0x8b => 0x6691, 0x8c => 0x66d9,
    0x8d => 0x6e1a, 0x8e => 0x5eb6, 0x8f => 0x7dd2, 0x90 => 0x7f72,
    0x91 => 0x66f8, 0x92 => 0x85af, 0x93 => 0x85f7, 0x94 => 0x8af8,
    0x95 => 0x52a9, 0x96 => 0x53d9, 0x97 => 0x5973, 0x98 => 0x5e8f,
    0x99 => 0x5f90, 0x9a => 0x6055, 0x9b => 0x92e4, 0x9c => 0x9664,
    0x9d => 0x50b7, 0x9e => 0x511f, 0x9f => 0x52dd, 0xa0 => 0x5320,
    0xa1 => 0x5347, 0xa2 => 0x53ec, 0xa3 => 0x54e8, 0xa4 => 0x5546,
    0xa5 => 0x5531, 0xa6 => 0x5617, 0xa7 => 0x5968, 0xa8 => 0x59be,
    0xa9 => 0x5a3c, 0xaa => 0x5bb5, 0xab => 0x5c06, 0xac => 0x5c0f,
    0xad => 0x5c11, 0xae => 0x5c1a, 0xaf => 0x5e84, 0xb0 => 0x5e8a,
    0xb1 => 0x5ee0, 0xb2 => 0x5f70, 0xb3 => 0x627f, 0xb4 => 0x6284,
    0xb5 => 0x62db, 0xb6 => 0x638c, 0xb7 => 0x6377, 0xb8 => 0x6607,
    0xb9 => 0x660c, 0xba => 0x662d, 0xbb => 0x6676, 0xbc => 0x677e,
    0xbd => 0x68a2, 0xbe => 0x6a1f, 0xbf => 0x6a35, 0xc0 => 0x6cbc,
    0xc1 => 0x6d88, 0xc2 => 0x6e09, 0xc3 => 0x6e58, 0xc4 => 0x713c,
    0xc5 => 0x7126, 0xc6 => 0x7167, 0xc7 => 0x75c7, 0xc8 => 0x7701,
    0xc9 => 0x785d, 0xca => 0x7901, 0xcb => 0x7965, 0xcc => 0x79f0,
    0xcd => 0x7ae0, 0xce => 0x7b11, 0xcf => 0x7ca7, 0xd0 => 0x7d39,
    0xd1 => 0x8096, 0xd2 => 0x83d6, 0xd3 => 0x848b, 0xd4 => 0x8549,
    0xd5 => 0x885d, 0xd6 => 0x88f3, 0xd7 => 0x8a1f, 0xd8 => 0x8a3c,
    0xd9 => 0x8a54, 0xda => 0x8a73, 0xdb => 0x8c61, 0xdc => 0x8cde,
    0xdd => 0x91a4, 0xde => 0x9266, 0xdf => 0x937e, 0xe0 => 0x9418,
    0xe1 => 0x969c, 0xe2 => 0x9798, 0xe3 => 0x4e0a, 0xe4 => 0x4e08,
    0xe5 => 0x4e1e, 0xe6 => 0x4e57, 0xe7 => 0x5197, 0xe8 => 0x5270,
    0xe9 => 0x57ce, 0xea => 0x5834, 0xeb => 0x58cc, 0xec => 0x5b22,
    0xed => 0x5e38, 0xee => 0x60c5, 0xef => 0x64fe, 0xf0 => 0x6761,
    0xf1 => 0x6756, 0xf2 => 0x6d44, 0xf3 => 0x72b6, 0xf4 => 0x7573,
    0xf5 => 0x7a63, 0xf6 => 0x84b8, 0xf7 => 0x8b72, 0xf8 => 0x91b8,
    0xf9 => 0x9320, 0xfa => 0x5631, 0xfb => 0x57f4, 0xfc => 0x98fe,
  },
  0x90 => {
    0x40 => 0x62ed, 0x41 => 0x690d, 0x42 => 0x6b96, 0x43 => 0x71ed,
    0x44 => 0x7e54, 0x45 => 0x8077, 0x46 => 0x8272, 0x47 => 0x89e6,
    0x48 => 0x98df, 0x49 => 0x8755, 0x4a => 0x8fb1, 0x4b => 0x5c3b,
    0x4c => 0x4f38, 0x4d => 0x4fe1, 0x4e => 0x4fb5, 0x4f => 0x5507,
    0x50 => 0x5a20, 0x51 => 0x5bdd, 0x52 => 0x5be9, 0x53 => 0x5fc3,
    0x54 => 0x614e, 0x55 => 0x632f, 0x56 => 0x65b0, 0x57 => 0x664b,
    0x58 => 0x68ee, 0x59 => 0x699b, 0x5a => 0x6d78, 0x5b => 0x6df1,
    0x5c => 0x7533, 0x5d => 0x75b9, 0x5e => 0x771f, 0x5f => 0x795e,
    0x60 => 0x79e6, 0x61 => 0x7d33, 0x62 => 0x81e3, 0x63 => 0x82af,
    0x64 => 0x85aa, 0x65 => 0x89aa, 0x66 => 0x8a3a, 0x67 => 0x8eab,
    0x68 => 0x8f9b, 0x69 => 0x9032, 0x6a => 0x91dd, 0x6b => 0x9707,
    0x6c => 0x4eba, 0x6d => 0x4ec1, 0x6e => 0x5203, 0x6f => 0x5875,
    0x70 => 0x58ec, 0x71 => 0x5c0b, 0x72 => 0x751a, 0x73 => 0x5c3d,
    0x74 => 0x814e, 0x75 => 0x8a0a, 0x76 => 0x8fc5, 0x77 => 0x9663,
    0x78 => 0x976d, 0x79 => 0x7b25, 0x7a => 0x8acf, 0x7b => 0x9808,
    0x7c => 0x9162, 0x7d => 0x56f3, 0x7e => 0x53a8, 0x80 => 0x9017,
    0x81 => 0x5439, 0x82 => 0x5782, 0x83 => 0x5e25, 0x84 => 0x63a8,
    0x85 => 0x6c34, 0x86 => 0x708a, 0x87 => 0x7761, 0x88 => 0x7c8b,
    0x89 => 0x7fe0, 0x8a => 0x8870, 0x8b => 0x9042, 0x8c => 0x9154,
    0x8d => 0x9310, 0x8e => 0x9318, 0x8f => 0x968f, 0x90 => 0x745e,
    0x91 => 0x9ac4, 0x92 => 0x5d07, 0x93 => 0x5d69, 0x94 => 0x6570,
    0x95 => 0x67a2, 0x96 => 0x8da8, 0x97 => 0x96db, 0x98 => 0x636e,
    0x99 => 0x6749, 0x9a => 0x6919, 0x9b => 0x83c5, 0x9c => 0x9817,
    0x9d => 0x96c0, 0x9e => 0x88fe, 0x9f => 0x6f84, 0xa0 => 0x647a,
    0xa1 => 0x5bf8, 0xa2 => 0x4e16, 0xa3 => 0x702c, 0xa4 => 0x755d,
    0xa5 => 0x662f, 0xa6 => 0x51c4, 0xa7 => 0x5236, 0xa8 => 0x52e2,
    0xa9 => 0x59d3, 0xaa => 0x5f81, 0xab => 0x6027, 0xac => 0x6210,
    0xad => 0x653f, 0xae => 0x6574, 0xaf => 0x661f, 0xb0 => 0x6674,
    0xb1 => 0x68f2, 0xb2 => 0x6816, 0xb3 => 0x6b63, 0xb4 => 0x6e05,
    0xb5 => 0x7272, 0xb6 => 0x751f, 0xb7 => 0x76db, 0xb8 => 0x7cbe,
    0xb9 => 0x8056, 0xba => 0x58f0, 0xbb => 0x88fd, 0xbc => 0x897f,
    0xbd => 0x8aa0, 0xbe => 0x8a93, 0xbf => 0x8acb, 0xc0 => 0x901d,
    0xc1 => 0x9192, 0xc2 => 0x9752, 0xc3 => 0x9759, 0xc4 => 0x6589,
    0xc5 => 0x7a0e, 0xc6 => 0x8106, 0xc7 => 0x96bb, 0xc8 => 0x5e2d,
    0xc9 => 0x60dc, 0xca => 0x621a, 0xcb => 0x65a5, 0xcc => 0x6614,
    0xcd => 0x6790, 0xce => 0x77f3, 0xcf => 0x7a4d, 0xd0 => 0x7c4d,
    0xd1 => 0x7e3e, 0xd2 => 0x810a, 0xd3 => 0x8cac, 0xd4 => 0x8d64,
    0xd5 => 0x8de1, 0xd6 => 0x8e5f, 0xd7 => 0x78a9, 0xd8 => 0x5207,
    0xd9 => 0x62d9, 0xda => 0x63a5, 0xdb => 0x6442, 0xdc => 0x6298,
    0xdd => 0x8a2d, 0xde => 0x7a83, 0xdf => 0x7bc0, 0xe0 => 0x8aac,
    0xe1 => 0x96ea, 0xe2 => 0x7d76, 0xe3 => 0x820c, 0xe4 => 0x8749,
    0xe5 => 0x4ed9, 0xe6 => 0x5148, 0xe7 => 0x5343, 0xe8 => 0x5360,
    0xe9 => 0x5ba3, 0xea => 0x5c02, 0xeb => 0x5c16, 0xec => 0x5ddd,
    0xed => 0x6226, 0xee => 0x6247, 0xef => 0x64b0, 0xf0 => 0x6813,
    0xf1 => 0x6834, 0xf2 => 0x6cc9, 0xf3 => 0x6d45, 0xf4 => 0x6d17,
    0xf5 => 0x67d3, 0xf6 => 0x6f5c, 0xf7 => 0x714e, 0xf8 => 0x717d,
    0xf9 => 0x65cb, 0xfa => 0x7a7f, 0xfb => 0x7bad, 0xfc => 0x7dda,
  },
  0x91 => {
    0x40 => 0x7e4a, 0x41 => 0x7fa8, 0x42 => 0x817a, 0x43 => 0x821b,
    0x44 => 0x8239, 0x45 => 0x85a6, 0x46 => 0x8a6e, 0x47 => 0x8cce,
    0x48 => 0x8df5, 0x49 => 0x9078, 0x4a => 0x9077, 0x4b => 0x92ad,
    0x4c => 0x9291, 0x4d => 0x9583, 0x4e => 0x9bae, 0x4f => 0x524d,
    0x50 => 0x5584, 0x51 => 0x6f38, 0x52 => 0x7136, 0x53 => 0x5168,
    0x54 => 0x7985, 0x55 => 0x7e55, 0x56 => 0x81b3, 0x57 => 0x7cce,
    0x58 => 0x564c, 0x59 => 0x5851, 0x5a => 0x5ca8, 0x5b => 0x63aa,
    0x5c => 0x66fe, 0x5d => 0x66fd, 0x5e => 0x695a, 0x5f => 0x72d9,
    0x60 => 0x758f, 0x61 => 0x758e, 0x62 => 0x790e, 0x63 => 0x7956,
    0x64 => 0x79df, 0x65 => 0x7c97, 0x66 => 0x7d20, 0x67 => 0x7d44,
    0x68 => 0x8607, 0x69 => 0x8a34, 0x6a => 0x963b, 0x6b => 0x9061,
    0x6c => 0x9f20, 0x6d => 0x50e7, 0x6e => 0x5275, 0x6f => 0x53cc,
    0x70 => 0x53e2, 0x71 => 0x5009, 0x72 => 0x55aa, 0x73 => 0x58ee,
    0x74 => 0x594f, 0x75 => 0x723d, 0x76 => 0x5b8b, 0x77 => 0x5c64,
    0x78 => 0x531d, 0x79 => 0x60e3, 0x7a => 0x60f3, 0x7b => 0x635c,
    0x7c => 0x6383, 0x7d => 0x633f, 0x7e => 0x63bb, 0x80 => 0x64cd,
    0x81 => 0x65e9, 0x82 => 0x66f9, 0x83 => 0x5de3, 0x84 => 0x69cd,
    0x85 => 0x69fd, 0x86 => 0x6f15, 0x87 => 0x71e5, 0x88 => 0x4e89,
    0x89 => 0x75e9, 0x8a => 0x76f8, 0x8b => 0x7a93, 0x8c => 0x7cdf,
    0x8d => 0x7dcf, 0x8e => 0x7d9c, 0x8f => 0x8061, 0x90 => 0x8349,
    0x91 => 0x8358, 0x92 => 0x846c, 0x93 => 0x84bc, 0x94 => 0x85fb,
    0x95 => 0x88c5, 0x96 => 0x8d70, 0x97 => 0x9001, 0x98 => 0x906d,
    0x99 => 0x9397, 0x9a => 0x971c, 0x9b => 0x9a12, 0x9c => 0x50cf,
    0x9d => 0x5897, 0x9e => 0x618e, 0x9f => 0x81d3, 0xa0 => 0x8535,
    0xa1 => 0x8d08, 0xa2 => 0x9020, 0xa3 => 0x4fc3, 0xa4 => 0x5074,
    0xa5 => 0x5247, 0xa6 => 0x5373, 0xa7 => 0x606f, 0xa8 => 0x6349,
    0xa9 => 0x675f, 0xaa => 0x6e2c, 0xab => 0x8db3, 0xac => 0x901f,
    0xad => 0x4fd7, 0xae => 0x5c5e, 0xaf => 0x8cca, 0xb0 => 0x65cf,
    0xb1 => 0x7d9a, 0xb2 => 0x5352, 0xb3 => 0x8896, 0xb4 => 0x5176,
    0xb5 => 0x63c3, 0xb6 => 0x5b58, 0xb7 => 0x5b6b, 0xb8 => 0x5c0a,
    0xb9 => 0x640d, 0xba => 0x6751, 0xbb => 0x905c, 0xbc => 0x4ed6,
    0xbd => 0x591a, 0xbe => 0x592a, 0xbf => 0x6c70, 0xc0 => 0x8a51,
    0xc1 => 0x553e, 0xc2 => 0x5815, 0xc3 => 0x59a5, 0xc4 => 0x60f0,
    0xc5 => 0x6253, 0xc6 => 0x67c1, 0xc7 => 0x8235, 0xc8 => 0x6955,
    0xc9 => 0x9640, 0xca => 0x99c4, 0xcb => 0x9a28, 0xcc => 0x4f53,
    0xcd => 0x5806, 0xce => 0x5bfe, 0xcf => 0x8010, 0xd0 => 0x5cb1,
    0xd1 => 0x5e2f, 0xd2 => 0x5f85, 0xd3 => 0x6020, 0xd4 => 0x614b,
    0xd5 => 0x6234, 0xd6 => 0x66ff, 0xd7 => 0x6cf0, 0xd8 => 0x6ede,
    0xd9 => 0x80ce, 0xda => 0x817f, 0xdb => 0x82d4, 0xdc => 0x888b,
    0xdd => 0x8cb8, 0xde => 0x9000, 0xdf => 0x902e, 0xe0 => 0x968a,
    0xe1 => 0x9edb, 0xe2 => 0x9bdb, 0xe3 => 0x4ee3, 0xe4 => 0x53f0,
    0xe5 => 0x5927, 0xe6 => 0x7b2c, 0xe7 => 0x918d, 0xe8 => 0x984c,
    0xe9 => 0x9df9, 0xea => 0x6edd, 0xeb => 0x7027, 0xec => 0x5353,
    0xed => 0x5544, 0xee => 0x5b85, 0xef => 0x6258, 0xf0 => 0x629e,
    0xf1 => 0x62d3, 0xf2 => 0x6ca2, 0xf3 => 0x6fef, 0xf4 => 0x7422,
    0xf5 => 0x8a17, 0xf6 => 0x9438, 0xf7 => 0x6fc1, 0xf8 => 0x8afe,
    0xf9 => 0x8338, 0xfa => 0x51e7, 0xfb => 0x86f8, 0xfc => 0x53ea,
  },
  0x92 => {
    0x40 => 0x53e9, 0x41 => 0x4f46, 0x42 => 0x9054, 0x43 => 0x8fb0,
    0x44 => 0x596a, 0x45 => 0x8131, 0x46 => 0x5dfd, 0x47 => 0x7aea,
    0x48 => 0x8fbf, 0x49 => 0x68da, 0x4a => 0x8c37, 0x4b => 0x72f8,
    0x4c => 0x9c48, 0x4d => 0x6a3d, 0x4e => 0x8ab0, 0x4f => 0x4e39,
    0x50 => 0x5358, 0x51 => 0x5606, 0x52 => 0x5766, 0x53 => 0x62c5,
    0x54 => 0x63a2, 0x55 => 0x65e6, 0x56 => 0x6b4e, 0x57 => 0x6de1,
    0x58 => 0x6e5b, 0x59 => 0x70ad, 0x5a => 0x77ed, 0x5b => 0x7aef,
    0x5c => 0x7baa, 0x5d => 0x7dbb, 0x5e => 0x803d, 0x5f => 0x80c6,
    0x60 => 0x86cb, 0x61 => 0x8a95, 0x62 => 0x935b, 0x63 => 0x56e3,
    0x64 => 0x58c7, 0x65 => 0x5f3e, 0x66 => 0x65ad, 0x67 => 0x6696,
    0x68 => 0x6a80, 0x69 => 0x6bb5, 0x6a => 0x7537, 0x6b => 0x8ac7,
    0x6c => 0x5024, 0x6d => 0x77e5, 0x6e => 0x5730, 0x6f => 0x5f1b,
    0x70 => 0x6065, 0x71 => 0x667a, 0x72 => 0x6c60, 0x73 => 0x75f4,
    0x74 => 0x7a1a, 0x75 => 0x7f6e, 0x76 => 0x81f4, 0x77 => 0x8718,
    0x78 => 0x9045, 0x79 => 0x99b3, 0x7a => 0x7bc9, 0x7b => 0x755c,
    0x7c => 0x7af9, 0x7d => 0x7b51, 0x7e => 0x84c4, 0x80 => 0x9010,
    0x81 => 0x79e9, 0x82 => 0x7a92, 0x83 => 0x8336, 0x84 => 0x5ae1,
    0x85 => 0x7740, 0x86 => 0x4e2d, 0x87 => 0x4ef2, 0x88 => 0x5b99,
    0x89 => 0x5fe0, 0x8a => 0x62bd, 0x8b => 0x663c, 0x8c => 0x67f1,
    0x8d => 0x6ce8, 0x8e => 0x866b, 0x8f => 0x8877, 0x90 => 0x8a3b,
    0x91 => 0x914e, 0x92 => 0x92f3, 0x93 => 0x99d0, 0x94 => 0x6a17,
    0x95 => 0x7026, 0x96 => 0x732a, 0x97 => 0x82e7, 0x98 => 0x8457,
    0x99 => 0x8caf, 0x9a => 0x4e01, 0x9b => 0x5146, 0x9c => 0x51cb,
    0x9d => 0x558b, 0x9e => 0x5bf5, 0x9f => 0x5e16, 0xa0 => 0x5e33,
    0xa1 => 0x5e81, 0xa2 => 0x5f14, 0xa3 => 0x5f35, 0xa4 => 0x5f6b,
    0xa5 => 0x5fb4, 0xa6 => 0x61f2, 0xa7 => 0x6311, 0xa8 => 0x66a2,
    0xa9 => 0x671d, 0xaa => 0x6f6e, 0xab => 0x7252, 0xac => 0x753a,
    0xad => 0x773a, 0xae => 0x8074, 0xaf => 0x8139, 0xb0 => 0x8178,
    0xb1 => 0x8776, 0xb2 => 0x8abf, 0xb3 => 0x8adc, 0xb4 => 0x8d85,
    0xb5 => 0x8df3, 0xb6 => 0x929a, 0xb7 => 0x9577, 0xb8 => 0x9802,
    0xb9 => 0x9ce5, 0xba => 0x52c5, 0xbb => 0x6357, 0xbc => 0x76f4,
    0xbd => 0x6715, 0xbe => 0x6c88, 0xbf => 0x73cd, 0xc0 => 0x8cc3,
    0xc1 => 0x93ae, 0xc2 => 0x9673, 0xc3 => 0x6d25, 0xc4 => 0x589c,
    0xc5 => 0x690e, 0xc6 => 0x69cc, 0xc7 => 0x8ffd, 0xc8 => 0x939a,
    0xc9 => 0x75db, 0xca => 0x901a, 0xcb => 0x585a, 0xcc => 0x6802,
    0xcd => 0x63b4, 0xce => 0x69fb, 0xcf => 0x4f43, 0xd0 => 0x6f2c,
    0xd1 => 0x67d8, 0xd2 => 0x8fbb, 0xd3 => 0x8526, 0xd4 => 0x7db4,
    0xd5 => 0x9354, 0xd6 => 0x693f, 0xd7 => 0x6f70, 0xd8 => 0x576a,
    0xd9 => 0x58f7, 0xda => 0x5b2c, 0xdb => 0x7d2c, 0xdc => 0x722a,
    0xdd => 0x540a, 0xde => 0x91e3, 0xdf => 0x9db4, 0xe0 => 0x4ead,
    0xe1 => 0x4f4e, 0xe2 => 0x505c, 0xe3 => 0x5075, 0xe4 => 0x5243,
    0xe5 => 0x8c9e, 0xe6 => 0x5448, 0xe7 => 0x5824, 0xe8 => 0x5b9a,
    0xe9 => 0x5e1d, 0xea => 0x5e95, 0xeb => 0x5ead, 0xec => 0x5ef7,
    0xed => 0x5f1f, 0xee => 0x608c, 0xef => 0x62b5, 0xf0 => 0x633a,
    0xf1 => 0x63d0, 0xf2 => 0x68af, 0xf3 => 0x6c40, 0xf4 => 0x7887,
    0xf5 => 0x798e, 0xf6 => 0x7a0b, 0xf7 => 0x7de0, 0xf8 => 0x8247,
    0xf9 => 0x8a02, 0xfa => 0x8ae6, 0xfb => 0x8e44, 0xfc => 0x9013,
  },
  0x93 => {
    0x40 => 0x90b8, 0x41 => 0x912d, 0x42 => 0x91d8, 0x43 => 0x9f0e,
    0x44 => 0x6ce5, 0x45 => 0x6458, 0x46 => 0x64e2, 0x47 => 0x6575,
    0x48 => 0x6ef4, 0x49 => 0x7684, 0x4a => 0x7b1b, 0x4b => 0x9069,
    0x4c => 0x93d1, 0x4d => 0x6eba, 0x4e => 0x54f2, 0x4f => 0x5fb9,
    0x50 => 0x64a4, 0x51 => 0x8f4d, 0x52 => 0x8fed, 0x53 => 0x9244,
    0x54 => 0x5178, 0x55 => 0x586b, 0x56 => 0x5929, 0x57 => 0x5c55,
    0x58 => 0x5e97, 0x59 => 0x6dfb, 0x5a => 0x7e8f, 0x5b => 0x751c,
    0x5c => 0x8cbc, 0x5d => 0x8ee2, 0x5e => 0x985b, 0x5f => 0x70b9,
    0x60 => 0x4f1d, 0x61 => 0x6bbf, 0x62 => 0x6fb1, 0x63 => 0x7530,
    0x64 => 0x96fb, 0x65 => 0x514e, 0x66 => 0x5410, 0x67 => 0x5835,
    0x68 => 0x5857, 0x69 => 0x59ac, 0x6a => 0x5c60, 0x6b => 0x5f92,
    0x6c => 0x6597, 0x6d => 0x675c, 0x6e => 0x6e21, 0x6f => 0x767b,
    0x70 => 0x83df, 0x71 => 0x8ced, 0x72 => 0x9014, 0x73 => 0x90fd,
    0x74 => 0x934d, 0x75 => 0x7825, 0x76 => 0x783a, 0x77 => 0x52aa,
    0x78 => 0x5ea6, 0x79 => 0x571f, 0x7a => 0x5974, 0x7b => 0x6012,
    0x7c => 0x5012, 0x7d => 0x515a, 0x7e => 0x51ac, 0x80 => 0x51cd,
    0x81 => 0x5200, 0x82 => 0x5510, 0x83 => 0x5854, 0x84 => 0x5858,
    0x85 => 0x5957, 0x86 => 0x5b95, 0x87 => 0x5cf6, 0x88 => 0x5d8b,
    0x89 => 0x60bc, 0x8a => 0x6295, 0x8b => 0x642d, 0x8c => 0x6771,
    0x8d => 0x6843, 0x8e => 0x68bc, 0x8f => 0x68df, 0x90 => 0x76d7,
    0x91 => 0x6dd8, 0x92 => 0x6e6f, 0x93 => 0x6d9b, 0x94 => 0x706f,
    0x95 => 0x71c8, 0x96 => 0x5f53, 0x97 => 0x75d8, 0x98 => 0x7977,
    0x99 => 0x7b49, 0x9a => 0x7b54, 0x9b => 0x7b52, 0x9c => 0x7cd6,
    0x9d => 0x7d71, 0x9e => 0x5230, 0x9f => 0x8463, 0xa0 => 0x8569,
    0xa1 => 0x85e4, 0xa2 => 0x8a0e, 0xa3 => 0x8b04, 0xa4 => 0x8c46,
    0xa5 => 0x8e0f, 0xa6 => 0x9003, 0xa7 => 0x900f, 0xa8 => 0x9419,
    0xa9 => 0x9676, 0xaa => 0x982d, 0xab => 0x9a30, 0xac => 0x95d8,
    0xad => 0x50cd, 0xae => 0x52d5, 0xaf => 0x540c, 0xb0 => 0x5802,
    0xb1 => 0x5c0e, 0xb2 => 0x61a7, 0xb3 => 0x649e, 0xb4 => 0x6d1e,
    0xb5 => 0x77b3, 0xb6 => 0x7ae5, 0xb7 => 0x80f4, 0xb8 => 0x8404,
    0xb9 => 0x9053, 0xba => 0x9285, 0xbb => 0x5ce0, 0xbc => 0x9d07,
    0xbd => 0x533f, 0xbe => 0x5f97, 0xbf => 0x5fb3, 0xc0 => 0x6d9c,
    0xc1 => 0x7279, 0xc2 => 0x7763, 0xc3 => 0x79bf, 0xc4 => 0x7be4,
    0xc5 => 0x6bd2, 0xc6 => 0x72ec, 0xc7 => 0x8aad, 0xc8 => 0x6803,
    0xc9 => 0x6a61, 0xca => 0x51f8, 0xcb => 0x7a81, 0xcc => 0x6934,
    0xcd => 0x5c4a, 0xce => 0x9cf6, 0xcf => 0x82eb, 0xd0 => 0x5bc5,
    0xd1 => 0x9149, 0xd2 => 0x701e, 0xd3 => 0x5678, 0xd4 => 0x5c6f,
    0xd5 => 0x60c7, 0xd6 => 0x6566, 0xd7 => 0x6c8c, 0xd8 => 0x8c5a,
    0xd9 => 0x9041, 0xda => 0x9813, 0xdb => 0x5451, 0xdc => 0x66c7,
    0xdd => 0x920d, 0xde => 0x5948, 0xdf => 0x90a3, 0xe0 => 0x5185,
    0xe1 => 0x4e4d, 0xe2 => 0x51ea, 0xe3 => 0x8599, 0xe4 => 0x8b0e,
    0xe5 => 0x7058, 0xe6 => 0x637a, 0xe7 => 0x934b, 0xe8 => 0x6962,
    0xe9 => 0x99b4, 0xea => 0x7e04, 0xeb => 0x7577, 0xec => 0x5357,
    0xed => 0x6960, 0xee => 0x8edf, 0xef => 0x96e3, 0xf0 => 0x6c5d,
    0xf1 => 0x4e8c, 0xf2 => 0x5c3c, 0xf3 => 0x5f10, 0xf4 => 0x8fe9,
    0xf5 => 0x5302, 0xf6 => 0x8cd1, 0xf7 => 0x8089, 0xf8 => 0x8679,
    0xf9 => 0x5eff, 0xfa => 0x65e5, 0xfb => 0x4e73, 0xfc => 0x5165,
  },
  0x94 => {
    0x40 => 0x5982, 0x41 => 0x5c3f, 0x42 => 0x97ee, 0x43 => 0x4efb,
    0x44 => 0x598a, 0x45 => 0x5fcd, 0x46 => 0x8a8d, 0x47 => 0x6fe1,
    0x48 => 0x79b0, 0x49 => 0x7962, 0x4a => 0x5be7, 0x4b => 0x8471,
    0x4c => 0x732b, 0x4d => 0x71b1, 0x4e => 0x5e74, 0x4f => 0x5ff5,
    0x50 => 0x637b, 0x51 => 0x649a, 0x52 => 0x71c3, 0x53 => 0x7c98,
    0x54 => 0x4e43, 0x55 => 0x5efc, 0x56 => 0x4e4b, 0x57 => 0x57dc,
    0x58 => 0x56a2, 0x59 => 0x60a9, 0x5a => 0x6fc3, 0x5b => 0x7d0d,
    0x5c => 0x80fd, 0x5d => 0x8133, 0x5e => 0x81bf, 0x5f => 0x8fb2,
    0x60 => 0x8997, 0x61 => 0x86a4, 0x62 => 0x5df4, 0x63 => 0x628a,
    0x64 => 0x64ad, 0x65 => 0x8987, 0x66 => 0x6777, 0x67 => 0x6ce2,
    0x68 => 0x6d3e, 0x69 => 0x7436, 0x6a => 0x7834, 0x6b => 0x5a46,
    0x6c => 0x7f75, 0x6d => 0x82ad, 0x6e => 0x99ac, 0x6f => 0x4ff3,
    0x70 => 0x5ec3, 0x71 => 0x62dd, 0x72 => 0x6392, 0x73 => 0x6557,
    0x74 => 0x676f, 0x75 => 0x76c3, 0x76 => 0x724c, 0x77 => 0x80cc,
    0x78 => 0x80ba, 0x79 => 0x8f29, 0x7a => 0x914d, 0x7b => 0x500d,
    0x7c => 0x57f9, 0x7d => 0x5a92, 0x7e => 0x6885, 0x80 => 0x6973,
    0x81 => 0x7164, 0x82 => 0x72fd, 0x83 => 0x8cb7, 0x84 => 0x58f2,
    0x85 => 0x8ce0, 0x86 => 0x966a, 0x87 => 0x9019, 0x88 => 0x877f,
    0x89 => 0x79e4, 0x8a => 0x77e7, 0x8b => 0x8429, 0x8c => 0x4f2f,
    0x8d => 0x5265, 0x8e => 0x535a, 0x8f => 0x62cd, 0x90 => 0x67cf,
    0x91 => 0x6cca, 0x92 => 0x767d, 0x93 => 0x7b94, 0x94 => 0x7c95,
    0x95 => 0x8236, 0x96 => 0x8584, 0x97 => 0x8feb, 0x98 => 0x66dd,
    0x99 => 0x6f20, 0x9a => 0x7206, 0x9b => 0x7e1b, 0x9c => 0x83ab,
    0x9d => 0x99c1, 0x9e => 0x9ea6, 0x9f => 0x51fd, 0xa0 => 0x7bb1,
    0xa1 => 0x7872, 0xa2 => 0x7bb8, 0xa3 => 0x8087, 0xa4 => 0x7b48,
    0xa5 => 0x6ae8, 0xa6 => 0x5e61, 0xa7 => 0x808c, 0xa8 => 0x7551,
    0xa9 => 0x7560, 0xaa => 0x516b, 0xab => 0x9262, 0xac => 0x6e8c,
    0xad => 0x767a, 0xae => 0x9197, 0xaf => 0x9aea, 0xb0 => 0x4f10,
    0xb1 => 0x7f70, 0xb2 => 0x629c, 0xb3 => 0x7b4f, 0xb4 => 0x95a5,
    0xb5 => 0x9ce9, 0xb6 => 0x567a, 0xb7 => 0x5859, 0xb8 => 0x86e4,
    0xb9 => 0x96bc, 0xba => 0x4f34, 0xbb => 0x5224, 0xbc => 0x534a,
    0xbd => 0x53cd, 0xbe => 0x53db, 0xbf => 0x5e06, 0xc0 => 0x642c,
    0xc1 => 0x6591, 0xc2 => 0x677f, 0xc3 => 0x6c3e, 0xc4 => 0x6c4e,
    0xc5 => 0x7248, 0xc6 => 0x72af, 0xc7 => 0x73ed, 0xc8 => 0x7554,
    0xc9 => 0x7e41, 0xca => 0x822c, 0xcb => 0x85e9, 0xcc => 0x8ca9,
    0xcd => 0x7bc4, 0xce => 0x91c6, 0xcf => 0x7169, 0xd0 => 0x9812,
    0xd1 => 0x98ef, 0xd2 => 0x633d, 0xd3 => 0x6669, 0xd4 => 0x756a,
    0xd5 => 0x76e4, 0xd6 => 0x78d0, 0xd7 => 0x8543, 0xd8 => 0x86ee,
    0xd9 => 0x532a, 0xda => 0x5351, 0xdb => 0x5426, 0xdc => 0x5983,
    0xdd => 0x5e87, 0xde => 0x5f7c, 0xdf => 0x60b2, 0xe0 => 0x6249,
    0xe1 => 0x6279, 0xe2 => 0x62ab, 0xe3 => 0x6590, 0xe4 => 0x6bd4,
    0xe5 => 0x6ccc, 0xe6 => 0x75b2, 0xe7 => 0x76ae, 0xe8 => 0x7891,
    0xe9 => 0x79d8, 0xea => 0x7dcb, 0xeb => 0x7f77, 0xec => 0x80a5,
    0xed => 0x88ab, 0xee => 0x8ab9, 0xef => 0x8cbb, 0xf0 => 0x907f,
    0xf1 => 0x975e, 0xf2 => 0x98db, 0xf3 => 0x6a0b, 0xf4 => 0x7c38,
    0xf5 => 0x5099, 0xf6 => 0x5c3e, 0xf7 => 0x5fae, 0xf8 => 0x6787,
    0xf9 => 0x6bd8, 0xfa => 0x7435, 0xfb => 0x7709, 0xfc => 0x7f8e,
  },
  0x95 => {
    0x40 => 0x9f3b, 0x41 => 0x67ca, 0x42 => 0x7a17, 0x43 => 0x5339,
    0x44 => 0x758b, 0x45 => 0x9aed, 0x46 => 0x5f66, 0x47 => 0x819d,
    0x48 => 0x83f1, 0x49 => 0x8098, 0x4a => 0x5f3c, 0x4b => 0x5fc5,
    0x4c => 0x7562, 0x4d => 0x7b46, 0x4e => 0x903c, 0x4f => 0x6867,
    0x50 => 0x59eb, 0x51 => 0x5a9b, 0x52 => 0x7d10, 0x53 => 0x767e,
    0x54 => 0x8b2c, 0x55 => 0x4ff5, 0x56 => 0x5f6a, 0x57 => 0x6a19,
    0x58 => 0x6c37, 0x59 => 0x6f02, 0x5a => 0x74e2, 0x5b => 0x7968,
    0x5c => 0x8868, 0x5d => 0x8a55, 0x5e => 0x8c79, 0x5f => 0x5edf,
    0x60 => 0x63cf, 0x61 => 0x75c5, 0x62 => 0x79d2, 0x63 => 0x82d7,
    0x64 => 0x9328, 0x65 => 0x92f2, 0x66 => 0x849c, 0x67 => 0x86ed,
    0x68 => 0x9c2d, 0x69 => 0x54c1, 0x6a => 0x5f6c, 0x6b => 0x658c,
    0x6c => 0x6d5c, 0x6d => 0x7015, 0x6e => 0x8ca7, 0x6f => 0x8cd3,
    0x70 => 0x983b, 0x71 => 0x654f, 0x72 => 0x74f6, 0x73 => 0x4e0d,
    0x74 => 0x4ed8, 0x75 => 0x57e0, 0x76 => 0x592b, 0x77 => 0x5a66,
    0x78 => 0x5bcc, 0x79 => 0x51a8, 0x7a => 0x5e03, 0x7b => 0x5e9c,
    0x7c => 0x6016, 0x7d => 0x6276, 0x7e => 0x6577, 0x80 => 0x65a7,
    0x81 => 0x666e, 0x82 => 0x6d6e, 0x83 => 0x7236, 0x84 => 0x7b26,
    0x85 => 0x8150, 0x86 => 0x819a, 0x87 => 0x8299, 0x88 => 0x8b5c,
    0x89 => 0x8ca0, 0x8a => 0x8ce6, 0x8b => 0x8d74, 0x8c => 0x961c,
    0x8d => 0x9644, 0x8e => 0x4fae, 0x8f => 0x64ab, 0x90 => 0x6b66,
    0x91 => 0x821e, 0x92 => 0x8461, 0x93 => 0x856a, 0x94 => 0x90e8,
    0x95 => 0x5c01, 0x96 => 0x6953, 0x97 => 0x98a8, 0x98 => 0x847a,
    0x99 => 0x8557, 0x9a => 0x4f0f, 0x9b => 0x526f, 0x9c => 0x5fa9,
    0x9d => 0x5e45, 0x9e => 0x670d, 0x9f => 0x798f, 0xa0 => 0x8179,
    0xa1 => 0x8907, 0xa2 => 0x8986, 0xa3 => 0x6df5, 0xa4 => 0x5f17,
    0xa5 => 0x6255, 0xa6 => 0x6cb8, 0xa7 => 0x4ecf, 0xa8 => 0x7269,
    0xa9 => 0x9b92, 0xaa => 0x5206, 0xab => 0x543b, 0xac => 0x5674,
    0xad => 0x58b3, 0xae => 0x61a4, 0xaf => 0x626e, 0xb0 => 0x711a,
    0xb1 => 0x596e, 0xb2 => 0x7c89, 0xb3 => 0x7cde, 0xb4 => 0x7d1b,
    0xb5 => 0x96f0, 0xb6 => 0x6587, 0xb7 => 0x805e, 0xb8 => 0x4e19,
    0xb9 => 0x4f75, 0xba => 0x5175, 0xbb => 0x5840, 0xbc => 0x5e63,
    0xbd => 0x5e73, 0xbe => 0x5f0a, 0xbf => 0x67c4, 0xc0 => 0x4e26,
    0xc1 => 0x853d, 0xc2 => 0x9589, 0xc3 => 0x965b, 0xc4 => 0x7c73,
    0xc5 => 0x9801, 0xc6 => 0x50fb, 0xc7 => 0x58c1, 0xc8 => 0x7656,
    0xc9 => 0x78a7, 0xca => 0x5225, 0xcb => 0x77a5, 0xcc => 0x8511,
    0xcd => 0x7b86, 0xce => 0x504f, 0xcf => 0x5909, 0xd0 => 0x7247,
    0xd1 => 0x7bc7, 0xd2 => 0x7de8, 0xd3 => 0x8fba, 0xd4 => 0x8fd4,
    0xd5 => 0x904d, 0xd6 => 0x4fbf, 0xd7 => 0x52c9, 0xd8 => 0x5a29,
    0xd9 => 0x5f01, 0xda => 0x97ad, 0xdb => 0x4fdd, 0xdc => 0x8217,
    0xdd => 0x92ea, 0xde => 0x5703, 0xdf => 0x6355, 0xe0 => 0x6b69,
    0xe1 => 0x752b, 0xe2 => 0x88dc, 0xe3 => 0x8f14, 0xe4 => 0x7a42,
    0xe5 => 0x52df, 0xe6 => 0x5893, 0xe7 => 0x6155, 0xe8 => 0x620a,
    0xe9 => 0x66ae, 0xea => 0x6bcd, 0xeb => 0x7c3f, 0xec => 0x83e9,
    0xed => 0x5023, 0xee => 0x4ff8, 0xef => 0x5305, 0xf0 => 0x5446,
    0xf1 => 0x5831, 0xf2 => 0x5949, 0xf3 => 0x5b9d, 0xf4 => 0x5cf0,
    0xf5 => 0x5cef, 0xf6 => 0x5d29, 0xf7 => 0x5e96, 0xf8 => 0x62b1,
    0xf9 => 0x6367, 0xfa => 0x653e, 0xfb => 0x65b9, 0xfc => 0x670b,
  },
  0x96 => {
    0x40 => 0x6cd5, 0x41 => 0x6ce1, 0x42 => 0x70f9, 0x43 => 0x7832,
    0x44 => 0x7e2b, 0x45 => 0x80de, 0x46 => 0x82b3, 0x47 => 0x840c,
    0x48 => 0x84ec, 0x49 => 0x8702, 0x4a => 0x8912, 0x4b => 0x8a2a,
    0x4c => 0x8c4a, 0x4d => 0x90a6, 0x4e => 0x92d2, 0x4f => 0x98fd,
    0x50 => 0x9cf3, 0x51 => 0x9d6c, 0x52 => 0x4e4f, 0x53 => 0x4ea1,
    0x54 => 0x508d, 0x55 => 0x5256, 0x56 => 0x574a, 0x57 => 0x59a8,
    0x58 => 0x5e3d, 0x59 => 0x5fd8, 0x5a => 0x5fd9, 0x5b => 0x623f,
    0x5c => 0x66b4, 0x5d => 0x671b, 0x5e => 0x67d0, 0x5f => 0x68d2,
    0x60 => 0x5192, 0x61 => 0x7d21, 0x62 => 0x80aa, 0x63 => 0x81a8,
    0x64 => 0x8b00, 0x65 => 0x8c8c, 0x66 => 0x8cbf, 0x67 => 0x927e,
    0x68 => 0x9632, 0x69 => 0x5420, 0x6a => 0x982c, 0x6b => 0x5317,
    0x6c => 0x50d5, 0x6d => 0x535c, 0x6e => 0x58a8, 0x6f => 0x64b2,
    0x70 => 0x6734, 0x71 => 0x7267, 0x72 => 0x7766, 0x73 => 0x7a46,
    0x74 => 0x91e6, 0x75 => 0x52c3, 0x76 => 0x6ca1, 0x77 => 0x6b86,
    0x78 => 0x5800, 0x79 => 0x5e4c, 0x7a => 0x5954, 0x7b => 0x672c,
    0x7c => 0x7ffb, 0x7d => 0x51e1, 0x7e => 0x76c6, 0x80 => 0x6469,
    0x81 => 0x78e8, 0x82 => 0x9b54, 0x83 => 0x9ebb, 0x84 => 0x57cb,
    0x85 => 0x59b9, 0x86 => 0x6627, 0x87 => 0x679a, 0x88 => 0x6bce,
    0x89 => 0x54e9, 0x8a => 0x69d9, 0x8b => 0x5e55, 0x8c => 0x819c,
    0x8d => 0x6795, 0x8e => 0x9baa, 0x8f => 0x67fe, 0x90 => 0x9c52,
    0x91 => 0x685d, 0x92 => 0x4ea6, 0x93 => 0x4fe3, 0x94 => 0x53c8,
    0x95 => 0x62b9, 0x96 => 0x672b, 0x97 => 0x6cab, 0x98 => 0x8fc4,
    0x99 => 0x4fad, 0x9a => 0x7e6d, 0x9b => 0x9ebf, 0x9c => 0x4e07,
    0x9d => 0x6162, 0x9e => 0x6e80, 0x9f => 0x6f2b, 0xa0 => 0x8513,
    0xa1 => 0x5473, 0xa2 => 0x672a, 0xa3 => 0x9b45, 0xa4 => 0x5df3,
    0xa5 => 0x7b95, 0xa6 => 0x5cac, 0xa7 => 0x5bc6, 0xa8 => 0x871c,
    0xa9 => 0x6e4a, 0xaa => 0x84d1, 0xab => 0x7a14, 0xac => 0x8108,
    0xad => 0x5999, 0xae => 0x7c8d, 0xaf => 0x6c11, 0xb0 => 0x7720,
    0xb1 => 0x52d9, 0xb2 => 0x5922, 0xb3 => 0x7121, 0xb4 => 0x725f,
    0xb5 => 0x77db, 0xb6 => 0x9727, 0xb7 => 0x9d61, 0xb8 => 0x690b,
    0xb9 => 0x5a7f, 0xba => 0x5a18, 0xbb => 0x51a5, 0xbc => 0x540d,
    0xbd => 0x547d, 0xbe => 0x660e, 0xbf => 0x76df, 0xc0 => 0x8ff7,
    0xc1 => 0x9298, 0xc2 => 0x9cf4, 0xc3 => 0x59ea, 0xc4 => 0x725d,
    0xc5 => 0x6ec5, 0xc6 => 0x514d, 0xc7 => 0x68c9, 0xc8 => 0x7dbf,
    0xc9 => 0x7dec, 0xca => 0x9762, 0xcb => 0x9eba, 0xcc => 0x6478,
    0xcd => 0x6a21, 0xce => 0x8302, 0xcf => 0x5984, 0xd0 => 0x5b5f,
    0xd1 => 0x6bdb, 0xd2 => 0x731b, 0xd3 => 0x76f2, 0xd4 => 0x7db2,
    0xd5 => 0x8017, 0xd6 => 0x8499, 0xd7 => 0x5132, 0xd8 => 0x6728,
    0xd9 => 0x9ed9, 0xda => 0x76ee, 0xdb => 0x6762, 0xdc => 0x52ff,
    0xdd => 0x9905, 0xde => 0x5c24, 0xdf => 0x623b, 0xe0 => 0x7c7e,
    0xe1 => 0x8cb0, 0xe2 => 0x554f, 0xe3 => 0x60b6, 0xe4 => 0x7d0b,
    0xe5 => 0x9580, 0xe6 => 0x5301, 0xe7 => 0x4e5f, 0xe8 => 0x51b6,
    0xe9 => 0x591c, 0xea => 0x723a, 0xeb => 0x8036, 0xec => 0x91ce,
    0xed => 0x5f25, 0xee => 0x77e2, 0xef => 0x5384, 0xf0 => 0x5f79,
    0xf1 => 0x7d04, 0xf2 => 0x85ac, 0xf3 => 0x8a33, 0xf4 => 0x8e8d,
    0xf5 => 0x9756, 0xf6 => 0x67f3, 0xf7 => 0x85ae, 0xf8 => 0x9453,
    0xf9 => 0x6109, 0xfa => 0x6108, 0xfb => 0x6cb9, 0xfc => 0x7652,
  },
  0x97 => {
    0x40 => 0x8aed, 0x41 => 0x8f38, 0x42 => 0x552f, 0x43 => 0x4f51,
    0x44 => 0x512a, 0x45 => 0x52c7, 0x46 => 0x53cb, 0x47 => 0x5ba5,
    0x48 => 0x5e7d, 0x49 => 0x60a0, 0x4a => 0x6182, 0x4b => 0x63d6,
    0x4c => 0x6709, 0x4d => 0x67da, 0x4e => 0x6e67, 0x4f => 0x6d8c,
    0x50 => 0x7336, 0x51 => 0x7337, 0x52 => 0x7531, 0x53 => 0x7950,
    0x54 => 0x88d5, 0x55 => 0x8a98, 0x56 => 0x904a, 0x57 => 0x9091,
    0x58 => 0x90f5, 0x59 => 0x96c4, 0x5a => 0x878d, 0x5b => 0x5915,
    0x5c => 0x4e88, 0x5d => 0x4f59, 0x5e => 0x4e0e, 0x5f => 0x8a89,
    0x60 => 0x8f3f, 0x61 => 0x9810, 0x62 => 0x50ad, 0x63 => 0x5e7c,
    0x64 => 0x5996, 0x65 => 0x5bb9, 0x66 => 0x5eb8, 0x67 => 0x63da,
    0x68 => 0x63fa, 0x69 => 0x64c1, 0x6a => 0x66dc, 0x6b => 0x694a,
    0x6c => 0x69d8, 0x6d => 0x6d0b, 0x6e => 0x6eb6, 0x6f => 0x7194,
    0x70 => 0x7528, 0x71 => 0x7aaf, 0x72 => 0x7f8a, 0x73 => 0x8000,
    0x74 => 0x8449, 0x75 => 0x84c9, 0x76 => 0x8981, 0x77 => 0x8b21,
    0x78 => 0x8e0a, 0x79 => 0x9065, 0x7a => 0x967d, 0x7b => 0x990a,
    0x7c => 0x617e, 0x7d => 0x6291, 0x7e => 0x6b32, 0x80 => 0x6c83,
    0x81 => 0x6d74, 0x82 => 0x7fcc, 0x83 => 0x7ffc, 0x84 => 0x6dc0,
    0x85 => 0x7f85, 0x86 => 0x87ba, 0x87 => 0x88f8, 0x88 => 0x6765,
    0x89 => 0x83b1, 0x8a => 0x983c, 0x8b => 0x96f7, 0x8c => 0x6d1b,
    0x8d => 0x7d61, 0x8e => 0x843d, 0x8f => 0x916a, 0x90 => 0x4e71,
    0x91 => 0x5375, 0x92 => 0x5d50, 0x93 => 0x6b04, 0x94 => 0x6feb,
    0x95 => 0x85cd, 0x96 => 0x862d, 0x97 => 0x89a7, 0x98 => 0x5229,
    0x99 => 0x540f, 0x9a => 0x5c65, 0x9b => 0x674e, 0x9c => 0x68a8,
    0x9d => 0x7406, 0x9e => 0x7483, 0x9f => 0x75e2, 0xa0 => 0x88cf,
    0xa1 => 0x88e1, 0xa2 => 0x91cc, 0xa3 => 0x96e2, 0xa4 => 0x9678,
    0xa5 => 0x5f8b, 0xa6 => 0x7387, 0xa7 => 0x7acb, 0xa8 => 0x844e,
    0xa9 => 0x63a0, 0xaa => 0x7565, 0xab => 0x5289, 0xac => 0x6d41,
    0xad => 0x6e9c, 0xae => 0x7409, 0xaf => 0x7559, 0xb0 => 0x786b,
    0xb1 => 0x7c92, 0xb2 => 0x9686, 0xb3 => 0x7adc, 0xb4 => 0x9f8d,
    0xb5 => 0x4fb6, 0xb6 => 0x616e, 0xb7 => 0x65c5, 0xb8 => 0x865c,
    0xb9 => 0x4e86, 0xba => 0x4eae, 0xbb => 0x50da, 0xbc => 0x4e21,
    0xbd => 0x51cc, 0xbe => 0x5bee, 0xbf => 0x6599, 0xc0 => 0x6881,
    0xc1 => 0x6dbc, 0xc2 => 0x731f, 0xc3 => 0x7642, 0xc4 => 0x77ad,
    0xc5 => 0x7a1c, 0xc6 => 0x7ce7, 0xc7 => 0x826f, 0xc8 => 0x8ad2,
    0xc9 => 0x907c, 0xca => 0x91cf, 0xcb => 0x9675, 0xcc => 0x9818,
    0xcd => 0x529b, 0xce => 0x7dd1, 0xcf => 0x502b, 0xd0 => 0x5398,
    0xd1 => 0x6797, 0xd2 => 0x6dcb, 0xd3 => 0x71d0, 0xd4 => 0x7433,
    0xd5 => 0x81e8, 0xd6 => 0x8f2a, 0xd7 => 0x96a3, 0xd8 => 0x9c57,
    0xd9 => 0x9e9f, 0xda => 0x7460, 0xdb => 0x5841, 0xdc => 0x6d99,
    0xdd => 0x7d2f, 0xde => 0x985e, 0xdf => 0x4ee4, 0xe0 => 0x4f36,
    0xe1 => 0x4f8b, 0xe2 => 0x51b7, 0xe3 => 0x52b1, 0xe4 => 0x5dba,
    0xe5 => 0x601c, 0xe6 => 0x73b2, 0xe7 => 0x793c, 0xe8 => 0x82d3,
    0xe9 => 0x9234, 0xea => 0x96b7, 0xeb => 0x96f6, 0xec => 0x970a,
    0xed => 0x9e97, 0xee => 0x9f62, 0xef => 0x66a6, 0xf0 => 0x6b74,
    0xf1 => 0x5217, 0xf2 => 0x52a3, 0xf3 => 0x70c8, 0xf4 => 0x88c2,
    0xf5 => 0x5ec9, 0xf6 => 0x604b, 0xf7 => 0x6190, 0xf8 => 0x6f23,
    0xf9 => 0x7149, 0xfa => 0x7c3e, 0xfb => 0x7df4, 0xfc => 0x806f,
  },
  0x98 => {
    0x40 => 0x84ee, 0x41 => 0x9023, 0x42 => 0x932c, 0x43 => 0x5442,
    0x44 => 0x9b6f, 0x45 => 0x6ad3, 0x46 => 0x7089, 0x47 => 0x8cc2,
    0x48 => 0x8def, 0x49 => 0x9732, 0x4a => 0x52b4, 0x4b => 0x5a41,
    0x4c => 0x5eca, 0x4d => 0x5f04, 0x4e => 0x6717, 0x4f => 0x697c,
    0x50 => 0x6994, 0x51 => 0x6d6a, 0x52 => 0x6f0f, 0x53 => 0x7262,
    0x54 => 0x72fc, 0x55 => 0x7bed, 0x56 => 0x8001, 0x57 => 0x807e,
    0x58 => 0x874b, 0x59 => 0x90ce, 0x5a => 0x516d, 0x5b => 0x9e93,
    0x5c => 0x7984, 0x5d => 0x808b, 0x5e => 0x9332, 0x5f => 0x8ad6,
    0x60 => 0x502d, 0x61 => 0x548c, 0x62 => 0x8a71, 0x63 => 0x6b6a,
    0x64 => 0x8cc4, 0x65 => 0x8107, 0x66 => 0x60d1, 0x67 => 0x67a0,
    0x68 => 0x9df2, 0x69 => 0x4e99, 0x6a => 0x4e98, 0x6b => 0x9c10,
    0x6c => 0x8a6b, 0x6d => 0x85c1, 0x6e => 0x8568, 0x6f => 0x6900,
    0x70 => 0x6e7e, 0x71 => 0x7897, 0x72 => 0x8155, 0x9f => 0x5f0c,
    0xa0 => 0x4e10, 0xa1 => 0x4e15, 0xa2 => 0x4e2a, 0xa3 => 0x4e31,
    0xa4 => 0x4e36, 0xa5 => 0x4e3c, 0xa6 => 0x4e3f, 0xa7 => 0x4e42,
    0xa8 => 0x4e56, 0xa9 => 0x4e58, 0xaa => 0x4e82, 0xab => 0x4e85,
    0xac => 0x8c6b, 0xad => 0x4e8a, 0xae => 0x8212, 0xaf => 0x5f0d,
    0xb0 => 0x4e8e, 0xb1 => 0x4e9e, 0xb2 => 0x4e9f, 0xb3 => 0x4ea0,
    0xb4 => 0x4ea2, 0xb5 => 0x4eb0, 0xb6 => 0x4eb3, 0xb7 => 0x4eb6,
    0xb8 => 0x4ece, 0xb9 => 0x4ecd, 0xba => 0x4ec4, 0xbb => 0x4ec6,
    0xbc => 0x4ec2, 0xbd => 0x4ed7, 0xbe => 0x4ede, 0xbf => 0x4eed,
    0xc0 => 0x4edf, 0xc1 => 0x4ef7, 0xc2 => 0x4f09, 0xc3 => 0x4f5a,
    0xc4 => 0x4f30, 0xc5 => 0x4f5b, 0xc6 => 0x4f5d, 0xc7 => 0x4f57,
    0xc8 => 0x4f47, 0xc9 => 0x4f76, 0xca => 0x4f88, 0xcb => 0x4f8f,
    0xcc => 0x4f98, 0xcd => 0x4f7b, 0xce => 0x4f69, 0xcf => 0x4f70,
    0xd0 => 0x4f91, 0xd1 => 0x4f6f, 0xd2 => 0x4f86, 0xd3 => 0x4f96,
    0xd4 => 0x5118, 0xd5 => 0x4fd4, 0xd6 => 0x4fdf, 0xd7 => 0x4fce,
    0xd8 => 0x4fd8, 0xd9 => 0x4fdb, 0xda => 0x4fd1, 0xdb => 0x4fda,
    0xdc => 0x4fd0, 0xdd => 0x4fe4, 0xde => 0x4fe5, 0xdf => 0x501a,
    0xe0 => 0x5028, 0xe1 => 0x5014, 0xe2 => 0x502a, 0xe3 => 0x5025,
    0xe4 => 0x5005, 0xe5 => 0x4f1c, 0xe6 => 0x4ff6, 0xe7 => 0x5021,
    0xe8 => 0x5029, 0xe9 => 0x502c, 0xea => 0x4ffe, 0xeb => 0x4fef,
    0xec => 0x5011, 0xed => 0x5006, 0xee => 0x5043, 0xef => 0x5047,
    0xf0 => 0x6703, 0xf1 => 0x5055, 0xf2 => 0x5050, 0xf3 => 0x5048,
    0xf4 => 0x505a, 0xf5 => 0x5056, 0xf6 => 0x506c, 0xf7 => 0x5078,
    0xf8 => 0x5080, 0xf9 => 0x509a, 0xfa => 0x5085, 0xfb => 0x50b4,
    0xfc => 0x50b2,
  },
  0x99 => {
    0x40 => 0x50c9, 0x41 => 0x50ca, 0x42 => 0x50b3, 0x43 => 0x50c2,
    0x44 => 0x50d6, 0x45 => 0x50de, 0x46 => 0x50e5, 0x47 => 0x50ed,
    0x48 => 0x50e3, 0x49 => 0x50ee, 0x4a => 0x50f9, 0x4b => 0x50f5,
    0x4c => 0x5109, 0x4d => 0x5101, 0x4e => 0x5102, 0x4f => 0x5116,
    0x50 => 0x5115, 0x51 => 0x5114, 0x52 => 0x511a, 0x53 => 0x5121,
    0x54 => 0x513a, 0x55 => 0x5137, 0x56 => 0x513c, 0x57 => 0x513b,
    0x58 => 0x513f, 0x59 => 0x5140, 0x5a => 0x5152, 0x5b => 0x514c,
    0x5c => 0x5154, 0x5d => 0x5162, 0x5e => 0x7af8, 0x5f => 0x5169,
    0x60 => 0x516a, 0x61 => 0x516e, 0x62 => 0x5180, 0x63 => 0x5182,
    0x64 => 0x56d8, 0x65 => 0x518c, 0x66 => 0x5189, 0x67 => 0x518f,
    0x68 => 0x5191, 0x69 => 0x5193, 0x6a => 0x5195, 0x6b => 0x5196,
    0x6c => 0x51a4, 0x6d => 0x51a6, 0x6e => 0x51a2, 0x6f => 0x51a9,
    0x70 => 0x51aa, 0x71 => 0x51ab, 0x72 => 0x51b3, 0x73 => 0x51b1,
    0x74 => 0x51b2, 0x75 => 0x51b0, 0x76 => 0x51b5, 0x77 => 0x51bd,
    0x78 => 0x51c5, 0x79 => 0x51c9, 0x7a => 0x51db, 0x7b => 0x51e0,
    0x7c => 0x8655, 0x7d => 0x51e9, 0x7e => 0x51ed, 0x80 => 0x51f0,
    0x81 => 0x51f5, 0x82 => 0x51fe, 0x83 => 0x5204, 0x84 => 0x520b,
    0x85 => 0x5214, 0x86 => 0x520e, 0x87 => 0x5227, 0x88 => 0x522a,
    0x89 => 0x522e, 0x8a => 0x5233, 0x8b => 0x5239, 0x8c => 0x524f,
    0x8d => 0x5244, 0x8e => 0x524b, 0x8f => 0x524c, 0x90 => 0x525e,
    0x91 => 0x5254, 0x92 => 0x526a, 0x93 => 0x5274, 0x94 => 0x5269,
    0x95 => 0x5273, 0x96 => 0x527f, 0x97 => 0x527d, 0x98 => 0x528d,
    0x99 => 0x5294, 0x9a => 0x5292, 0x9b => 0x5271, 0x9c => 0x5288,
    0x9d => 0x5291, 0x9e => 0x8fa8, 0x9f => 0x8fa7, 0xa0 => 0x52ac,
    0xa1 => 0x52ad, 0xa2 => 0x52bc, 0xa3 => 0x52b5, 0xa4 => 0x52c1,
    0xa5 => 0x52cd, 0xa6 => 0x52d7, 0xa7 => 0x52de, 0xa8 => 0x52e3,
    0xa9 => 0x52e6, 0xaa => 0x98ed, 0xab => 0x52e0, 0xac => 0x52f3,
    0xad => 0x52f5, 0xae => 0x52f8, 0xaf => 0x52f9, 0xb0 => 0x5306,
    0xb1 => 0x5308, 0xb2 => 0x7538, 0xb3 => 0x530d, 0xb4 => 0x5310,
    0xb5 => 0x530f, 0xb6 => 0x5315, 0xb7 => 0x531a, 0xb8 => 0x5323,
    0xb9 => 0x532f, 0xba => 0x5331, 0xbb => 0x5333, 0xbc => 0x5338,
    0xbd => 0x5340, 0xbe => 0x5346, 0xbf => 0x5345, 0xc0 => 0x4e17,
    0xc1 => 0x5349, 0xc2 => 0x534d, 0xc3 => 0x51d6, 0xc4 => 0x535e,
    0xc5 => 0x5369, 0xc6 => 0x536e, 0xc7 => 0x5918, 0xc8 => 0x537b,
    0xc9 => 0x5377, 0xca => 0x5382, 0xcb => 0x5396, 0xcc => 0x53a0,
    0xcd => 0x53a6, 0xce => 0x53a5, 0xcf => 0x53ae, 0xd0 => 0x53b0,
    0xd1 => 0x53b6, 0xd2 => 0x53c3, 0xd3 => 0x7c12, 0xd4 => 0x96d9,
    0xd5 => 0x53df, 0xd6 => 0x66fc, 0xd7 => 0x71ee, 0xd8 => 0x53ee,
    0xd9 => 0x53e8, 0xda => 0x53ed, 0xdb => 0x53fa, 0xdc => 0x5401,
    0xdd => 0x543d, 0xde => 0x5440, 0xdf => 0x542c, 0xe0 => 0x542d,
    0xe1 => 0x543c, 0xe2 => 0x542e, 0xe3 => 0x5436, 0xe4 => 0x5429,
    0xe5 => 0x541d, 0xe6 => 0x544e, 0xe7 => 0x548f, 0xe8 => 0x5475,
    0xe9 => 0x548e, 0xea => 0x545f, 0xeb => 0x5471, 0xec => 0x5477,
    0xed => 0x5470, 0xee => 0x5492, 0xef => 0x547b, 0xf0 => 0x5480,
    0xf1 => 0x5476, 0xf2 => 0x5484, 0xf3 => 0x5490, 0xf4 => 0x5486,
    0xf5 => 0x54c7, 0xf6 => 0x54a2, 0xf7 => 0x54b8, 0xf8 => 0x54a5,
    0xf9 => 0x54ac, 0xfa => 0x54c4, 0xfb => 0x54c8, 0xfc => 0x54a8,
  },
  0x9a => {
    0x40 => 0x54ab, 0x41 => 0x54c2, 0x42 => 0x54a4, 0x43 => 0x54be,
    0x44 => 0x54bc, 0x45 => 0x54d8, 0x46 => 0x54e5, 0x47 => 0x54e6,
    0x48 => 0x550f, 0x49 => 0x5514, 0x4a => 0x54fd, 0x4b => 0x54ee,
    0x4c => 0x54ed, 0x4d => 0x54fa, 0x4e => 0x54e2, 0x4f => 0x5539,
    0x50 => 0x5540, 0x51 => 0x5563, 0x52 => 0x554c, 0x53 => 0x552e,
    0x54 => 0x555c, 0x55 => 0x5545, 0x56 => 0x5556, 0x57 => 0x5557,
    0x58 => 0x5538, 0x59 => 0x5533, 0x5a => 0x555d, 0x5b => 0x5599,
    0x5c => 0x5580, 0x5d => 0x54af, 0x5e => 0x558a, 0x5f => 0x559f,
    0x60 => 0x557b, 0x61 => 0x557e, 0x62 => 0x5598, 0x63 => 0x559e,
    0x64 => 0x55ae, 0x65 => 0x557c, 0x66 => 0x5583, 0x67 => 0x55a9,
    0x68 => 0x5587, 0x69 => 0x55a8, 0x6a => 0x55da, 0x6b => 0x55c5,
    0x6c => 0x55df, 0x6d => 0x55c4, 0x6e => 0x55dc, 0x6f => 0x55e4,
    0x70 => 0x55d4, 0x71 => 0x5614, 0x72 => 0x55f7, 0x73 => 0x5616,
    0x74 => 0x55fe, 0x75 => 0x55fd, 0x76 => 0x561b, 0x77 => 0x55f9,
    0x78 => 0x564e, 0x79 => 0x5650, 0x7a => 0x71df, 0x7b => 0x5634,
    0x7c => 0x5636, 0x7d => 0x5632, 0x7e => 0x5638, 0x80 => 0x566b,
    0x81 => 0x5664, 0x82 => 0x562f, 0x83 => 0x566c, 0x84 => 0x566a,
    0x85 => 0x5686, 0x86 => 0x5680, 0x87 => 0x568a, 0x88 => 0x56a0,
    0x89 => 0x5694, 0x8a => 0x568f, 0x8b => 0x56a5, 0x8c => 0x56ae,
    0x8d => 0x56b6, 0x8e => 0x56b4, 0x8f => 0x56c2, 0x90 => 0x56bc,
    0x91 => 0x56c1, 0x92 => 0x56c3, 0x93 => 0x56c0, 0x94 => 0x56c8,
    0x95 => 0x56ce, 0x96 => 0x56d1, 0x97 => 0x56d3, 0x98 => 0x56d7,
    0x99 => 0x56ee, 0x9a => 0x56f9, 0x9b => 0x5700, 0x9c => 0x56ff,
    0x9d => 0x5704, 0x9e => 0x5709, 0x9f => 0x5708, 0xa0 => 0x570b,
    0xa1 => 0x570d, 0xa2 => 0x5713, 0xa3 => 0x5718, 0xa4 => 0x5716,
    0xa5 => 0x55c7, 0xa6 => 0x571c, 0xa7 => 0x5726, 0xa8 => 0x5737,
    0xa9 => 0x5738, 0xaa => 0x574e, 0xab => 0x573b, 0xac => 0x5740,
    0xad => 0x574f, 0xae => 0x5769, 0xaf => 0x57c0, 0xb0 => 0x5788,
    0xb1 => 0x5761, 0xb2 => 0x577f, 0xb3 => 0x5789, 0xb4 => 0x5793,
    0xb5 => 0x57a0, 0xb6 => 0x57b3, 0xb7 => 0x57a4, 0xb8 => 0x57aa,
    0xb9 => 0x57b0, 0xba => 0x57c3, 0xbb => 0x57c6, 0xbc => 0x57d4,
    0xbd => 0x57d2, 0xbe => 0x57d3, 0xbf => 0x580a, 0xc0 => 0x57d6,
    0xc1 => 0x57e3, 0xc2 => 0x580b, 0xc3 => 0x5819, 0xc4 => 0x581d,
    0xc5 => 0x5872, 0xc6 => 0x5821, 0xc7 => 0x5862, 0xc8 => 0x584b,
    0xc9 => 0x5870, 0xca => 0x6bc0, 0xcb => 0x5852, 0xcc => 0x583d,
    0xcd => 0x5879, 0xce => 0x5885, 0xcf => 0x58b9, 0xd0 => 0x589f,
    0xd1 => 0x58ab, 0xd2 => 0x58ba, 0xd3 => 0x58de, 0xd4 => 0x58bb,
    0xd5 => 0x58b8, 0xd6 => 0x58ae, 0xd7 => 0x58c5, 0xd8 => 0x58d3,
    0xd9 => 0x58d1, 0xda => 0x58d7, 0xdb => 0x58d9, 0xdc => 0x58d8,
    0xdd => 0x58e5, 0xde => 0x58dc, 0xdf => 0x58e4, 0xe0 => 0x58df,
    0xe1 => 0x58ef, 0xe2 => 0x58fa, 0xe3 => 0x58f9, 0xe4 => 0x58fb,
    0xe5 => 0x58fc, 0xe6 => 0x58fd, 0xe7 => 0x5902, 0xe8 => 0x590a,
    0xe9 => 0x5910, 0xea => 0x591b, 0xeb => 0x68a6, 0xec => 0x5925,
    0xed => 0x592c, 0xee => 0x592d, 0xef => 0x5932, 0xf0 => 0x5938,
    0xf1 => 0x593e, 0xf2 => 0x7ad2, 0xf3 => 0x5955, 0xf4 => 0x5950,
    0xf5 => 0x594e, 0xf6 => 0x595a, 0xf7 => 0x5958, 0xf8 => 0x5962,
    0xf9 => 0x5960, 0xfa => 0x5967, 0xfb => 0x596c, 0xfc => 0x5969,
  },
  0x9b => {
    0x40 => 0x5978, 0x41 => 0x5981, 0x42 => 0x599d, 0x43 => 0x4f5e,
    0x44 => 0x4fab, 0x45 => 0x59a3, 0x46 => 0x59b2, 0x47 => 0x59c6,
    0x48 => 0x59e8, 0x49 => 0x59dc, 0x4a => 0x598d, 0x4b => 0x59d9,
    0x4c => 0x59da, 0x4d => 0x5a25, 0x4e => 0x5a1f, 0x4f => 0x5a11,
    0x50 => 0x5a1c, 0x51 => 0x5a09, 0x52 => 0x5a1a, 0x53 => 0x5a40,
    0x54 => 0x5a6c, 0x55 => 0x5a49, 0x56 => 0x5a35, 0x57 => 0x5a36,
    0x58 => 0x5a62, 0x59 => 0x5a6a, 0x5a => 0x5a9a, 0x5b => 0x5abc,
    0x5c => 0x5abe, 0x5d => 0x5acb, 0x5e => 0x5ac2, 0x5f => 0x5abd,
    0x60 => 0x5ae3, 0x61 => 0x5ad7, 0x62 => 0x5ae6, 0x63 => 0x5ae9,
    0x64 => 0x5ad6, 0x65 => 0x5afa, 0x66 => 0x5afb, 0x67 => 0x5b0c,
    0x68 => 0x5b0b, 0x69 => 0x5b16, 0x6a => 0x5b32, 0x6b => 0x5ad0,
    0x6c => 0x5b2a, 0x6d => 0x5b36, 0x6e => 0x5b3e, 0x6f => 0x5b43,
    0x70 => 0x5b45, 0x71 => 0x5b40, 0x72 => 0x5b51, 0x73 => 0x5b55,
    0x74 => 0x5b5a, 0x75 => 0x5b5b, 0x76 => 0x5b65, 0x77 => 0x5b69,
    0x78 => 0x5b70, 0x79 => 0x5b73, 0x7a => 0x5b75, 0x7b => 0x5b78,
    0x7c => 0x6588, 0x7d => 0x5b7a, 0x7e => 0x5b80, 0x80 => 0x5b83,
    0x81 => 0x5ba6, 0x82 => 0x5bb8, 0x83 => 0x5bc3, 0x84 => 0x5bc7,
    0x85 => 0x5bc9, 0x86 => 0x5bd4, 0x87 => 0x5bd0, 0x88 => 0x5be4,
    0x89 => 0x5be6, 0x8a => 0x5be2, 0x8b => 0x5bde, 0x8c => 0x5be5,
    0x8d => 0x5beb, 0x8e => 0x5bf0, 0x8f => 0x5bf6, 0x90 => 0x5bf3,
    0x91 => 0x5c05, 0x92 => 0x5c07, 0x93 => 0x5c08, 0x94 => 0x5c0d,
    0x95 => 0x5c13, 0x96 => 0x5c20, 0x97 => 0x5c22, 0x98 => 0x5c28,
    0x99 => 0x5c38, 0x9a => 0x5c39, 0x9b => 0x5c41, 0x9c => 0x5c46,
    0x9d => 0x5c4e, 0x9e => 0x5c53, 0x9f => 0x5c50, 0xa0 => 0x5c4f,
    0xa1 => 0x5b71, 0xa2 => 0x5c6c, 0xa3 => 0x5c6e, 0xa4 => 0x4e62,
    0xa5 => 0x5c76, 0xa6 => 0x5c79, 0xa7 => 0x5c8c, 0xa8 => 0x5c91,
    0xa9 => 0x5c94, 0xaa => 0x599b, 0xab => 0x5cab, 0xac => 0x5cbb,
    0xad => 0x5cb6, 0xae => 0x5cbc, 0xaf => 0x5cb7, 0xb0 => 0x5cc5,
    0xb1 => 0x5cbe, 0xb2 => 0x5cc7, 0xb3 => 0x5cd9, 0xb4 => 0x5ce9,
    0xb5 => 0x5cfd, 0xb6 => 0x5cfa, 0xb7 => 0x5ced, 0xb8 => 0x5d8c,
    0xb9 => 0x5cea, 0xba => 0x5d0b, 0xbb => 0x5d15, 0xbc => 0x5d17,
    0xbd => 0x5d5c, 0xbe => 0x5d1f, 0xbf => 0x5d1b, 0xc0 => 0x5d11,
    0xc1 => 0x5d14, 0xc2 => 0x5d22, 0xc3 => 0x5d1a, 0xc4 => 0x5d19,
    0xc5 => 0x5d18, 0xc6 => 0x5d4c, 0xc7 => 0x5d52, 0xc8 => 0x5d4e,
    0xc9 => 0x5d4b, 0xca => 0x5d6c, 0xcb => 0x5d73, 0xcc => 0x5d76,
    0xcd => 0x5d87, 0xce => 0x5d84, 0xcf => 0x5d82, 0xd0 => 0x5da2,
    0xd1 => 0x5d9d, 0xd2 => 0x5dac, 0xd3 => 0x5dae, 0xd4 => 0x5dbd,
    0xd5 => 0x5d90, 0xd6 => 0x5db7, 0xd7 => 0x5dbc, 0xd8 => 0x5dc9,
    0xd9 => 0x5dcd, 0xda => 0x5dd3, 0xdb => 0x5dd2, 0xdc => 0x5dd6,
    0xdd => 0x5ddb, 0xde => 0x5deb, 0xdf => 0x5df2, 0xe0 => 0x5df5,
    0xe1 => 0x5e0b, 0xe2 => 0x5e1a, 0xe3 => 0x5e19, 0xe4 => 0x5e11,
    0xe5 => 0x5e1b, 0xe6 => 0x5e36, 0xe7 => 0x5e37, 0xe8 => 0x5e44,
    0xe9 => 0x5e43, 0xea => 0x5e40, 0xeb => 0x5e4e, 0xec => 0x5e57,
    0xed => 0x5e54, 0xee => 0x5e5f, 0xef => 0x5e62, 0xf0 => 0x5e64,
    0xf1 => 0x5e47, 0xf2 => 0x5e75, 0xf3 => 0x5e76, 0xf4 => 0x5e7a,
    0xf5 => 0x9ebc, 0xf6 => 0x5e7f, 0xf7 => 0x5ea0, 0xf8 => 0x5ec1,
    0xf9 => 0x5ec2, 0xfa => 0x5ec8, 0xfb => 0x5ed0, 0xfc => 0x5ecf,
  },
  0x9c => {
    0x40 => 0x5ed6, 0x41 => 0x5ee3, 0x42 => 0x5edd, 0x43 => 0x5eda,
    0x44 => 0x5edb, 0x45 => 0x5ee2, 0x46 => 0x5ee1, 0x47 => 0x5ee8,
    0x48 => 0x5ee9, 0x49 => 0x5eec, 0x4a => 0x5ef1, 0x4b => 0x5ef3,
    0x4c => 0x5ef0, 0x4d => 0x5ef4, 0x4e => 0x5ef8, 0x4f => 0x5efe,
    0x50 => 0x5f03, 0x51 => 0x5f09, 0x52 => 0x5f5d, 0x53 => 0x5f5c,
    0x54 => 0x5f0b, 0x55 => 0x5f11, 0x56 => 0x5f16, 0x57 => 0x5f29,
    0x58 => 0x5f2d, 0x59 => 0x5f38, 0x5a => 0x5f41, 0x5b => 0x5f48,
    0x5c => 0x5f4c, 0x5d => 0x5f4e, 0x5e => 0x5f2f, 0x5f => 0x5f51,
    0x60 => 0x5f56, 0x61 => 0x5f57, 0x62 => 0x5f59, 0x63 => 0x5f61,
    0x64 => 0x5f6d, 0x65 => 0x5f73, 0x66 => 0x5f77, 0x67 => 0x5f83,
    0x68 => 0x5f82, 0x69 => 0x5f7f, 0x6a => 0x5f8a, 0x6b => 0x5f88,
    0x6c => 0x5f91, 0x6d => 0x5f87, 0x6e => 0x5f9e, 0x6f => 0x5f99,
    0x70 => 0x5f98, 0x71 => 0x5fa0, 0x72 => 0x5fa8, 0x73 => 0x5fad,
    0x74 => 0x5fbc, 0x75 => 0x5fd6, 0x76 => 0x5ffb, 0x77 => 0x5fe4,
    0x78 => 0x5ff8, 0x79 => 0x5ff1, 0x7a => 0x5fdd, 0x7b => 0x60b3,
    0x7c => 0x5fff, 0x7d => 0x6021, 0x7e => 0x6060, 0x80 => 0x6019,
    0x81 => 0x6010, 0x82 => 0x6029, 0x83 => 0x600e, 0x84 => 0x6031,
    0x85 => 0x601b, 0x86 => 0x6015, 0x87 => 0x602b, 0x88 => 0x6026,
    0x89 => 0x600f, 0x8a => 0x603a, 0x8b => 0x605a, 0x8c => 0x6041,
    0x8d => 0x606a, 0x8e => 0x6077, 0x8f => 0x605f, 0x90 => 0x604a,
    0x91 => 0x6046, 0x92 => 0x604d, 0x93 => 0x6063, 0x94 => 0x6043,
    0x95 => 0x6064, 0x96 => 0x6042, 0x97 => 0x606c, 0x98 => 0x606b,
    0x99 => 0x6059, 0x9a => 0x6081, 0x9b => 0x608d, 0x9c => 0x60e7,
    0x9d => 0x6083, 0x9e => 0x609a, 0x9f => 0x6084, 0xa0 => 0x609b,
    0xa1 => 0x6096, 0xa2 => 0x6097, 0xa3 => 0x6092, 0xa4 => 0x60a7,
    0xa5 => 0x608b, 0xa6 => 0x60e1, 0xa7 => 0x60b8, 0xa8 => 0x60e0,
    0xa9 => 0x60d3, 0xaa => 0x60b4, 0xab => 0x5ff0, 0xac => 0x60bd,
    0xad => 0x60c6, 0xae => 0x60b5, 0xaf => 0x60d8, 0xb0 => 0x614d,
    0xb1 => 0x6115, 0xb2 => 0x6106, 0xb3 => 0x60f6, 0xb4 => 0x60f7,
    0xb5 => 0x6100, 0xb6 => 0x60f4, 0xb7 => 0x60fa, 0xb8 => 0x6103,
    0xb9 => 0x6121, 0xba => 0x60fb, 0xbb => 0x60f1, 0xbc => 0x610d,
    0xbd => 0x610e, 0xbe => 0x6147, 0xbf => 0x613e, 0xc0 => 0x6128,
    0xc1 => 0x6127, 0xc2 => 0x614a, 0xc3 => 0x613f, 0xc4 => 0x613c,
    0xc5 => 0x612c, 0xc6 => 0x6134, 0xc7 => 0x613d, 0xc8 => 0x6142,
    0xc9 => 0x6144, 0xca => 0x6173, 0xcb => 0x6177, 0xcc => 0x6158,
    0xcd => 0x6159, 0xce => 0x615a, 0xcf => 0x616b, 0xd0 => 0x6174,
    0xd1 => 0x616f, 0xd2 => 0x6165, 0xd3 => 0x6171, 0xd4 => 0x615f,
    0xd5 => 0x615d, 0xd6 => 0x6153, 0xd7 => 0x6175, 0xd8 => 0x6199,
    0xd9 => 0x6196, 0xda => 0x6187, 0xdb => 0x61ac, 0xdc => 0x6194,
    0xdd => 0x619a, 0xde => 0x618a, 0xdf => 0x6191, 0xe0 => 0x61ab,
    0xe1 => 0x61ae, 0xe2 => 0x61cc, 0xe3 => 0x61ca, 0xe4 => 0x61c9,
    0xe5 => 0x61f7, 0xe6 => 0x61c8, 0xe7 => 0x61c3, 0xe8 => 0x61c6,
    0xe9 => 0x61ba, 0xea => 0x61cb, 0xeb => 0x7f79, 0xec => 0x61cd,
    0xed => 0x61e6, 0xee => 0x61e3, 0xef => 0x61f6, 0xf0 => 0x61fa,
    0xf1 => 0x61f4, 0xf2 => 0x61ff, 0xf3 => 0x61fd, 0xf4 => 0x61fc,
    0xf5 => 0x61fe, 0xf6 => 0x6200, 0xf7 => 0x6208, 0xf8 => 0x6209,
    0xf9 => 0x620d, 0xfa => 0x620c, 0xfb => 0x6214, 0xfc => 0x621b,
  },
  0x9d => {
    0x40 => 0x621e, 0x41 => 0x6221, 0x42 => 0x622a, 0x43 => 0x622e,
    0x44 => 0x6230, 0x45 => 0x6232, 0x46 => 0x6233, 0x47 => 0x6241,
    0x48 => 0x624e, 0x49 => 0x625e, 0x4a => 0x6263, 0x4b => 0x625b,
    0x4c => 0x6260, 0x4d => 0x6268, 0x4e => 0x627c, 0x4f => 0x6282,
    0x50 => 0x6289, 0x51 => 0x627e, 0x52 => 0x6292, 0x53 => 0x6293,
    0x54 => 0x6296, 0x55 => 0x62d4, 0x56 => 0x6283, 0x57 => 0x6294,
    0x58 => 0x62d7, 0x59 => 0x62d1, 0x5a => 0x62bb, 0x5b => 0x62cf,
    0x5c => 0x62ff, 0x5d => 0x62c6, 0x5e => 0x64d4, 0x5f => 0x62c8,
    0x60 => 0x62dc, 0x61 => 0x62cc, 0x62 => 0x62ca, 0x63 => 0x62c2,
    0x64 => 0x62c7, 0x65 => 0x629b, 0x66 => 0x62c9, 0x67 => 0x630c,
    0x68 => 0x62ee, 0x69 => 0x62f1, 0x6a => 0x6327, 0x6b => 0x6302,
    0x6c => 0x6308, 0x6d => 0x62ef, 0x6e => 0x62f5, 0x6f => 0x6350,
    0x70 => 0x633e, 0x71 => 0x634d, 0x72 => 0x641c, 0x73 => 0x634f,
    0x74 => 0x6396, 0x75 => 0x638e, 0x76 => 0x6380, 0x77 => 0x63ab,
    0x78 => 0x6376, 0x79 => 0x63a3, 0x7a => 0x638f, 0x7b => 0x6389,
    0x7c => 0x639f, 0x7d => 0x63b5, 0x7e => 0x636b, 0x80 => 0x6369,
    0x81 => 0x63be, 0x82 => 0x63e9, 0x83 => 0x63c0, 0x84 => 0x63c6,
    0x85 => 0x63e3, 0x86 => 0x63c9, 0x87 => 0x63d2, 0x88 => 0x63f6,
    0x89 => 0x63c4, 0x8a => 0x6416, 0x8b => 0x6434, 0x8c => 0x6406,
    0x8d => 0x6413, 0x8e => 0x6426, 0x8f => 0x6436, 0x90 => 0x651d,
    0x91 => 0x6417, 0x92 => 0x6428, 0x93 => 0x640f, 0x94 => 0x6467,
    0x95 => 0x646f, 0x96 => 0x6476, 0x97 => 0x644e, 0x98 => 0x652a,
    0x99 => 0x6495, 0x9a => 0x6493, 0x9b => 0x64a5, 0x9c => 0x64a9,
    0x9d => 0x6488, 0x9e => 0x64bc, 0x9f => 0x64da, 0xa0 => 0x64d2,
    0xa1 => 0x64c5, 0xa2 => 0x64c7, 0xa3 => 0x64bb, 0xa4 => 0x64d8,
    0xa5 => 0x64c2, 0xa6 => 0x64f1, 0xa7 => 0x64e7, 0xa8 => 0x8209,
    0xa9 => 0x64e0, 0xaa => 0x64e1, 0xab => 0x62ac, 0xac => 0x64e3,
    0xad => 0x64ef, 0xae => 0x652c, 0xaf => 0x64f6, 0xb0 => 0x64f4,
    0xb1 => 0x64f2, 0xb2 => 0x64fa, 0xb3 => 0x6500, 0xb4 => 0x64fd,
    0xb5 => 0x6518, 0xb6 => 0x651c, 0xb7 => 0x6505, 0xb8 => 0x6524,
    0xb9 => 0x6523, 0xba => 0x652b, 0xbb => 0x6534, 0xbc => 0x6535,
    0xbd => 0x6537, 0xbe => 0x6536, 0xbf => 0x6538, 0xc0 => 0x754b,
    0xc1 => 0x6548, 0xc2 => 0x6556, 0xc3 => 0x6555, 0xc4 => 0x654d,
    0xc5 => 0x6558, 0xc6 => 0x655e, 0xc7 => 0x655d, 0xc8 => 0x6572,
    0xc9 => 0x6578, 0xca => 0x6582, 0xcb => 0x6583, 0xcc => 0x8b8a,
    0xcd => 0x659b, 0xce => 0x659f, 0xcf => 0x65ab, 0xd0 => 0x65b7,
    0xd1 => 0x65c3, 0xd2 => 0x65c6, 0xd3 => 0x65c1, 0xd4 => 0x65c4,
    0xd5 => 0x65cc, 0xd6 => 0x65d2, 0xd7 => 0x65db, 0xd8 => 0x65d9,
    0xd9 => 0x65e0, 0xda => 0x65e1, 0xdb => 0x65f1, 0xdc => 0x6772,
    0xdd => 0x660a, 0xde => 0x6603, 0xdf => 0x65fb, 0xe0 => 0x6773,
    0xe1 => 0x6635, 0xe2 => 0x6636, 0xe3 => 0x6634, 0xe4 => 0x661c,
    0xe5 => 0x664f, 0xe6 => 0x6644, 0xe7 => 0x6649, 0xe8 => 0x6641,
    0xe9 => 0x665e, 0xea => 0x665d, 0xeb => 0x6664, 0xec => 0x6667,
    0xed => 0x6668, 0xee => 0x665f, 0xef => 0x6662, 0xf0 => 0x6670,
    0xf1 => 0x6683, 0xf2 => 0x6688, 0xf3 => 0x668e, 0xf4 => 0x6689,
    0xf5 => 0x6684, 0xf6 => 0x6698, 0xf7 => 0x669d, 0xf8 => 0x66c1,
    0xf9 => 0x66b9, 0xfa => 0x66c9, 0xfb => 0x66be, 0xfc => 0x66bc,
  },
  0x9e => {
    0x40 => 0x66c4, 0x41 => 0x66b8, 0x42 => 0x66d6, 0x43 => 0x66da,
    0x44 => 0x66e0, 0x45 => 0x663f, 0x46 => 0x66e6, 0x47 => 0x66e9,
    0x48 => 0x66f0, 0x49 => 0x66f5, 0x4a => 0x66f7, 0x4b => 0x670f,
    0x4c => 0x6716, 0x4d => 0x671e, 0x4e => 0x6726, 0x4f => 0x6727,
    0x50 => 0x9738, 0x51 => 0x672e, 0x52 => 0x673f, 0x53 => 0x6736,
    0x54 => 0x6741, 0x55 => 0x6738, 0x56 => 0x6737, 0x57 => 0x6746,
    0x58 => 0x675e, 0x59 => 0x6760, 0x5a => 0x6759, 0x5b => 0x6763,
    0x5c => 0x6764, 0x5d => 0x6789, 0x5e => 0x6770, 0x5f => 0x67a9,
    0x60 => 0x677c, 0x61 => 0x676a, 0x62 => 0x678c, 0x63 => 0x678b,
    0x64 => 0x67a6, 0x65 => 0x67a1, 0x66 => 0x6785, 0x67 => 0x67b7,
    0x68 => 0x67ef, 0x69 => 0x67b4, 0x6a => 0x67ec, 0x6b => 0x67b3,
    0x6c => 0x67e9, 0x6d => 0x67b8, 0x6e => 0x67e4, 0x6f => 0x67de,
    0x70 => 0x67dd, 0x71 => 0x67e2, 0x72 => 0x67ee, 0x73 => 0x67b9,
    0x74 => 0x67ce, 0x75 => 0x67c6, 0x76 => 0x67e7, 0x77 => 0x6a9c,
    0x78 => 0x681e, 0x79 => 0x6846, 0x7a => 0x6829, 0x7b => 0x6840,
    0x7c => 0x684d, 0x7d => 0x6832, 0x7e => 0x684e, 0x80 => 0x68b3,
    0x81 => 0x682b, 0x82 => 0x6859, 0x83 => 0x6863, 0x84 => 0x6877,
    0x85 => 0x687f, 0x86 => 0x689f, 0x87 => 0x688f, 0x88 => 0x68ad,
    0x89 => 0x6894, 0x8a => 0x689d, 0x8b => 0x689b, 0x8c => 0x6883,
    0x8d => 0x6aae, 0x8e => 0x68b9, 0x8f => 0x6874, 0x90 => 0x68b5,
    0x91 => 0x68a0, 0x92 => 0x68ba, 0x93 => 0x690f, 0x94 => 0x688d,
    0x95 => 0x687e, 0x96 => 0x6901, 0x97 => 0x68ca, 0x98 => 0x6908,
    0x99 => 0x68d8, 0x9a => 0x6922, 0x9b => 0x6926, 0x9c => 0x68e1,
    0x9d => 0x690c, 0x9e => 0x68cd, 0x9f => 0x68d4, 0xa0 => 0x68e7,
    0xa1 => 0x68d5, 0xa2 => 0x6936, 0xa3 => 0x6912, 0xa4 => 0x6904,
    0xa5 => 0x68d7, 0xa6 => 0x68e3, 0xa7 => 0x6925, 0xa8 => 0x68f9,
    0xa9 => 0x68e0, 0xaa => 0x68ef, 0xab => 0x6928, 0xac => 0x692a,
    0xad => 0x691a, 0xae => 0x6923, 0xaf => 0x6921, 0xb0 => 0x68c6,
    0xb1 => 0x6979, 0xb2 => 0x6977, 0xb3 => 0x695c, 0xb4 => 0x6978,
    0xb5 => 0x696b, 0xb6 => 0x6954, 0xb7 => 0x697e, 0xb8 => 0x696e,
    0xb9 => 0x6939, 0xba => 0x6974, 0xbb => 0x693d, 0xbc => 0x6959,
    0xbd => 0x6930, 0xbe => 0x6961, 0xbf => 0x695e, 0xc0 => 0x695d,
    0xc1 => 0x6981, 0xc2 => 0x696a, 0xc3 => 0x69b2, 0xc4 => 0x69ae,
    0xc5 => 0x69d0, 0xc6 => 0x69bf, 0xc7 => 0x69c1, 0xc8 => 0x69d3,
    0xc9 => 0x69be, 0xca => 0x69ce, 0xcb => 0x5be8, 0xcc => 0x69ca,
    0xcd => 0x69dd, 0xce => 0x69bb, 0xcf => 0x69c3, 0xd0 => 0x69a7,
    0xd1 => 0x6a2e, 0xd2 => 0x6991, 0xd3 => 0x69a0, 0xd4 => 0x699c,
    0xd5 => 0x6995, 0xd6 => 0x69b4, 0xd7 => 0x69de, 0xd8 => 0x69e8,
    0xd9 => 0x6a02, 0xda => 0x6a1b, 0xdb => 0x69ff, 0xdc => 0x6b0a,
    0xdd => 0x69f9, 0xde => 0x69f2, 0xdf => 0x69e7, 0xe0 => 0x6a05,
    0xe1 => 0x69b1, 0xe2 => 0x6a1e, 0xe3 => 0x69ed, 0xe4 => 0x6a14,
    0xe5 => 0x69eb, 0xe6 => 0x6a0a, 0xe7 => 0x6a12, 0xe8 => 0x6ac1,
    0xe9 => 0x6a23, 0xea => 0x6a13, 0xeb => 0x6a44, 0xec => 0x6a0c,
    0xed => 0x6a72, 0xee => 0x6a36, 0xef => 0x6a78, 0xf0 => 0x6a47,
    0xf1 => 0x6a62, 0xf2 => 0x6a59, 0xf3 => 0x6a66, 0xf4 => 0x6a48,
    0xf5 => 0x6a38, 0xf6 => 0x6a22, 0xf7 => 0x6a90, 0xf8 => 0x6a8d,
    0xf9 => 0x6aa0, 0xfa => 0x6a84, 0xfb => 0x6aa2, 0xfc => 0x6aa3,
  },
  0x9f => {
    0x40 => 0x6a97, 0x41 => 0x8617, 0x42 => 0x6abb, 0x43 => 0x6ac3,
    0x44 => 0x6ac2, 0x45 => 0x6ab8, 0x46 => 0x6ab3, 0x47 => 0x6aac,
    0x48 => 0x6ade, 0x49 => 0x6ad1, 0x4a => 0x6adf, 0x4b => 0x6aaa,
    0x4c => 0x6ada, 0x4d => 0x6aea, 0x4e => 0x6afb, 0x4f => 0x6b05,
    0x50 => 0x8616, 0x51 => 0x6afa, 0x52 => 0x6b12, 0x53 => 0x6b16,
    0x54 => 0x9b31, 0x55 => 0x6b1f, 0x56 => 0x6b38, 0x57 => 0x6b37,
    0x58 => 0x76dc, 0x59 => 0x6b39, 0x5a => 0x98ee, 0x5b => 0x6b47,
    0x5c => 0x6b43, 0x5d => 0x6b49, 0x5e => 0x6b50, 0x5f => 0x6b59,
    0x60 => 0x6b54, 0x61 => 0x6b5b, 0x62 => 0x6b5f, 0x63 => 0x6b61,
    0x64 => 0x6b78, 0x65 => 0x6b79, 0x66 => 0x6b7f, 0x67 => 0x6b80,
    0x68 => 0x6b84, 0x69 => 0x6b83, 0x6a => 0x6b8d, 0x6b => 0x6b98,
    0x6c => 0x6b95, 0x6d => 0x6b9e, 0x6e => 0x6ba4, 0x6f => 0x6baa,
    0x70 => 0x6bab, 0x71 => 0x6baf, 0x72 => 0x6bb2, 0x73 => 0x6bb1,
    0x74 => 0x6bb3, 0x75 => 0x6bb7, 0x76 => 0x6bbc, 0x77 => 0x6bc6,
    0x78 => 0x6bcb, 0x79 => 0x6bd3, 0x7a => 0x6bdf, 0x7b => 0x6bec,
    0x7c => 0x6beb, 0x7d => 0x6bf3, 0x7e => 0x6bef, 0x80 => 0x9ebe,
    0x81 => 0x6c08, 0x82 => 0x6c13, 0x83 => 0x6c14, 0x84 => 0x6c1b,
    0x85 => 0x6c24, 0x86 => 0x6c23, 0x87 => 0x6c5e, 0x88 => 0x6c55,
    0x89 => 0x6c62, 0x8a => 0x6c6a, 0x8b => 0x6c82, 0x8c => 0x6c8d,
    0x8d => 0x6c9a, 0x8e => 0x6c81, 0x8f => 0x6c9b, 0x90 => 0x6c7e,
    0x91 => 0x6c68, 0x92 => 0x6c73, 0x93 => 0x6c92, 0x94 => 0x6c90,
    0x95 => 0x6cc4, 0x96 => 0x6cf1, 0x97 => 0x6cd3, 0x98 => 0x6cbd,
    0x99 => 0x6cd7, 0x9a => 0x6cc5, 0x9b => 0x6cdd, 0x9c => 0x6cae,
    0x9d => 0x6cb1, 0x9e => 0x6cbe, 0x9f => 0x6cba, 0xa0 => 0x6cdb,
    0xa1 => 0x6cef, 0xa2 => 0x6cd9, 0xa3 => 0x6cea, 0xa4 => 0x6d1f,
    0xa5 => 0x884d, 0xa6 => 0x6d36, 0xa7 => 0x6d2b, 0xa8 => 0x6d3d,
    0xa9 => 0x6d38, 0xaa => 0x6d19, 0xab => 0x6d35, 0xac => 0x6d33,
    0xad => 0x6d12, 0xae => 0x6d0c, 0xaf => 0x6d63, 0xb0 => 0x6d93,
    0xb1 => 0x6d64, 0xb2 => 0x6d5a, 0xb3 => 0x6d79, 0xb4 => 0x6d59,
    0xb5 => 0x6d8e, 0xb6 => 0x6d95, 0xb7 => 0x6fe4, 0xb8 => 0x6d85,
    0xb9 => 0x6df9, 0xba => 0x6e15, 0xbb => 0x6e0a, 0xbc => 0x6db5,
    0xbd => 0x6dc7, 0xbe => 0x6de6, 0xbf => 0x6db8, 0xc0 => 0x6dc6,
    0xc1 => 0x6dec, 0xc2 => 0x6dde, 0xc3 => 0x6dcc, 0xc4 => 0x6de8,
    0xc5 => 0x6dd2, 0xc6 => 0x6dc5, 0xc7 => 0x6dfa, 0xc8 => 0x6dd9,
    0xc9 => 0x6de4, 0xca => 0x6dd5, 0xcb => 0x6dea, 0xcc => 0x6dee,
    0xcd => 0x6e2d, 0xce => 0x6e6e, 0xcf => 0x6e2e, 0xd0 => 0x6e19,
    0xd1 => 0x6e72, 0xd2 => 0x6e5f, 0xd3 => 0x6e3e, 0xd4 => 0x6e23,
    0xd5 => 0x6e6b, 0xd6 => 0x6e2b, 0xd7 => 0x6e76, 0xd8 => 0x6e4d,
    0xd9 => 0x6e1f, 0xda => 0x6e43, 0xdb => 0x6e3a, 0xdc => 0x6e4e,
    0xdd => 0x6e24, 0xde => 0x6eff, 0xdf => 0x6e1d, 0xe0 => 0x6e38,
    0xe1 => 0x6e82, 0xe2 => 0x6eaa, 0xe3 => 0x6e98, 0xe4 => 0x6ec9,
    0xe5 => 0x6eb7, 0xe6 => 0x6ed3, 0xe7 => 0x6ebd, 0xe8 => 0x6eaf,
    0xe9 => 0x6ec4, 0xea => 0x6eb2, 0xeb => 0x6ed4, 0xec => 0x6ed5,
    0xed => 0x6e8f, 0xee => 0x6ea5, 0xef => 0x6ec2, 0xf0 => 0x6e9f,
    0xf1 => 0x6f41, 0xf2 => 0x6f11, 0xf3 => 0x704c, 0xf4 => 0x6eec,
    0xf5 => 0x6ef8, 0xf6 => 0x6efe, 0xf7 => 0x6f3f, 0xf8 => 0x6ef2,
    0xf9 => 0x6f31, 0xfa => 0x6eef, 0xfb => 0x6f32, 0xfc => 0x6ecc,
  },
  0xa1 => 0xff61, 0xa2 => 0xff62, 0xa3 => 0xff63, 0xa4 => 0xff64,
  0xa5 => 0xff65, 0xa6 => 0xff66, 0xa7 => 0xff67, 0xa8 => 0xff68,
  0xa9 => 0xff69, 0xaa => 0xff6a, 0xab => 0xff6b, 0xac => 0xff6c,
  0xad => 0xff6d, 0xae => 0xff6e, 0xaf => 0xff6f, 0xb0 => 0xff70,
  0xb1 => 0xff71, 0xb2 => 0xff72, 0xb3 => 0xff73, 0xb4 => 0xff74,
  0xb5 => 0xff75, 0xb6 => 0xff76, 0xb7 => 0xff77, 0xb8 => 0xff78,
  0xb9 => 0xff79, 0xba => 0xff7a, 0xbb => 0xff7b, 0xbc => 0xff7c,
  0xbd => 0xff7d, 0xbe => 0xff7e, 0xbf => 0xff7f, 0xc0 => 0xff80,
  0xc1 => 0xff81, 0xc2 => 0xff82, 0xc3 => 0xff83, 0xc4 => 0xff84,
  0xc5 => 0xff85, 0xc6 => 0xff86, 0xc7 => 0xff87, 0xc8 => 0xff88,
  0xc9 => 0xff89, 0xca => 0xff8a, 0xcb => 0xff8b, 0xcc => 0xff8c,
  0xcd => 0xff8d, 0xce => 0xff8e, 0xcf => 0xff8f, 0xd0 => 0xff90,
  0xd1 => 0xff91, 0xd2 => 0xff92, 0xd3 => 0xff93, 0xd4 => 0xff94,
  0xd5 => 0xff95, 0xd6 => 0xff96, 0xd7 => 0xff97, 0xd8 => 0xff98,
  0xd9 => 0xff99, 0xda => 0xff9a, 0xdb => 0xff9b, 0xdc => 0xff9c,
  0xdd => 0xff9d, 0xde => 0xff9e, 0xdf => 0xff9f,
  0xe0 => {
    0x40 => 0x6f3e, 0x41 => 0x6f13, 0x42 => 0x6ef7, 0x43 => 0x6f86,
    0x44 => 0x6f7a, 0x45 => 0x6f78, 0x46 => 0x6f81, 0x47 => 0x6f80,
    0x48 => 0x6f6f, 0x49 => 0x6f5b, 0x4a => 0x6ff3, 0x4b => 0x6f6d,
    0x4c => 0x6f82, 0x4d => 0x6f7c, 0x4e => 0x6f58, 0x4f => 0x6f8e,
    0x50 => 0x6f91, 0x51 => 0x6fc2, 0x52 => 0x6f66, 0x53 => 0x6fb3,
    0x54 => 0x6fa3, 0x55 => 0x6fa1, 0x56 => 0x6fa4, 0x57 => 0x6fb9,
    0x58 => 0x6fc6, 0x59 => 0x6faa, 0x5a => 0x6fdf, 0x5b => 0x6fd5,
    0x5c => 0x6fec, 0x5d => 0x6fd4, 0x5e => 0x6fd8, 0x5f => 0x6ff1,
    0x60 => 0x6fee, 0x61 => 0x6fdb, 0x62 => 0x7009, 0x63 => 0x700b,
    0x64 => 0x6ffa, 0x65 => 0x7011, 0x66 => 0x7001, 0x67 => 0x700f,
    0x68 => 0x6ffe, 0x69 => 0x701b, 0x6a => 0x701a, 0x6b => 0x6f74,
    0x6c => 0x701d, 0x6d => 0x7018, 0x6e => 0x701f, 0x6f => 0x7030,
    0x70 => 0x703e, 0x71 => 0x7032, 0x72 => 0x7051, 0x73 => 0x7063,
    0x74 => 0x7099, 0x75 => 0x7092, 0x76 => 0x70af, 0x77 => 0x70f1,
    0x78 => 0x70ac, 0x79 => 0x70b8, 0x7a => 0x70b3, 0x7b => 0x70ae,
    0x7c => 0x70df, 0x7d => 0x70cb, 0x7e => 0x70dd, 0x80 => 0x70d9,
    0x81 => 0x7109, 0x82 => 0x70fd, 0x83 => 0x711c, 0x84 => 0x7119,
    0x85 => 0x7165, 0x86 => 0x7155, 0x87 => 0x7188, 0x88 => 0x7166,
    0x89 => 0x7162, 0x8a => 0x714c, 0x8b => 0x7156, 0x8c => 0x716c,
    0x8d => 0x718f, 0x8e => 0x71fb, 0x8f => 0x7184, 0x90 => 0x7195,
    0x91 => 0x71a8, 0x92 => 0x71ac, 0x93 => 0x71d7, 0x94 => 0x71b9,
    0x95 => 0x71be, 0x96 => 0x71d2, 0x97 => 0x71c9, 0x98 => 0x71d4,
    0x99 => 0x71ce, 0x9a => 0x71e0, 0x9b => 0x71ec, 0x9c => 0x71e7,
    0x9d => 0x71f5, 0x9e => 0x71fc, 0x9f => 0x71f9, 0xa0 => 0x71ff,
    0xa1 => 0x720d, 0xa2 => 0x7210, 0xa3 => 0x721b, 0xa4 => 0x7228,
    0xa5 => 0x722d, 0xa6 => 0x722c, 0xa7 => 0x7230, 0xa8 => 0x7232,
    0xa9 => 0x723b, 0xaa => 0x723c, 0xab => 0x723f, 0xac => 0x7240,
    0xad => 0x7246, 0xae => 0x724b, 0xaf => 0x7258, 0xb0 => 0x7274,
    0xb1 => 0x727e, 0xb2 => 0x7282, 0xb3 => 0x7281, 0xb4 => 0x7287,
    0xb5 => 0x7292, 0xb6 => 0x7296, 0xb7 => 0x72a2, 0xb8 => 0x72a7,
    0xb9 => 0x72b9, 0xba => 0x72b2, 0xbb => 0x72c3, 0xbc => 0x72c6,
    0xbd => 0x72c4, 0xbe => 0x72ce, 0xbf => 0x72d2, 0xc0 => 0x72e2,
    0xc1 => 0x72e0, 0xc2 => 0x72e1, 0xc3 => 0x72f9, 0xc4 => 0x72f7,
    0xc5 => 0x500f, 0xc6 => 0x7317, 0xc7 => 0x730a, 0xc8 => 0x731c,
    0xc9 => 0x7316, 0xca => 0x731d, 0xcb => 0x7334, 0xcc => 0x732f,
    0xcd => 0x7329, 0xce => 0x7325, 0xcf => 0x733e, 0xd0 => 0x734e,
    0xd1 => 0x734f, 0xd2 => 0x9ed8, 0xd3 => 0x7357, 0xd4 => 0x736a,
    0xd5 => 0x7368, 0xd6 => 0x7370, 0xd7 => 0x7378, 0xd8 => 0x7375,
    0xd9 => 0x737b, 0xda => 0x737a, 0xdb => 0x73c8, 0xdc => 0x73b3,
    0xdd => 0x73ce, 0xde => 0x73bb, 0xdf => 0x73c0, 0xe0 => 0x73e5,
    0xe1 => 0x73ee, 0xe2 => 0x73de, 0xe3 => 0x74a2, 0xe4 => 0x7405,
    0xe5 => 0x746f, 0xe6 => 0x7425, 0xe7 => 0x73f8, 0xe8 => 0x7432,
    0xe9 => 0x743a, 0xea => 0x7455, 0xeb => 0x743f, 0xec => 0x745f,
    0xed => 0x7459, 0xee => 0x7441, 0xef => 0x745c, 0xf0 => 0x7469,
    0xf1 => 0x7470, 0xf2 => 0x7463, 0xf3 => 0x746a, 0xf4 => 0x7476,
    0xf5 => 0x747e, 0xf6 => 0x748b, 0xf7 => 0x749e, 0xf8 => 0x74a7,
    0xf9 => 0x74ca, 0xfa => 0x74cf, 0xfb => 0x74d4, 0xfc => 0x73f1,
  },
  0xe1 => {
    0x40 => 0x74e0, 0x41 => 0x74e3, 0x42 => 0x74e7, 0x43 => 0x74e9,
    0x44 => 0x74ee, 0x45 => 0x74f2, 0x46 => 0x74f0, 0x47 => 0x74f1,
    0x48 => 0x74f8, 0x49 => 0x74f7, 0x4a => 0x7504, 0x4b => 0x7503,
    0x4c => 0x7505, 0x4d => 0x750c, 0x4e => 0x750e, 0x4f => 0x750d,
    0x50 => 0x7515, 0x51 => 0x7513, 0x52 => 0x751e, 0x53 => 0x7526,
    0x54 => 0x752c, 0x55 => 0x753c, 0x56 => 0x7544, 0x57 => 0x754d,
    0x58 => 0x754a, 0x59 => 0x7549, 0x5a => 0x755b, 0x5b => 0x7546,
    0x5c => 0x755a, 0x5d => 0x7569, 0x5e => 0x7564, 0x5f => 0x7567,
    0x60 => 0x756b, 0x61 => 0x756d, 0x62 => 0x7578, 0x63 => 0x7576,
    0x64 => 0x7586, 0x65 => 0x7587, 0x66 => 0x7574, 0x67 => 0x758a,
    0x68 => 0x7589, 0x69 => 0x7582, 0x6a => 0x7594, 0x6b => 0x759a,
    0x6c => 0x759d, 0x6d => 0x75a5, 0x6e => 0x75a3, 0x6f => 0x75c2,
    0x70 => 0x75b3, 0x71 => 0x75c3, 0x72 => 0x75b5, 0x73 => 0x75bd,
    0x74 => 0x75b8, 0x75 => 0x75bc, 0x76 => 0x75b1, 0x77 => 0x75cd,
    0x78 => 0x75ca, 0x79 => 0x75d2, 0x7a => 0x75d9, 0x7b => 0x75e3,
    0x7c => 0x75de, 0x7d => 0x75fe, 0x7e => 0x75ff, 0x80 => 0x75fc,
    0x81 => 0x7601, 0x82 => 0x75f0, 0x83 => 0x75fa, 0x84 => 0x75f2,
    0x85 => 0x75f3, 0x86 => 0x760b, 0x87 => 0x760d, 0x88 => 0x7609,
    0x89 => 0x761f, 0x8a => 0x7627, 0x8b => 0x7620, 0x8c => 0x7621,
    0x8d => 0x7622, 0x8e => 0x7624, 0x8f => 0x7634, 0x90 => 0x7630,
    0x91 => 0x763b, 0x92 => 0x7647, 0x93 => 0x7648, 0x94 => 0x7646,
    0x95 => 0x765c, 0x96 => 0x7658, 0x97 => 0x7661, 0x98 => 0x7662,
    0x99 => 0x7668, 0x9a => 0x7669, 0x9b => 0x766a, 0x9c => 0x7667,
    0x9d => 0x766c, 0x9e => 0x7670, 0x9f => 0x7672, 0xa0 => 0x7676,
    0xa1 => 0x7678, 0xa2 => 0x767c, 0xa3 => 0x7680, 0xa4 => 0x7683,
    0xa5 => 0x7688, 0xa6 => 0x768b, 0xa7 => 0x768e, 0xa8 => 0x7696,
    0xa9 => 0x7693, 0xaa => 0x7699, 0xab => 0x769a, 0xac => 0x76b0,
    0xad => 0x76b4, 0xae => 0x76b8, 0xaf => 0x76b9, 0xb0 => 0x76ba,
    0xb1 => 0x76c2, 0xb2 => 0x76cd, 0xb3 => 0x76d6, 0xb4 => 0x76d2,
    0xb5 => 0x76de, 0xb6 => 0x76e1, 0xb7 => 0x76e5, 0xb8 => 0x76e7,
    0xb9 => 0x76ea, 0xba => 0x862f, 0xbb => 0x76fb, 0xbc => 0x7708,
    0xbd => 0x7707, 0xbe => 0x7704, 0xbf => 0x7729, 0xc0 => 0x7724,
    0xc1 => 0x771e, 0xc2 => 0x7725, 0xc3 => 0x7726, 0xc4 => 0x771b,
    0xc5 => 0x7737, 0xc6 => 0x7738, 0xc7 => 0x7747, 0xc8 => 0x775a,
    0xc9 => 0x7768, 0xca => 0x776b, 0xcb => 0x775b, 0xcc => 0x7765,
    0xcd => 0x777f, 0xce => 0x777e, 0xcf => 0x7779, 0xd0 => 0x778e,
    0xd1 => 0x778b, 0xd2 => 0x7791, 0xd3 => 0x77a0, 0xd4 => 0x779e,
    0xd5 => 0x77b0, 0xd6 => 0x77b6, 0xd7 => 0x77b9, 0xd8 => 0x77bf,
    0xd9 => 0x77bc, 0xda => 0x77bd, 0xdb => 0x77bb, 0xdc => 0x77c7,
    0xdd => 0x77cd, 0xde => 0x77d7, 0xdf => 0x77da, 0xe0 => 0x77dc,
    0xe1 => 0x77e3, 0xe2 => 0x77ee, 0xe3 => 0x77fc, 0xe4 => 0x780c,
    0xe5 => 0x7812, 0xe6 => 0x7926, 0xe7 => 0x7820, 0xe8 => 0x792a,
    0xe9 => 0x7845, 0xea => 0x788e, 0xeb => 0x7874, 0xec => 0x7886,
    0xed => 0x787c, 0xee => 0x789a, 0xef => 0x788c, 0xf0 => 0x78a3,
    0xf1 => 0x78b5, 0xf2 => 0x78aa, 0xf3 => 0x78af, 0xf4 => 0x78d1,
    0xf5 => 0x78c6, 0xf6 => 0x78cb, 0xf7 => 0x78d4, 0xf8 => 0x78be,
    0xf9 => 0x78bc, 0xfa => 0x78c5, 0xfb => 0x78ca, 0xfc => 0x78ec,
  },
  0xe2 => {
    0x40 => 0x78e7, 0x41 => 0x78da, 0x42 => 0x78fd, 0x43 => 0x78f4,
    0x44 => 0x7907, 0x45 => 0x7912, 0x46 => 0x7911, 0x47 => 0x7919,
    0x48 => 0x792c, 0x49 => 0x792b, 0x4a => 0x7940, 0x4b => 0x7960,
    0x4c => 0x7957, 0x4d => 0x795f, 0x4e => 0x795a, 0x4f => 0x7955,
    0x50 => 0x7953, 0x51 => 0x797a, 0x52 => 0x797f, 0x53 => 0x798a,
    0x54 => 0x799d, 0x55 => 0x79a7, 0x56 => 0x9f4b, 0x57 => 0x79aa,
    0x58 => 0x79ae, 0x59 => 0x79b3, 0x5a => 0x79b9, 0x5b => 0x79ba,
    0x5c => 0x79c9, 0x5d => 0x79d5, 0x5e => 0x79e7, 0x5f => 0x79ec,
    0x60 => 0x79e1, 0x61 => 0x79e3, 0x62 => 0x7a08, 0x63 => 0x7a0d,
    0x64 => 0x7a18, 0x65 => 0x7a19, 0x66 => 0x7a20, 0x67 => 0x7a1f,
    0x68 => 0x7980, 0x69 => 0x7a31, 0x6a => 0x7a3b, 0x6b => 0x7a3e,
    0x6c => 0x7a37, 0x6d => 0x7a43, 0x6e => 0x7a57, 0x6f => 0x7a49,
    0x70 => 0x7a61, 0x71 => 0x7a62, 0x72 => 0x7a69, 0x73 => 0x9f9d,
    0x74 => 0x7a70, 0x75 => 0x7a79, 0x76 => 0x7a7d, 0x77 => 0x7a88,
    0x78 => 0x7a97, 0x79 => 0x7a95, 0x7a => 0x7a98, 0x7b => 0x7a96,
    0x7c => 0x7aa9, 0x7d => 0x7ac8, 0x7e => 0x7ab0, 0x80 => 0x7ab6,
    0x81 => 0x7ac5, 0x82 => 0x7ac4, 0x83 => 0x7abf, 0x84 => 0x9083,
    0x85 => 0x7ac7, 0x86 => 0x7aca, 0x87 => 0x7acd, 0x88 => 0x7acf,
    0x89 => 0x7ad5, 0x8a => 0x7ad3, 0x8b => 0x7ad9, 0x8c => 0x7ada,
    0x8d => 0x7add, 0x8e => 0x7ae1, 0x8f => 0x7ae2, 0x90 => 0x7ae6,
    0x91 => 0x7aed, 0x92 => 0x7af0, 0x93 => 0x7b02, 0x94 => 0x7b0f,
    0x95 => 0x7b0a, 0x96 => 0x7b06, 0x97 => 0x7b33, 0x98 => 0x7b18,
    0x99 => 0x7b19, 0x9a => 0x7b1e, 0x9b => 0x7b35, 0x9c => 0x7b28,
    0x9d => 0x7b36, 0x9e => 0x7b50, 0x9f => 0x7b7a, 0xa0 => 0x7b04,
    0xa1 => 0x7b4d, 0xa2 => 0x7b0b, 0xa3 => 0x7b4c, 0xa4 => 0x7b45,
    0xa5 => 0x7b75, 0xa6 => 0x7b65, 0xa7 => 0x7b74, 0xa8 => 0x7b67,
    0xa9 => 0x7b70, 0xaa => 0x7b71, 0xab => 0x7b6c, 0xac => 0x7b6e,
    0xad => 0x7b9d, 0xae => 0x7b98, 0xaf => 0x7b9f, 0xb0 => 0x7b8d,
    0xb1 => 0x7b9c, 0xb2 => 0x7b9a, 0xb3 => 0x7b8b, 0xb4 => 0x7b92,
    0xb5 => 0x7b8f, 0xb6 => 0x7b5d, 0xb7 => 0x7b99, 0xb8 => 0x7bcb,
    0xb9 => 0x7bc1, 0xba => 0x7bcc, 0xbb => 0x7bcf, 0xbc => 0x7bb4,
    0xbd => 0x7bc6, 0xbe => 0x7bdd, 0xbf => 0x7be9, 0xc0 => 0x7c11,
    0xc1 => 0x7c14, 0xc2 => 0x7be6, 0xc3 => 0x7be5, 0xc4 => 0x7c60,
    0xc5 => 0x7c00, 0xc6 => 0x7c07, 0xc7 => 0x7c13, 0xc8 => 0x7bf3,
    0xc9 => 0x7bf7, 0xca => 0x7c17, 0xcb => 0x7c0d, 0xcc => 0x7bf6,
    0xcd => 0x7c23, 0xce => 0x7c27, 0xcf => 0x7c2a, 0xd0 => 0x7c1f,
    0xd1 => 0x7c37, 0xd2 => 0x7c2b, 0xd3 => 0x7c3d, 0xd4 => 0x7c4c,
    0xd5 => 0x7c43, 0xd6 => 0x7c54, 0xd7 => 0x7c4f, 0xd8 => 0x7c40,
    0xd9 => 0x7c50, 0xda => 0x7c58, 0xdb => 0x7c5f, 0xdc => 0x7c64,
    0xdd => 0x7c56, 0xde => 0x7c65, 0xdf => 0x7c6c, 0xe0 => 0x7c75,
    0xe1 => 0x7c83, 0xe2 => 0x7c90, 0xe3 => 0x7ca4, 0xe4 => 0x7cad,
    0xe5 => 0x7ca2, 0xe6 => 0x7cab, 0xe7 => 0x7ca1, 0xe8 => 0x7ca8,
    0xe9 => 0x7cb3, 0xea => 0x7cb2, 0xeb => 0x7cb1, 0xec => 0x7cae,
    0xed => 0x7cb9, 0xee => 0x7cbd, 0xef => 0x7cc0, 0xf0 => 0x7cc5,
    0xf1 => 0x7cc2, 0xf2 => 0x7cd8, 0xf3 => 0x7cd2, 0xf4 => 0x7cdc,
    0xf5 => 0x7ce2, 0xf6 => 0x9b3b, 0xf7 => 0x7cef, 0xf8 => 0x7cf2,
    0xf9 => 0x7cf4, 0xfa => 0x7cf6, 0xfb => 0x7cfa, 0xfc => 0x7d06,
  },
  0xe3 => {
    0x40 => 0x7d02, 0x41 => 0x7d1c, 0x42 => 0x7d15, 0x43 => 0x7d0a,
    0x44 => 0x7d45, 0x45 => 0x7d4b, 0x46 => 0x7d2e, 0x47 => 0x7d32,
    0x48 => 0x7d3f, 0x49 => 0x7d35, 0x4a => 0x7d46, 0x4b => 0x7d73,
    0x4c => 0x7d56, 0x4d => 0x7d4e, 0x4e => 0x7d72, 0x4f => 0x7d68,
    0x50 => 0x7d6e, 0x51 => 0x7d4f, 0x52 => 0x7d63, 0x53 => 0x7d93,
    0x54 => 0x7d89, 0x55 => 0x7d5b, 0x56 => 0x7d8f, 0x57 => 0x7d7d,
    0x58 => 0x7d9b, 0x59 => 0x7dba, 0x5a => 0x7dae, 0x5b => 0x7da3,
    0x5c => 0x7db5, 0x5d => 0x7dc7, 0x5e => 0x7dbd, 0x5f => 0x7dab,
    0x60 => 0x7e3d, 0x61 => 0x7da2, 0x62 => 0x7daf, 0x63 => 0x7ddc,
    0x64 => 0x7db8, 0x65 => 0x7d9f, 0x66 => 0x7db0, 0x67 => 0x7dd8,
    0x68 => 0x7ddd, 0x69 => 0x7de4, 0x6a => 0x7dde, 0x6b => 0x7dfb,
    0x6c => 0x7df2, 0x6d => 0x7de1, 0x6e => 0x7e05, 0x6f => 0x7e0a,
    0x70 => 0x7e23, 0x71 => 0x7e21, 0x72 => 0x7e12, 0x73 => 0x7e31,
    0x74 => 0x7e1f, 0x75 => 0x7e09, 0x76 => 0x7e0b, 0x77 => 0x7e22,
    0x78 => 0x7e46, 0x79 => 0x7e66, 0x7a => 0x7e3b, 0x7b => 0x7e35,
    0x7c => 0x7e39, 0x7d => 0x7e43, 0x7e => 0x7e37, 0x80 => 0x7e32,
    0x81 => 0x7e3a, 0x82 => 0x7e67, 0x83 => 0x7e5d, 0x84 => 0x7e56,
    0x85 => 0x7e5e, 0x86 => 0x7e59, 0x87 => 0x7e5a, 0x88 => 0x7e79,
    0x89 => 0x7e6a, 0x8a => 0x7e69, 0x8b => 0x7e7c, 0x8c => 0x7e7b,
    0x8d => 0x7e83, 0x8e => 0x7dd5, 0x8f => 0x7e7d, 0x90 => 0x8fae,
    0x91 => 0x7e7f, 0x92 => 0x7e88, 0x93 => 0x7e89, 0x94 => 0x7e8c,
    0x95 => 0x7e92, 0x96 => 0x7e90, 0x97 => 0x7e93, 0x98 => 0x7e94,
    0x99 => 0x7e96, 0x9a => 0x7e8e, 0x9b => 0x7e9b, 0x9c => 0x7e9c,
    0x9d => 0x7f38, 0x9e => 0x7f3a, 0x9f => 0x7f45, 0xa0 => 0x7f4c,
    0xa1 => 0x7f4d, 0xa2 => 0x7f4e, 0xa3 => 0x7f50, 0xa4 => 0x7f51,
    0xa5 => 0x7f55, 0xa6 => 0x7f54, 0xa7 => 0x7f58, 0xa8 => 0x7f5f,
    0xa9 => 0x7f60, 0xaa => 0x7f68, 0xab => 0x7f69, 0xac => 0x7f67,
    0xad => 0x7f78, 0xae => 0x7f82, 0xaf => 0x7f86, 0xb0 => 0x7f83,
    0xb1 => 0x7f88, 0xb2 => 0x7f87, 0xb3 => 0x7f8c, 0xb4 => 0x7f94,
    0xb5 => 0x7f9e, 0xb6 => 0x7f9d, 0xb7 => 0x7f9a, 0xb8 => 0x7fa3,
    0xb9 => 0x7faf, 0xba => 0x7fb2, 0xbb => 0x7fb9, 0xbc => 0x7fae,
    0xbd => 0x7fb6, 0xbe => 0x7fb8, 0xbf => 0x8b71, 0xc0 => 0x7fc5,
    0xc1 => 0x7fc6, 0xc2 => 0x7fca, 0xc3 => 0x7fd5, 0xc4 => 0x7fd4,
    0xc5 => 0x7fe1, 0xc6 => 0x7fe6, 0xc7 => 0x7fe9, 0xc8 => 0x7ff3,
    0xc9 => 0x7ff9, 0xca => 0x98dc, 0xcb => 0x8006, 0xcc => 0x8004,
    0xcd => 0x800b, 0xce => 0x8012, 0xcf => 0x8018, 0xd0 => 0x8019,
    0xd1 => 0x801c, 0xd2 => 0x8021, 0xd3 => 0x8028, 0xd4 => 0x803f,
    0xd5 => 0x803b, 0xd6 => 0x804a, 0xd7 => 0x8046, 0xd8 => 0x8052,
    0xd9 => 0x8058, 0xda => 0x805a, 0xdb => 0x805f, 0xdc => 0x8062,
    0xdd => 0x8068, 0xde => 0x8073, 0xdf => 0x8072, 0xe0 => 0x8070,
    0xe1 => 0x8076, 0xe2 => 0x8079, 0xe3 => 0x807d, 0xe4 => 0x807f,
    0xe5 => 0x8084, 0xe6 => 0x8086, 0xe7 => 0x8085, 0xe8 => 0x809b,
    0xe9 => 0x8093, 0xea => 0x809a, 0xeb => 0x80ad, 0xec => 0x5190,
    0xed => 0x80ac, 0xee => 0x80db, 0xef => 0x80e5, 0xf0 => 0x80d9,
    0xf1 => 0x80dd, 0xf2 => 0x80c4, 0xf3 => 0x80da, 0xf4 => 0x80d6,
    0xf5 => 0x8109, 0xf6 => 0x80ef, 0xf7 => 0x80f1, 0xf8 => 0x811b,
    0xf9 => 0x8129, 0xfa => 0x8123, 0xfb => 0x812f, 0xfc => 0x814b,
  },
  0xe4 => {
    0x40 => 0x968b, 0x41 => 0x8146, 0x42 => 0x813e, 0x43 => 0x8153,
    0x44 => 0x8151, 0x45 => 0x80fc, 0x46 => 0x8171, 0x47 => 0x816e,
    0x48 => 0x8165, 0x49 => 0x8166, 0x4a => 0x8174, 0x4b => 0x8183,
    0x4c => 0x8188, 0x4d => 0x818a, 0x4e => 0x8180, 0x4f => 0x8182,
    0x50 => 0x81a0, 0x51 => 0x8195, 0x52 => 0x81a4, 0x53 => 0x81a3,
    0x54 => 0x815f, 0x55 => 0x8193, 0x56 => 0x81a9, 0x57 => 0x81b0,
    0x58 => 0x81b5, 0x59 => 0x81be, 0x5a => 0x81b8, 0x5b => 0x81bd,
    0x5c => 0x81c0, 0x5d => 0x81c2, 0x5e => 0x81ba, 0x5f => 0x81c9,
    0x60 => 0x81cd, 0x61 => 0x81d1, 0x62 => 0x81d9, 0x63 => 0x81d8,
    0x64 => 0x81c8, 0x65 => 0x81da, 0x66 => 0x81df, 0x67 => 0x81e0,
    0x68 => 0x81e7, 0x69 => 0x81fa, 0x6a => 0x81fb, 0x6b => 0x81fe,
    0x6c => 0x8201, 0x6d => 0x8202, 0x6e => 0x8205, 0x6f => 0x8207,
    0x70 => 0x820a, 0x71 => 0x820d, 0x72 => 0x8210, 0x73 => 0x8216,
    0x74 => 0x8229, 0x75 => 0x822b, 0x76 => 0x8238, 0x77 => 0x8233,
    0x78 => 0x8240, 0x79 => 0x8259, 0x7a => 0x8258, 0x7b => 0x825d,
    0x7c => 0x825a, 0x7d => 0x825f, 0x7e => 0x8264, 0x80 => 0x8262,
    0x81 => 0x8268, 0x82 => 0x826a, 0x83 => 0x826b, 0x84 => 0x822e,
    0x85 => 0x8271, 0x86 => 0x8277, 0x87 => 0x8278, 0x88 => 0x827e,
    0x89 => 0x828d, 0x8a => 0x8292, 0x8b => 0x82ab, 0x8c => 0x829f,
    0x8d => 0x82bb, 0x8e => 0x82ac, 0x8f => 0x82e1, 0x90 => 0x82e3,
    0x91 => 0x82df, 0x92 => 0x82d2, 0x93 => 0x82f4, 0x94 => 0x82f3,
    0x95 => 0x82fa, 0x96 => 0x8393, 0x97 => 0x8303, 0x98 => 0x82fb,
    0x99 => 0x82f9, 0x9a => 0x82de, 0x9b => 0x8306, 0x9c => 0x82dc,
    0x9d => 0x8309, 0x9e => 0x82d9, 0x9f => 0x8335, 0xa0 => 0x8334,
    0xa1 => 0x8316, 0xa2 => 0x8332, 0xa3 => 0x8331, 0xa4 => 0x8340,
    0xa5 => 0x8339, 0xa6 => 0x8350, 0xa7 => 0x8345, 0xa8 => 0x832f,
    0xa9 => 0x832b, 0xaa => 0x8317, 0xab => 0x8318, 0xac => 0x8385,
    0xad => 0x839a, 0xae => 0x83aa, 0xaf => 0x839f, 0xb0 => 0x83a2,
    0xb1 => 0x8396, 0xb2 => 0x8323, 0xb3 => 0x838e, 0xb4 => 0x8387,
    0xb5 => 0x838a, 0xb6 => 0x837c, 0xb7 => 0x83b5, 0xb8 => 0x8373,
    0xb9 => 0x8375, 0xba => 0x83a0, 0xbb => 0x8389, 0xbc => 0x83a8,
    0xbd => 0x83f4, 0xbe => 0x8413, 0xbf => 0x83eb, 0xc0 => 0x83ce,
    0xc1 => 0x83fd, 0xc2 => 0x8403, 0xc3 => 0x83d8, 0xc4 => 0x840b,
    0xc5 => 0x83c1, 0xc6 => 0x83f7, 0xc7 => 0x8407, 0xc8 => 0x83e0,
    0xc9 => 0x83f2, 0xca => 0x840d, 0xcb => 0x8422, 0xcc => 0x8420,
    0xcd => 0x83bd, 0xce => 0x8438, 0xcf => 0x8506, 0xd0 => 0x83fb,
    0xd1 => 0x846d, 0xd2 => 0x842a, 0xd3 => 0x843c, 0xd4 => 0x855a,
    0xd5 => 0x8484, 0xd6 => 0x8477, 0xd7 => 0x846b, 0xd8 => 0x84ad,
    0xd9 => 0x846e, 0xda => 0x8482, 0xdb => 0x8469, 0xdc => 0x8446,
    0xdd => 0x842c, 0xde => 0x846f, 0xdf => 0x8479, 0xe0 => 0x8435,
    0xe1 => 0x84ca, 0xe2 => 0x8462, 0xe3 => 0x84b9, 0xe4 => 0x84bf,
    0xe5 => 0x849f, 0xe6 => 0x84d9, 0xe7 => 0x84cd, 0xe8 => 0x84bb,
    0xe9 => 0x84da, 0xea => 0x84d0, 0xeb => 0x84c1, 0xec => 0x84c6,
    0xed => 0x84d6, 0xee => 0x84a1, 0xef => 0x8521, 0xf0 => 0x84ff,
    0xf1 => 0x84f4, 0xf2 => 0x8517, 0xf3 => 0x8518, 0xf4 => 0x852c,
    0xf5 => 0x851f, 0xf6 => 0x8515, 0xf7 => 0x8514, 0xf8 => 0x84fc,
    0xf9 => 0x8540, 0xfa => 0x8563, 0xfb => 0x8558, 0xfc => 0x8548,
  },
  0xe5 => {
    0x40 => 0x8541, 0x41 => 0x8602, 0x42 => 0x854b, 0x43 => 0x8555,
    0x44 => 0x8580, 0x45 => 0x85a4, 0x46 => 0x8588, 0x47 => 0x8591,
    0x48 => 0x858a, 0x49 => 0x85a8, 0x4a => 0x856d, 0x4b => 0x8594,
    0x4c => 0x859b, 0x4d => 0x85ea, 0x4e => 0x8587, 0x4f => 0x859c,
    0x50 => 0x8577, 0x51 => 0x857e, 0x52 => 0x8590, 0x53 => 0x85c9,
    0x54 => 0x85ba, 0x55 => 0x85cf, 0x56 => 0x85b9, 0x57 => 0x85d0,
    0x58 => 0x85d5, 0x59 => 0x85dd, 0x5a => 0x85e5, 0x5b => 0x85dc,
    0x5c => 0x85f9, 0x5d => 0x860a, 0x5e => 0x8613, 0x5f => 0x860b,
    0x60 => 0x85fe, 0x61 => 0x85fa, 0x62 => 0x8606, 0x63 => 0x8622,
    0x64 => 0x861a, 0x65 => 0x8630, 0x66 => 0x863f, 0x67 => 0x864d,
    0x68 => 0x4e55, 0x69 => 0x8654, 0x6a => 0x865f, 0x6b => 0x8667,
    0x6c => 0x8671, 0x6d => 0x8693, 0x6e => 0x86a3, 0x6f => 0x86a9,
    0x70 => 0x86aa, 0x71 => 0x868b, 0x72 => 0x868c, 0x73 => 0x86b6,
    0x74 => 0x86af, 0x75 => 0x86c4, 0x76 => 0x86c6, 0x77 => 0x86b0,
    0x78 => 0x86c9, 0x79 => 0x8823, 0x7a => 0x86ab, 0x7b => 0x86d4,
    0x7c => 0x86de, 0x7d => 0x86e9, 0x7e => 0x86ec, 0x80 => 0x86df,
    0x81 => 0x86db, 0x82 => 0x86ef, 0x83 => 0x8712, 0x84 => 0x8706,
    0x85 => 0x8708, 0x86 => 0x8700, 0x87 => 0x8703, 0x88 => 0x86fb,
    0x89 => 0x8711, 0x8a => 0x8709, 0x8b => 0x870d, 0x8c => 0x86f9,
    0x8d => 0x870a, 0x8e => 0x8734, 0x8f => 0x873f, 0x90 => 0x8737,
    0x91 => 0x873b, 0x92 => 0x8725, 0x93 => 0x8729, 0x94 => 0x871a,
    0x95 => 0x8760, 0x96 => 0x875f, 0x97 => 0x8778, 0x98 => 0x874c,
    0x99 => 0x874e, 0x9a => 0x8774, 0x9b => 0x8757, 0x9c => 0x8768,
    0x9d => 0x876e, 0x9e => 0x8759, 0x9f => 0x8753, 0xa0 => 0x8763,
    0xa1 => 0x876a, 0xa2 => 0x8805, 0xa3 => 0x87a2, 0xa4 => 0x879f,
    0xa5 => 0x8782, 0xa6 => 0x87af, 0xa7 => 0x87cb, 0xa8 => 0x87bd,
    0xa9 => 0x87c0, 0xaa => 0x87d0, 0xab => 0x96d6, 0xac => 0x87ab,
    0xad => 0x87c4, 0xae => 0x87b3, 0xaf => 0x87c7, 0xb0 => 0x87c6,
    0xb1 => 0x87bb, 0xb2 => 0x87ef, 0xb3 => 0x87f2, 0xb4 => 0x87e0,
    0xb5 => 0x880f, 0xb6 => 0x880d, 0xb7 => 0x87fe, 0xb8 => 0x87f6,
    0xb9 => 0x87f7, 0xba => 0x880e, 0xbb => 0x87d2, 0xbc => 0x8811,
    0xbd => 0x8816, 0xbe => 0x8815, 0xbf => 0x8822, 0xc0 => 0x8821,
    0xc1 => 0x8831, 0xc2 => 0x8836, 0xc3 => 0x8839, 0xc4 => 0x8827,
    0xc5 => 0x883b, 0xc6 => 0x8844, 0xc7 => 0x8842, 0xc8 => 0x8852,
    0xc9 => 0x8859, 0xca => 0x885e, 0xcb => 0x8862, 0xcc => 0x886b,
    0xcd => 0x8881, 0xce => 0x887e, 0xcf => 0x889e, 0xd0 => 0x8875,
    0xd1 => 0x887d, 0xd2 => 0x88b5, 0xd3 => 0x8872, 0xd4 => 0x8882,
    0xd5 => 0x8897, 0xd6 => 0x8892, 0xd7 => 0x88ae, 0xd8 => 0x8899,
    0xd9 => 0x88a2, 0xda => 0x888d, 0xdb => 0x88a4, 0xdc => 0x88b0,
    0xdd => 0x88bf, 0xde => 0x88b1, 0xdf => 0x88c3, 0xe0 => 0x88c4,
    0xe1 => 0x88d4, 0xe2 => 0x88d8, 0xe3 => 0x88d9, 0xe4 => 0x88dd,
    0xe5 => 0x88f9, 0xe6 => 0x8902, 0xe7 => 0x88fc, 0xe8 => 0x88f4,
    0xe9 => 0x88e8, 0xea => 0x88f2, 0xeb => 0x8904, 0xec => 0x890c,
    0xed => 0x890a, 0xee => 0x8913, 0xef => 0x8943, 0xf0 => 0x891e,
    0xf1 => 0x8925, 0xf2 => 0x892a, 0xf3 => 0x892b, 0xf4 => 0x8941,
    0xf5 => 0x8944, 0xf6 => 0x893b, 0xf7 => 0x8936, 0xf8 => 0x8938,
    0xf9 => 0x894c, 0xfa => 0x891d, 0xfb => 0x8960, 0xfc => 0x895e,
  },
  0xe6 => {
    0x40 => 0x8966, 0x41 => 0x8964, 0x42 => 0x896d, 0x43 => 0x896a,
    0x44 => 0x896f, 0x45 => 0x8974, 0x46 => 0x8977, 0x47 => 0x897e,
    0x48 => 0x8983, 0x49 => 0x8988, 0x4a => 0x898a, 0x4b => 0x8993,
    0x4c => 0x8998, 0x4d => 0x89a1, 0x4e => 0x89a9, 0x4f => 0x89a6,
    0x50 => 0x89ac, 0x51 => 0x89af, 0x52 => 0x89b2, 0x53 => 0x89ba,
    0x54 => 0x89bd, 0x55 => 0x89bf, 0x56 => 0x89c0, 0x57 => 0x89da,
    0x58 => 0x89dc, 0x59 => 0x89dd, 0x5a => 0x89e7, 0x5b => 0x89f4,
    0x5c => 0x89f8, 0x5d => 0x8a03, 0x5e => 0x8a16, 0x5f => 0x8a10,
    0x60 => 0x8a0c, 0x61 => 0x8a1b, 0x62 => 0x8a1d, 0x63 => 0x8a25,
    0x64 => 0x8a36, 0x65 => 0x8a41, 0x66 => 0x8a5b, 0x67 => 0x8a52,
    0x68 => 0x8a46, 0x69 => 0x8a48, 0x6a => 0x8a7c, 0x6b => 0x8a6d,
    0x6c => 0x8a6c, 0x6d => 0x8a62, 0x6e => 0x8a85, 0x6f => 0x8a82,
    0x70 => 0x8a84, 0x71 => 0x8aa8, 0x72 => 0x8aa1, 0x73 => 0x8a91,
    0x74 => 0x8aa5, 0x75 => 0x8aa6, 0x76 => 0x8a9a, 0x77 => 0x8aa3,
    0x78 => 0x8ac4, 0x79 => 0x8acd, 0x7a => 0x8ac2, 0x7b => 0x8ada,
    0x7c => 0x8aeb, 0x7d => 0x8af3, 0x7e => 0x8ae7, 0x80 => 0x8ae4,
    0x81 => 0x8af1, 0x82 => 0x8b14, 0x83 => 0x8ae0, 0x84 => 0x8ae2,
    0x85 => 0x8af7, 0x86 => 0x8ade, 0x87 => 0x8adb, 0x88 => 0x8b0c,
    0x89 => 0x8b07, 0x8a => 0x8b1a, 0x8b => 0x8ae1, 0x8c => 0x8b16,
    0x8d => 0x8b10, 0x8e => 0x8b17, 0x8f => 0x8b20, 0x90 => 0x8b33,
    0x91 => 0x97ab, 0x92 => 0x8b26, 0x93 => 0x8b2b, 0x94 => 0x8b3e,
    0x95 => 0x8b28, 0x96 => 0x8b41, 0x97 => 0x8b4c, 0x98 => 0x8b4f,
    0x99 => 0x8b4e, 0x9a => 0x8b49, 0x9b => 0x8b56, 0x9c => 0x8b5b,
    0x9d => 0x8b5a, 0x9e => 0x8b6b, 0x9f => 0x8b5f, 0xa0 => 0x8b6c,
    0xa1 => 0x8b6f, 0xa2 => 0x8b74, 0xa3 => 0x8b7d, 0xa4 => 0x8b80,
    0xa5 => 0x8b8c, 0xa6 => 0x8b8e, 0xa7 => 0x8b92, 0xa8 => 0x8b93,
    0xa9 => 0x8b96, 0xaa => 0x8b99, 0xab => 0x8b9a, 0xac => 0x8c3a,
    0xad => 0x8c41, 0xae => 0x8c3f, 0xaf => 0x8c48, 0xb0 => 0x8c4c,
    0xb1 => 0x8c4e, 0xb2 => 0x8c50, 0xb3 => 0x8c55, 0xb4 => 0x8c62,
    0xb5 => 0x8c6c, 0xb6 => 0x8c78, 0xb7 => 0x8c7a, 0xb8 => 0x8c82,
    0xb9 => 0x8c89, 0xba => 0x8c85, 0xbb => 0x8c8a, 0xbc => 0x8c8d,
    0xbd => 0x8c8e, 0xbe => 0x8c94, 0xbf => 0x8c7c, 0xc0 => 0x8c98,
    0xc1 => 0x621d, 0xc2 => 0x8cad, 0xc3 => 0x8caa, 0xc4 => 0x8cbd,
    0xc5 => 0x8cb2, 0xc6 => 0x8cb3, 0xc7 => 0x8cae, 0xc8 => 0x8cb6,
    0xc9 => 0x8cc8, 0xca => 0x8cc1, 0xcb => 0x8ce4, 0xcc => 0x8ce3,
    0xcd => 0x8cda, 0xce => 0x8cfd, 0xcf => 0x8cfa, 0xd0 => 0x8cfb,
    0xd1 => 0x8d04, 0xd2 => 0x8d05, 0xd3 => 0x8d0a, 0xd4 => 0x8d07,
    0xd5 => 0x8d0f, 0xd6 => 0x8d0d, 0xd7 => 0x8d10, 0xd8 => 0x9f4e,
    0xd9 => 0x8d13, 0xda => 0x8ccd, 0xdb => 0x8d14, 0xdc => 0x8d16,
    0xdd => 0x8d67, 0xde => 0x8d6d, 0xdf => 0x8d71, 0xe0 => 0x8d73,
    0xe1 => 0x8d81, 0xe2 => 0x8d99, 0xe3 => 0x8dc2, 0xe4 => 0x8dbe,
    0xe5 => 0x8dba, 0xe6 => 0x8dcf, 0xe7 => 0x8dda, 0xe8 => 0x8dd6,
    0xe9 => 0x8dcc, 0xea => 0x8ddb, 0xeb => 0x8dcb, 0xec => 0x8dea,
    0xed => 0x8deb, 0xee => 0x8ddf, 0xef => 0x8de3, 0xf0 => 0x8dfc,
    0xf1 => 0x8e08, 0xf2 => 0x8e09, 0xf3 => 0x8dff, 0xf4 => 0x8e1d,
    0xf5 => 0x8e1e, 0xf6 => 0x8e10, 0xf7 => 0x8e1f, 0xf8 => 0x8e42,
    0xf9 => 0x8e35, 0xfa => 0x8e30, 0xfb => 0x8e34, 0xfc => 0x8e4a,
  },
  0xe7 => {
    0x40 => 0x8e47, 0x41 => 0x8e49, 0x42 => 0x8e4c, 0x43 => 0x8e50,
    0x44 => 0x8e48, 0x45 => 0x8e59, 0x46 => 0x8e64, 0x47 => 0x8e60,
    0x48 => 0x8e2a, 0x49 => 0x8e63, 0x4a => 0x8e55, 0x4b => 0x8e76,
    0x4c => 0x8e72, 0x4d => 0x8e7c, 0x4e => 0x8e81, 0x4f => 0x8e87,
    0x50 => 0x8e85, 0x51 => 0x8e84, 0x52 => 0x8e8b, 0x53 => 0x8e8a,
    0x54 => 0x8e93, 0x55 => 0x8e91, 0x56 => 0x8e94, 0x57 => 0x8e99,
    0x58 => 0x8eaa, 0x59 => 0x8ea1, 0x5a => 0x8eac, 0x5b => 0x8eb0,
    0x5c => 0x8ec6, 0x5d => 0x8eb1, 0x5e => 0x8ebe, 0x5f => 0x8ec5,
    0x60 => 0x8ec8, 0x61 => 0x8ecb, 0x62 => 0x8edb, 0x63 => 0x8ee3,
    0x64 => 0x8efc, 0x65 => 0x8efb, 0x66 => 0x8eeb, 0x67 => 0x8efe,
    0x68 => 0x8f0a, 0x69 => 0x8f05, 0x6a => 0x8f15, 0x6b => 0x8f12,
    0x6c => 0x8f19, 0x6d => 0x8f13, 0x6e => 0x8f1c, 0x6f => 0x8f1f,
    0x70 => 0x8f1b, 0x71 => 0x8f0c, 0x72 => 0x8f26, 0x73 => 0x8f33,
    0x74 => 0x8f3b, 0x75 => 0x8f39, 0x76 => 0x8f45, 0x77 => 0x8f42,
    0x78 => 0x8f3e, 0x79 => 0x8f4c, 0x7a => 0x8f49, 0x7b => 0x8f46,
    0x7c => 0x8f4e, 0x7d => 0x8f57, 0x7e => 0x8f5c, 0x80 => 0x8f62,
    0x81 => 0x8f63, 0x82 => 0x8f64, 0x83 => 0x8f9c, 0x84 => 0x8f9f,
    0x85 => 0x8fa3, 0x86 => 0x8fad, 0x87 => 0x8faf, 0x88 => 0x8fb7,
    0x89 => 0x8fda, 0x8a => 0x8fe5, 0x8b => 0x8fe2, 0x8c => 0x8fea,
    0x8d => 0x8fef, 0x8e => 0x9087, 0x8f => 0x8ff4, 0x90 => 0x9005,
    0x91 => 0x8ff9, 0x92 => 0x8ffa, 0x93 => 0x9011, 0x94 => 0x9015,
    0x95 => 0x9021, 0x96 => 0x900d, 0x97 => 0x901e, 0x98 => 0x9016,
    0x99 => 0x900b, 0x9a => 0x9027, 0x9b => 0x9036, 0x9c => 0x9035,
    0x9d => 0x9039, 0x9e => 0x8ff8, 0x9f => 0x904f, 0xa0 => 0x9050,
    0xa1 => 0x9051, 0xa2 => 0x9052, 0xa3 => 0x900e, 0xa4 => 0x9049,
    0xa5 => 0x903e, 0xa6 => 0x9056, 0xa7 => 0x9058, 0xa8 => 0x905e,
    0xa9 => 0x9068, 0xaa => 0x906f, 0xab => 0x9076, 0xac => 0x96a8,
    0xad => 0x9072, 0xae => 0x9082, 0xaf => 0x907d, 0xb0 => 0x9081,
    0xb1 => 0x9080, 0xb2 => 0x908a, 0xb3 => 0x9089, 0xb4 => 0x908f,
    0xb5 => 0x90a8, 0xb6 => 0x90af, 0xb7 => 0x90b1, 0xb8 => 0x90b5,
    0xb9 => 0x90e2, 0xba => 0x90e4, 0xbb => 0x6248, 0xbc => 0x90db,
    0xbd => 0x9102, 0xbe => 0x9112, 0xbf => 0x9119, 0xc0 => 0x9132,
    0xc1 => 0x9130, 0xc2 => 0x914a, 0xc3 => 0x9156, 0xc4 => 0x9158,
    0xc5 => 0x9163, 0xc6 => 0x9165, 0xc7 => 0x9169, 0xc8 => 0x9173,
    0xc9 => 0x9172, 0xca => 0x918b, 0xcb => 0x9189, 0xcc => 0x9182,
    0xcd => 0x91a2, 0xce => 0x91ab, 0xcf => 0x91af, 0xd0 => 0x91aa,
    0xd1 => 0x91b5, 0xd2 => 0x91b4, 0xd3 => 0x91ba, 0xd4 => 0x91c0,
    0xd5 => 0x91c1, 0xd6 => 0x91c9, 0xd7 => 0x91cb, 0xd8 => 0x91d0,
    0xd9 => 0x91d6, 0xda => 0x91df, 0xdb => 0x91e1, 0xdc => 0x91db,
    0xdd => 0x91fc, 0xde => 0x91f5, 0xdf => 0x91f6, 0xe0 => 0x921e,
    0xe1 => 0x91ff, 0xe2 => 0x9214, 0xe3 => 0x922c, 0xe4 => 0x9215,
    0xe5 => 0x9211, 0xe6 => 0x925e, 0xe7 => 0x9257, 0xe8 => 0x9245,
    0xe9 => 0x9249, 0xea => 0x9264, 0xeb => 0x9248, 0xec => 0x9295,
    0xed => 0x923f, 0xee => 0x924b, 0xef => 0x9250, 0xf0 => 0x929c,
    0xf1 => 0x9296, 0xf2 => 0x9293, 0xf3 => 0x929b, 0xf4 => 0x925a,
    0xf5 => 0x92cf, 0xf6 => 0x92b9, 0xf7 => 0x92b7, 0xf8 => 0x92e9,
    0xf9 => 0x930f, 0xfa => 0x92fa, 0xfb => 0x9344, 0xfc => 0x932e,
  },
  0xe8 => {
    0x40 => 0x9319, 0x41 => 0x9322, 0x42 => 0x931a, 0x43 => 0x9323,
    0x44 => 0x933a, 0x45 => 0x9335, 0x46 => 0x933b, 0x47 => 0x935c,
    0x48 => 0x9360, 0x49 => 0x937c, 0x4a => 0x936e, 0x4b => 0x9356,
    0x4c => 0x93b0, 0x4d => 0x93ac, 0x4e => 0x93ad, 0x4f => 0x9394,
    0x50 => 0x93b9, 0x51 => 0x93d6, 0x52 => 0x93d7, 0x53 => 0x93e8,
    0x54 => 0x93e5, 0x55 => 0x93d8, 0x56 => 0x93c3, 0x57 => 0x93dd,
    0x58 => 0x93d0, 0x59 => 0x93c8, 0x5a => 0x93e4, 0x5b => 0x941a,
    0x5c => 0x9414, 0x5d => 0x9413, 0x5e => 0x9403, 0x5f => 0x9407,
    0x60 => 0x9410, 0x61 => 0x9436, 0x62 => 0x942b, 0x63 => 0x9435,
    0x64 => 0x9421, 0x65 => 0x943a, 0x66 => 0x9441, 0x67 => 0x9452,
    0x68 => 0x9444, 0x69 => 0x945b, 0x6a => 0x9460, 0x6b => 0x9462,
    0x6c => 0x945e, 0x6d => 0x946a, 0x6e => 0x9229, 0x6f => 0x9470,
    0x70 => 0x9475, 0x71 => 0x9477, 0x72 => 0x947d, 0x73 => 0x945a,
    0x74 => 0x947c, 0x75 => 0x947e, 0x76 => 0x9481, 0x77 => 0x947f,
    0x78 => 0x9582, 0x79 => 0x9587, 0x7a => 0x958a, 0x7b => 0x9594,
    0x7c => 0x9596, 0x7d => 0x9598, 0x7e => 0x9599, 0x80 => 0x95a0,
    0x81 => 0x95a8, 0x82 => 0x95a7, 0x83 => 0x95ad, 0x84 => 0x95bc,
    0x85 => 0x95bb, 0x86 => 0x95b9, 0x87 => 0x95be, 0x88 => 0x95ca,
    0x89 => 0x6ff6, 0x8a => 0x95c3, 0x8b => 0x95cd, 0x8c => 0x95cc,
    0x8d => 0x95d5, 0x8e => 0x95d4, 0x8f => 0x95d6, 0x90 => 0x95dc,
    0x91 => 0x95e1, 0x92 => 0x95e5, 0x93 => 0x95e2, 0x94 => 0x9621,
    0x95 => 0x9628, 0x96 => 0x962e, 0x97 => 0x962f, 0x98 => 0x9642,
    0x99 => 0x964c, 0x9a => 0x964f, 0x9b => 0x964b, 0x9c => 0x9677,
    0x9d => 0x965c, 0x9e => 0x965e, 0x9f => 0x965d, 0xa0 => 0x965f,
    0xa1 => 0x9666, 0xa2 => 0x9672, 0xa3 => 0x966c, 0xa4 => 0x968d,
    0xa5 => 0x9698, 0xa6 => 0x9695, 0xa7 => 0x9697, 0xa8 => 0x96aa,
    0xa9 => 0x96a7, 0xaa => 0x96b1, 0xab => 0x96b2, 0xac => 0x96b0,
    0xad => 0x96b4, 0xae => 0x96b6, 0xaf => 0x96b8, 0xb0 => 0x96b9,
    0xb1 => 0x96ce, 0xb2 => 0x96cb, 0xb3 => 0x96c9, 0xb4 => 0x96cd,
    0xb5 => 0x894d, 0xb6 => 0x96dc, 0xb7 => 0x970d, 0xb8 => 0x96d5,
    0xb9 => 0x96f9, 0xba => 0x9704, 0xbb => 0x9706, 0xbc => 0x9708,
    0xbd => 0x9713, 0xbe => 0x970e, 0xbf => 0x9711, 0xc0 => 0x970f,
    0xc1 => 0x9716, 0xc2 => 0x9719, 0xc3 => 0x9724, 0xc4 => 0x972a,
    0xc5 => 0x9730, 0xc6 => 0x9739, 0xc7 => 0x973d, 0xc8 => 0x973e,
    0xc9 => 0x9744, 0xca => 0x9746, 0xcb => 0x9748, 0xcc => 0x9742,
    0xcd => 0x9749, 0xce => 0x975c, 0xcf => 0x9760, 0xd0 => 0x9764,
    0xd1 => 0x9766, 0xd2 => 0x9768, 0xd3 => 0x52d2, 0xd4 => 0x976b,
    0xd5 => 0x9771, 0xd6 => 0x9779, 0xd7 => 0x9785, 0xd8 => 0x977c,
    0xd9 => 0x9781, 0xda => 0x977a, 0xdb => 0x9786, 0xdc => 0x978b,
    0xdd => 0x978f, 0xde => 0x9790, 0xdf => 0x979c, 0xe0 => 0x97a8,
    0xe1 => 0x97a6, 0xe2 => 0x97a3, 0xe3 => 0x97b3, 0xe4 => 0x97b4,
    0xe5 => 0x97c3, 0xe6 => 0x97c6, 0xe7 => 0x97c8, 0xe8 => 0x97cb,
    0xe9 => 0x97dc, 0xea => 0x97ed, 0xeb => 0x9f4f, 0xec => 0x97f2,
    0xed => 0x7adf, 0xee => 0x97f6, 0xef => 0x97f5, 0xf0 => 0x980f,
    0xf1 => 0x980c, 0xf2 => 0x9838, 0xf3 => 0x9824, 0xf4 => 0x9821,
    0xf5 => 0x9837, 0xf6 => 0x983d, 0xf7 => 0x9846, 0xf8 => 0x984f,
    0xf9 => 0x984b, 0xfa => 0x986b, 0xfb => 0x986f, 0xfc => 0x9870,
  },
  0xe9 => {
    0x40 => 0x9871, 0x41 => 0x9874, 0x42 => 0x9873, 0x43 => 0x98aa,
    0x44 => 0x98af, 0x45 => 0x98b1, 0x46 => 0x98b6, 0x47 => 0x98c4,
    0x48 => 0x98c3, 0x49 => 0x98c6, 0x4a => 0x98e9, 0x4b => 0x98eb,
    0x4c => 0x9903, 0x4d => 0x9909, 0x4e => 0x9912, 0x4f => 0x9914,
    0x50 => 0x9918, 0x51 => 0x9921, 0x52 => 0x991d, 0x53 => 0x991e,
    0x54 => 0x9924, 0x55 => 0x9920, 0x56 => 0x992c, 0x57 => 0x992e,
    0x58 => 0x993d, 0x59 => 0x993e, 0x5a => 0x9942, 0x5b => 0x9949,
    0x5c => 0x9945, 0x5d => 0x9950, 0x5e => 0x994b, 0x5f => 0x9951,
    0x60 => 0x9952, 0x61 => 0x994c, 0x62 => 0x9955, 0x63 => 0x9997,
    0x64 => 0x9998, 0x65 => 0x99a5, 0x66 => 0x99ad, 0x67 => 0x99ae,
    0x68 => 0x99bc, 0x69 => 0x99df, 0x6a => 0x99db, 0x6b => 0x99dd,
    0x6c => 0x99d8, 0x6d => 0x99d1, 0x6e => 0x99ed, 0x6f => 0x99ee,
    0x70 => 0x99f1, 0x71 => 0x99f2, 0x72 => 0x99fb, 0x73 => 0x99f8,
    0x74 => 0x9a01, 0x75 => 0x9a0f, 0x76 => 0x9a05, 0x77 => 0x99e2,
    0x78 => 0x9a19, 0x79 => 0x9a2b, 0x7a => 0x9a37, 0x7b => 0x9a45,
    0x7c => 0x9a42, 0x7d => 0x9a40, 0x7e => 0x9a43, 0x80 => 0x9a3e,
    0x81 => 0x9a55, 0x82 => 0x9a4d, 0x83 => 0x9a5b, 0x84 => 0x9a57,
    0x85 => 0x9a5f, 0x86 => 0x9a62, 0x87 => 0x9a65, 0x88 => 0x9a64,
    0x89 => 0x9a69, 0x8a => 0x9a6b, 0x8b => 0x9a6a, 0x8c => 0x9aad,
    0x8d => 0x9ab0, 0x8e => 0x9abc, 0x8f => 0x9ac0, 0x90 => 0x9acf,
    0x91 => 0x9ad1, 0x92 => 0x9ad3, 0x93 => 0x9ad4, 0x94 => 0x9ade,
    0x95 => 0x9adf, 0x96 => 0x9ae2, 0x97 => 0x9ae3, 0x98 => 0x9ae6,
    0x99 => 0x9aef, 0x9a => 0x9aeb, 0x9b => 0x9aee, 0x9c => 0x9af4,
    0x9d => 0x9af1, 0x9e => 0x9af7, 0x9f => 0x9afb, 0xa0 => 0x9b06,
    0xa1 => 0x9b18, 0xa2 => 0x9b1a, 0xa3 => 0x9b1f, 0xa4 => 0x9b22,
    0xa5 => 0x9b23, 0xa6 => 0x9b25, 0xa7 => 0x9b27, 0xa8 => 0x9b28,
    0xa9 => 0x9b29, 0xaa => 0x9b2a, 0xab => 0x9b2e, 0xac => 0x9b2f,
    0xad => 0x9b32, 0xae => 0x9b44, 0xaf => 0x9b43, 0xb0 => 0x9b4f,
    0xb1 => 0x9b4d, 0xb2 => 0x9b4e, 0xb3 => 0x9b51, 0xb4 => 0x9b58,
    0xb5 => 0x9b74, 0xb6 => 0x9b93, 0xb7 => 0x9b83, 0xb8 => 0x9b91,
    0xb9 => 0x9b96, 0xba => 0x9b97, 0xbb => 0x9b9f, 0xbc => 0x9ba0,
    0xbd => 0x9ba8, 0xbe => 0x9bb4, 0xbf => 0x9bc0, 0xc0 => 0x9bca,
    0xc1 => 0x9bb9, 0xc2 => 0x9bc6, 0xc3 => 0x9bcf, 0xc4 => 0x9bd1,
    0xc5 => 0x9bd2, 0xc6 => 0x9be3, 0xc7 => 0x9be2, 0xc8 => 0x9be4,
    0xc9 => 0x9bd4, 0xca => 0x9be1, 0xcb => 0x9c3a, 0xcc => 0x9bf2,
    0xcd => 0x9bf1, 0xce => 0x9bf0, 0xcf => 0x9c15, 0xd0 => 0x9c14,
    0xd1 => 0x9c09, 0xd2 => 0x9c13, 0xd3 => 0x9c0c, 0xd4 => 0x9c06,
    0xd5 => 0x9c08, 0xd6 => 0x9c12, 0xd7 => 0x9c0a, 0xd8 => 0x9c04,
    0xd9 => 0x9c2e, 0xda => 0x9c1b, 0xdb => 0x9c25, 0xdc => 0x9c24,
    0xdd => 0x9c21, 0xde => 0x9c30, 0xdf => 0x9c47, 0xe0 => 0x9c32,
    0xe1 => 0x9c46, 0xe2 => 0x9c3e, 0xe3 => 0x9c5a, 0xe4 => 0x9c60,
    0xe5 => 0x9c67, 0xe6 => 0x9c76, 0xe7 => 0x9c78, 0xe8 => 0x9ce7,
    0xe9 => 0x9cec, 0xea => 0x9cf0, 0xeb => 0x9d09, 0xec => 0x9d08,
    0xed => 0x9ceb, 0xee => 0x9d03, 0xef => 0x9d06, 0xf0 => 0x9d2a,
    0xf1 => 0x9d26, 0xf2 => 0x9daf, 0xf3 => 0x9d23, 0xf4 => 0x9d1f,
    0xf5 => 0x9d44, 0xf6 => 0x9d15, 0xf7 => 0x9d12, 0xf8 => 0x9d41,
    0xf9 => 0x9d3f, 0xfa => 0x9d3e, 0xfb => 0x9d46, 0xfc => 0x9d48,
  },
  0xea => {
    0x40 => 0x9d5d, 0x41 => 0x9d5e, 0x42 => 0x9d64, 0x43 => 0x9d51,
    0x44 => 0x9d50, 0x45 => 0x9d59, 0x46 => 0x9d72, 0x47 => 0x9d89,
    0x48 => 0x9d87, 0x49 => 0x9dab, 0x4a => 0x9d6f, 0x4b => 0x9d7a,
    0x4c => 0x9d9a, 0x4d => 0x9da4, 0x4e => 0x9da9, 0x4f => 0x9db2,
    0x50 => 0x9dc4, 0x51 => 0x9dc1, 0x52 => 0x9dbb, 0x53 => 0x9db8,
    0x54 => 0x9dba, 0x55 => 0x9dc6, 0x56 => 0x9dcf, 0x57 => 0x9dc2,
    0x58 => 0x9dd9, 0x59 => 0x9dd3, 0x5a => 0x9df8, 0x5b => 0x9de6,
    0x5c => 0x9ded, 0x5d => 0x9def, 0x5e => 0x9dfd, 0x5f => 0x9e1a,
    0x60 => 0x9e1b, 0x61 => 0x9e1e, 0x62 => 0x9e75, 0x63 => 0x9e79,
    0x64 => 0x9e7d, 0x65 => 0x9e81, 0x66 => 0x9e88, 0x67 => 0x9e8b,
    0x68 => 0x9e8c, 0x69 => 0x9e92, 0x6a => 0x9e95, 0x6b => 0x9e91,
    0x6c => 0x9e9d, 0x6d => 0x9ea5, 0x6e => 0x9ea9, 0x6f => 0x9eb8,
    0x70 => 0x9eaa, 0x71 => 0x9ead, 0x72 => 0x9761, 0x73 => 0x9ecc,
    0x74 => 0x9ece, 0x75 => 0x9ecf, 0x76 => 0x9ed0, 0x77 => 0x9ed4,
    0x78 => 0x9edc, 0x79 => 0x9ede, 0x7a => 0x9edd, 0x7b => 0x9ee0,
    0x7c => 0x9ee5, 0x7d => 0x9ee8, 0x7e => 0x9eef, 0x80 => 0x9ef4,
    0x81 => 0x9ef6, 0x82 => 0x9ef7, 0x83 => 0x9ef9, 0x84 => 0x9efb,
    0x85 => 0x9efc, 0x86 => 0x9efd, 0x87 => 0x9f07, 0x88 => 0x9f08,
    0x89 => 0x76b7, 0x8a => 0x9f15, 0x8b => 0x9f21, 0x8c => 0x9f2c,
    0x8d => 0x9f3e, 0x8e => 0x9f4a, 0x8f => 0x9f52, 0x90 => 0x9f54,
    0x91 => 0x9f63, 0x92 => 0x9f5f, 0x93 => 0x9f60, 0x94 => 0x9f61,
    0x95 => 0x9f66, 0x96 => 0x9f67, 0x97 => 0x9f6c, 0x98 => 0x9f6a,
    0x99 => 0x9f77, 0x9a => 0x9f72, 0x9b => 0x9f76, 0x9c => 0x9f95,
    0x9d => 0x9f9c, 0x9e => 0x9fa0, 0x9f => 0x582f, 0xa0 => 0x69c7,
    0xa1 => 0x9059, 0xa2 => 0x7464, 0xa3 => 0x51dc, 0xa4 => 0x7199,
  },
);

1; # end
